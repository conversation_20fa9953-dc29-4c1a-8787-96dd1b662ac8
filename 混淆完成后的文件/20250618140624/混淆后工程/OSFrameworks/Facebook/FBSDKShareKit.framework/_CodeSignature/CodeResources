<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FBSDKShareBridgeOptions.h</key>
		<data>
		PRcVIWI/lUKbLXj1doF8hG3WWQ4=
		</data>
		<key>Headers/FBSDKShareErrorDomain.h</key>
		<data>
		ccdtzvuodQ/lvKIVoHt8KIM9u2U=
		</data>
		<key>Headers/FBSDKShareKit-Swift.h</key>
		<data>
		sJUYcBWDie7F2w76ccQLlvlLhGo=
		</data>
		<key>Headers/FBSDKShareKit.h</key>
		<data>
		3Vlzn/6bUD/wgctBGh6x9fepcJg=
		</data>
		<key>Info.plist</key>
		<data>
		X3nXUMLmyD9IMEZJrNMxuYvPZDU=
		</data>
		<key>Modules/FBSDKShareKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		MHIFHDB4UX4yF5gJM6m5SgL+8qs=
		</data>
		<key>Modules/FBSDKShareKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		eGfF8OneNghvIBPcC1Co+SgfyJw=
		</data>
		<key>Modules/FBSDKShareKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		/omJo/X08UF1rbYFAFKFeh6DAok=
		</data>
		<key>Modules/FBSDKShareKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		eGfF8OneNghvIBPcC1Co+SgfyJw=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		F2phmox2/Nyl1bfdxSueRQruwII=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		sxCL0AuQFbfgrW1VlQ4wZcOI4a0=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FBSDKShareBridgeOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			PRcVIWI/lUKbLXj1doF8hG3WWQ4=
			</data>
			<key>hash2</key>
			<data>
			Cku79zSceCw9+BVL+6EZxnI4F/e9pPBLtOuH6c+/yNQ=
			</data>
		</dict>
		<key>Headers/FBSDKShareErrorDomain.h</key>
		<dict>
			<key>hash</key>
			<data>
			ccdtzvuodQ/lvKIVoHt8KIM9u2U=
			</data>
			<key>hash2</key>
			<data>
			Gq/jBWHIi9/+dLPC1EqzBf+XM4GcHg1JpVUb0DiK6jM=
			</data>
		</dict>
		<key>Headers/FBSDKShareKit-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			sJUYcBWDie7F2w76ccQLlvlLhGo=
			</data>
			<key>hash2</key>
			<data>
			8MZFJDykTKjCYaZjPdfWtqpX6WbQ0WFQTofxJ9v2uME=
			</data>
		</dict>
		<key>Headers/FBSDKShareKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			3Vlzn/6bUD/wgctBGh6x9fepcJg=
			</data>
			<key>hash2</key>
			<data>
			BizbBQ3VvhD8p64UL0pkalRRg0wPwYLFP3IXGeJnkE4=
			</data>
		</dict>
		<key>Modules/FBSDKShareKit.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			MHIFHDB4UX4yF5gJM6m5SgL+8qs=
			</data>
			<key>hash2</key>
			<data>
			Adz4/K7B4m+NitylDSaE+vgPRTltSPmwyQzvWsl3+YE=
			</data>
		</dict>
		<key>Modules/FBSDKShareKit.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			eGfF8OneNghvIBPcC1Co+SgfyJw=
			</data>
			<key>hash2</key>
			<data>
			475dTihNVmpejZXdgWBHPw1+Ci/LtBMlADm0U466mtU=
			</data>
		</dict>
		<key>Modules/FBSDKShareKit.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			/omJo/X08UF1rbYFAFKFeh6DAok=
			</data>
			<key>hash2</key>
			<data>
			JjluF4AKOCiiD9i+IgzeIN0Xt1BIiVadm4f/jnCEk28=
			</data>
		</dict>
		<key>Modules/FBSDKShareKit.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			eGfF8OneNghvIBPcC1Co+SgfyJw=
			</data>
			<key>hash2</key>
			<data>
			475dTihNVmpejZXdgWBHPw1+Ci/LtBMlADm0U466mtU=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			F2phmox2/Nyl1bfdxSueRQruwII=
			</data>
			<key>hash2</key>
			<data>
			Vg70H8UUVY5JoGFeTc5wpApvZn+OvWSQT41qOJiOU2o=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			sxCL0AuQFbfgrW1VlQ4wZcOI4a0=
			</data>
			<key>hash2</key>
			<data>
			U9JlwjJ1udFMf1BlKX8Yr7Hs0o7eZHGVNDBEKwoRDNU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
