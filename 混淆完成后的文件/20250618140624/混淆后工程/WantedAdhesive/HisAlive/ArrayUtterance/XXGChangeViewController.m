






#import "XXGChangeViewController.h"
#import "XXGToast.h"
#import "ZBObjectiveCBeaver.h"
@import WebKit;

@interface XXGChangeViewController ()

@property (nonatomic, strong) UITextField *xxpk_accountTextField;
@property (nonatomic, strong) UITextField *xxpk_passwordTextField;
@property (nonatomic, strong) UITextField *xxpk_newPasswordTextField;

@end

@implementation XXGChangeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.xxpk_closeButton.hidden = NO;
    
    
    self.xxpk_accountTextField = [XXGUIDriver xxpk_textFieldOfAccount];
    self.xxpk_accountTextField.text = [XXGUIDriver xxpk_comeinedBoxName];
    self.xxpk_accountTextField.enabled = NO;
    [self.view addSubview:self.xxpk_accountTextField];
    [self.xxpk_accountTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float40);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    
    self.xxpk_passwordTextField = [XXGUIDriver xxpk_textFieldOfPassword:NO];
    [self.view addSubview:self.xxpk_passwordTextField];
    [self.xxpk_passwordTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_accountTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float8);
        make.left.right.equalTo(self.xxpk_accountTextField);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    UIButton *rightButton = self.xxpk_passwordTextField.rightView.subviews.firstObject;
    [rightButton addTarget:self action:@selector(__xxpk_textFieldButtonClickHandler:) forControlEvents:(UIControlEventTouchUpInside)];
    
    
    self.xxpk_newPasswordTextField = [XXGUIDriver xxpk_textFieldOfPassword:YES];
    [self.view addSubview:self.xxpk_newPasswordTextField];
    [self.xxpk_newPasswordTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_passwordTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float8);
        make.left.right.equalTo(self.xxpk_accountTextField);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    UIButton *nweRightButton = self.xxpk_newPasswordTextField.rightView.subviews.firstObject;
    [nweRightButton addTarget:self action:@selector(__xxpk_newTextFieldButtonClickHandler:) forControlEvents:(UIControlEventTouchUpInside)];
    
    
    UIButton *xxpk_changeBoxKeyButton = [XXGUIDriver xxpk_buttonMainColor:XXGUIDriver.xxpk_string_ui.xxpk_change_boxkey];
    [xxpk_changeBoxKeyButton addTarget:self action:@selector(xxpk_changeBoxKeyButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_changeBoxKeyButton];
    [xxpk_changeBoxKeyButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_newPasswordTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float8);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float40);
    }];
    
    
    UIView *xxpk_tapViewleft = [UIView new];
    xxpk_tapViewleft.userInteractionEnabled = YES;
    xxpk_tapViewleft.backgroundColor = UIColor.clearColor;
    [self.view addSubview:xxpk_tapViewleft];
    [xxpk_tapViewleft mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(XXGUIDriver.xxpk_data_ui.xxpk_float40, XXGUIDriver.xxpk_data_ui.xxpk_float40));
        make.left.bottom.equalTo(self.view);
    }];
    
    UITapGestureRecognizer *xxpk_tapGestureleft = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(xxpk_debuginfo)];
    xxpk_tapGestureleft.numberOfTapsRequired = XXGUIDriver.xxpk_data_ui.xxpk_float5;
    [xxpk_tapViewleft addGestureRecognizer:xxpk_tapGestureleft];
}

- (void)xxpk_debuginfo {
    [ZBLogViewController showFromViewController:self];
}

- (void)__xxpk_textFieldButtonClickHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.xxpk_passwordTextField.secureTextEntry = !self.xxpk_passwordTextField.isSecureTextEntry;
}

- (void)__xxpk_newTextFieldButtonClickHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.xxpk_newPasswordTextField.secureTextEntry = !self.xxpk_newPasswordTextField.isSecureTextEntry;
}

- (void)xxpk_changeBoxKeyButtonAction:(UIButton *)sender  {
    if (self.xxpk_passwordTextField.text.length < XXGUIDriver.xxpk_data_ui.xxpk_float6 ||
        self.xxpk_newPasswordTextField.text.length < XXGUIDriver.xxpk_data_ui.xxpk_float6) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_boxkey_verified completion:nil];
        return;
    }
    if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_changeBoxKeyButtonDidClickWithOldKBoxKey:newBoxKey:completion:)]) {
        [XXGLoadingView showLoadingOnWindow];
        [self.xxpk_delegate xxpk_changeBoxKeyButtonDidClickWithOldKBoxKey:self.xxpk_passwordTextField.text newBoxKey:self.xxpk_newPasswordTextField.text completion:^(id object) {
            [XXGLoadingView hideLoadingFromWindow];
            if ([object boolValue]) {
                [[XXGWindowManager shared] xxpk_dismissWindowWithRootViewController:self.navigationController];
                [XXGToast showBottom:XXGUIDriver.xxpk_string_ui.xxpk_change_boxkey_success];
                
                if (self.xxpk_object && [self.xxpk_object isKindOfClass:[WKWebView class]]) {
                    WKWebView *xxpk_vkview = (WKWebView *)self.xxpk_object;
                    [xxpk_vkview reload];
                }
            }
        }];
    }
}

@end
