






#import "XXGRegistViewController.h"
#import "XXGProtocolLabel.h"
#import "XXGContentTextViewController.h"

@interface XXGRegistViewController ()

@property (nonatomic, strong) UITextField *xxpk_accountTextField;
@property (nonatomic, strong) UITextField *xxpk_passwordTextField;
@property (nonatomic,strong) XXGProtocolLabel *xxpk_protoclLabel;
@end



@implementation XXGRegistViewController

- (XXGProtocolLabel *)xxpk_protoclLabel {
    if (!_xxpk_protoclLabel) {
        _xxpk_protoclLabel = [XXGProtocolLabel xxpk_protocolLabel:NO];
    }
    return _xxpk_protoclLabel;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.xxpk_accountTextField = [XXGUIDriver xxpk_textFieldOfAccount];
    [self.view addSubview:self.xxpk_accountTextField];
    [self.xxpk_accountTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float45);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    
    self.xxpk_passwordTextField = [XXGUIDriver xxpk_textFieldOfPassword:NO];
    [self.view addSubview:self.xxpk_passwordTextField];
    [self.xxpk_passwordTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_accountTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float12);
        make.left.right.equalTo(self.xxpk_accountTextField);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    UIButton *rightButton = self.xxpk_passwordTextField.rightView.subviews.firstObject;
    [rightButton addTarget:self action:@selector(__xxpk_textFieldButtonClickHandler:) forControlEvents:(UIControlEventTouchUpInside)];

    
    UIButton *xxpk_registerButton = [XXGUIDriver xxpk_buttonMainColor:XXGUIDriver.xxpk_string_ui.xxpk_register];
    [xxpk_registerButton addTarget:self action:@selector(xxpk_registerRequestButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_registerButton];
    [xxpk_registerButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_passwordTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float12);
        make.left.right.equalTo(self.xxpk_passwordTextField);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float40);
    }];
    
    [self.view addSubview:self.xxpk_protoclLabel];
    weakify(self);
    [self.xxpk_protoclLabel setXxpk_handleProtocolTap:^{
        strongify(self);
        [self xxpk_userProtocolButtonAction];
    }];
    [self.xxpk_protoclLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float18);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float20);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float20);
    }];
}

- (void)__xxpk_textFieldButtonClickHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.xxpk_passwordTextField.secureTextEntry = !self.xxpk_passwordTextField.isSecureTextEntry;
}

- (void)xxpk_registerRequestButtonAction:(UIButton *)sender {
    if (self.xxpk_accountTextField.text.length < XXGUIDriver.xxpk_data_ui.xxpk_float6) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_account_verified completion:nil];
        return;
    }
    if (self.xxpk_passwordTextField.text.length < XXGUIDriver.xxpk_data_ui.xxpk_float6) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_boxkey_verified completion:nil];
        return;
    }
    if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_registerButtonDidClickWithBoxName:boxKey:completion:)]) {
        [XXGLoadingView showLoadingOnWindow];
        [self.xxpk_delegate xxpk_registerButtonDidClickWithBoxName:self.xxpk_accountTextField.text boxKey:self.xxpk_passwordTextField.text completion:^(id object) {
            [XXGLoadingView hideLoadingFromWindow];
        }];
    }
}


- (void)xxpk_userProtocolButtonAction {
    XXGContentTextViewController *agreement_vc = [XXGContentTextViewController new];
    agreement_vc.xxpk_object = @(YES);
    agreement_vc.xxpk_delegate = self.xxpk_delegate;
    [agreement_vc setXxpk_completion:^(BOOL result) {
        self.xxpk_protoclLabel.xxpk_isChecked = result;
    }];
    [self.navigationController pushViewController:agreement_vc animated:NO];
}

@end
