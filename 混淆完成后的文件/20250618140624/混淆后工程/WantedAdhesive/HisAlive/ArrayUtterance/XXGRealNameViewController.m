






#import "XXGRealNameViewController.h"
#import "SDWebImageManager.h"
#import "XXGToast.h"
#import "NSString+XXGString.h"

@interface XXGRealNameViewController ()

@property (nonatomic, strong) UIImageView *xxpk_rewardImageView;
@property (nonatomic, strong) UITextField *xxpk_nameTextField;
@property (nonatomic, strong) UITextField *xxpk_numberTextField;

@end

@implementation XXGRealNameViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.xxpk_closeButton.hidden = [self.xxpk_object[0] boolValue];
    
    CGFloat topBottomMargin = XXGUIDriver.xxpk_data_ui.xxpk_float12;
    
    UILabel *tipLabel = [XXGUIDriver xxpk_labelNormal:XXGUIDriver.xxpk_string_ui.xxpk_realname_tips];
    tipLabel.numberOfLines = 0;
    [self.view addSubview:tipLabel];
    [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float35);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float30);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float30);
    }];
    
    
    self.xxpk_nameTextField = [XXGUIDriver __xxpk_textField:XXGUIDriver.xxpk_string_ui.xxpk_realname_entername isSecure:NO];
    [self.view addSubview:self.xxpk_nameTextField];
    [self.xxpk_nameTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(tipLabel.mas_bottom).offset(topBottomMargin);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    
    self.xxpk_numberTextField = [XXGUIDriver __xxpk_textField:XXGUIDriver.xxpk_string_ui.xxpk_realname_enternumber isSecure:NO];;
    [self.view addSubview:self.xxpk_numberTextField];
    [self.xxpk_numberTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_nameTextField.mas_bottom).offset(topBottomMargin);
        make.left.right.equalTo(self.xxpk_nameTextField);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    
    UIImageView *rewardImageView = nil;
    if ([self.xxpk_object[1] length] > 0) {
        
        rewardImageView = [[UIImageView alloc] init];
        rewardImageView.backgroundColor = UIColor.lightGrayColor;
        rewardImageView.contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:rewardImageView];
        [rewardImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.xxpk_numberTextField.mas_bottom).offset(topBottomMargin);
            make.width.mas_equalTo(self.xxpk_numberTextField);
            make.centerX.mas_equalTo(0);
        }];
        self.xxpk_rewardImageView = rewardImageView;
        
        [[SDWebImageManager sharedManager] loadImageWithURL:[NSURL URLWithString:self.xxpk_object[1]] options:0 progress:nil completed:^(UIImage * _Nullable image2, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
            dispatch_async(dispatch_get_main_queue(), ^{
                rewardImageView.image = image2;
                CGFloat ratio = image2.size.height / image2.size.width;
                [rewardImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo((XXGUIDriver.xxpk_data_ui.xxpk_contentSizeWidth -XXGUIDriver.xxpk_data_ui.xxpk_float25*2)*ratio);
                }];
                [self.view layoutIfNeeded];
                
                [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.center.mas_equalTo(0);
                    CGFloat height = self.xxpk_rewardImageView ? self.xxpk_rewardImageView.frame.size.height + XXGUIDriver.xxpk_data_ui.xxpk_float12 +XXGUIDriver.xxpk_data_ui.xxpk_contentSizeHeight : XXGUIDriver.xxpk_data_ui.xxpk_contentSizeHeight;
                    
                    make.size.mas_equalTo(CGSizeMake(XXGUIDriver.xxpk_data_ui.xxpk_contentSizeWidth, MIN(height, UIScreen.mainScreen.bounds.size.height)));
                    if (height > UIScreen.mainScreen.bounds.size.height) {
                        [self.xxpk_rewardImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                            make.height.mas_equalTo(self.xxpk_rewardImageView.frame.size.height-(height-UIScreen.mainScreen.bounds.size.height));
                        }];
                    }
                }];
            });
        }];
    }
    
    
    UIButton *xxpk_realButton = [XXGUIDriver xxpk_buttonMainColor:XXGUIDriver.xxpk_string_ui.xxpk_realname_btn];
    [xxpk_realButton addTarget:self action:@selector(xxpk_realButtonDidClick:) forControlEvents:(UIControlEventTouchUpInside)];
    [self.view addSubview:xxpk_realButton];
    [xxpk_realButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float17);
        make.left.right.equalTo(self.xxpk_nameTextField);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float40);
    }];
    
}

- (void)xxpk_realButtonDidClick:(id)sender {

}

@end
