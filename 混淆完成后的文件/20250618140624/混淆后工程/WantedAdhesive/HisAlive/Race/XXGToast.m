






#import "XXGToast.h"
#import "XXGWindowManager.h"


static UIColor *_defaultBackgroundColor = nil;
static UIColor *_defaultTextColor = nil;
static UIFont *_defaultFont = nil;
static CGFloat _defaultCornerRadius = 6.0;
static UIEdgeInsets _defaultContentInset = {10, 16, 10, 16};

@interface XXGToast()
@property (nonatomic, strong) UILabel *messageLabel;
@property (nonatomic, strong) NSTimer *dismissTimer;
@property (nonatomic, assign) XXGToastPosition position;
@end

@implementation XXGToast


- (instancetype)initWithMessage:(NSString *)message {
    self = [super initWithFrame:CGRectZero];
    if (self) {
        self.userInteractionEnabled = NO;
        self.backgroundColor = UIColor.clearColor;
        
        
        UIView *container = [UIView new];
        container.backgroundColor = _defaultBackgroundColor ?:
            [[UIColor blackColor] colorWithAlphaComponent:0.85];
        container.layer.cornerRadius = _defaultCornerRadius;
        container.clipsToBounds = YES;
        container.translatesAutoresizingMaskIntoConstraints = NO;
        [self addSubview:container];
        
        
        _messageLabel = [UILabel new];
        _messageLabel.text = message;
        _messageLabel.textColor = _defaultTextColor ?: UIColor.whiteColor;
        _messageLabel.font = _defaultFont ?: [UIFont systemFontOfSize:14];
        _messageLabel.textAlignment = NSTextAlignmentCenter;
        _messageLabel.numberOfLines = 0;
        _messageLabel.translatesAutoresizingMaskIntoConstraints = NO;
        [container addSubview:_messageLabel];
        
        
        [NSLayoutConstraint activateConstraints:@[
            
            [container.leadingAnchor constraintEqualToAnchor:_messageLabel.leadingAnchor
                                                   constant:-_defaultContentInset.left],
            [container.trailingAnchor constraintEqualToAnchor:_messageLabel.trailingAnchor
                                                    constant:_defaultContentInset.right],
            [container.topAnchor constraintEqualToAnchor:_messageLabel.topAnchor
                                              constant:-_defaultContentInset.top],
            [container.bottomAnchor constraintEqualToAnchor:_messageLabel.bottomAnchor
                                                 constant:_defaultContentInset.bottom],
            
            
            [container.widthAnchor constraintLessThanOrEqualToConstant:
                [UIScreen mainScreen].bounds.size.width - 40]
        ]];
    }
    return self;
}


+ (void)show:(NSString *)message
    duration:(NSTimeInterval)duration
    position:(XXGToastPosition)position
{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        XXGToast *toast = [[XXGToast alloc] initWithMessage:message];
        toast.position = position;
        [toast setupConstraints];
        [toast showWithDuration:duration];
    });
}

- (void)showWithDuration:(NSTimeInterval)duration {
    UIWindow *window = [XXGWindowManager.shared xxpk_currentWindow];
    [window addSubview:self];
    
    
    self.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.leadingAnchor constraintEqualToAnchor:window.leadingAnchor],
        [self.trailingAnchor constraintEqualToAnchor:window.trailingAnchor],
        [self.topAnchor constraintEqualToAnchor:window.topAnchor],
        [self.bottomAnchor constraintEqualToAnchor:window.bottomAnchor]
    ]];
    
    
    [self performEntranceAnimation];
    
    
    if (duration > 0) {
        __weak typeof(self) weakSelf = self;
        self.dismissTimer = [NSTimer scheduledTimerWithTimeInterval:duration repeats:YES block:^(NSTimer * _Nonnull timer) {
            [weakSelf dismiss];
        }];
    }
}

- (void)dismiss {
    [self.dismissTimer invalidate];
    [self performExitAnimationWithCompletion:^{
        [self removeFromSuperview];
    }];
}


- (void)performEntranceAnimation {
    CGAffineTransform transform;
    switch (self.position) {
        case XXGToastPositionTop:
            transform = CGAffineTransformMakeTranslation(0, -100);
            break;
        case XXGToastPositionBottom:
            transform = CGAffineTransformMakeTranslation(0, 100);
            break;
        default:
            transform = CGAffineTransformMakeScale(0.8, 0.8);
            break;
    }
    
    self.alpha = 0;
    self.messageLabel.superview.transform = transform;
    
    [UIView animateWithDuration:0.3
                          delay:0
         usingSpringWithDamping:0.7
          initialSpringVelocity:0.1
                        options:UIViewAnimationOptionCurveEaseOut
                     animations:^{
        self.alpha = 1;
        self.messageLabel.superview.transform = CGAffineTransformIdentity;
    } completion:nil];
}

- (void)performExitAnimationWithCompletion:(void(^)(void))completion {
    CGAffineTransform transform;
    switch (self.position) {
        case XXGToastPositionTop:
            transform = CGAffineTransformMakeTranslation(0, -self.messageLabel.superview.frame.size.height - 50);
            break;
        case XXGToastPositionBottom:
            transform = CGAffineTransformMakeTranslation(0, self.messageLabel.superview.frame.size.height + 50);
            break;
        default:
            transform = CGAffineTransformMakeScale(0.8, 0.8);
            break;
    }
    
    [UIView animateWithDuration:0.25
                     animations:^{
        self.alpha = 0;
        self.messageLabel.superview.transform = transform;
    } completion:^(BOOL finished) {
        if (completion) completion();
    }];
}


- (void)setupConstraints {
    UIView *container = self.messageLabel.superview;
    
    
    switch (self.position) {
        case XXGToastPositionTop: {
            [container.topAnchor constraintEqualToAnchor:self.safeAreaLayoutGuide.topAnchor
                                               constant:30].active = YES;
            break;
        }
        case XXGToastPositionCenter: {
            [container.centerYAnchor constraintEqualToAnchor:self.centerYAnchor].active = YES;
            break;
        }
        case XXGToastPositionBottom: {
            [container.bottomAnchor constraintEqualToAnchor:self.safeAreaLayoutGuide.bottomAnchor
                                                  constant:-30].active = YES;
            break;
        }
    }
    
    
    [container.centerXAnchor constraintEqualToAnchor:self.centerXAnchor].active = YES;
}


+ (void)setDefaultBackgroundColor:(UIColor *)color {
    _defaultBackgroundColor = color;
}

+ (void)setDefaultTextColor:(UIColor *)color {
    _defaultTextColor = color;
}

+ (void)setDefaultFont:(UIFont *)font {
    _defaultFont = font;
}

+ (void)setDefaultCornerRadius:(CGFloat)radius {
    _defaultCornerRadius = radius;
}


+ (void)showTop:(NSString *)message {
    [self show:message duration:2.0 position:XXGToastPositionTop];
}

+ (void)showCenter:(NSString *)message {
    [self show:message duration:2.0 position:XXGToastPositionCenter];
}

+ (void)showBottom:(NSString *)message {
    [self show:message duration:2.0 position:XXGToastPositionBottom];
}

- (void)dealloc {
    
}

@end
