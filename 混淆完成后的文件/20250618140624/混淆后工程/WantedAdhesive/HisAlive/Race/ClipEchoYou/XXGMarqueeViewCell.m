






#import "XXGMarqueeViewCell.h"
#import "XXGMQTTTopicInfo.h"
#import "Masonry.h"
#import "UIColor+XXGColor.h"

@implementation XXGMarqueeViewCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        self.backgroundColor = [UIColor clearColor];
        
        self.clipsToBounds = YES;
        
        [self addSubview:self.messageLabel];
        
        [self.messageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsMake(0, 4, 0, 0));
        }];
        
    }
    return self;
}

- (void)setModel:(XXGMQTTTopicInfo *)model
{
    _model = model;
    _messageLabel.text = model.xxpk_message;
    _messageLabel.font = [UIFont systemFontOfSize:model.xxpk_style_text_font_size];
    _messageLabel.textColor = [UIColor xxpk_colorWithHexString:model.xxpk_style_text_color];
    self.backgroundColor = [[UIColor xxpk_colorWithHexString:model.xxpk_style_background_color] colorWithAlphaComponent:model.xxpk_style_background_alpha];
    self.layer.cornerRadius = 2;
}

- (UILabel *)messageLabel {
    if (!_messageLabel) {
        _messageLabel = [[UILabel alloc] init];
        _messageLabel.backgroundColor = [UIColor clearColor];
        _messageLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _messageLabel;
}

@end
