







#import <UIKit/UIKit.h>

typedef enum : NSUInteger {
    EStatusOfBarrage_Start,
    EStatusOfBarrage_Stop,
    EStatusOfBarrage_AfterDelay
} EStatusOfBarrage;

@interface XXGLiveBarrageCell : UIView{
    id _model;
}



@property (nonatomic, assign) CGSize            barrageSize;



@property (nonatomic, assign) CGFloat           barrageSpeed;



@property (nonatomic, strong) id                model;


@property (nonatomic, assign) NSInteger         channelCount;


@property (nonatomic, assign) CGFloat           margin;


@property (nonatomic, assign) CGFloat           afterDelayExit;



@property (nonatomic, assign) EStatusOfBarrage status;


- (void)startAnimations:(void(^)(void))animations completion:(void(^)(BOOL))completion;


- (void)pause;


- (void)resume;
@end
