







#import <UIKit/UIKit.h>
#import "XXGLiveBarrageCell.h"

@protocol XXGLiveBarrageDelegate;


@interface XXGLiveBarrage : UIView


@property (weak, nonatomic) id<XXGLiveBarrageDelegate> delegate;


- (void)insertBarrages:(NSArray <XXGLiveBarrageCell *> *)barrages;


- (void)start;


- (void)stop;

@end


@protocol XXGLiveBarrageDelegate <NSObject>

@optional


- (void)xxpk_barrageView:(XXGLiveBarrage *)barrageView didSelectedCell:(XXGLiveBarrageCell *)cell;


- (void)xxpk_barrageViewCompletedCurrentAnimations:(XXGLiveBarrage *)barrageView;


- (void)xxpk_barrageView:(XXGLiveBarrage *)barrageView willDisplayCell:(XXGLiveBarrageCell *)cell;


- (void)xxpk_barrageView:(XXGLiveBarrage *)barrageView didEndDisplayingCell:(XXGLiveBarrageCell *)cell;

@end
