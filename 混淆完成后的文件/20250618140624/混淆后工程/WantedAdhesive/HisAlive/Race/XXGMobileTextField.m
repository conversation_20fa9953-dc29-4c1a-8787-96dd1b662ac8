






#import "XXGMobileTextField.h"
#import "XXGUIDriver.h"
#import "Masonry.h"
#import "NSString+XXGString.h"
#import "XXGCountryCodeButton.h"

@interface XXGMobileTextField()

@property (nonatomic,strong) XXGCountryCodeButton *xxpk_countryCodeButton;

@end

@implementation XXGMobileTextField

- (instancetype)initWithController:(UIViewController *)vc
{
    self = [super init];
    if (self) {
        self.layer.borderColor = [XXGUIDriver xxpk_mainColor].CGColor;
        self.layer.borderWidth = 0.6;
        self.layer.cornerRadius = 2;
        

        self.xxpk_countryCodeButton = [[XXGCountryCodeButton alloc] initWithCurrentViewController:vc];
        [self addSubview:self.xxpk_countryCodeButton];
        [self.xxpk_countryCodeButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(0);
            make.left.mas_equalTo(0);
            make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
        }];
        
        [self.xxpk_countryCodeButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
        
        
        self.xxpk_mobileTextField = [XXGUIDriver xxpk_textFieldOfMobile];
        self.xxpk_mobileTextField.layer.borderWidth = 0;
        self.xxpk_mobileTextField.layer.cornerRadius = 2.f;
        self.xxpk_mobileTextField.layer.maskedCorners = kCALayerMaxXMaxYCorner;
        self.xxpk_mobileTextField.layer.masksToBounds = YES;
        [self addSubview:self.xxpk_mobileTextField];
        [self.xxpk_mobileTextField mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(0);
make.left.mas_equalTo(self.xxpk_countryCodeButton.mas_right);

            make.right.mas_equalTo(0);
            make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
        }];
    }
    return self;
}

- (NSString *)xxpk_dial_code {
return [NSString stringWithFormat:@"%@%@",XXGUIDriver.xxpk_data_ui.xxpk_code_jia,[self.xxpk_countryCodeButton.xxpk_currentCountry.xxpk_dialCode stringByReplacingOccurrencesOfString:@" " withString:@""]];
    return @"";
}

- (NSString *)xxpk_mobile_num {
    return self.xxpk_mobileTextField.text.xxpk_isNotEmpty ? [NSString stringWithFormat:@"%@%@",self.xxpk_dial_code,self.xxpk_mobileTextField.text] : @"";
}
@end
