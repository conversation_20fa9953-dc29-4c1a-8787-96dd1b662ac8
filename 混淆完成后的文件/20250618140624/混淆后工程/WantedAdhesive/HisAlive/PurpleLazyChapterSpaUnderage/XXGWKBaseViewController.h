






#import "XXGBaseViewController.h"
#import <WebKit/WebKit.h>
#import <WebKit/WKFoundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGWKBaseViewController : XXGBaseViewController

@property (nonatomic, copy) NSString * xxpk_router;

@property (nonatomic,strong) WKWebView * xxpk_wkview;


- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation;

- (void)xxpk_makeMethodActionOfAPPScheme:(NSURL *)URL;
@end

NS_ASSUME_NONNULL_END
