






#import "XXGUCenterViewController.h"
#import "XXGWindowManager.h"
#import "XXGFloatView.h"
#import "UIColor+XXGColor.h"
#import "UIDevice+XXGDevice.h"
#import "UIImage+XXGImage.h"
#import "XXGPlayKitConfig.h"

@interface XXGUCenterViewController ()

@property (nonatomic, strong) UIView * tipNavView;
@property (nonatomic, strong) UIImageView * tipView;

@end

@implementation XXGUCenterViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = XXGUIDriver.xxpk_ucenter_bangs_color ? [UIColor xxpk_colorWithHexString:XXGUIDriver.xxpk_ucenter_bangs_color]:UIColor.whiteColor;
    self.xxpk_router = self.xxpk_object;
    [XXGFloatView xxpk_hide];
    
    self.tipNavView = [UIView new];
    self.tipNavView.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:self.tipNavView];
    
    self.tipView = [[UIImageView alloc] initWithImage:[UIImage xxpk_imageBundleOfName:XXGUIDriver.xxpk_data_ui.xxpk_img_ucenter_colse]];;
    [self.view addSubview:self.tipView];
    
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self xxpk_setOrientation];
}

-(void)viewWillDisappear:(BOOL)animated {
    if ([XXGUIDriver xxpk_comeinedBoxName] && XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_docker.xxpk_status) {
        [XXGFloatView xxpk_show];
    }
    [super viewWillDisappear:animated];
    
}

- (CGSize)xxpk_ucenterSize {
    if ([UIDevice isIPad]) {
        return CGSizeMake(XXGUIDriver.xxpk_ucenter_size, XXGUIDriver.xxpk_ucenter_size);
    }
    UIWindow *currentWindow = [[XXGWindowManager shared] xxpk_currentWindow];
    UIEdgeInsets safe = currentWindow.safeAreaInsets;
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
    if (orientation == UIInterfaceOrientationPortrait || orientation == UIInterfaceOrientationPortraitUpsideDown) {
        if (![UIDevice hasNotch]) {
            return CGSizeMake(UIScreen.mainScreen.bounds.size.width, XXGUIDriver.xxpk_ucenter_size);
        }
        return CGSizeMake(UIScreen.mainScreen.bounds.size.width, XXGUIDriver.xxpk_ucenter_size + safe.bottom);
    }else {
        if (![UIDevice hasNotch]) {
            return CGSizeMake(XXGUIDriver.xxpk_ucenter_size,UIScreen.mainScreen.bounds.size.height);
        }
        if (orientation == UIInterfaceOrientationLandscapeRight) {
            return CGSizeMake(XXGUIDriver.xxpk_ucenter_size + safe.left,UIScreen.mainScreen.bounds.size.height);
        }else {
            return CGSizeMake(XXGUIDriver.xxpk_ucenter_size + 5,UIScreen.mainScreen.bounds.size.height);
        }
    }
}

- (void)xxpk_setOrientation {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
    if (orientation == UIInterfaceOrientationPortrait || orientation == UIInterfaceOrientationPortraitUpsideDown) {
        [self.tipNavView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view.mas_top);
            make.left.right.equalTo(self.view);
            make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        }];
        [self.tipView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.tipNavView.mas_top);
            make.centerX.equalTo(self.view);
            make.size.mas_equalTo(CGSizeMake(XXGUIDriver.xxpk_data_ui.xxpk_float75, XXGUIDriver.xxpk_data_ui.xxpk_float22));
        }];
        [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerX.bottom.mas_equalTo(0);
            make.size.mas_equalTo(self.xxpk_ucenterSize);
        }];
        [self.xxpk_wkview mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.right.mas_equalTo(0);
            make.height.mas_equalTo(XXGUIDriver.xxpk_ucenter_size);
        }];
        self.view.transform = CGAffineTransformMakeTranslation(0, self.xxpk_ucenterSize.height);
    }else {
        [self.tipNavView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.view.mas_right);
            make.top.bottom.equalTo(self.view);
            make.width.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        }];
        UIImage *originalImage = [UIImage xxpk_imageBundleOfName:XXGUIDriver.xxpk_data_ui.xxpk_img_ucenter_colse];
        UIImage *rotatedImage = [UIImage imageWithCGImage:originalImage.CGImage
                                                    scale:originalImage.scale
                                              orientation:UIImageOrientationRight]; 
        self.tipView.image = rotatedImage;
        [self.tipView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.tipNavView.mas_right);
            make.centerY.equalTo(self.view);
            make.size.mas_equalTo(CGSizeMake(XXGUIDriver.xxpk_data_ui.xxpk_float22, XXGUIDriver.xxpk_data_ui.xxpk_float75));
        }];
        [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.left.mas_equalTo(0);
            make.size.mas_equalTo(self.xxpk_ucenterSize);
        }];
        [self.xxpk_wkview mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.right.mas_equalTo(0);
            make.width.mas_equalTo(XXGUIDriver.xxpk_ucenter_size);
        }];
        self.view.transform = CGAffineTransformMakeTranslation(-self.xxpk_ucenterSize.width, 0);
    }
    [UIView animateWithDuration:0.3 animations:^{
        self.view.transform = CGAffineTransformIdentity;
    }];
}

- (void)xxpk_touchesBlank:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super xxpk_touchesBlank:touches withEvent:event];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
    if (orientation == UIInterfaceOrientationPortrait || orientation == UIInterfaceOrientationPortraitUpsideDown) {
        [UIView animateWithDuration:0.3 animations:^{
            self.view.transform = CGAffineTransformMakeTranslation(0, self.xxpk_ucenterSize.height);;
        } completion:^(BOOL finished) {
            [[XXGWindowManager shared] xxpk_dismissWindowWithRootViewController:self.navigationController];
        }];
    }else {
        [UIView animateWithDuration:0.3 animations:^{
            self.view.transform = CGAffineTransformMakeTranslation(-self.xxpk_ucenterSize.width, 0);
        } completion:^(BOOL finished) {
            [[XXGWindowManager shared] xxpk_dismissWindowWithRootViewController:self.navigationController];
        }];
    }
}

@end
