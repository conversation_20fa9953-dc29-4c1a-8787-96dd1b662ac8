






#import <Foundation/Foundation.h>
@class WKWebView,XXGSelectProductItem;

typedef void(^XXGUIkitDelegateCompletion)(id object);

@protocol XXGUIkitDelegate <NSObject>

@optional
- (void)xxpk_customerServiceButtonDidClick:(NSString *)url;
- (void)xxpk_gusetButtonDidClick:(XXGUIkitDelegateCompletion)completion;

- (void)xxpk_facebookButtondDidClick:(XXGUIkitDelegateCompletion)completion;
- (void)xxpk_VKButtonDidClick:(XXGUIkitDelegateCompletion)completion;
- (void)xxpk_poopoButtonDidClick:(XXGUIkitDelegateCompletion)completion;
- (void)xxpk_selectComeinButtonDidClickWithBoxName:(NSString *)boxName completion:(XXGUIkitDelegateCompletion)completion;
- (void)xxpk_selectDeleteBoxWithBoxName:(NSString *)boxName completion:(XXGUIkitDelegateCompletion)completion;
- (void)xxpk_registerButtonDidClickWithBoxName:(NSString *)boxName boxKey:(NSString *)boxKey completion:(XXGUIkitDelegateCompletion)completion;
- (void)xxpk_accountComeinButtonDidClickWithBoxName:(NSString *)boxName boxKey:(NSString *)boxKey completion:(XXGUIkitDelegateCompletion)completion;
- (void)xxpk_sendCodeButtonDidClickWithType:(NSString *)type xxpk_moblil:(NSString *)xxpk_moblil dialCode:(NSString *)dialCode completion:(XXGUIkitDelegateCompletion)completion;
- (void)xxpk_mobileComeinButtonDidClickWithMobile:(NSString *)moblile code:(NSString *)code dialCode:(NSString *)dialCode completion:(XXGUIkitDelegateCompletion)completion;
- (void)xxpk_forgetButtonDidClickWithMobile:(NSString *)mobile code:(NSString *)code dialCode:(NSString *)dialCode newKey:(NSString *)newKey completion:(XXGUIkitDelegateCompletion)completion;
- (void)xxpk_changeBoxKeyButtonDidClickWithOldKBoxKey:(NSString *)oldBoxKey newBoxKey:(NSString *)newBoxKey completion:(XXGUIkitDelegateCompletion)completion;
- (void)xxpk_bindMobileButtonDidClickWithMobile:(NSString *)mobile code:(NSString *)code dialCode:(NSString *)dialCode completion:(XXGUIkitDelegateCompletion)completion;
- (void)xxpk_wkView:(WKWebView *)wkView makeMethodAction:(NSString *)method arg:(id)arg;
- (void)xxpk_selectPayMethodPayButtonDidClickOfProductItem:(XXGSelectProductItem *)productItem;
- (void)xxpk_selectPayMethodCloseButtonDidClick;
@end

