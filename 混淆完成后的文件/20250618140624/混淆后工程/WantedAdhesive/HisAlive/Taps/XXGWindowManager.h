






#import <Foundation/Foundation.h>
@import UIKit;

NS_ASSUME_NONNULL_BEGIN

@interface XXGWindowManager : NSObject

+ (instancetype)shared;

- (UIWindow *)xxpk_firstWindow;
- (UIWindow *)xxpk_currentWindow;

- (void)__xxpk_showNoStackWindowWithRootViewController:(UIViewController *)rootVC;
- (void)xxpk_showWindowWithRootViewController:(UIViewController *)rootVC;
- (void)xxpk_showWindowWithRootView:(UIView *)view;
- (void)xxpk_dismissWindowWithRootViewController:(UIViewController *)rootViewController;
- (void)xxpk_dismissWindow;
- (void)xxpk_dismissAllWindows;

@end

NS_ASSUME_NONNULL_END
