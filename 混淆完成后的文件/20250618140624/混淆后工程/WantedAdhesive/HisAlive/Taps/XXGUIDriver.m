






#import "XXGUIDriver.h"
#import "NSString+XXGString.h"
#import "UIImageView+WebCache.h"
#import "XXGAppInfo.h"
#import "UIColor+XXGColor.h"
#import "UIImage+XXGImage.h"
#import "Masonry.h"

#import "XXGPlayKitConfig.h"
#import "XXGBoxManager.h"
#import "XXGLocaleString.h"

static XXGLocalizedUI *_xxpk_string_ui = nil;
static XXGDatasUI *_xxpk_data_ui = nil;

@implementation XXGUIDriver

+ (XXGLocalizedUI *)xxpk_string_ui {
    if (!_xxpk_string_ui) {
        _xxpk_string_ui = [XXGLocaleString xxpk_loadLanguagesWithClass:[XXGLocalizedUI class]];
    }
    return _xxpk_string_ui;
}

+ (XXGDatasUI *)xxpk_data_ui {
    if (!_xxpk_data_ui) {
        _xxpk_data_ui = [XXGLocaleString xxpk_loadDatasWithClass:[XXGDatasUI class]];
    }
    return _xxpk_data_ui;
}

+ (NSString *)xxpk_comeinedBoxName {
    return [XXGBoxManager xxpk_comeinedBox].xxpk_boxName;
}

+ (NSString *)xxpk_comeinedBoxToken {
    return [XXGBoxManager xxpk_comeinedBox].xxpk_boxToken;
}

+ (CGFloat)xxpk_ucenter_size {
    return XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_box_center.xxpk_size?:self.xxpk_data_ui.xxpk_ucenterW;
}

+ (NSString *)xxpk_docker_image {
    return XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_docker.xxpk_image;
}

+ (NSString *)xxpk_ucenter_bangs_color {
    return XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_box_center.xxpk_bangs_color;
}

+ (NSString *)xxpk_contentText1 {
    return XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_service.xxpk_agreement;
}
+ (NSString *)xxpk_contentText2 {
    return XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_service.xxpk_privacy;
}

+ (BOOL)xxpk_isComeinOnly {
    return XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_skin_comein_only;
}

+ (BOOL)xxpk_closeButtonHidden {
    return [XXGPlayKitConfig shared].xxpk_closeButtonHidden;
}

+ (NSArray *)xxpk_boxsFromLocal {
    NSArray *localBoxContents = [XXGBoxManager xxpk_getBoxsContentFromLocal];
    NSMutableArray *boxs = [NSMutableArray arrayWithCapacity:localBoxContents.count];
    
    for (XXGBoxContent *obj in localBoxContents) {
        NSString *image = self.xxpk_data_ui.xxpk_img_comein_register;
        switch (obj.xxpk_boxType) {
            case XXGComeinTypeGuest:
                image = self.xxpk_data_ui.xxpk_img_comein_guest;
                break;
            case XXGComeinTypeAccount:
            case XXGComeinTypeRegister:
                image = self.xxpk_data_ui.xxpk_img_comein_register;
                break;
            case XXGComeinTypeMobile:
                image = self.xxpk_data_ui.xxpk_img_comein_mobile;
                break;

case XXGComeinTypeFacebook:
                image = self.xxpk_data_ui.xxpk_img_comein_facebook;
                break;
            case XXGComeinTypeVK:
                image = self.xxpk_data_ui.xxpk_img_comein_vk;
                break;
            default:
                image = self.xxpk_data_ui.xxpk_img_comein_guest;
                break;
        }
        
        NSArray *box = @[obj.xxpk_boxName ?: @"",image,obj.xxpk_lastComeinTime];
        [boxs addObject:box];
    }
    
    
    NSArray *sortedBoxs = [boxs sortedArrayUsingComparator:^NSComparisonResult(NSArray *a, NSArray *b) {
        double t1 = [a[2] doubleValue];
        double t2 = [b[2] doubleValue];
        if (t1 > t2) {
            return NSOrderedAscending; 
        } else if (t1 < t2) {
            return NSOrderedDescending;
        }
        return NSOrderedSame;
    }];
    
    return sortedBoxs;
}

+ (CGSize)xxpk_mainContentViewSize {
    return CGSizeMake(self.xxpk_data_ui.xxpk_contentSizeWidth, self.xxpk_data_ui.xxpk_contentSizeHeight);
}

+ (UIColor *)xxpk_textColor{
    return [UIColor xxpk_colorWithHexString:XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_skin_theme.xxpk_textColor?:self.xxpk_data_ui.xxpk_textColor];
}

+ (UIColor *)xxpk_mainColor{
    return [UIColor xxpk_colorWithHexString:XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_skin_theme.xxpk_mainColor?:self.xxpk_data_ui.xxpk_mainColor];
}

+ (UIColor *)xxpk_backgroundColor{
    return [UIColor xxpk_colorWithHexString:XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_skin_theme.xxpk_backgroundColor?:self.xxpk_data_ui.xxpk_backgroundColor];
}

+ (void)xxpk_closeButtonAction {
    if (XXGPlayKitConfig.shared.xxpk_comeinStatus != XXGPlayKitComeInStatusFinish) {
        XXGPlayKitConfig.shared.xxpk_comeinStatus = XXGPlayKitComeInStatusNot;
    }
}

+ (UIView *)xxpk_logoView {
    if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_skin_logo.xxpk_isNotEmpty) {
        UIImageView *view = [[UIImageView alloc] init];
        [view sd_setImageWithURL:[NSURL URLWithString:XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_skin_logo]];
        view.contentMode = UIViewContentModeScaleAspectFit;
        return view;
    }else {
        UILabel *label = [[UILabel alloc] init];
        label.text = [XXGAppInfo xxpk_appName];
        label.textColor = [self xxpk_mainColor];
        label.font = [UIFont systemFontOfSize:30];
        label.textAlignment = NSTextAlignmentCenter;
        return label;
    }
}

+ (UILabel *)xxpk_labelNormal:(NSString *)title {
    UILabel *label = [UILabel new];
    label.text = title;
    label.textColor = [self xxpk_mainColor];
    label.font = [UIFont systemFontOfSize:13];
    return label;
}

+ (UIButton *)xxpk_buttonNormal:(NSString *)title {
    UIButton *button = [[UIButton alloc] init];
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitleColor:[self xxpk_mainColor] forState:UIControlStateNormal];
    [button setTitleColor:UIColor.lightGrayColor forState:UIControlStateHighlighted];
    button.titleLabel.font = [UIFont systemFontOfSize:13];
    return button;
}

+ (UIButton *)xxpk_buttonMainColor:(NSString *)title {
    
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    [button setTitle:title forState:UIControlStateNormal];
    [button setBackgroundImage:[UIImage xxpk_imageWithColor:[self xxpk_mainColor]] forState:UIControlStateNormal];
    [button setBackgroundImage:[UIImage xxpk_imageWithColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]] forState:UIControlStateHighlighted];
    button.titleLabel.font = [UIFont systemFontOfSize:16];
    button.layer.cornerRadius = 2.f;
    button.layer.masksToBounds = YES;
    return button;
}

+ (NSArray *)xxpk_comeinBtnsWithTarget:(id)target action:(SEL)action {
    
    NSMutableArray *array = [[NSMutableArray alloc] init];
    
    for (XXGSkinModel *obj in XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_skin_btns) {
        UIView *button = [self __xxpk_comeinViewWithTitle:obj.xxpk_labelText xxpktitleColor:[UIColor xxpk_colorWithHexString:obj.xxpk_labelColor] xxpkimage:[self __xxpk_comeinBtnImg:obj] xxpkidentifier:obj.xxpk_name target:target action:action];
        [array addObject:button];
    }
    
    return array;
}

+ (NSString *)__xxpk_comeinBtnImg:(XXGSkinModel *)obj {
    
    static NSDictionary<NSString *, NSString *> *map;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        map = @{
            
            self.xxpk_data_ui.xxpk_guest    : self.xxpk_data_ui.xxpk_img_comein_guest,
            self.xxpk_data_ui.xxpk_mobile   : self.xxpk_data_ui.xxpk_img_comein_mobile,
            self.xxpk_data_ui.xxpk_register : self.xxpk_data_ui.xxpk_img_comein_register,

self.xxpk_data_ui.xxpk_vk       : self.xxpk_data_ui.xxpk_img_comein_vk,
            self.xxpk_data_ui.xxpk_facebook : self.xxpk_data_ui.xxpk_img_comein_facebook,
            self.xxpk_data_ui.xxpk_poopo : self.xxpk_data_ui.xxpk_img_comein_guest
        };
    });
    if (obj.xxpk_image.xxpk_isEmpty) {
        
        obj.xxpk_image = map[obj.xxpk_name];
    }
    return obj.xxpk_image;
}

+ (UIView *)__xxpk_comeinViewWithTitle:(NSString *)title
                      xxpktitleColor:(UIColor *)titleColor
                           xxpkimage:(NSString *)image
                      xxpkidentifier:(NSString *)idf
                              target:(id)target
                              action:(SEL)action {
    
    UIView *view = [[UIView alloc] init];
    view.backgroundColor = UIColor.clearColor;
    
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.layer.masksToBounds = YES;
    button.accessibilityIdentifier = idf;
    
    if ([self __xxpk_isValidUrl:image]) {
        [[SDWebImageManager sharedManager] loadImageWithURL:[NSURL URLWithString:image] options:0 progress:nil completed:^(UIImage * _Nullable image2, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [button setImage:image2 forState:UIControlStateNormal];
            });
        }];

    }else {
        UIImage *btImage = [[UIImage xxpk_imageBundleOfName:image] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        button.tintColor = [self xxpk_mainColor];
        [button setImage:btImage forState:UIControlStateNormal];
    }
    
    button.contentEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 0);
    [[button imageView] setContentMode:UIViewContentModeScaleAspectFill];
    button.contentHorizontalAlignment= UIControlContentHorizontalAlignmentFill;
    button.contentVerticalAlignment = UIControlContentVerticalAlignmentFill;
    [button addTarget:target action:action forControlEvents:(UIControlEventTouchUpInside)];
    [view addSubview:button];
    
    UILabel *label = [XXGUIDriver xxpk_labelNormal:title];
    label.textColor = titleColor;
    label.textAlignment = NSTextAlignmentCenter;
    label.font = [UIFont systemFontOfSize:12];
    label.numberOfLines = 0;
    [view addSubview:label];
    
    [button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(view);
        make.size.equalTo(view);
    }];
    
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(view.mas_bottom).offset(8);
        make.left.right.equalTo(view);
    }];
    
    return view;
}

+ (BOOL)__xxpk_isValidUrl:(NSString *)url
{
    NSString *regex =@"[a-zA-z]+://[^\\s]*";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    return [predicate evaluateWithObject:url];
}

+ (UITextField *)xxpk_textFieldOfVerificationCode {
    UITextField *textField = [self __xxpk_textField:self.xxpk_string_ui.xxpk_enterVerificationCode isSecure:NO];
    textField.textContentType = UITextContentTypeOneTimeCode;
    return textField;
}

+ (UITextField *)xxpk_textFieldOfMobile {
    UITextField *textField = [self __xxpk_textField:self.xxpk_string_ui.xxpk_enterMobile isSecure:NO];
    textField.keyboardType = UIKeyboardTypeNumberPad;
    return textField;
}

+ (UITextField *)xxpk_textFieldOfAccount {
    return [self __xxpk_textField:self.xxpk_string_ui.xxpk_enterBox isSecure:NO];
}

+ (UITextField *)xxpk_textFieldOfPassword:(BOOL)isNew {
    UITextField *textField = [self __xxpk_textField:isNew?self.xxpk_string_ui.xxpk_enterBoxKeyNew:self.xxpk_string_ui.xxpk_enterBoxKey isSecure:YES];
    [self __xxpk_setTextFieldRightPadding:textField forSize:CGSizeMake(XXGUIDriver.xxpk_data_ui.xxpk_float38, XXGUIDriver.xxpk_data_ui.xxpk_float38)];
    UIButton * rightButton = [UIButton buttonWithType:UIButtonTypeCustom];
    UIImage *lookImage = [UIImage xxpk_imageBundleOfName:self.xxpk_data_ui.xxpk_img_ps_look];
    UIImage *unlookImage = [UIImage xxpk_imageBundleOfName:self.xxpk_data_ui.xxpk_img_ps_unlook];
    rightButton.frame = CGRectMake(0, 0, XXGUIDriver.xxpk_data_ui.xxpk_float38, XXGUIDriver.xxpk_data_ui.xxpk_float38);
    [rightButton setImage:lookImage forState:UIControlStateNormal];
    [rightButton setImage:unlookImage forState:UIControlStateSelected];
    CGFloat imageviewtap = (XXGUIDriver.xxpk_data_ui.xxpk_float38 - 24)/2;
    [rightButton setImageEdgeInsets:UIEdgeInsetsMake(imageviewtap, imageviewtap, imageviewtap, imageviewtap)];
    rightButton.contentMode = UIViewContentModeScaleAspectFit;
    [textField.rightView addSubview:rightButton];
    return textField;
}

+ (UITextField *)__xxpk_textField:(NSString *)placeholder isSecure:(BOOL)isSecure {
    UITextField *textField = [UITextField new];
    textField.secureTextEntry = isSecure;
    textField.clearButtonMode = UITextFieldViewModeWhileEditing;
    textField.autocorrectionType = UITextAutocorrectionTypeNo;
    textField.autocapitalizationType = UITextAutocapitalizationTypeNone;
    textField.font = [UIFont systemFontOfSize:15];
    textField.layer.borderColor = [self xxpk_mainColor].CGColor;
    textField.layer.borderWidth = 0.6;
    textField.layer.cornerRadius = 2;
    textField.backgroundColor = UIColor.whiteColor;
    textField.textColor = UIColor.darkGrayColor;
    textField.attributedPlaceholder = [[NSAttributedString alloc] initWithString:placeholder attributes:@{NSForegroundColorAttributeName: [UIColor lightGrayColor]}];
    [self __xxpk_setTextFieldLeftPadding:textField forSize:CGSizeMake(10, XXGUIDriver.xxpk_data_ui.xxpk_float38)];
    textField.rightViewMode = UITextFieldViewModeAlways;
    return textField;
}

+ (void)__xxpk_setTextFieldLeftPadding:(UITextField *)textField forSize:(CGSize)size
{
    CGRect frame = {{0,0},size};
    UIView *leftview = [[UIView alloc] initWithFrame:frame];
    textField.leftViewMode = UITextFieldViewModeAlways;
    textField.leftView = leftview;
}

+ (void)__xxpk_setTextFieldRightPadding:(UITextField *)textField forSize:(CGSize)size
{
    CGRect frame = {{0,0},size};
    UIView *rightview = [[UIView alloc] initWithFrame:frame];
    textField.rightViewMode = UITextFieldViewModeAlways;
    textField.rightView = rightview;
}
@end
