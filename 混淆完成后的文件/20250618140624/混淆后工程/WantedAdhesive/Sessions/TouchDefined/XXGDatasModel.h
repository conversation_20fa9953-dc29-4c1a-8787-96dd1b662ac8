






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGDatasModel : NSObject

@property(nonatomic, copy) NSString *xxpk_app;
@property(nonatomic, assign) CGFloat xxpk_contentSizeWidth;
@property(nonatomic, assign) CGFloat xxpk_contentSizeHeight;

@property(nonatomic, copy) NSString *xxpk_tools_iap_priceString;
@property(nonatomic, copy) NSString *xxpk_tools_iap_seriverOrder;
@property(nonatomic, copy) NSString *xxpk_tools_iap_userId;
@property(nonatomic, copy) NSString *xxpk_tools_iap_codeString;
@property(nonatomic, copy) NSString *xxpk_tools_iap_productIdentifier;
@property(nonatomic, copy) NSString *xxpk_tools_iap_applicationUsername;
@property(nonatomic, copy) NSString *xxpk_tools_iap_transactionStatus;
@property(nonatomic, copy) NSString *xxpk_tools_iap_transactionDate;
@property(nonatomic, copy) NSString *xxpk_tools_iap_domain;
@property(nonatomic, copy) NSString *xxpk_tools_laiguo;
@property(nonatomic, copy) NSString *xxpk_tools_photolibaddusgaedes;
@property(nonatomic, copy) NSString *xxpk_tools_support_anguage;
@property(nonatomic, copy) NSString *xxpk_tools_countries;
@property(nonatomic, copy) NSString *xxpk_tools_languages;
@property(nonatomic, copy) NSString *xxpk_tools_logger_queue_label;
@property(nonatomic, copy) NSString *xxpk_tools_logger_level_verbose;
@property(nonatomic, copy) NSString *xxpk_tools_logger_level_debug;
@property(nonatomic, copy) NSString *xxpk_tools_logger_level_info;
@property(nonatomic, copy) NSString *xxpk_tools_logger_level_warning;
@property(nonatomic, copy) NSString *xxpk_tools_logger_level_error;
@property(nonatomic, copy) NSString *xxpk_tools_logger_level_all;
@property(nonatomic, copy) NSString *xxpk_tools_logger_color_verbose;
@property(nonatomic, copy) NSString *xxpk_tools_logger_color_debug;
@property(nonatomic, copy) NSString *xxpk_tools_logger_color_info;
@property(nonatomic, copy) NSString *xxpk_tools_logger_color_warning;
@property(nonatomic, copy) NSString *xxpk_tools_logger_color_error;
@property(nonatomic, copy) NSString *xxpk_tools_logger_color_all;
@property(nonatomic, copy) NSString *xxpk_tools_logger_formatter;
@property(nonatomic, copy) NSString *xxpk_tools_logger_format;
@property(nonatomic, copy) NSString *xxpk_tools_logger_format_file;
@property(nonatomic, copy) NSString *xxpk_tools_logger_null;
@property(nonatomic, copy) NSString *xxpk_tools_logger_empty;
@property(nonatomic, copy) NSString *xxpk_tools_logger_text;
@property(nonatomic, copy) NSString *xxpk_tools_logger_text_chars;
@property(nonatomic, copy) NSString *xxpk_tools_logger_data;
@property(nonatomic, copy) NSString *xxpk_tools_logger_no_error;
@property(nonatomic, copy) NSString *xxpk_tools_logger_code;
@property(nonatomic, copy) NSString *xxpk_tools_logger_desc;
@property(nonatomic, copy) NSString *xxpk_tools_logger_info;
@property(nonatomic, copy) NSString *xxpk_tools_logger_items;
@property(nonatomic, copy) NSString *xxpk_tools_logger_data_bytes;
@property(nonatomic, copy) NSString *xxpk_tools_logger_date_format;
@property(nonatomic, copy) NSString *xxpk_tools_logger_file_date_format;
@property(nonatomic, copy) NSString *xxpk_tools_logger_uptime_format;
@property(nonatomic, copy) NSString *xxpk_tools_logger_file_separator;
@property(nonatomic, copy) NSString *xxpk_tools_logger_request_format;
@property(nonatomic, copy) NSString *xxpk_tools_logger_response_format;
@property(nonatomic, copy) NSString *xxpk_tools_logger_network_error_format;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ellipsis;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_title;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_not_init;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_no_logs;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_info;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_no_files;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_ok;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_share_logs;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_share_all;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_share_file;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_cancel;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_select_date;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_all_logs;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_today;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_yesterday;
@property(nonatomic, copy) NSString *xxpk_tools_logger_ui_date_format;
@property(nonatomic, strong) NSDictionary *xxpk_tools_country_model;


@property(nonatomic, copy) NSString *xxpk_img_comein_guest;
@property(nonatomic, copy) NSString *xxpk_img_comein_mobile;
@property(nonatomic, copy) NSString *xxpk_img_comein_register;
@property(nonatomic, copy) NSString *xxpk_img_comein_vx;
@property(nonatomic, copy) NSString *xxpk_img_comein_oneclick;
@property(nonatomic, copy) NSString *xxpk_img_comein_vk;
@property(nonatomic, copy) NSString *xxpk_img_comein_facebook;
@property(nonatomic, copy) NSString *xxpk_img_onclick_back;
@property(nonatomic, copy) NSString *xxpk_img_ps_look;
@property(nonatomic, copy) NSString *xxpk_img_ps_unlook;
@property(nonatomic, copy) NSString *xxpk_img_check_box_on;
@property(nonatomic, copy) NSString *xxpk_img_check_box_off;
@property(nonatomic, copy) NSString *xxpk_img_float_ball;
@property(nonatomic, copy) NSString *xxpk_img_sp_cell_ns;
@property(nonatomic, copy) NSString *xxpk_img_sp_cell_ss;
@property(nonatomic, copy) NSString *xxpk_img_code_pulldown;
@property(nonatomic, copy) NSString *xxpk_img_ucenter_colse;

@property(nonatomic, copy) NSString *xxpk_p_block_orderExtra;

@property(nonatomic, copy) NSString *xxpk_guest;
@property(nonatomic, copy) NSString *xxpk_mobile;
@property(nonatomic, copy) NSString *xxpk_register;
@property(nonatomic, copy) NSString *xxpk_vx;
@property(nonatomic, copy) NSString *xxpk_one_click;
@property(nonatomic, copy) NSString *xxpk_vk;
@property(nonatomic, copy) NSString *xxpk_facebook;
@property(nonatomic, copy) NSString *xxpk_poopo;

@end

NS_ASSUME_NONNULL_END
