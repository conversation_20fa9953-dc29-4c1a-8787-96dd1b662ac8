


#import <Foundation/Foundation.h>
#import "SDWebImageCompat.h"
#import "SDImageCoder.h"

NS_ASSUME_NONNULL_BEGIN


@interface SDImageFramePool : NSObject


+ (instancetype)registerProvider:(id<SDAnimatedImageProvider>)provider;

+ (void)unregisterProvider:(id<SDAnimatedImageProvider>)provider;


- (void)prefetchFrameAtIndex:(NSUInteger)index;


@property (nonatomic, assign) NSUInteger maxBufferCount;

@property (nonatomic, assign) NSUInteger maxConcurrentCount;


@property (nonatomic, readonly) NSUInteger currentFrameCount;
- (nullable UIImage *)frameAtIndex:(NSUInteger)index;
- (void)setFrame:(nullable UIImage *)frame atIndex:(NSUInteger)index;
- (void)removeFrameAtIndex:(NSUInteger)index;
- (void)removeAllFrames;

NS_ASSUME_NONNULL_END

@end
