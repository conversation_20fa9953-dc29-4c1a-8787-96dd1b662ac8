


#import "SDWebImageCompat.h"



@interface UIImage (ForceDecode)



@property (nonatomic, assign) BOOL sd_isDecoded;



+ (nullable UIImage *)sd_decodedImageWithImage:(nullable UIImage *)image;



+ (nullable UIImage *)sd_decodedAndScaledDownImageWithImage:(nullable UIImage *)image;



+ (nullable UIImage *)sd_decodedAndScaledDownImageWithImage:(nullable UIImage *)image limitBytes:(NSUInteger)bytes;

@end
