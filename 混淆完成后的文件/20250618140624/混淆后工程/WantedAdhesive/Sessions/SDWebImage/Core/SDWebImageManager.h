


#import "SDWebImageCompat.h"
#import "SDWebImageOperation.h"
#import "SDImageCacheDefine.h"
#import "SDImageLoader.h"
#import "SDImageTransformer.h"
#import "SDWebImageCacheKeyFilter.h"
#import "SDWebImageCacheSerializer.h"
#import "SDWebImageOptionsProcessor.h"

typedef void(^SDExternalCompletionBlock)(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL);

typedef void(^SDInternalCompletionBlock)(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL);



@interface SDWebImageCombinedOperation : NSObject <SDWebImageOperation>



- (void)cancel;


@property (nonatomic, assign, readonly, getter=isCancelled) BOOL cancelled;



@property (strong, nonatomic, nullable, readonly) id<SDWebImageOperation> cacheOperation;



@property (strong, nonatomic, nullable, readonly) id<SDWebImageOperation> loaderOperation;

@end


@class SDWebImageManager;



@protocol SDWebImageManagerDelegate <NSObject>

@optional



- (BOOL)imageManager:(nonnull SDWebImageManager *)imageManager shouldDownloadImageForURL:(nonnull NSURL *)imageURL;



- (BOOL)imageManager:(nonnull SDWebImageManager *)imageManager shouldBlockFailedURL:(nonnull NSURL *)imageURL withError:(nonnull NSError *)error;

@end



@interface SDWebImageManager : NSObject



@property (weak, nonatomic, nullable) id <SDWebImageManagerDelegate> delegate;



@property (strong, nonatomic, readonly, nonnull) id<SDImageCache> imageCache;



@property (strong, nonatomic, readonly, nonnull) id<SDImageLoader> imageLoader;



@property (strong, nonatomic, nullable) id<SDImageTransformer> transformer;



@property (nonatomic, strong, nullable) id<SDWebImageCacheKeyFilter> cacheKeyFilter;



@property (nonatomic, strong, nullable) id<SDWebImageCacheSerializer> cacheSerializer;



@property (nonatomic, strong, nullable) id<SDWebImageOptionsProcessor> optionsProcessor;



@property (nonatomic, assign, readonly, getter=isRunning) BOOL running;



@property (nonatomic, class, nullable) id<SDImageCache> defaultImageCache;



@property (nonatomic, class, nullable) id<SDImageLoader> defaultImageLoader;



@property (nonatomic, class, readonly, nonnull) SDWebImageManager *sharedManager;



- (nonnull instancetype)initWithCache:(nonnull id<SDImageCache>)cache loader:(nonnull id<SDImageLoader>)loader NS_DESIGNATED_INITIALIZER;



- (nullable SDWebImageCombinedOperation *)loadImageWithURL:(nullable NSURL *)url
                                                   options:(SDWebImageOptions)options
                                                  progress:(nullable SDImageLoaderProgressBlock)progressBlock
                                                 completed:(nonnull SDInternalCompletionBlock)completedBlock;



- (nullable SDWebImageCombinedOperation *)loadImageWithURL:(nullable NSURL *)url
                                                   options:(SDWebImageOptions)options
                                                   context:(nullable SDWebImageContext *)context
                                                  progress:(nullable SDImageLoaderProgressBlock)progressBlock
                                                 completed:(nonnull SDInternalCompletionBlock)completedBlock;



- (void)cancelAll;



- (void)removeFailedURL:(nonnull NSURL *)url;



- (void)removeAllFailedURLs;



- (nullable NSString *)cacheKeyForURL:(nullable NSURL *)url;



- (nullable NSString *)cacheKeyForURL:(nullable NSURL *)url context:(nullable SDWebImageContext *)context;

@end
