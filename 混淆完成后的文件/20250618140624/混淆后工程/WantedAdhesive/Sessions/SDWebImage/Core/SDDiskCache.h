


#import "SDWebImageCompat.h"

@class SDImageCacheConfig;


@protocol SDDiskCache <NSObject>


@required


- (nullable instancetype)initWithCachePath:(nonnull NSString *)cachePath config:(nonnull SDImageCacheConfig *)config;



- (BOOL)containsDataForKey:(nonnull NSString *)key;



- (nullable NSData *)dataForKey:(nonnull NSString *)key;



- (void)setData:(nullable NSData *)data forKey:(nonnull NSString *)key;



- (nullable NSData *)extendedDataForKey:(nonnull NSString *)key;



- (void)setExtendedData:(nullable NSData *)extendedData forKey:(nonnull NSString *)key;



- (void)removeDataForKey:(nonnull NSString *)key;



- (void)removeAllData;



- (void)removeExpiredData;



- (nullable NSString *)cachePathForKey:(nonnull NSString *)key;



- (NSUInteger)totalCount;



- (NSUInteger)totalSize;

@end



@interface SDDiskCache : NSObject <SDDiskCache>


@property (nonatomic, strong, readonly, nonnull) SDImageCacheConfig *config;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;



- (void)moveCacheDirectoryFromPath:(nonnull NSString *)srcPath toPath:(nonnull NSString *)dstPath;

@end
