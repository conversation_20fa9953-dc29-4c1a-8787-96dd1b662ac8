


#import "SDWebImageCompat.h"





typedef void (^SDGraphicsImageDrawingActions)(CGContextRef _Nonnull context);

typedef NS_ENUM(NSInteger, SDGraphicsImageRendererFormatRange) {
    
    SDGraphicsImageRendererFormatRangeUnspecified = -1,
    
    SDGraphicsImageRendererFormatRangeAutomatic = 0,
    
    SDGraphicsImageRendererFormatRangeExtended,
    
    SDGraphicsImageRendererFormatRangeStandard
};


@interface SDGraphicsImageRendererFormat : NSObject

#if SD_UIKIT

@property (nonatomic, strong, nonnull) UIGraphicsImageRendererFormat *uiformat API_AVAILABLE(ios(10.0), tvos(10.0));
#endif



@property (nonatomic) CGFloat scale;



@property (nonatomic) BOOL opaque;





@property (nonatomic) SDGraphicsImageRendererFormatRange preferredRange;


- (nonnull instancetype)init;


+ (nonnull instancetype)preferredFormat;

@end


@interface SDGraphicsImageRenderer : NSObject




- (nonnull instancetype)initWithSize:(CGSize)size;





- (nonnull instancetype)initWithSize:(CGSize)size format:(nonnull SDGraphicsImageRendererFormat *)format;





- (nonnull UIImage *)imageWithActions:(nonnull NS_NOESCAPE SDGraphicsImageDrawingActions)actions;

@end
