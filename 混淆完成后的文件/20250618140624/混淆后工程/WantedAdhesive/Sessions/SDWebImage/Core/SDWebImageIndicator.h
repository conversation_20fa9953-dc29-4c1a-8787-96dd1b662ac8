


#import "SDWebImageCompat.h"

#if SD_UIKIT || SD_MAC



@protocol SDWebImageIndicator <NSObject>

@required


@property (nonatomic, strong, readonly, nonnull) UIView *indicatorView;



- (void)startAnimatingIndicator;



- (void)stopAnimatingIndicator;

@optional


- (void)updateIndicatorProgress:(double)progress;

@end





@interface SDWebImageActivityIndicator : NSObject <SDWebImageIndicator>

#if SD_UIKIT
@property (nonatomic, strong, readonly, nonnull) UIActivityIndicatorView *indicatorView;
#else
@property (nonatomic, strong, readonly, nonnull) NSProgressIndicator *indicatorView;
#endif

@end



@interface SDWebImageActivityIndicator (Conveniences)

#if !SD_VISION


@property (nonatomic, class, nonnull, readonly) SDWebImageActivityIndicator *grayIndicator;

@property (nonatomic, class, nonnull, readonly) SDWebImageActivityIndicator *grayLargeIndicator;

@property (nonatomic, class, nonnull, readonly) SDWebImageActivityIndicator *whiteIndicator;

@property (nonatomic, class, nonnull, readonly) SDWebImageActivityIndicator *whiteLargeIndicator;
#endif


@property (nonatomic, class, nonnull, readonly) SDWebImageActivityIndicator *largeIndicator;

@property (nonatomic, class, nonnull, readonly) SDWebImageActivityIndicator *mediumIndicator;

@end





@interface SDWebImageProgressIndicator : NSObject <SDWebImageIndicator>

#if SD_UIKIT
@property (nonatomic, strong, readonly, nonnull) UIProgressView *indicatorView;
#else
@property (nonatomic, strong, readonly, nonnull) NSProgressIndicator *indicatorView;
#endif

@end



@interface SDWebImageProgressIndicator (Conveniences)


@property (nonatomic, class, nonnull, readonly) SDWebImageProgressIndicator *defaultIndicator;
#if SD_UIKIT

@property (nonatomic, class, nonnull, readonly) SDWebImageProgressIndicator *barIndicator API_UNAVAILABLE(tvos);
#endif

@end

#endif
