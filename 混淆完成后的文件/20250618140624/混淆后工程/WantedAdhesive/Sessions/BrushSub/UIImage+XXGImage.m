






#import "UIImage+XXGImage.h"
#import "NSData+SunHope.h"
#import "NSString+XXGString.h"
#import "XXGLocaleString.h"

@implementation UIImage (XXGImage)

+ (UIImage *)xxpk_imageWithColor:(UIColor *)color {
    
    CGRect rect=CGRectMake(0.0f,0.0f, 1.0f,1.0f);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    UIImage *theImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return theImage;
}

+ (UIImage *)xxpk_imageBundleOfName:(NSString *)imageName {
    
    if (!imageName) {
        return nil;
    }
    
    UIImage *image = nil;
    
    NSString *imgPath= [[XXGLocaleString getBundle] stringByAppendingPathComponent:imageName];
    
    if (imgPath.xxpk_isNotEmpty) {
        
        image = [UIImage imageWithContentsOfFile:imgPath];
    }
    
    if (!image) {
        
        NSData *encryptedData = [NSData dataWithContentsOfFile:imgPath];
       
       
        image = [encryptedData xxpk_decryptImageWithRandomFactor];
    }
    
    return image;
}

- (UIImage *)xxpk_imageWithTintColor:(UIColor *)tintColor {
   
    if (!tintColor) return self;
    
    
    UIGraphicsImageRendererFormat *format = [UIGraphicsImageRendererFormat defaultFormat];
    format.scale = self.scale;
    format.opaque = NO;
    
    UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:self.size format:format];
    
    return [renderer imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull context) {
        
        [tintColor setFill];
        
        
        CGRect bounds = CGRectMake(0, 0, self.size.width, self.size.height);
        [self drawInRect:bounds];
        
        
        CGContextSetBlendMode(context.CGContext, kCGBlendModeSourceIn);
        CGContextFillRect(context.CGContext, bounds);
    }];
}
@end
