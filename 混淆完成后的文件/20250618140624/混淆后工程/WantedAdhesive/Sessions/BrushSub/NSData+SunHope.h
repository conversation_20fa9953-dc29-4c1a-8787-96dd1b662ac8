






#import <Foundation/Foundation.h>
#import "NSString+XXGMd5.h"
@class UIImage;

NS_ASSUME_NONNULL_BEGIN

@interface NSData (SunHope)

- (NSString *)xxpk_sign:(NSString *)appSecret;

- (NSData *)unDataZip;

- (NSData *)enDataZip;


- (UIImage *)xxpk_decryptImageWithRandomFactor;


- (id)xxpk_decryptJsonWithRandomFactor;





- (NSData *)___xxpk_encryptWithRandomIV;



- (NSData *)__xxpk_decryptDataWithRandomIV;
@end

NS_ASSUME_NONNULL_END
