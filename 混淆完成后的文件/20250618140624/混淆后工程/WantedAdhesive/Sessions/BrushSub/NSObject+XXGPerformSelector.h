






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSObject (XXGPerformSelector)

//- (id)xxpk_performSelector:(SEL)selector withObject:(id _Nullable)object,...NS_REQUIRES_NIL_TERMINATION;
- (id)xxpk_performSelector:(SEL)aSelector;

- (id)xxpk_performSelector:(SEL)aSelector
                withObject:(id)object1;

- (id)xxpk_performSelector:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2;

- (id)xxpk_performSelector:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3;

- (id)xxpk_performSelector:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3
                withObject:(id)object4;

- (id)xxpk_performSelector:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3
                withObject:(id)object4
                withObject:(id)object5;

- (id)xxpk_performSelector:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3
                withObject:(id)object4
                withObject:(id)object5
                withObject:(id)object6;
@end

NS_ASSUME_NONNULL_END
