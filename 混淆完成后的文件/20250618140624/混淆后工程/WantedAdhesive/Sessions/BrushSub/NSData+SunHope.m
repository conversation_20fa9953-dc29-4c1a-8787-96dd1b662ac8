






#import "NSData+SunHope.h"
#import <CommonCrypto/CommonCrypto.h>
#import <zlib.h>
#pragma clang diagnostic ignored "-Wcast-qual"
@import UIKit;

@implementation NSData (SunHope)

- (NSString *)xxpk_sign:(NSString *)appSecret {
    return [[self md5] stringByAppendingString:appSecret].md5;
}

- (NSString*)md5 {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    //1: 创建一个MD5对象
    CC_MD5_CTX md5;
    //2: 初始化MD5
    CC_MD5_Init(&md5);
    //3: 准备MD5加密
    CC_MD5_Update(&md5, self.bytes, (CC_LONG)self.length);
    //4: 准备一个字符串数组, 存储MD5加密之后的数据
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    //5: 结束MD5加密
    CC_MD5_Final(result, &md5);
#pragma clang diagnostic pop
    NSMutableString *resultString = [NSMutableString string];
    //6:从result数组中获取最终结果
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [resultString appendFormat:@"%02X", result[i]];
    }
    return resultString.lowercaseString;
}

- (NSData *)gzippedDataWithCompressionLevel:(float)level
{
    if (self.length == 0 || [self isGzippedData])
    {
        return self;
    }
    
    z_stream stream;
    stream.zalloc = Z_NULL;
    stream.zfree = Z_NULL;
    stream.opaque = Z_NULL;
    stream.avail_in = (uint)self.length;
    stream.next_in = (Bytef *)(void *)self.bytes;
    stream.total_out = 0;
    stream.avail_out = 0;
    
    static const NSUInteger ChunkSize = 16384;
    
    NSMutableData *output = nil;
    int compression = (level < 0.0f)? Z_DEFAULT_COMPRESSION: (int)(roundf(level * 9));
    if (deflateInit2(&stream, compression, Z_DEFLATED, 31, 8, Z_DEFAULT_STRATEGY) == Z_OK)
    {
        output = [NSMutableData dataWithLength:ChunkSize];
        while (stream.avail_out == 0)
        {
            if (stream.total_out >= output.length)
            {
                output.length += ChunkSize;
            }
            stream.next_out = (uint8_t *)output.mutableBytes + stream.total_out;
            stream.avail_out = (uInt)(output.length - stream.total_out);
            deflate(&stream, Z_FINISH);
        }
        deflateEnd(&stream);
        output.length = stream.total_out;
    }
    
    return output;
}

- (NSData *)gzippedData
{
    return [self gzippedDataWithCompressionLevel:-1.0f];
}

- (NSData *)ungzippedData
{
    if (self.length == 0 || ![self isGzippedData])
    {
        return self;
    }
    
    z_stream stream;
    stream.zalloc = Z_NULL;
    stream.zfree = Z_NULL;
    stream.avail_in = (uint)self.length;
    stream.next_in = (Bytef *)self.bytes;
    stream.total_out = 0;
    stream.avail_out = 0;
    
    NSMutableData *output = nil;
    if (inflateInit2(&stream, 47) == Z_OK)
    {
        int status = Z_OK;
        output = [NSMutableData dataWithCapacity:self.length * 2];
        while (status == Z_OK)
        {
            if (stream.total_out >= output.length)
            {
                output.length += self.length / 2;
            }
            stream.next_out = (uint8_t *)output.mutableBytes + stream.total_out;
            stream.avail_out = (uInt)(output.length - stream.total_out);
            status = inflate (&stream, Z_SYNC_FLUSH);
        }
        if (inflateEnd(&stream) == Z_OK)
        {
            if (status == Z_STREAM_END)
            {
                output.length = stream.total_out;
            }
        }
    }
    
    return output;
}

- (BOOL)isGzippedData
{
    const UInt8 *bytes = (const UInt8 *)self.bytes;
    return (self.length >= 2 && bytes[0] == 0x1f && bytes[1] == 0x8b);
}

- (NSData *)unDataZip {
    NSMutableData *data = self.mutableCopy;
    if (data.length > 8) {
        const UInt8 header[] = {0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00};
        [data replaceBytesInRange:NSMakeRange(0, 8) withBytes:header];
    }
    return [data ungzippedData];
}

- (NSData *)enDataZip {
    NSMutableData *data = [self gzippedData].mutableCopy;
    if (data.length > 8) {
        UInt8 *bytes = data.mutableBytes;
        const NSArray *randIndexes = @[@0,@1,@2,@4,@5,@6,@7];
        for (NSNumber *index in randIndexes) {
            bytes[index.unsignedIntValue] = arc4random_uniform(256);
        }
    }
    return data;
}




- (NSData *)___xxpk_encryptWithRandomIV {
    if (self.length == 0) {
        return [NSData data];
    }
    
    
    uint8_t iv[16];
    int status = SecRandomCopyBytes(kSecRandomDefault, sizeof(iv), iv);
    if (status != errSecSuccess) {
        return nil; 
    }
    
    
    NSData *ivData = [NSData dataWithBytes:iv length:16];
    
    
    NSMutableData *encryptedData = [NSMutableData dataWithCapacity:self.length];
    const uint8_t *plainBytes = self.bytes;
    
    for (NSUInteger i = 0; i < self.length; i++) {
        uint8_t cipherByte = plainBytes[i] ^ iv[i % 16];
        [encryptedData appendBytes:&cipherByte length:1];
    }
    
    
    NSMutableData *resultData = [NSMutableData dataWithData:ivData];
    [resultData appendData:encryptedData];
    
    return resultData;
}



- (NSData *)__xxpk_decryptDataWithRandomIV {
    if (self.length < 16) {
        return [NSData data];
    }
    
    
    NSData *ivData = [self subdataWithRange:NSMakeRange(0, 16)];
    const uint8_t *iv = ivData.bytes;
    
    
    NSData *cipherData = [self subdataWithRange:NSMakeRange(16, self.length - 16)];
    const uint8_t *cipherBytes = cipherData.bytes;
    
    
    NSMutableData *plainData = [NSMutableData dataWithCapacity:cipherData.length];
    
    for (NSUInteger i = 0; i < cipherData.length; i++) {
        uint8_t plainByte = cipherBytes[i] ^ iv[i % 16];
        [plainData appendBytes:&plainByte length:1];
    }
    
    return [plainData copy];
}


- (UIImage *)xxpk_decryptImageWithRandomFactor {
    if (self == nil) {
        return nil; 
    }

    NSData *decryptedData = [self __xxpk_decryptDataWithRandomIV]; 
    if (decryptedData == nil) {
        return nil; 
    }

    UIImage *image = [UIImage imageWithData:decryptedData]; 
    return image;
}


- (id)xxpk_decryptJsonWithRandomFactor {
    if (self == nil) {
        return nil; 
    }

    NSData *decryptedData = [self __xxpk_decryptDataWithRandomIV]; 
    if (!decryptedData) {
        return nil;
    }

    NSError *error = nil;
    id jsonObject = [NSJSONSerialization JSONObjectWithData:decryptedData options:0 error:&error];
    if (error) {
        
        return nil;
    }
    return jsonObject;
}




- (NSString *)dataToHexString:(NSData *)data {
    if (!data || data.length == 0) {
        return @"";
    }
    
    NSMutableString *hexString = [NSMutableString stringWithCapacity:data.length * 2];
    const unsigned char *bytes = data.bytes;
    
    for (NSUInteger i = 0; i < data.length; i++) {
        [hexString appendFormat:@"%02X", bytes[i]];
    }
    
    return hexString;
}



- (NSData *)hexStringToData:(NSString *)hexString {
    if (!hexString || hexString.length == 0) {
        return [NSData data];
    }
    
    NSMutableData *data = [NSMutableData dataWithCapacity:hexString.length / 2];
    unsigned char byte;
    char byteChars[3] = {'\0','\0','\0'};
    
    for (NSUInteger i = 0; i < hexString.length; i += 2) {
        byteChars[0] = [hexString characterAtIndex:i];
        byteChars[1] = [hexString characterAtIndex:i+1];
        byte = strtol(byteChars, NULL, 16);
        [data appendBytes:&byte length:1];
    }
    
    return data;
}

@end
