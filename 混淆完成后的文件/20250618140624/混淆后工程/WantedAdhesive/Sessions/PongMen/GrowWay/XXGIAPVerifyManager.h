







#import <Foundation/Foundation.h>

@class XXGIAPTransactionModel;
NS_ASSUME_NONNULL_BEGIN

@protocol XXGIAPVerifyManagerDelegate <NSObject>

- (void)startPaymentTransactionVerifingModel:(XXGIAPTransactionModel *)transactionModel;

@end


@interface XXGIAPVerifyManager : NSObject



@property (nonatomic,weak)id<XXGIAPVerifyManagerDelegate> delegate;

@property (nonatomic, assign) BOOL isVerifing;



- (instancetype)initWithKeychainService:(NSString *)keychainService keychainAccount:(NSString *)keychainAccount;


- (NSMutableArray <XXGIAPTransactionModel *>*)fetchAllPaymentTransactionModel;



- (void)appendPaymentTransactionModel:(XXGIAPTransactionModel *)transactionModel;




- (void)startPaymentTransactionVerifingModel:(XXGIAPTransactionModel *)transactionModel;





- (void)updatePaymentTransactionModelStatus:(XXGIAPTransactionModel *)transactionModel;




-(void)updatePaymentTransactionCheckCount:(XXGIAPTransactionModel *)transactionModel;



- (void)finishPaymentTransactionVerifingModel:(XXGIAPTransactionModel *)transactionModel;




- (void)deletePaymentTransactionModel:(XXGIAPTransactionModel *)transactionModel;





- (void)cleanAllModels;




- (void)savePaymentTransactionModels:(NSArray <XXGIAPTransactionModel *>*)models;
@end

NS_ASSUME_NONNULL_END
