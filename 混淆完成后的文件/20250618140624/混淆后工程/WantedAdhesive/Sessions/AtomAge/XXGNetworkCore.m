






#import "XXGNetworkCore.h"

#define weakify(obj) __weak typeof(obj) weak##obj = obj;
#define strongify(obj) __strong typeof(obj) obj = weak##obj;

@interface XXGNetworkCore()

@property (nonatomic,strong) NSURLSession *xxpk_session;

@end

@implementation XXGNetworkCore


+ (instancetype)shared {
    static XXGNetworkCore *shared = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        shared = [[super allocWithZone:NULL] init];
        shared.xxpk_session = [NSURLSession sessionWithConfiguration:[NSURLSessionConfiguration defaultSessionConfiguration] delegate:shared delegateQueue:[[NSOperationQueue alloc] init]];
        shared.xxpk_session.delegateQueue.maxConcurrentOperationCount = 1;
    });
    return shared;
}

- (void)xxpk_sendBaseRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock
                     success:(void(^)(NSDictionary * responseObject))success
                     failure:(void(^)(NSError *error))failure
                  retryCount:(NSInteger)retryCount {

    [self xxpk_sendRequest:request
                   process:processBlock
                   success:success
                   failure:failure
                retryCount:retryCount
            currentAttempt:0];
}


- (void)xxpk_sendRequest:(NSMutableURLRequest *)request
                 process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock
                 success:(void(^)(NSDictionary * responseObject))success
                 failure:(void(^)(NSError *error))failure
              retryCount:(NSInteger)retryCount
          currentAttempt:(NSInteger)currentAttempt {

    weakify(self);
    NSURLSessionDataTask *task = [self.xxpk_session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        strongify(self);
        
        NSError *finalError = [self handleError:error response:response data:data];
        if (finalError) {
            

            
            if (currentAttempt < retryCount) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self xxpk_sendRequest:request process:processBlock success:success failure:failure retryCount:retryCount currentAttempt:currentAttempt + 1];
                });
                return;
            }

            
            if (failure) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    failure(finalError);
                });
            }
            return;
        }

        
        NSData *processedData = processBlock ? processBlock(data) : data;
        if (!processedData) {
            NSError *processingError = [NSError errorWithDomain:@"NetworkCore"
                                                           code:-30002
                                                       userInfo:@{NSLocalizedDescriptionKey : @"Data processing failed"}];
            if (failure) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    failure(processingError);
                });
            }
            return;
        }

        NSError *jsonError;
        NSDictionary *jsonResponse = [NSJSONSerialization JSONObjectWithData:processedData options:0 error:&jsonError];

        if (!jsonError && [jsonResponse isKindOfClass:[NSDictionary class]]) {
            if (success) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    success(jsonResponse);
                });
            }
        } else {
            
            if (failure) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    failure(jsonError);
                });
            }
        }
    }];

    [task resume];
}


- (NSError *)handleError:(NSError *)error response:(NSURLResponse *)response data:(NSData *)data {
    if (error) {
        return error;
    }

    if (!data) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:-30001
                               userInfo:@{NSLocalizedDescriptionKey : @"The data is empty."}];
    }

    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
    if (![httpResponse isKindOfClass:[NSHTTPURLResponse class]] || httpResponse.statusCode != 200) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:httpResponse.statusCode
                               userInfo:@{NSLocalizedDescriptionKey : [NSString stringWithFormat:@"HTTPError，code: %ld", (long)httpResponse.statusCode]}];
    }

    return nil;
}

@end
