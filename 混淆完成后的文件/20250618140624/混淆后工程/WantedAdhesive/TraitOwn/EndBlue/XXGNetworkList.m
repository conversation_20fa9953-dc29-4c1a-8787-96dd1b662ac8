






#import "XXGNetworkList.h"
#import "XXGNetwork.h"
#import "NSObject+XXGModel.h"
#import "XXGPlayKitConfig.h"
#import "XXGNetworkMonitor.h"
#import "XXGBaseURL.h"
#import "NSData+SunHope.h"
#import "NSString+XXGString.h"
#import "XXGBoxManager.h"
#import "XXGSkinModel.h"
#import "XXGPlayKitCore.h"
#import "XXGAlertView.h"

@implementation XXGNetworkList


- (void)xxpk_networkStart:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure  {
    
    NSDictionary *params = [[XXGPlayKitConfig shared].xxpk_body xxpk_modelToDict];
    params[__data_core.xxpk_device][__data_core.xxpk_extend] = [[XXGPlayKitConfig shared].xxpk_deviceInfo xxpk_modelToDict];
    
    [self xxpk_sendRequest:XXGBaseURL.shared.xxpk_currentBaseUrl params:params success:^(NSDictionary * _Nonnull responseObject) {
        
        [XXGPlayKitConfig shared].xxpk_netList = [XXGNetListModel xxpk_modelWithDict:responseObject[__data_core.xxpk_api_list]];
        
        [XXGPlayKitConfig shared].xxpk_security = responseObject[__data_core.xxpk_app][__data_core.xxpk_secret];
        
        [XXGPlayKitConfig shared].xxpk_body.xxpk_deviceId = responseObject[__data_core.xxpk_device][__data_core.xxpk_id];
        
        [XXGPlayKitConfig shared].xxpk_adaptionCof = [XXGAdaptionCof xxpk_modelWithDict:responseObject[__data_core.xxpk_config]];
        
        [XXGPlayKitConfig shared].xxpk_serverInfo = [XXGServerInfo xxpk_modelWithDict:responseObject[__data_core.xxpk_server]];

[XXGPlayKitConfig shared].xxpk_extraParams = [XXGExtraParams xxpk_modelWithDict:responseObject[__data_core.xxpk_extra_params_str]];
        
        if (success) {
            success(responseObject);
        }
        [[XXGBaseURL shared] xxpk_setCurrentBaseUrlIdx];
        
    } failure:^(NSError * _Nonnull error) {
        if (!XXGNetworkMonitor.xxpk_isConnected || error.code == __data_core.xxpk_net_code_error) {
            if (failure) {
                failure(error);
            }
        }else {
            [[XXGBaseURL shared] xxpk_next];
            [self xxpk_networkStart:success failure:failure];
        }
    }];
}

- (void)xxpk_handleNetworkComeinSuccess:(XXGBoxContent *)box {
    
    box.xxpk_lastComeinTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    
    
    XXGBoxContent *xxpk_comeined_box =[XXGBoxManager xxpk_localBoxContentWithBoxName:box.xxpk_boxName];
    if (xxpk_comeined_box) {
        box.xxpk_boxType = xxpk_comeined_box.xxpk_boxType;
    }
    
    
    [XXGBoxManager xxpk_setComeinedBox:box];
    
    
    [XXGBoxManager xxpk_saveBoxContentToLocal:box];
}

- (NSString *)xxpk_handleNetworkComeinUrl:(XXGComeinType)type {
    
    static NSDictionary<NSNumber *, NSString *> *map;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        map = @{
            
            @(XXGComeinTypeGuest)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_comein_guest?:@"",
            @(XXGComeinTypeRegister)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_register?:@"",
            @(XXGComeinTypeAccount)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_comein?:@"",
            @(XXGComeinTypeMobile)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_comein_mobile?:@"",
            @(XXGComeinTypeToken)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_comein_token?:@"",

@(XXGComeinTypeFacebook)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_register?:@"",
            @(XXGComeinTypeVK)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_register?:@"",
        };
    });
    
    
    return map[@(type)];
}


- (void)xxpk_recomeinWithUrl:(NSString *)url
                      params:(NSDictionary *)params
                     success:(void(^)(NSDictionary *responseObject))success
                     failure:(void(^)(NSError *error))failure {
    if ([self.xxpk_url isEqual:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeToken]]) {
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_comeinedBox];
        [self xxpk_networkAccountWithBoxName:xxpk_box.xxpk_boxName boxKey:xxpk_box.xxpk_boxKey success:success failure:failure];
    }else {
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_comeinedBox];
        [self xxpk_networkAccountWithBoxName:xxpk_box.xxpk_boxName boxKey:xxpk_box.xxpk_boxKey success:^(NSDictionary * _Nonnull responseObject) {
            [self xxpk_sendRequest:url params:params success:success failure:failure];
        } failure:^(NSError * _Nonnull error) {
            if (error.code == __data_core.xxpk_net_code_error) {
                [XXGPlayKitCore.shared xxpk_logout];
                [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_comeinError message:error.localizedDescription completion:nil];
            }else {
                failure(error);
            }
        }];
    }
}


- (void)xxpk_networkRegisterWithBoxName:(NSString *)boxName boxKey:(NSString *)boxKey success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_boxName] = boxName;
    _params[__data_core.xxpk_boxKey] = boxKey;
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeRegister] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[__data_core.xxpk_box]];
        xxpk_box.xxpk_boxType = XXGComeinTypeRegister;
        xxpk_box.xxpk_boxName = boxName;
        xxpk_box.xxpk_boxKey = boxKey;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}




- (void)xxpk_networkAccountWithBoxName:(NSString *)boxName boxKey:(NSString *)boxKey success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_boxName] = boxName;
    _params[__data_core.xxpk_boxKey] = boxKey;
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeAccount] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[__data_core.xxpk_box]];
        xxpk_box.xxpk_boxType = XXGComeinTypeAccount;
        xxpk_box.xxpk_boxKey = boxKey;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}


- (void)xxpk_networkGuest:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_localBoxContentWithBoxType:(XXGComeinTypeGuest)];
    if (xxpk_box) {
        [XXGBoxManager xxpk_setComeinedBox:xxpk_box];
        [self xxpk_networkToken:success failure:failure];
        return;
    }
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeGuest] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[__data_core.xxpk_box]];
        xxpk_box.xxpk_boxType = XXGComeinTypeGuest;
        xxpk_box.xxpk_boxKey = xxpk_box.xxpk_boxKey.md5.lowercaseString;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
        
        [[XXGPlayKitCore shared] xxpk_showUIofSavePS:@{
            __data_core.xxpk_boxName:xxpk_box.xxpk_boxName,
            __data_core.xxpk_boxKey:responseObject[__data_core.xxpk_box][__data_core.xxpk_boxKey],
        }];
    } failure:failure];
}


- (void)xxpk_networkFacebookWithUid:(NSString *)uid uToken:(NSString *)uToken authToken:(NSString *)authToken nonce:(NSString *)nonce success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_net_oauth] = @{
        __data_core.xxpk_net_src:__data_core.xxpk_net_src_facebook,
        __data_core.xxpk_box:@{
            __data_core.xxpk_id:uid?:@"",
            __data_core.xxpk_token:uToken?:@"",
            __data_core.xxpk_net_auth_token:authToken?:@"",
            __data_core.xxpk_net_nonce:nonce?:@""
        }
    };
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeGuest] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[__data_core.xxpk_box]];
        xxpk_box.xxpk_fbBind = YES;
        xxpk_box.xxpk_facebookUid = uid;
        xxpk_box.xxpk_facebookToken = uToken;
        xxpk_box.xxpk_facebookAuthToken = authToken;
        xxpk_box.xxpk_facebookNonce = nonce;
        xxpk_box.xxpk_boxType = XXGComeinTypeFacebook;
        xxpk_box.xxpk_boxKey = xxpk_box.xxpk_boxKey.md5.lowercaseString;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}


- (void)xxpk_networkBindFacebookWithUid:(NSString *)uid uToken:(NSString *)uToken authToken:(NSString *)authToken nonce:(NSString *)nonce success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_net_oauth] = @{
        __data_core.xxpk_net_src:__data_core.xxpk_net_src_facebook,
        __data_core.xxpk_box:@{
            __data_core.xxpk_id:uid?:@"",
            __data_core.xxpk_token:uToken?:@"",
            __data_core.xxpk_net_auth_token:authToken?:@"",
            __data_core.xxpk_net_nonce:nonce?:@""
        }
    };
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_facebook_auth params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_comeinedBox];
        xxpk_box.xxpk_fbBind = YES;
        xxpk_box.xxpk_facebookUid = uid;
        xxpk_box.xxpk_facebookToken = uToken;
        xxpk_box.xxpk_facebookAuthToken = authToken;
        xxpk_box.xxpk_facebookNonce = nonce;
        
        [XXGBoxManager xxpk_setComeinedBox:xxpk_box];
        
        [XXGBoxManager xxpk_saveBoxContentToLocal:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}


- (void)xxpk_networkVKWithUid:(NSString *)uid uToken:(NSString *)uToken success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_net_oauth] = @{
        __data_core.xxpk_net_src:__data_core.xxpk_net_src_vk,
        __data_core.xxpk_box:@{
            __data_core.xxpk_id:uid?:@"",
            __data_core.xxpk_token:uToken?:@"",
        }
    };
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeGuest] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[__data_core.xxpk_box]];
        xxpk_box.xxpk_vkBind = YES;
        xxpk_box.xxpk_vkUid = uid;
        xxpk_box.xxpk_vkToken = uToken;
        xxpk_box.xxpk_boxType = XXGComeinTypeVK;
        xxpk_box.xxpk_boxKey = xxpk_box.xxpk_boxKey.md5.lowercaseString;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}


- (void)xxpk_networkBindVKWithUid:(NSString *)uid uToken:(NSString *)uToken success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_net_oauth] = @{
        __data_core.xxpk_net_src:__data_core.xxpk_net_src_vk,
        __data_core.xxpk_box:@{
            __data_core.xxpk_id:uid?:@"",
            __data_core.xxpk_token:uToken?:@"",
        }
    };
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_vk_auth params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_comeinedBox];
        xxpk_box.xxpk_vkBind = YES;
        xxpk_box.xxpk_vkUid = uid;
        xxpk_box.xxpk_vkToken = uToken;
        
        [XXGBoxManager xxpk_setComeinedBox:xxpk_box];
        
        [XXGBoxManager xxpk_saveBoxContentToLocal:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}


- (void)xxpk_networkPoopoWithUid:(NSString *)uid uToken:(NSString *)uToken success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_net_oauth] = @{
        __data_core.xxpk_net_src:__data_core.xxpk_net_src_poopo,
        __data_core.xxpk_box:@{
            __data_core.xxpk_id:uid?:@"",
            __data_core.xxpk_token:uToken?:@"",
        }
    };
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeGuest] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[__data_core.xxpk_box]];
        xxpk_box.xxpk_boxType = XXGComeinTypePoopo;
        xxpk_box.xxpk_boxKey = xxpk_box.xxpk_boxKey.md5.lowercaseString;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}


- (void)xxpk_networkReportAdjustId:(NSString *)arg {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_net_real_adjid] = arg;
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_adjustid_report params:_params success:nil failure:nil];
}


- (void)xxpk_networkReportlogWithType:(NSString *)xxpk_type xxpk_content:(NSString *)xxpk_content {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_data] = @{
        __data_core.xxpk_type:xxpk_type?:@"",
        __data_core.xxpk_content:xxpk_content?:@""
    };
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_test_report params:_params success:nil failure:nil];
}


- (void)xxpk_networkToken:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeToken] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        NSString *xxpk_box_token = [XXGBoxContent xxpk_modelWithDict:responseObject[__data_core.xxpk_box]].xxpk_boxToken;
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_comeinedBox];
        xxpk_box.xxpk_boxToken = xxpk_box_token;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}


- (void)xxpk_networkVerifyCodeType:(NSString *)type mobile:(NSString *)xxpk_mobile dialCode:(NSString *)dialCode success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_mobile] = xxpk_mobile;
    _params[__data_core.xxpk_purpose] = type;
    _params[__data_core.xxpk_dial_code] = dialCode;
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_sms_code params:_params success:success failure:failure];
}


- (void)xxpk_networkMobileWithMobile:(NSString *)xxpk_mobile code:(NSString *)code dialCode:(NSString *)dialCode success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
   NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_mobile] = xxpk_mobile;
    _params[__data_core.xxpk_sms_code] = code;
    _params[__data_core.xxpk_dial_code] = dialCode;
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeMobile] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[__data_core.xxpk_box]];
        xxpk_box.xxpk_boxType = XXGComeinTypeMobile;
        xxpk_box.xxpk_boxMobile = xxpk_mobile;
        xxpk_box.xxpk_boxKey = xxpk_box.xxpk_boxKey.md5.lowercaseString;
       [self xxpk_handleNetworkComeinSuccess:xxpk_box];
       if (success) {
           success(responseObject);
       }
   } failure:failure];
}


- (void)xxpk_networkForgetKeyWithMobile:(NSString *)xxpk_mobile code:(NSString *)code dialCode:(NSString *)dialCode newKey:(NSString *)newKey success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure  {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_mobile] = xxpk_mobile;
    _params[__data_core.xxpk_sms_code] = code;
    _params[__data_core.xxpk_new_key] = newKey;
    _params[__data_core.xxpk_dial_code] = dialCode;
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_password_reset params:_params success:^(NSDictionary * _Nonnull responseObject) {
        
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_localBoxContentWithBoxName:responseObject[__data_core.xxpk_box][__data_core.xxpk_name]];
        xxpk_box.xxpk_boxKey = newKey;
        
        [XXGBoxManager xxpk_saveBoxContentToLocal:xxpk_box];
        
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}


- (void)xxpk_networkChangeBoxKeyWithOldKBoxKey:(NSString *)oldBoxKey newBoxKey:(NSString *)newBoxKey success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_old_key] = oldBoxKey;
    _params[__data_core.xxpk_new_key] = newBoxKey;
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_password_change params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_comeinedBox];
        xxpk_box.xxpk_boxKey = newBoxKey;
        [XXGBoxManager xxpk_setComeinedBox:xxpk_box];
        [XXGBoxManager xxpk_saveBoxContentToLocal:xxpk_box];
        if (success) {
            [self xxpk_networkToken:nil failure:nil];
            success(responseObject);
        }
    } failure:failure];
}


- (void)xxpk_networkBindMobileWithMobile:(NSString *)xxpk_mobile code:(NSString *)code dialCode:(NSString *)dialCode success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[__data_core.xxpk_mobile] = xxpk_mobile;
    _params[__data_core.xxpk_sms_code] = code;
    _params[__data_core.xxpk_dial_code] = dialCode;
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_bind_mobile params:_params success:success failure:failure];
}


- (void)xxpk_networkCreateOrder:(BOOL)isCoin params:(NSDictionary *)params success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSString *url = isCoin ?XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_coin_booking:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_booking;
    [self xxpk_sendRequest:url params:params success:success failure:failure];
}


- (void)xxpk_networkValidateReceipt:(NSDictionary *)params success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_booking_receipt params:params success:success failure:failure];
}


- (void)xxpk_networkOrderExtra:(NSString *)xxpk_orderId pmethod:(NSString *)pmethod success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSDictionary *_params = @{
        __data_core.xxpk_order:@{
            __data_core.xxpk_id:xxpk_orderId,
            __data_core.xxpk_pm:pmethod
        }
    };
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_booking_extra params:_params success:success failure:failure];
}


- (void)xxpk_networkCheckOrderWithIsCoin:(BOOL)isCoin
                            xxpk_orderId:(NSString *)xxpk_orderId
                                 success:(void(^)(NSDictionary *responseObject))success
                                 failure:(void(^)(NSError *error))failure
                              retryCount:(NSInteger)retryCount
                          currentAttempt:(NSInteger)currentAttempt {
    NSString *url = isCoin ?XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_coin_booking_check:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_booking_check;
    NSMutableDictionary *params = [NSMutableDictionary new];
    params[__data_core.xxpk_order] = @{__data_core.xxpk_id:xxpk_orderId};
    [self xxpk_sendRequest:url params:params success:^(NSDictionary * _Nonnull responseObject) {
        NSInteger status = [responseObject[__data_core.xxpk_order][__data_core.xxpk_p_status] integerValue];
        if ((status == 0) && (currentAttempt < retryCount)) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self xxpk_networkCheckOrderWithIsCoin:isCoin xxpk_orderId:xxpk_orderId success:success failure:failure retryCount:retryCount currentAttempt:currentAttempt+1];
            });
        }else {
            if (success) success(responseObject);
        }
    } failure:failure];
}


- (void)xxpk_networkUploadRoleInfo:(NSDictionary *)params success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_role params:params success:success failure:failure];
}


- (void)xxpk_networkMqtt:(void(^)(NSDictionary *responseObject))success {
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_subscribe params:nil success:success failure:^(NSError * _Nonnull error) {
        if (error.code != __data_core.xxpk_net_code_error) {
            [self xxpk_networkMqtt:success];
        }
    }];
}


- (void)xxpk_networkRemoveAccount:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_account_remove params:_params success:success failure:failure];
}
@end
