






#import "XXGNetwork.h"
#import "XXGNetworkCore.h"
#import "NSData+SunHope.h"
#import "XXGPlayKitConfig.h"
#import "XXGStartBody.h"
#import "XXGAlertView.h"
#import "XXGBoxManager.h"
#import "ZBObjectiveCBeaver.h"

#define weakify(obj) __weak typeof(obj) weak##obj = obj;
#define strongify(obj) __strong typeof(obj) obj = weak##obj;

@interface XXGNetwork ()
@property (nonatomic, assign) NSUInteger xxpk_retryCount; 
@end

@implementation XXGNetwork

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.xxpk_retryCount = 6;
    }
    return self;
}

+ (instancetype)xxpk_defaultNetwork {
    id instance = [[super alloc] init];
    return instance;
}

- (NSMutableDictionary *)xxpk_networkParams:(NSDictionary *)params {
    NSMutableDictionary *xxpk_networkParams = [params mutableCopy];
    xxpk_networkParams[__data_core.xxpk_timestamp] = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    XXGBoxContent *model = [XXGBoxManager xxpk_comeinedBox];
    if (model) {
        xxpk_networkParams[__data_core.xxpk_box] = @{
            __data_core.xxpk_token:model.xxpk_boxToken?:@"",
            __data_core.xxpk_id:model.xxpk_boxId?:@""
        };
    }
    return xxpk_networkParams;
}

- (NSMutableURLRequest *)xxpk_networkRequest:(NSString *)url paramsData:(NSData *)paramsData {
    
    NSData *data = [paramsData enDataZip];
    
    NSString *xsign = [data xxpk_sign:XXGPlayKitConfig.shared.xxpk_security];
    
    NSString *urlString = [url stringByAppendingString:[NSString stringWithFormat:__data_core.xxpk_sign, xsign]];
    
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:urlString]];
    
    
    [request addValue:__data_core.xxpk_gzip forHTTPHeaderField:__data_core.xxpk_content_encoding];
    [request addValue:__data_core.xxpk_application_json forHTTPHeaderField:__data_core.xxpk_content_type];
    [request setHTTPMethod:__data_core.xxpk_http_method];
    
    
    [request setHTTPBody:data];
    
    return request;
}

- (void)xxpk_sendRequest:(NSString *)url
                  params:(NSDictionary *)params
                 success:(void(^)(NSDictionary *responseObject))success
                 failure:(void(^)(NSError *error))failure {
    
    NSMutableDictionary *networkParams = [self xxpk_networkParams:params?:@{}];
    _xxpk_url = url;
    
    ZBLogRequest(url, networkParams);
    
    NSError *error = nil;
    NSData *paramsData = [NSJSONSerialization dataWithJSONObject:networkParams?:@{} options:(NSJSONWritingPrettyPrinted) error:&error];
    if (error) {
        if (failure) {
            failure(error);
        }
    }
    NSMutableURLRequest *request = [self xxpk_networkRequest:url paramsData:paramsData];
    [[XXGNetworkCore shared] xxpk_sendBaseRequest:request process:^NSData * _Nullable(NSData * _Nullable rawData) {
        return [rawData unDataZip];;
    } success:^(NSDictionary * _Nonnull responseObject) {
        
        ZBLogResponse(url, responseObject);
        
        [self xxpk_sendRequestSuccessWithUrl:url responseObject:responseObject params:params success:success failure:failure];
        
    } failure:^(NSError * _Nonnull error) {
        
        ZBLogNetworkError(url, error);
        
        if (failure) {
            failure(error);
        }
    } retryCount:self.xxpk_retryCount];
}

- (void)xxpk_sendRequestSuccessWithUrl:(NSString *)url
                        responseObject:(NSDictionary *)responseObject
                                params:(NSDictionary *)params
                               success:(void(^)(NSDictionary *responseObject))success
                               failure:(void(^)(NSError *error))failure {
    
    NSString *status = responseObject[__data_core.xxpk_status];
    
    if ([status isEqualToString:__data_core.xxpk_redirect]) {
        [self xxpk_sendRequest:responseObject[__data_core.xxpk_url] params:params success:success failure:failure];
    }
    
    if ([status isEqualToString:__data_core.xxpk_error]) {
        if (failure) {
            failure([NSError errorWithDomain:__data_core.xxpk_network
                                        code:__data_core.xxpk_net_code_error
                                    userInfo:@{NSLocalizedDescriptionKey : responseObject[__data_core.xxpk_errmsg]}]);
        }
    }
    
    if ([status isEqualToString:__data_core.xxpk_ok]) {
        if (success) {
            success(responseObject);
            if ([responseObject[__data_core.xxpk_tip] length] > 0) {
                [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:responseObject[__data_core.xxpk_tip] completion:nil];
            }
        }
    }
    
    if ([status isEqualToString:__data_core.xxpk_recomein]) {
        [self xxpk_recomeinWithUrl:url params:params success:success failure:failure];
    }
}

- (void)xxpk_recomeinWithUrl:(NSString *)url
                      params:(NSDictionary *)params
                     success:(void(^)(NSDictionary *responseObject))success
                     failure:(void(^)(NSError *error))failure {}

- (void)dealloc {
    
}
@end
