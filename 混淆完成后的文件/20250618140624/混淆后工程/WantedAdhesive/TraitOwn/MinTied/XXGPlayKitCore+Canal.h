






#import "XXGPlayKitCore.h"
@class XXGSelectProductItem,XXGProductBody;

NS_ASSUME_NONNULL_BEGIN

@interface XXGPlayKitCore (Canal)


- (BOOL)__xxpk_canal_autoComeinType;


- (void)xxpk_canal_logout;


- (BOOL)_xxpk_canal_selectPWithSPItme:(XXGSelectProductItem *)spitem pitem:(XXGProductBody *)pitem;


- (void)xxpk_canal_uploadRoleInfo:(XXGRoleBody *)roleInfo;

- (void)xxpk_poopo_login;

@end

NS_ASSUME_NONNULL_END
