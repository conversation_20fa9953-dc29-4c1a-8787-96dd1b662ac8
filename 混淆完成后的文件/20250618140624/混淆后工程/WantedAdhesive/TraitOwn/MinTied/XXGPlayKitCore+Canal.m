






#import "XXGPlayKitCore+Canal.h"
#import "XXGPlayKitConfig.h"
#import "XXGLoadingView.h"
#import "XXGNetworkList.h"
#import "XXGAlertView.h"
#import "XXGProductBody.h"
#import "XXGIAPManager.h"
#import "XXGSelectProductItem.h"
#import "XXGWindowManager.h"
#import "ZBObjectiveCBeaver.h"

#import "XXGPoopoManager.h"

@implementation XXGPlayKitCore (Canal)


- (BOOL)__xxpk_canal_autoComeinType {
    

    if (XXGPlayKitConfig.shared.xxpk_isPoopo
        && XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_skin_btns.count == 1
        && [XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_skin_btns[0].xxpk_name isEqualToString:__data_core.xxpk_poopo]) {
        
        
        [self xxpk_poopo_login];
        return YES;
    }
    
    return NO;
}


- (void)xxpk_canal_logout {
    

    if (XXGPlayKitConfig.shared.xxpk_isPoopo) {
        [XXGPoopoManager xxpk_logout];
    }
    
}


- (BOOL)_xxpk_canal_selectPWithSPItme:(XXGSelectProductItem *)spitem pitem:(XXGProductBody *)pitem {

    if (XXGPlayKitConfig.shared.xxpk_isPoopo && [spitem.xxpk_type containsString:__data_core.xxpk_poopo_p]) {
        [self  xxpk_poopo_creatOrder:pitem];
        return YES;
    }
    return NO;
}

- (void)xxpk_canal_uploadRoleInfo:(XXGRoleBody *)roleInfo {
    

    if (XXGPlayKitConfig.shared.xxpk_isPoopo) {
        [self xxpk_poopo_uploadRoleInfo:roleInfo];
    }
    
}

- (void)xxpk_poopo_login {
    [XXGWindowManager.shared xxpk_dismissWindow];
    [XXGPoopoManager xxpk_login:^(NSString * _Nonnull uid, NSString * _Nonnull token) {
        [XXGLoadingView showLoadingOnWindow];
        [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkPoopoWithUid:uid uToken:token success:^(NSDictionary * _Nonnull responseObject) {
            [XXGLoadingView hideLoadingFromWindow];
            [self __xxpk_comeined:responseObject];
        } failure:^(NSError * _Nonnull error) {
            [XXGLoadingView hideLoadingFromWindow];
            NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
            [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_comeinError message:errorDescrip completion:nil];
        }];
    }];
}

- (void)xxpk_poopo_creatOrder:(XXGProductBody *)item {
    [XXGLoadingView hideLoadingFromWindow];
    [XXGPoopoManager xxpk_creatOrder:item.xxpk_productCode orderNo:item.xxpk_orderId subject:item.xxpk_productName total:item.xxpk_amount currentcy:item.xxpk_currency extras_params:item.xxpk_extraInfo];
}

- (void)xxpk_poopo_uploadRoleInfo:(XXGRoleBody *)roleInfo {
    [XXGPoopoManager xxpk_uploadRoleInfo:roleInfo.xxpk_serverId xxpk_serverName:roleInfo.xxpk_serverName xxpk_roleId:roleInfo.xxpk_roleId xxpk_roleName:roleInfo.xxpk_roleName xxpk_roleLevel:roleInfo.xxpk_roleLevel];
}

@end
