






#import <Foundation/Foundation.h>
#import "XXGSetting.h"
#import "XXGNetListModel.h"
#import "XXGStartBody.h"
#import "XXGDeviceInfo.h"
#import "XXGAdaptionCof.h"
#import "XXGLocalizedCore.h"
#import "XXGDatasCore.h"
#import "XXGExtraParams.h"
#import "XXGServerInfo.h"

NS_ASSUME_NONNULL_BEGIN

#define __data_core XXGPlayKitConfig.shared.xxpk_data_core

#define __string_core XXGPlayKitConfig.shared.xxpk_string_core

@interface XXGPlayKitConfig : NSObject

+ (instancetype)shared;


@property (nonatomic, strong) XXGNetListModel *xxpk_netList;

@property (nonatomic, strong) XXGStartBody *xxpk_body;

@property (nonatomic, strong) XXGDeviceInfo *xxpk_deviceInfo;

@property (nonatomic, strong) XXGAdaptionCof *xxpk_adaptionCof;

@property (nonatomic, strong) XXGExtraParams *xxpk_extraParams;

@property (nonatomic, strong) XXGServerInfo *xxpk_serverInfo;


@property (nonatomic, strong) XXGLocalizedCore *xxpk_string_core;

@property (nonatomic, strong) XXGDatasCore *xxpk_data_core;

@property (nonatomic, copy) NSString *xxpk_startid;

@property (nonatomic, copy) NSString *xxpk_security;

@property (nonatomic, assign) BOOL xxpk_trampoline;

@property (nonatomic, copy) NSString *xxpk_deeplink;

@property (nonatomic, copy) NSString *xxpk_current_sdkname;

@property (nonatomic, assign) XXGPlayKitStartStatus xxpk_startStatus;

@property (nonatomic, assign) XXGPlayKitComeInStatus xxpk_comeinStatus;


@property (nonatomic, assign) BOOL xxpk_isCanal;


@property (nonatomic, assign) BOOL xxpk_isPoopo;


@property (nonatomic, assign) BOOL xxpk_closeButtonHidden;

@property (nonatomic, copy) NSString *xxpk_testAppId;

@property (nonatomic, copy) NSString *xxpk_testBundleId;

@property (nonatomic, copy) NSString *xxpk_testAppVersion;

@end

NS_ASSUME_NONNULL_END
