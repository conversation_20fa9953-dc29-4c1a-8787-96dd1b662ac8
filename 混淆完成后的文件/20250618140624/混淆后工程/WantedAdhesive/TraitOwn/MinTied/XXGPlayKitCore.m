






#import "XXGPlayKitCore.h"
#import "XXGNetworkList.h"
#import "XXGNetworkMonitor.h"
#import "XXGAppInfo.h"
#import "XXGPlayKitConfig.h"
#import "XXGAlertView.h"
#import "NSObject+XXGModel.h"
#import "XXGExecuteActions.h"
#import "XXGSetting.h"
#import "XXGBoxManager.h"
#import "XXGUIKit.h"
#import "XXGFloatView.h"
#import "XXGToast.h"
#import "NSString+XXGString.h"
#import "XXGIAPManager.h"
#import "XXGLoadingView.h"
#import "NSString+URLEncoding.h"
#import <WebKit/WebKit.h>
#import "XXGMQTTManager.h"
#import "XXGWindowManager.h"
#import "XXGWKMethodAction.h"
#import "NSURL+XXGAnalyse.h"
#import "XXGThirdManager.h"
#import <WebKit/WebKit.h>
#import "XXGPlayKitCore+Delegates.h"
#import "XXGPlayKitCore+Canal.h"
#import "ZBObjectiveCBeaver.h"
#import "XXGUIDriver.h"
#import "NSURL+XXGAnalyse.h"
#import "XXGUCenterViewController.h"

#define weakify(obj) __weak typeof(obj) weak##obj = obj;
#define strongify(obj) __strong typeof(obj) obj = weak##obj;

@interface XXGPlayKitCore()

@end

@implementation XXGPlayKitCore

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

+ (void)load {
    
    
    [[NSNotificationCenter defaultCenter] addObserverForName:XXGSetting.XXGNotificationNameKeyStartStatusChange object:nil queue:nil usingBlock:^(NSNotification * _Nonnull note) {
        if ([[note object] integerValue] == XXGPlayKitStartStatusFinish) {
            if ([XXGPlayKitConfig shared].xxpk_comeinStatus == XXGPlayKitComeInStatusWillBegin) {
                ZBLogInfo(__data_core.xxpk_log_init_login);
                [[XXGPlayKitCore shared] xxpk_coreComein];
            }
            
            
            if (!XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_logStatus) {
                [ZBLog zb_removeAllDestinations];
            }
        }
    }];
    
    
    [self __sdk_prepare_before_initialization];
}

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

+ (void)__preloadResource {
    ZBLogInfo(__data_core.xxpk_log_resource_load_begin);
    [XXGPlayKitConfig.shared xxpk_data_core];
    [XXGPlayKitConfig.shared xxpk_string_core];
    [XXGUIDriver xxpk_data_ui];
    [XXGUIDriver xxpk_string_ui];
    ZBLogInfo(__data_core.xxpk_log_resource_load_success);
}

+ (void)__sdk_prepare_before_initialization {
    
    
    [ZBLogViewController setupLogger];
    
    
    [self __preloadResource];
    
    dispatch_group_t group = dispatch_group_create();
    
    
    dispatch_group_enter(group);
    [XXGNetworkMonitor xxpk_checkNetworkTypeAsync:^(BOOL xxpk_isConnected) {
        ZBLogInfo(__data_core.xxpk_log_net_status, XXGNetworkMonitor.xxpk_networkType);
        if (xxpk_isConnected) {
            dispatch_group_leave(group);
        }
    }];
    
    
    dispatch_group_enter(group);
    [XXGAppInfo xxpk_requestIDFAIfNeeded:^{
        dispatch_group_leave(group);
    }];
    
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        [[XXGPlayKitCore shared] __xxpk_coreStart];
    });
}

- (void)__xxpk_coreStart {
    
    if (XXGPlayKitConfig.shared.xxpk_startStatus != XXGPlayKitStartStatusNot) {
        ZBLogInfo(__data_core.xxpk_log_init_already, XXGPlayKitConfig.shared.xxpk_startStatus);
        return;
    }
    
    ZBLogVerbose(__data_core.xxpk_log_init_start);
    XXGPlayKitConfig.shared.xxpk_startStatus = XXGPlayKitStartStatusBegin;
    
    weakify(self);
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkStart:^(NSDictionary * _Nonnull responseObject) {
        
        NSArray *xxpk_actions = [XXGActionItem xxpk_modelArrayWithDictArray:responseObject[__data_core.xxpk_actions]];
        
        [XXGExecuteActions xxpk_executeActions:xxpk_actions index:0 completion:^{
            ZBLogVerbose(__data_core.xxpk_log_init_success);
            XXGPlayKitConfig.shared.xxpk_startStatus = XXGPlayKitStartStatusFinish;
        }];
        
    } failure:^(NSError * _Nonnull error) {
        strongify(self);
        [self __xxpk_handleStartError:error];
    }];
}


- (void)__xxpk_handleStartError:(NSError *)error {
    XXGPlayKitConfig.shared.xxpk_startStatus = XXGPlayKitStartStatusNot;
    NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
    ZBLogVerbose(__data_core.xxpk_log_init_failed, errorDescrip);
    if (error.code == __data_core.xxpk_net_code_error) {
        weakify(self);
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_startError message:errorDescrip completion:^(NSInteger buttonIndex) {
            strongify(self);
            [self __xxpk_coreStart];
        }];
    }else {
        [self __xxpk_coreStart];
    }
}

- (void)__xxpk_autoComeinType {
    
    
    if ([self __xxpk_canal_autoComeinType]) {
        return;
    }
    
    
    if ([XXGBoxManager xxpk_comeinedBox]) {
        [XXGLoadingView showLoadingOnWindow];
        [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkToken:^(NSDictionary * _Nonnull responseObject) {
            [XXGLoadingView hideLoadingFromWindow];
            [self __xxpk_comeined:responseObject];
        } failure:^(NSError * _Nonnull error) {
            [XXGBoxManager xxpk_removeComeinedBox];
            [self __xxpk_autoComeinType];
        }];
        return;
    }
    
    
    if ([XXGPlayKitConfig shared].xxpk_adaptionCof.xxpk_autoComein) {
        [XXGLoadingView showLoadingOnWindow];
        [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkGuest:^(NSDictionary * _Nonnull responseObject) {
            [XXGLoadingView hideLoadingFromWindow];
            [self __xxpk_comeined:responseObject];
        } failure:^(NSError * _Nonnull error) {
            NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
            [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_comeinError message:errorDescrip completion:nil];
        }];
        return;
    }
    
    
    if ([XXGBoxManager xxpk_getBoxsContentFromLocal].count > 0) {
        [XXGUIKit xxpk_showViewControllerWithType:XXGShowViewControllerTypeSelectAccount xxpk_delegate:self];
        return;
    }
    
    
    [XXGUIKit xxpk_showViewControllerWithType:XXGShowViewControllerTypeComein xxpk_delegate:self];
}

- (void)__xxpk_comeined:(NSDictionary *)responseObject {
    
    [XXGUIKit xxpk_dissmissAllWindows];
    
    NSArray *xxpk_actions = [XXGActionItem xxpk_modelArrayWithDictArray:responseObject[__data_core.xxpk_actions]];
    
    [XXGExecuteActions xxpk_executeActions:xxpk_actions index:0 completion:^{
        ZBLogVerbose(__data_core.xxpk_log_login_success);
        
        XXGPlayKitConfig.shared.xxpk_comeinStatus = XXGPlayKitComeInStatusFinish;
        

        if ([responseObject[__data_core.xxpk_user_info_url] xxpk_isNotEmpty]) {
            [self xxpk_showUIofPopup:__data_core.xxpk_user_info_url];
        }
        
        
        [[XXGIAPManager shared] xxpk_registerP];
        [XXGIAPManager shared].xxpk_delegate = self;
        
        
        [XXGToast showBottom:__string_core.xxpk_comein_sus];
        
        
        if(XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_docker.xxpk_status){
            [XXGFloatView xxpk_show];
            [[XXGFloatView shared] setXxpk_tapHandler:^(NSString *url){
                [self xxpk_showUIofUCenter:url.xxpk_isNotEmpty?url:XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_box_center.xxpk_url];
            }];
        }
        
        if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_comeinFinish:)]) {
            [self.xxpk_delegate xxpk_comeinFinish:[XXGBoxManager xxpk_comeinedBoxJson]];
        }
        
    }];
}


- (void)xxpk_coreComein {
   
    if (XXGPlayKitConfig.shared.xxpk_comeinStatus == XXGPlayKitComeInStatusBegining) {
        ZBLogVerbose(__data_core.xxpk_log_login_ing);
        return;
    }
    
    if (XXGPlayKitConfig.shared.xxpk_comeinStatus == XXGPlayKitComeInStatusFinish) {
        ZBLogVerbose(__data_core.xxpk_log_login_logined);
        if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_comeinFinish:)]) {
            [self.xxpk_delegate xxpk_comeinFinish:[XXGBoxManager xxpk_comeinedBoxJson]];
        }
        return;
    }
    
    ZBLogVerbose(__data_core.xxpk_log_login_prepare);
    XXGPlayKitConfig.shared.xxpk_comeinStatus = XXGPlayKitComeInStatusWillBegin;
    
    if (XXGPlayKitConfig.shared.xxpk_startStatus != XXGPlayKitStartStatusFinish) {
        ZBLogVerbose(__data_core.xxpk_log_login_not_init);
        return;
    }
    
    ZBLogVerbose(__data_core.xxpk_log_login_start);
    XXGPlayKitConfig.shared.xxpk_comeinStatus = XXGPlayKitComeInStatusBegining;
    
    [self __xxpk_autoComeinType];
}

- (void)xxpk_logout {
    ZBLogVerbose(__data_core.xxpk_log_logout);
    
    
    [self xxpk_canal_logout];
    
    
    [[XXGMQTTManager shared] xxpk_disconnect];
    
    
    [XXGBoxManager xxpk_removeComeinedBox];
    
    XXGPlayKitConfig.shared.xxpk_comeinStatus = XXGPlayKitComeInStatusNot;
    
    [XXGUIKit xxpk_dissmissAllWindows];
    
    
    if(XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_docker.xxpk_status){
        
        [XXGFloatView xxpk_hide];
    }
    
    if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_logouted)]) {
        [self.xxpk_delegate xxpk_logouted];
    }
}

- (void)xxpk_uploadRoleInfo:(XXGRoleBody *)roleInfo {
    ZBLogVerbose(__data_core.xxpk_log_report_role);
    
    if (XXGPlayKitConfig.shared.xxpk_comeinStatus != XXGPlayKitComeInStatusFinish) {
        if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_uploadRoleFinished:)]) {
            [self.xxpk_delegate xxpk_uploadRoleFinished:NO];
        }
        return;
    }
    
    if (roleInfo.xxpk_serverName.xxpk_isEmpty
        ||roleInfo.xxpk_serverId.xxpk_isEmpty
        ||roleInfo.xxpk_roleId.xxpk_isEmpty
        ||roleInfo.xxpk_roleName.xxpk_isEmpty
        ||roleInfo.xxpk_roleLevel.xxpk_isEmpty) {
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:__string_core.xxpk_uploadrole_error completion:nil];
        return;
    }
    
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkUploadRoleInfo:[roleInfo xxpk_modelToDict] success:^(NSDictionary * _Nonnull responseObject) {
        ZBLogVerbose(__data_core.xxpk_log_report_role_success);
        
        
        [self xxpk_canal_uploadRoleInfo:roleInfo];
        
        if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_uploadRoleFinished:)]) {
            [self.xxpk_delegate xxpk_uploadRoleFinished:YES];
        }
    } failure:^(NSError * _Nonnull error) {
        ZBLogVerbose(__data_core.xxpk_log_report_role_failed);
        if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_uploadRoleFinished:)]) {
            [self.xxpk_delegate xxpk_uploadRoleFinished:NO];
        }
    }];
}

- (void)xxpk_creatOrder:(XXGProductBody *)body xxpk_isCoinOrder:(BOOL)isCoin {
    ZBLogVerbose(__data_core.xxpk_log_pay_start);
    if (XXGPlayKitConfig.shared.xxpk_comeinStatus != XXGPlayKitComeInStatusFinish && !isCoin) {
        if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_payFinished:)]) {
            [self.xxpk_delegate xxpk_payFinished:NO];
        }
        return;
    }
    [XXGThirdManager xxpk_logAddedToCartEvent];
    [XXGLoadingView showLoadingOnWindow];
    [[XXGIAPManager shared] xxpk_createOrder:body xxpk_isCoinOrder:isCoin];
}


- (void)xxpk_didFinishLaunchingWithOptions:(NSDictionary *)launchOptions xconnectOptions:(UISceneConnectionOptions *)connetOptions {
    if (launchOptions) {
        
        if (launchOptions[UIApplicationLaunchOptionsURLKey]) {
            NSURL *url = launchOptions[UIApplicationLaunchOptionsURLKey];
            XXGPlayKitConfig.shared.xxpk_deeplink = url.absoluteString;
        }
    }
    if (connetOptions) {
        
       NSArray<UIOpenURLContext*> *urlContexts = connetOptions.URLContexts.allObjects;
       if (urlContexts.count > 0) {
           NSURL *url = urlContexts.firstObject.URL;
           XXGPlayKitConfig.shared.xxpk_deeplink = url.absoluteString;
       }
    }
    ZBLogInfo(__data_core.xxpk_log_cold_start_url, XXGPlayKitConfig.shared.xxpk_deeplink);
    [XXGThirdManager xxpk_didFinishLaunchingWithOptions:launchOptions xconnectOptions:connetOptions];
}


- (BOOL)xxpk_applicationOpenURL:(NSURL *)url xoptions:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options xURLContexts:(NSSet<UIOpenURLContext *> *)URLContexts {
    NSString *hot_url = nil;
    if (options) {
        hot_url = url.absoluteString;
    }
    if (URLContexts) {
        hot_url = URLContexts.allObjects.firstObject.URL.absoluteString;
    }
    
    ZBLogInfo(__data_core.xxpk_log_hot_start_url, hot_url);
    
    if ([url.scheme hasPrefix:__data_core.xxpk_app]) {
        [self xxpk_wkView:nil makeMethodAction:url.host arg:url];
        return YES;
    }

    else {
        return [XXGThirdManager xxpk_applicationOpenURL:url xoptions:options xURLContexts:URLContexts];
    }
}

- (void)xxpk_coreHandleOpenUrl:(NSString *)url {
    if (url.xxpk_isEmpty) {
        return;
    }
    NSURL *_url = [NSURL URLWithString:[url xxpk_urlDecodedString]];
    if ([_url.scheme hasPrefix:__data_core.xxpk_app]) {
        [self xxpk_wkView:nil makeMethodAction:_url.host arg:_url];
    }else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [[UIApplication sharedApplication] openURL:_url options:@{} completionHandler:nil];
        });
    }
}

- (void)xxpk_openUserCenterSidebar:(NSString *)type {
    if (XXGPlayKitConfig.shared.xxpk_comeinStatus != XXGPlayKitComeInStatusFinish) {
        return;
    }
    NSString *url = [XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_box_center.xxpk_url stringByAppendingFormat:__data_core.xxpk_core_open_page,type];
    [XXGPlayKitCore.shared xxpk_showUIofUCenter:url];
}

- (void)xxpk_iapRepair {
    [XXGIAPManager xxpk_iapRepair];
    [XXGPlayKitCore.shared xxpk_logout];
}


- (void)xxpk_dissmissCurrentUI {
    [XXGUIKit xxpk_dissmissCurrentWinow];
}
- (void)xxpk_showUIofChangeBoxKey:(id)object {
    [XXGUIKit xxpk_showViewControllerWithType:(XXGShowViewControllerTypeChangePassword) xxpk_object:object xxpk_delegate:self];
}
- (void)xxpk_showUIofbindMobile:(id)object hasWkView:(WKWebView *)hasWkView {
    NSArray *xxpk_bindArray = @[object,hasWkView?:@""];
    [XXGUIKit xxpk_showViewControllerWithType:(XXGShowViewControllerTypeBindMobile) xxpk_object:xxpk_bindArray xxpk_delegate:self];
}
- (void)xxpk_showUIofUCenter:(id)object {
    [XXGUIKit xxpk_showViewControllerWithType:XXGShowViewControllerTypeUserCenter xxpk_object:object xxpk_delegate:self];
}
- (void)xxpk_showUIofSelectPayMethod:(id)objcet xxpk_delegate:(id<XXGUIkitDelegate>)xxpk_delegate {
    [XXGUIKit xxpk_showViewControllerWithType:XXGShowViewControllerTypeSelectPP xxpk_object:objcet xxpk_delegate:xxpk_delegate];
}
- (void)xxpk_showUIofPopup:(id)objcet {
    [XXGUIKit xxpk_showViewControllerWithType:XXGShowViewControllerTypePopup xxpk_object:objcet xxpk_delegate:self];
}
- (void)xxpk_showUIofTrampoline:(id)objcet  {
    [XXGUIKit __xxpk_showNoStackViewControllerWithType:XXGShowViewControllerTypePopup xxpk_object:objcet xxpk_delegate:self];
}
- (void)xxpk_showUIofSavePS:(id)object {
    [XXGUIKit xxpk_showViewControllerWithType:XXGShowViewControllerTypeSavePS xxpk_object:object xxpk_delegate:self];
}

@end
