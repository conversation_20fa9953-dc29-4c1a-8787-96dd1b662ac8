






#import "XXGPlayKitCore.h"

NS_ASSUME_NONNULL_BEGIN

@interface XXGPlayKitCore (Others)

@property (class, nonatomic, assign, readonly) BOOL xxpk_isBindFacebook;
@property (class, nonatomic, assign, readonly) BOOL xxpk_isBindVK;

+ (void)xxpk_facebookShareWithUrl:(NSString *)url;

+ (void)xxpk_facebookShareWithImgUrl:(NSString *)imgUrl;

+ (void)xxpk_facebookSub;

+ (void)xxpk_facebookInvite;

+ (void)xxpk_facebookBind:(void(^)(NSDictionary *_Nullable userInfo, NSString* errorMsg))handler;

+ (void)xxpk_VKBind:(void(^)(NSDictionary *_Nullable userInfo, NSString* errorMsg))handler;

+ (void)xxpk_logFacebookEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logAppFlyerEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logFirebaseEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logAdjustEvent:(NSString *)event params:(NSDictionary *_Nullable)params;


+ (void)xxpk_showRewardedAdForCustomData:(nullable NSString *)customData complate:(void(^)(BOOL result))complate;


- (void)xxpk_reportlogWithType:(NSString *)xxpk_type xxpk_content:(NSString *)xxpk_content;

@end

NS_ASSUME_NONNULL_END
