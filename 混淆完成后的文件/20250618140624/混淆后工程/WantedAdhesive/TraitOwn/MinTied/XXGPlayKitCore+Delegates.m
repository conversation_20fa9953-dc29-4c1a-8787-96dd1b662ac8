






#import "XXGPlayKitCore+Delegates.h"
#import "XXGLoadingView.h"
#import "XXGToast.h"
#import "XXGAlertView.h"
#import "XXGThirdManager.h"
#import "XXGPlayKitConfig.h"
#import "NSString+XXGString.h"
#import "XXGIAPManager.h"
#import "XXGWKMethodAction.h"
#import "XXGNetworkList.h"
#import "UIColor+XXGColor.h"
#import "UIImage+XXGImage.h"
#import "XXGBoxManager.h"
#import "NSString+XXGMd5.h"
#import "XXGWindowManager.h"
#import "XXGPlayKitCore+Canal.h"
#import "ZBObjectiveCBeaver.h"

#import "XXGFacebookManager.h"
#import "XXGVKManager.h"

@implementation XXGPlayKitCore (Delegates)



- (void)xxpk_iapManager:(XXGIAPManager *)manager paySuccessWithItem:(XXGProductBody *)xxpk_item {
    ZBLogVerbose(__data_core.xxpk_log_pay_success);
    [XXGLoadingView hideLoadingFromWindow];
    [XXGToast showBottom:__string_core.xxpk_p_sustip];
    [XXGThirdManager xxpk_logPurchasedEventOrderId:xxpk_item.xxpk_orderId currency:xxpk_item.xxpk_currency price:[xxpk_item.xxpk_amount doubleValue]];
    
    if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_payFinished:)] && !manager.xxpk_isCoinOrder) {
        [self.xxpk_delegate xxpk_payFinished:YES];
    }
}

- (void)xxpk_iapManager:(XXGIAPManager *)manager payFialedWithMessage:(NSString *)message {
    ZBLogVerbose(@"%@-%@",__data_core.xxpk_log_pay_failed,message);
    [XXGLoadingView hideLoadingFromWindow];
    [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:message completion:nil];
    
    if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_payFinished:)] && !manager.xxpk_isCoinOrder) {
        [self.xxpk_delegate xxpk_payFinished:NO];
    }
}

- (void)xxpk_iapManagerCancel:(XXGIAPManager *)manager {
    ZBLogVerbose(__data_core.xxpk_log_pay_cancel);
    [XXGLoadingView hideLoadingFromWindow];
    [XXGToast showCenter:__string_core.xxpk_p_cancel];
    
    if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_payFinished:)] && !manager.xxpk_isCoinOrder) {
        [self.xxpk_delegate xxpk_payFinished:NO];
    }
}

- (void)xxpk_IAPManagerOpenOfOrderUrl:(NSString *)url {
    [self xxpk_coreHandleOpenUrl:url];
}


- (void)xxpk_wkView:(WKWebView *)wkView makeMethodAction:(NSString *)method arg:(id)arg {
    [XXGWKMethodAction xxpk_wkView:wkView makeMethodAction:method arg:arg];
}

- (void)xxpk_customerServiceButtonDidClick:(NSString *)url {
    [self xxpk_coreHandleOpenUrl:url];
}

- (void)xxpk_gusetButtonDidClick:(XXGUIkitDelegateCompletion)completion {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkGuest:^(NSDictionary * _Nonnull responseObject) {
        [self __xxpk_comeined:responseObject];
        completion(nil);
    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_comeinError message:errorDescrip completion:nil];
        completion(nil);
    }];
}

- (void)xxpk_facebookButtondDidClick:(XXGUIkitDelegateCompletion)completion {
    [XXGFacebookManager xxpk_oauth:XXGWindowManager.shared.xxpk_currentWindow.rootViewController handler:^(NSString * _Nonnull userID, NSString * _Nonnull name, NSString * _Nonnull token, NSString * _Nonnull auth_token, NSString * _Nonnull nonce, NSError * _Nonnull error, BOOL isCancelled) {
        if (isCancelled) {
            [XXGToast showBottom:__string_core.xxpk_comein_cancel];
            completion(nil);
        }else if (error) {
            [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:error.localizedDescription completion:nil];
            completion(nil);
        }else {
            [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkFacebookWithUid:userID uToken:token authToken:auth_token nonce:nonce success:^(NSDictionary * _Nonnull responseObject) {
                [self __xxpk_comeined:responseObject];
                completion(nil);
            } failure:^(NSError * _Nonnull error) {
                NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
                [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_comeinError message:errorDescrip completion:nil];
                completion(nil);
            }];
        }
    }];
}

- (void)xxpk_VKButtonDidClick:(XXGUIkitDelegateCompletion)completion {
    [XXGVKManager xxpk_oauthOnViewController:XXGWindowManager.shared.xxpk_currentWindow.rootViewController handler:^(BOOL isCancell, NSString * _Nonnull userID, NSString * _Nonnull token, NSString * _Nonnull error) {
        if (isCancell) {
            [XXGToast showBottom:__string_core.xxpk_comein_cancel];
            completion(nil);
        }else if(error.xxpk_isNotEmpty) {
            [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:error completion:nil];
            completion(nil);
        }else {
            [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkVKWithUid:userID uToken:token success:^(NSDictionary * _Nonnull responseObject) {
                [self __xxpk_comeined:responseObject];
                completion(nil);
            } failure:^(NSError * _Nonnull error) {
                NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
                [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_comeinError message:errorDescrip completion:nil];
                completion(nil);
            }];
        }
    }];
}

- (void)xxpk_poopoButtonDidClick:(XXGUIkitDelegateCompletion)completion {
    [self xxpk_poopo_login];
}

- (void)xxpk_selectComeinButtonDidClickWithBoxName:(NSString *)boxName completion:(XXGUIkitDelegateCompletion)completion {
    XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_localBoxContentWithBoxName:boxName];
    [XXGBoxManager xxpk_setComeinedBox:xxpk_box];
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkToken:^(NSDictionary * _Nonnull responseObject) {
        [self __xxpk_comeined:responseObject];
        completion(nil);
    } failure:^(NSError * _Nonnull error) {
        [XXGBoxManager xxpk_removeComeinedBox];
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_comeinError message:errorDescrip completion:nil];
        completion(nil);
    }];
    return;
}

- (void)xxpk_selectDeleteBoxWithBoxName:(NSString *)boxName completion:(XXGUIkitDelegateCompletion)completion {
    [XXGBoxManager xxpk_deleteBoxToLocalWithName:boxName];
    if ([XXGBoxManager xxpk_getBoxsContentFromLocal].count == 0) {
        [XXGUIKit xxpk_dissmissAllWindows];
        [XXGUIKit xxpk_showViewControllerWithType:XXGShowViewControllerTypeComein xxpk_delegate:self];
    }
}

- (void)xxpk_registerButtonDidClickWithBoxName:(NSString *)boxName boxKey:(NSString *)boxKey completion:(XXGUIkitDelegateCompletion)completion {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkRegisterWithBoxName:boxName boxKey:boxKey.md5.lowercaseString success:^(NSDictionary * _Nonnull responseObject) {
        [self __xxpk_comeined:responseObject];
        completion(nil);
    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_registerError message:errorDescrip completion:nil];
        completion(nil);
    }];
}

- (void)xxpk_accountComeinButtonDidClickWithBoxName:(NSString *)boxName boxKey:(NSString *)boxKey completion:(XXGUIkitDelegateCompletion)completion {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkAccountWithBoxName:boxName boxKey:boxKey.md5.lowercaseString success:^(NSDictionary * _Nonnull responseObject) {
        [self __xxpk_comeined:responseObject];
        completion(nil);
    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_comeinError message:errorDescrip completion:nil];
        completion(nil);
    }];
}

- (void)xxpk_sendCodeButtonDidClickWithType:(NSString *)type xxpk_moblil:(NSString *)xxpk_moblil dialCode:(NSString *)dialCode completion:(XXGUIkitDelegateCompletion)completion {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkVerifyCodeType:type mobile:xxpk_moblil dialCode:dialCode success:^(NSDictionary * _Nonnull responseObject) {
        if (completion) {
            completion(@(YES));
        }
    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:errorDescrip completion:nil];
        completion(@(NO));
    }];
}

- (void)xxpk_mobileComeinButtonDidClickWithMobile:(NSString *)moblile code:(NSString *)code dialCode:(NSString *)dialCode completion:(XXGUIkitDelegateCompletion)completion {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkMobileWithMobile:moblile code:code dialCode:dialCode success:^(NSDictionary * _Nonnull responseObject) {
        [self __xxpk_comeined:responseObject];
        completion(nil);
    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_comeinError message:errorDescrip completion:nil];
        completion(nil);
    }];
}

- (void)xxpk_forgetButtonDidClickWithMobile:(NSString *)mobile code:(NSString *)code dialCode:(NSString *)dialCode newKey:(NSString *)newKey completion:(XXGUIkitDelegateCompletion)completion {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkForgetKeyWithMobile:mobile code:code dialCode:dialCode newKey:newKey.md5.lowercaseString success:^(NSDictionary * _Nonnull responseObject) {
        completion(responseObject[__data_core.xxpk_box][__data_core.xxpk_name]);
    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:errorDescrip completion:nil];
        completion(nil);
    }];
}

- (void)xxpk_changeBoxKeyButtonDidClickWithOldKBoxKey:(NSString *)oldBoxKey newBoxKey:(NSString *)newBoxKey completion:(XXGUIkitDelegateCompletion)completion {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkChangeBoxKeyWithOldKBoxKey:oldBoxKey.md5.lowercaseString newBoxKey:newBoxKey.md5.lowercaseString success:^(NSDictionary * _Nonnull responseObject) {
        completion(@(YES));
    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:errorDescrip completion:nil];
        completion(@(NO));
    }];
}

- (void)xxpk_bindMobileButtonDidClickWithMobile:(NSString *)mobile code:(NSString *)code dialCode:(NSString *)dialCode completion:(XXGUIkitDelegateCompletion)completion {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkBindMobileWithMobile:mobile code:code dialCode:dialCode success:^(NSDictionary * _Nonnull responseObject) {
        completion(@(YES));
    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:errorDescrip completion:nil];
        completion(@(NO));
    }];
}

@end
