






#import "XXGStartBody.h"
#import "XXGAppInfo.h"
#import "UICKeyChainStore.h"
#import "XXGPlayKitConfig.h"
#import "NSString+XXGString.h"

@implementation XXGStartBody

+ (NSDictionary *)xxpk_replacedKeyFromPropertyName {
    return __data_core.xxpk_start_body;
}


- (NSString *)xxpk_sdkName {
    return XXGPlayKitConfig.shared.xxpk_current_sdkname;
}

- (NSString *)xxpk_version {
    return __data_core.xxpk_version;
}

- (NSString *)xxpk_platform {
    return __data_core.xxpk_platform;
}

- (NSString *)xxpk_campaign {
    return __data_core.xxpk_campaign;
}

- (NSString *)xxpk_type {
    return __data_core.xxpk_app;
}


- (NSString *)xxpk_appId {
    return XXGPlayKitConfig.shared.xxpk_startid;
}

- (NSString *)xxpk_appBundleId {
    if (XXGPlayKitConfig.shared.xxpk_testBundleId && XXGPlayKitConfig.shared.xxpk_testBundleId.xxpk_isNotEmpty) {
        return XXGPlayKitConfig.shared.xxpk_testBundleId;
    }
    return XXGAppInfo.xxpk_appBundleIdentifier;
}

- (NSString *)xxpk_appVersion {
    if (XXGPlayKitConfig.shared.xxpk_testAppVersion && XXGPlayKitConfig.shared.xxpk_testAppVersion.xxpk_isNotEmpty) {
        return XXGPlayKitConfig.shared.xxpk_testAppVersion;
    }
    return XXGAppInfo.xxpk_appVersion;
}

- (NSString *)xxpk_appName {
    return XXGAppInfo.xxpk_appName;
}

- (NSString *)xxpk_deviceId {
    UICKeyChainStore *keychain = [UICKeyChainStore keyChainStoreWithService:XXGAppInfo.xxpk_appBundleIdentifier];
    return keychain[__data_core.xxpk_device] ?: XXGPlayKitConfig.shared.xxpk_deviceInfo.xxpk_idfa?: [[NSUUID UUID] UUIDString];
}

- (void)setXxpk_deviceId:(NSString *)xxpk_deviceId {
    UICKeyChainStore *keychain = [UICKeyChainStore keyChainStoreWithService:XXGAppInfo.xxpk_appBundleIdentifier];
    if (![xxpk_deviceId isEqualToString:keychain[__data_core.xxpk_device]]) {
        keychain[__data_core.xxpk_device] = xxpk_deviceId;
    }
}

@end

