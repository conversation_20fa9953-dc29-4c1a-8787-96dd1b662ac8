






#import "XXGBoxCenterCof.h"
#import "XXGPlayKitConfig.h"
#import "XXGBoxManager.h"

@implementation XXGBoxCenterCof

+ (NSDictionary *)xxpk_replacedKeyFromPropertyName {
    return __data_core.xxpk_box_center_cof;
}

- (NSString *)xxpk_url {
    NSString *xxpk_wenhao = [_xxpk_url containsString:__data_core.xxpk_vk_wenhao] ?__data_core.xxpk_vk_and:__data_core.xxpk_vk_wenhao;
    NSString *xxpk_landscape = XXGPlayKitConfig.shared.xxpk_deviceInfo.xxpk_landscape;
    NSString *xxpk_lang = XXGPlayKitConfig.shared.xxpk_deviceInfo.xxpk_lang;
    NSString *xxpk_token = [XXGBoxManager xxpk_comeinedBox].xxpk_boxToken;
    NSString *xxpk_url_tmp = _xxpk_url;

    xxpk_url_tmp = [NSString stringWithFormat:__data_core.xxpk_vk_roter_os,_xxpk_url,xxpk_wenhao,xxpk_landscape,xxpk_lang,xxpk_token];

    return xxpk_url_tmp;
}
@end
