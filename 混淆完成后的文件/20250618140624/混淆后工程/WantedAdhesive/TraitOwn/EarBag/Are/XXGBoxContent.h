






#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger, XXGComeinType){
    XXGComeinTypeGuest,
    XXGComeinTypeRegister,
    XXGComeinTypeAccount,
    XXGComeinTypeMobile,
    XXGComeinTypeToken,
    XXGComeinTypeWeiXin,
    XXGComeinTypeOneClick,
    XXGComeinTypeFacebook,
    XXGComeinTypeVK,
    XXGComeinTypePoopo
};

NS_ASSUME_NONNULL_BEGIN

@interface XXGBoxContent : NSObject


@property (nonatomic, copy) NSString * xxpk_boxId;

@property (nonatomic, copy) NSString * xxpk_boxName;

@property (nonatomic, copy) NSString * xxpk_boxKey;
@property (nonatomic, copy) NSString * xxpk_boxToken;
@property (nonatomic, copy) NSString * xxpk_boxMobile;
@property (nonatomic, copy) NSString * xxpk_lastComeinTime;
@property (nonatomic, assign) XXGComeinType xxpk_boxType;

@property (nonatomic, assign) BOOL xxpk_created;
@property (nonatomic, assign) BOOL xxpk_fbBind;
@property (nonatomic, assign) BOOL xxpk_vkBind;
@property (nonatomic, assign) BOOL xxpk_mobileBind;
@property (nonatomic, copy) NSString * xxpk_facebookUid;
@property (nonatomic, copy) NSString * xxpk_facebookToken;
@property (nonatomic, copy) NSString * xxpk_facebookAuthToken;
@property (nonatomic, copy) NSString * xxpk_facebookNonce;
@property (nonatomic, copy) NSString * xxpk_vkUid;
@property (nonatomic, copy) NSString * xxpk_vkToken;
@property (nonatomic, copy) NSString * xxpk_poopoUid;
@property (nonatomic, copy) NSString * xxpk_poopoToken;

@end

NS_ASSUME_NONNULL_END
