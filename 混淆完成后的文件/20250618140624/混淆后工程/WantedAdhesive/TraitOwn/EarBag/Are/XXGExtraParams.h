






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGExtraParams : NSObject


@property (nonatomic, copy) NSString * xxpk_adjustRegister;
@property (nonatomic, copy) NSString * xxpk_adjustLogin;
@property (nonatomic, copy) NSString * xxpk_adjustClickPay;
@property (nonatomic, copy) NSString * xxpk_adjustPay;
@property (nonatomic, copy) NSString * xxpk_adjustAppToken;
@property (nonatomic, copy) NSString * xxpk_adjustActivate;


@property (nonatomic, copy) NSString * xxpk_afDevKey;
@property (nonatomic, copy) NSString * xxpk_afAppid;
@property (nonatomic, copy) NSString * xxpk_afClickPay;
@property (nonatomic, copy) NSString * xxpk_afActivate;
@property (nonatomic, copy) NSString * xxpk_afPay;


@property (nonatomic, copy) NSString * xxpk_fireClickPay;
@property (nonatomic, copy) NSString * xxpk_fireActivate;
@property (nonatomic, copy) NSString * xxpk_firePay;


@property (nonatomic, copy) NSString * xxpk_fbClickPay;
@property (nonatomic, copy) NSString * xxpk_fbPay;


@property (nonatomic, copy) NSString * xxpk_vk_clientid;
@property (nonatomic, copy) NSString * xxpk_vk_client_secret;


@property (nonatomic, copy) NSString * xxpk_max_key;
@property (nonatomic, copy) NSString * xxpk_max_reward_id;


@property (nonatomic, copy) NSString * xxpk_poopo_code;
@end

NS_ASSUME_NONNULL_END
