






#import <Foundation/Foundation.h>
#import "XXGSkinModel.h"
#import "XXGThemeColor.h"
#import "XXGDockerCof.h"
#import "XXGServiceInfo.h"
#import "XXGBoxCenterCof.h"

NS_ASSUME_NONNULL_BEGIN

@interface XXGAdaptionCof : NSObject

@property (nonatomic, assign) BOOL xxpk_autoComein;
@property (nonatomic, assign) BOOL xxpk_logStatus;

@property (nonatomic, assign) BOOL xxpk_adaption_report_adjust;
@property (nonatomic, assign) BOOL xxpk_adaption_report_appsFlyer;
@property (nonatomic, assign) BOOL xxpk_adaption_report_facebook;
@property (nonatomic, assign) BOOL xxpk_adaption_report_firebase;

@property (nonatomic, copy)   NSString                  *xxpk_adaption_type;

@property (nonatomic, strong) NSArray<XXGSkinModel *>   *xxpk_adaption_skin_btns;
@property (nonatomic, strong) NSDictionary              *xxpk_adaption_skin_comein;
@property (nonatomic, assign) BOOL                      xxpk_adaption_skin_comein_only;
@property (nonatomic, copy)   NSString                  *xxpk_adaption_skin_logo;
@property (nonatomic, strong) XXGThemeColor             *xxpk_adaption_skin_theme;

@property (nonatomic, strong) XXGDockerCof *xxpk_docker;
@property (nonatomic, strong) XXGServiceInfo *xxpk_service;
@property (nonatomic, strong) XXGBoxCenterCof *xxpk_box_center;

@end

NS_ASSUME_NONNULL_END
