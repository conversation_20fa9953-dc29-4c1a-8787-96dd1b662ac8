






#import "XXGShanYanManager.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGPerformSelector.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGShanYanManager

+ (id)xxpk_middlewareClass {
    Class class = NSClassFromString(__data_core.xxpk_middleware_shanyan);
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        ZBLogInfo(__data_core.xxpk_log_manager_shanyan,class?__data_core.xxpk_manager_status_exist:__data_core.xxpk_manager_status_not_exist);
    });
    if (class) {
        return [class xxpk_performSelector:@selector(shared)];
    }
    return nil;
}

+ (void)xxpk_preGetPhonenumberWithAppId:(NSString *)appId complete:(void (^_Nullable)(BOOL isCanLogin))complete {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_preGetPhonenumberWithAppId:complete:) withObject:appId withObject:complete];
    }else {
        complete(NO);
    }
}

+ (void)xxpk_getLoginTokenController:(UIViewController *_Nonnull)controller array:(NSArray *)array success:(void (^_Nullable)(NSDictionary * _Nonnull resultDic))success xxpk_error:(void (^_Nullable)(NSString * _Nonnull error))error shanyanAction:(void(^)(NSInteger))action {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_getLoginTokenController:array:success:xxpk_error:shanyanAction:) withObject:controller withObject:array withObject:success withObject:error withObject:action];
    }else {
        error(__string_core.xxpk_comeinError);
    }
}
@end
