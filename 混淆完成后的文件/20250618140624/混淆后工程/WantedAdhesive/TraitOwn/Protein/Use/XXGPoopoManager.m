






#import "XXGPoopoManager.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGPerformSelector.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGPoopoManager

+ (id)xxpk_middlewareClass {
    Class class = NSClassFromString(__data_core.xxpk_middleware_poopo);
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        ZBLogInfo(__data_core.xxpk_log_manager_poopo,class?__data_core.xxpk_manager_status_exist:__data_core.xxpk_manager_status_not_exist);
    });
    if (class) {
        return [class xxpk_performSelector:@selector(shared)];
    }
    return nil;
}

+ (BOOL)xxpk_application:(UIApplication *)application
                xopenURL:(NSURL *)url
                xoptions:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    if ([self xxpk_middlewareClass]) {
        return [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_application:xopenURL:xoptions:) withObject:application withObject:url withObject:options];
    }
    return NO;
}

+ (void)xxpk_initSDKWithProductCode:(NSString *)xxpk_productCode {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_initSDKWithProductCode:) withObject:xxpk_productCode];
    }
}

+ (void)xxpk_login:(void(^)(NSString *uid, NSString*token))callback  {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_login:) withObject:callback];
    }
}

+ (void)xxpk_creatOrder:(NSString *)xxpk_productCode
                orderNo:(NSString *)orderNo
                subject:(NSString *)subject
                  total:(NSString *)totalPrice
              currentcy:(NSString *)currentcy
          extras_params:(NSString *)extras_params {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_creatOrder:orderNo:subject:total:currentcy:extras_params:) withObject:xxpk_productCode withObject:orderNo withObject:subject withObject:totalPrice withObject:currentcy withObject:extras_params];
    }
}

+ (void)xxpk_uploadRoleInfo:(NSString * _Nonnull)xxpk_serverId
            xxpk_serverName:(NSString * _Nonnull)xxpk_serverName
                xxpk_roleId:(NSString * _Nonnull)xxpk_roleId
              xxpk_roleName:(NSString * _Nonnull)xxpk_roleName
             xxpk_roleLevel:(NSString * _Nonnull)xxpk_roleLevel {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_uploadRoleInfo:xxpk_serverName:xxpk_roleId:xxpk_roleName:xxpk_roleLevel:) withObject:xxpk_serverId withObject:xxpk_serverName withObject:xxpk_roleId withObject:xxpk_roleName withObject:xxpk_roleLevel];
    }
}

+ (void)xxpk_logout {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logout)];
    }
}

+ (void)xxpk_userLogout:(void(^)(void))xxpk_userLogout {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_userLogout:) withObject:xxpk_userLogout];
    }
}
@end
