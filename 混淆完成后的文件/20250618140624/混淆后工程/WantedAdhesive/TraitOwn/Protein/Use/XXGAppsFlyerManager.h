

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGAppsFlyerManager : NSObject

+ (NSString *)xxpk_appsFlyerUID;

+ (void)xxpk_appsFlyerLibConfigureAtSDKInitStatusFinishWithKey:(NSString *)key xxpk_appleid:(NSString *)aid withActivateEvent:(NSString *)event;

+ (void)xxpk_logViewedContentEvent:(NSString *)uid;

+ (void)xxpk_logCompletedRegistrationEvent:(NSString *)uid;

+ (void)xxpk_logAddedToCartEvent:(NSString *)event withUid:(NSString *)uid;

+ (void)xxpk_logPurchasedEvent:(NSString *)event
                  xxpk_orderId:(NSString*)xxpk_orderId
                 currency:(NSString*)currency
                    price:(double)price;

+ (void)xxpk_appsFlyerUniversalLogEvent:(NSString *)event params:(NSDictionary *)params withUid:(NSString *)uid;

@end

NS_ASSUME_NONNULL_END
