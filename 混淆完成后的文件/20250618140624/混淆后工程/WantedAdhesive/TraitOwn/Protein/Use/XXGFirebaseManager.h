






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGFirebaseManager : NSObject

+ (void)xxpk_application:(UIApplication * _Nonnull)application xdidFinishLaunchingWithOptions:(NSDictionary<UIApplicationLaunchOptionsKey, id> * _Nullable)launchOptions;

+(void)xxpk_initiateOnDeviceConversionMeasurementWithPhoneNumber:(NSString *)phoneNumber;

+ (NSString *)xxpk_firebaseInstanceId;


+ (void)xxpk_logActivateWithEvent:(NSString *)event;


+ (void)xxpk_logViewedContentEvent:(NSString *)uid;


+ (void)xxpk_logCompletedRegistrationEvent:(NSString *)uid;


+ (void)xxpk_logAddedToCartEvent:(NSString *)event withUid:(NSString *)uid;


+ (void)xxpk_logPurchasedEvent:(NSString *)event xxpk_orderId:(NSString*)xxpk_orderId currency:(NSString*)currency price:(double)price;

+ (void)xxpk_firebaseUniversalLogEvent:(NSString *)event params:(NSDictionary *)params withUid:(NSString *)uid;
@end

NS_ASSUME_NONNULL_END
