






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGAppLovinManager : NSObject

+ (void)xxpk_initApplovinSDKWithKey:(NSString *)xxpk_maxkey xxpk_rewardedadid:(NSString *)xxpk_rewardedadid xxpk_testDevices:(NSArray *)xxpk_testDevices;

+ (void)xxpk_showRewardedAdForCustomData:(nullable NSString *)customData complate:(void(^)(BOOL result))complate;

@end

NS_ASSUME_NONNULL_END
