






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGThirdManager : NSObject

+ (void)xxpk_didFinishLaunchingWithOptions:(NSDictionary *)launchOptions xconnectOptions:(UISceneConnectionOptions *)connetOptions;

+ (BOOL)xxpk_applicationOpenURL:(NSURL *)url xoptions:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options xURLContexts:(NSSet<UIOpenURLContext *> *)URLContexts;


+ (void)xxpk_logAddedToCartEvent;


+ (void)xxpk_logPurchasedEventOrderId:(NSString*)xxpk_orderId
                        currency:(NSString*)currency
                           price:(double)price;


+ (void)xxpk_logFacebookEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logAppFlyerEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logFirebaseEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logAdjustEvent:(NSString *)event params:(NSDictionary *_Nullable)params;

@end

NS_ASSUME_NONNULL_END
