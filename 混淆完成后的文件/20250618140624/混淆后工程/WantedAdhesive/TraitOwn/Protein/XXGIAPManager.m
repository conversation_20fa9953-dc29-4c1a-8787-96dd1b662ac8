






#import "XXGIAPManager.h"
#import "XXGNetworkList.h"
#import "NSObject+XXGModel.h"
#import "XXGPlayKitConfig.h"
#import "XXGSelectProductItem.h"
#import "NSString+XXGString.h"
#import "XXGIAPHelp.h"
#import "XXGBoxManager.h"
#import "XXGValidateReceiptBody.h"
#import "XXGProductBody.h"
#import "XXGPlayKitCore.h"
#import "XXGAlertView.h"
#import "XXGLoadingView.h"
#import "XXGPlayKitCore+Canal.h"
#import "XXGPlayKitCore.h"
#import "XXGSelectProduct.h"
#import "XXGUIkitProtocol.h"
#import "XXGLoadingView.h"

#define weakify(obj) __weak typeof(obj) weak##obj = obj;
#define strongify(obj) __strong typeof(obj) obj = weak##obj;

@interface XXGIAPManager()<XXGIAPPayDelegate,XXGUIkitDelegate>

@end

@implementation XXGIAPManager

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

+ (void)xxpk_iapRepair {
    [XXGLoadingView showLoadingOnWindowWithText:__string_core.xxpk_tool_iap_repair_start];
    NSArray* transactions = [SKPaymentQueue defaultQueue].transactions;
    if (transactions.count > 0) {
        for (int i = 0; i<transactions.count; i++) {
            SKPaymentTransaction *transaction = transactions[i];
            [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
        }
    }
    [[XXGIAPHelpManager sharedManager] cleanAllModels];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [XXGLoadingView showLoadingOnWindowWithText:__string_core.xxpk_tool_iap_repair_complete];
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [XXGLoadingView hideLoadingFromWindow];
    });
}

- (void)xxpk_registerP {
    [XXGIAPHelpManager sharedManager].delegate = self;
    [[XXGIAPHelpManager sharedManager] registerPay];
}

- (void)xxpk_createOrder:(XXGProductBody *)item xxpk_isCoinOrder:(BOOL)isCoin {
    
    if (item.xxpk_amount.xxpk_isEmpty
        ||item.xxpk_cpOrderId.xxpk_isEmpty
        ||item.xxpk_productCode.xxpk_isEmpty
        ||item.xxpk_productName.xxpk_isEmpty
        ||item.xxpk_serverId.xxpk_isEmpty) {
        [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:__string_core.xxpk_p_params_error];
        return;
    }
    
    self.xxpk_isCoinOrder = isCoin;
    weakify(self);
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkCreateOrder:isCoin params:[item xxpk_modelToDict] success:^(NSDictionary * _Nonnull responseObject) {

        XXGSelectProduct *xxpk_product = [XXGSelectProduct xxpk_modelWithDict:responseObject[__data_core.xxpk_order]];

        weakself.xxpk_item = item;
        weakself.xxpk_item.xxpk_orderId = xxpk_product.xxpk_orderId;
        weakself.xxpk_item.xxpk_currency = xxpk_product.xxpk_currency;

        if (xxpk_product.xxpk_pay_method.count == 0) {
            [weakself.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:__string_core.xxpk_p_not_config];
            return;
        }

        
        if (xxpk_product.xxpk_pay_method.count == 1
&& (!xxpk_product.xxpk_pay_method[0].xxpk_note || xxpk_product.xxpk_pay_method[0].xxpk_note.xxpk_isEmpty)
            ) {
            [weakself _xxpk_selectPBeforeExtraWithSPItme:xxpk_product.xxpk_pay_method[0] xxpk_productCode:item.xxpk_productCode xxpk_orderId:self.xxpk_item.xxpk_orderId];
            return;
        }

        [[XXGPlayKitCore shared] xxpk_showUIofSelectPayMethod:xxpk_product xxpk_delegate:self];

    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:errorDescrip];
    }];
}

- (void)_xxpk_selectPBeforeExtraWithSPItme:(XXGSelectProductItem *)item xxpk_productCode:(NSString *)xxpk_productCode xxpk_orderId:(NSString *)xxpk_orderId {

[self _xxpk_selectPWithSPItme:item xxpk_productCode:xxpk_productCode xxpk_orderId:xxpk_orderId];
}


- (void)_xxpk_selectPWithSPItme:(XXGSelectProductItem *)item xxpk_productCode:(NSString *)xxpk_productCode xxpk_orderId:(NSString *)xxpk_orderId {

    
    if ([[XXGPlayKitCore shared] _xxpk_canal_selectPWithSPItme:item pitem:self.xxpk_item]) {
        return;
    }

    
    if ([item.xxpk_type containsString:__data_core.xxpk_iap]) {
        [[XXGIAPHelpManager sharedManager] buyProductWithUserID:[XXGBoxManager xxpk_comeinedBox].xxpk_boxId productIdentifier:xxpk_productCode xxpk_orderId:xxpk_orderId];
        return;
    }

    
    if ([item.xxpk_type containsString:__data_core.xxpk_h5]) {
        [self.xxpk_delegate xxpk_IAPManagerOpenOfOrderUrl:item.xxpk_order_url];
        [self __showCheckH5OrderAlertWith:xxpk_orderId];
        return;
    }

    [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:__string_core.xxpk_p_notype];
}

- (void)__showCheckH5OrderAlertWith:(NSString *)xxpk_orderId {
    [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips
                                  message:__string_core.xxpk_p_pornop
                             buttonTitles:@[__string_core.xxpk_p_cancel,__string_core.xxpk_p_sustip]
                               completion:^(NSInteger buttonIndex) {
        if (buttonIndex == 0) {
            [self.xxpk_delegate xxpk_iapManagerCancel:self];
        }else {
            [self __checkH5Order:xxpk_orderId];
        }
    }];
}

- (void)__checkH5Order:(NSString *)xxpk_orderId {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkCheckOrderWithIsCoin:self.xxpk_isCoinOrder xxpk_orderId:xxpk_orderId success:^(NSDictionary * _Nonnull responseObject) {
        NSInteger status = [responseObject[__data_core.xxpk_order][__data_core.xxpk_p_status] integerValue];
        if (status == 1) {
            [self.xxpk_delegate xxpk_iapManager:self paySuccessWithItem:self.xxpk_item];
        }else {
            [self.xxpk_delegate xxpk_iapManagerCancel:self];
        }
    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:errorDescrip];
    } retryCount:10 currentAttempt:0];
}


- (void)__checkIAPOrder:(XXGValidateReceiptBody *)model resultAction:(VerifyRsultBlock)resultAction {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkCheckOrderWithIsCoin:self.xxpk_isCoinOrder xxpk_orderId:model.xxpk_order_id success:^(NSDictionary * _Nonnull responseObject) {
        NSInteger status = [responseObject[__data_core.xxpk_order][__data_core.xxpk_p_status] integerValue];
        if (status == -1) {
            resultAction(XXGIAPVerifyInvalid);
            [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:__string_core.xxpk_p_error];
        }else if (status == 1) {
            resultAction(XXGIAPVerifyValid);
            [self.xxpk_delegate xxpk_iapManager:self paySuccessWithItem:self.xxpk_item];
        }else {
            [self __checkIAPOrder:model resultAction:resultAction];
        }
    } failure:^(NSError * _Nonnull error) {
        if (error.code == __data_core.xxpk_net_code_error) {
            resultAction(XXGIAPVerifyInvalid);
            NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
            [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:errorDescrip];
        }else {
            [self __checkIAPOrder:model resultAction:resultAction];
        }
    } retryCount:36 currentAttempt:0];
}

- (void)__uploadReceiptWithModel:(XXGValidateReceiptBody *)model resultAction:(VerifyRsultBlock)resultAction {
    if (XXGPlayKitConfig.shared.xxpk_comeinStatus != XXGPlayKitComeInStatusFinish) {
        return;
    }
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkValidateReceipt:[model xxpk_modelToDict] success:^(NSDictionary * _Nonnull responseObject) {
        [self __checkIAPOrder:model resultAction:resultAction];
    } failure:^(NSError * _Nonnull error) {
        [self __uploadReceiptWithModel:model resultAction:resultAction];
    }];
}



- (void)xxpk_selectPayMethodPayButtonDidClickOfProductItem:(XXGSelectProductItem *)productItem {
    [self _xxpk_selectPBeforeExtraWithSPItme:productItem xxpk_productCode:self.xxpk_item.xxpk_productCode xxpk_orderId:self.xxpk_item.xxpk_orderId];
}


- (void)xxpk_selectPayMethodCloseButtonDidClick {
    [self.xxpk_delegate xxpk_iapManagerCancel:self];
}


- (void)verifyWithModel:(XXGIAPTransactionModel *)model resultAction:(VerifyRsultBlock)resultAction {
    XXGValidateReceiptBody *body = [[XXGValidateReceiptBody alloc] init];
    body.xxpk_order_id = model.xxpk_seriverOrder;
    body.xxpk_receipt_data = model.xxpk_appStoreReceipt;
    body.xxpk_product_code = model.xxpk_productIdentifier;
    body.xxpk_transaction_id = model.xxpk_transactionIdentifier;
    body.xxpk_price = model.xxpk_priceString;
    body.xxpk_currency = model.xxpk_codeString;
    if (!_xxpk_item) {
        _xxpk_item = [XXGProductBody new];
        _xxpk_item.xxpk_productCode = model.xxpk_productIdentifier;
        _xxpk_item.xxpk_orderId = model.xxpk_seriverOrder;
        _xxpk_item.xxpk_amount = model.xxpk_priceString;
    }
    _xxpk_item.xxpk_currency = model.xxpk_codeString;
    [self __uploadReceiptWithModel:body resultAction:resultAction];
}

- (void)onIAPPayFailue:(XXGIAPTransactionModel *)model withError:(NSError *)error {
    if (model.xxpk_transactionStatus == TransactionStatusAppleCancel) {
        [self.xxpk_delegate xxpk_iapManagerCancel:self];
    }else {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:errorDescrip];
    }
    if (error.code == XXGIAPErrorCodeHasUnfinishedTransaction) {
        [[XXGIAPHelpManager sharedManager] checkUnfinishTransaction];
    }
}

- (void)onLaunProductListFinish:(SKProduct *)products withError:(NSError *)error {
    NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
    [self.xxpk_delegate xxpk_iapManager:self payFialedWithMessage:errorDescrip];
}

- (void)currentStatus:(XXGIAPLoadingStatus)status {
    switch (status) {
        case XXGIAPLoadingStatus_CheckingProduct:
            [XXGLoadingView showLoadingOnWindowWithText:__string_core.xxpk_tool_iap_checkingproduct];
            break;
        case XXGIAPLoadingStatus_Paying:
            [XXGLoadingView showLoadingOnWindowWithText:__string_core.xxpk_tool_iap_paying];
            break;
        case XXGIAPLoadingStatus_Restoring:
            [XXGLoadingView showLoadingOnWindowWithText:__string_core.xxpk_tool_iap_restoring];
            break;
        case XXGIAPLoadingStatus_Verifying:
            [XXGLoadingView showLoadingOnWindowWithText:__string_core.xxpk_tool_iap_verifying];
            break;
        default:
            break;
    }
}
@end
