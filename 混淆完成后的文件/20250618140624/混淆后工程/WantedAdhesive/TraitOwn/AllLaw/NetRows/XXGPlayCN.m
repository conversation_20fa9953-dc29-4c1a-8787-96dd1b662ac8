






#import "XXGPlayCN.h"
#import "XXGPlayKitCore.h"
#import "XXGPlayKitConfig.h"


@implementation XXGPlayCN

+ (void)xxpk_setPlayDelegate:(id<XXGPlayDelegate>)delegate {
    XXGPlayKitCore.shared.xxpk_delegate = delegate;
}

+ (void)xxpk_comein {
    if (XXGPlayKitConfig.shared.xxpk_trampoline) {
        return;
    }
    [[XXGPlayKitCore shared] xxpk_coreComein];
}

+ (void)xxpk_logout {
    if (XXGPlayKitConfig.shared.xxpk_trampoline) {
        return;
    }
    [[XXGPlayKitCore shared] xxpk_logout];
}

+ (void)xxpk_createOrder:(NSString *)xxpk_cpOrderId
        xxpk_productCode:(NSString *)xxpk_productCode
             xxpk_amount:(NSString *)xxpk_amount
        xxpk_productName:(NSString *)xxpk_productName
           xxpk_serverId:(NSString *)xxpk_serverId
          xxpk_extraInfo:(NSString *)xxpk_extraInfo
             xxpk_roleId:(NSString *)xxpk_roleId
           xxpk_roleName:(NSString *)xxpk_roleName
          xxpk_roleLevel:(NSString *)xxpk_roleLevel {
    if (XXGPlayKitConfig.shared.xxpk_trampoline) {
        return;
    }
    XXGProductBody *xxpk_body = [XXGProductBody new];
    xxpk_body.xxpk_cpOrderId = xxpk_cpOrderId;
    xxpk_body.xxpk_productCode = xxpk_productCode;
    xxpk_body.xxpk_amount = xxpk_amount;
    xxpk_body.xxpk_productName = xxpk_productName;
    xxpk_body.xxpk_serverId = xxpk_serverId;
    xxpk_body.xxpk_roleId = xxpk_roleId;
    xxpk_body.xxpk_roleName = xxpk_roleName;
    xxpk_body.xxpk_roleLevel = xxpk_roleLevel;
    xxpk_body.xxpk_extraInfo = xxpk_extraInfo;
    [[XXGPlayKitCore shared] xxpk_creatOrder:xxpk_body xxpk_isCoinOrder:NO];
}

+ (void)xxpk_uploadRoleInfo:(NSString * _Nonnull)xxpk_serverId
            xxpk_serverName:(NSString * _Nonnull)xxpk_serverName
                xxpk_roleId:(NSString * _Nonnull)xxpk_roleId
              xxpk_roleName:(NSString * _Nonnull)xxpk_roleName
             xxpk_roleLevel:(NSString * _Nonnull)xxpk_roleLevel
                xxpk_extend:(NSDictionary * _Nullable)xxpk_extend {
    
    if (XXGPlayKitConfig.shared.xxpk_trampoline) {
        return;
    }
    XXGRoleBody *xxpk_role_body = [XXGRoleBody new];
    xxpk_role_body.xxpk_serverId = xxpk_serverId;
    xxpk_role_body.xxpk_serverName = xxpk_serverName;
    xxpk_role_body.xxpk_roleId = xxpk_roleId;
    xxpk_role_body.xxpk_roleName = xxpk_roleName;
    xxpk_role_body.xxpk_roleLevel = xxpk_roleLevel;
    xxpk_role_body.xxpk_extend = xxpk_extend;
    [[XXGPlayKitCore shared] xxpk_uploadRoleInfo:xxpk_role_body];
}

+ (void)xxpk_didFinishLaunchingWithOptions:(NSDictionary *)launchOptions xconnectOptions:(UISceneConnectionOptions *)connectionOptions {
    [[XXGPlayKitCore shared] xxpk_didFinishLaunchingWithOptions:launchOptions xconnectOptions:connectionOptions];
}

+ (BOOL)xxpk_applicationOpenURL:(NSURL *)url xoptions:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options xURLContexts:(NSSet<UIOpenURLContext *> *)URLContexts {
    return [[XXGPlayKitCore shared] xxpk_applicationOpenURL:url xoptions:options xURLContexts:URLContexts];
}


+ (void)xxpk_openUserCenterSidebar:(NSString *)type {
    [[XXGPlayKitCore shared] xxpk_openUserCenterSidebar:type];
}

+ (void)xxpk_iapRepair {
    [[XXGPlayKitCore shared] xxpk_iapRepair];
}
@end
