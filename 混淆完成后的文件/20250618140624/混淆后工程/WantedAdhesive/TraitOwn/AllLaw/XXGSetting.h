






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN


typedef NS_ENUM(NSUInteger, XXGPlayKitStartStatus) {
    XXGPlayKitStartStatusNot,
    XXGPlayKitStartStatusBegin,
    XXGPlayKitStartStatusFinish
};


typedef NS_ENUM(NSUInteger, XXGPlayKitComeInStatus) {
    XXGPlayKitComeInStatusNot,
    XXGPlayKitComeInStatusWillBegin,
    XXGPlayKitComeInStatusBegining,
    XXGPlayKitComeInStatusFinish
};

@interface XXGSetting : NSObject


@property (class, nonatomic,readonly, copy) NSString *XXGNotificationNameKeyStartStatusChange;


@property (class, nonatomic,readonly, copy) NSString *XXGNotificationNameKeyComeinStatusChange;

@property (class, nonatomic,readonly, assign) XXGPlayKitStartStatus xxpk_startStatus;

@property (class, nonatomic,readonly, assign) XXGPlayKitComeInStatus xxpk_comeinStatus;

@property (class, nonatomic,readonly, copy) NSString *xxpk_version;

+ (void)xxpk_setCloseButtonHidden:(BOOL)hidden;

+ (void)xxpk_setInitAppId:(NSString *)appid;

+ (void)xxpk_setTestAppId:(NSString *)appid bundleId:(NSString *_Nullable)bundleId appVersion:(NSString *_Nullable)appVersion;

@end

NS_ASSUME_NONNULL_END
