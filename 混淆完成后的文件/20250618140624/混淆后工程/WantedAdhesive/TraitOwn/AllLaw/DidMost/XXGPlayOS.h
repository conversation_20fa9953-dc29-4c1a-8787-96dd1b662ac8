






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
@protocol XXGPlayDelegate;

NS_ASSUME_NONNULL_BEGIN

@interface XXGPlayOS : NSObject


+ (void)xxpk_setPlayDelegate:(id<XXGPlayDelegate>)delegate;


+ (void)xxpk_comein;


+ (void)xxpk_logout;


+ (void)xxpk_createOrder:(NSString *)xxpk_cpOrderId
        xxpk_productCode:(NSString *)xxpk_productCode
             xxpk_amount:(NSString *)xxpk_amount
        xxpk_productName:(NSString *)xxpk_productName
           xxpk_serverId:(NSString *)xxpk_serverId
          xxpk_extraInfo:(NSString *)xxpk_extraInfo
             xxpk_roleId:(NSString *)xxpk_roleId
           xxpk_roleName:(NSString *)xxpk_roleName
          xxpk_roleLevel:(NSString *)xxpk_roleLevel;


+ (void)xxpk_uploadRoleInfo:(NSString * _Nonnull)xxpk_serverId
            xxpk_serverName:(NSString * _Nonnull)xxpk_serverName
                xxpk_roleId:(NSString * _Nonnull)xxpk_roleId
              xxpk_roleName:(NSString * _Nonnull)xxpk_roleName
             xxpk_roleLevel:(NSString * _Nonnull)xxpk_roleLevel
                xxpk_extend:(NSDictionary * _Nullable)xxpk_extend;


+ (void)xxpk_didFinishLaunchingWithOptions:(NSDictionary *_Nullable)launchOptions xconnectOptions:(UISceneConnectionOptions *_Nullable)connectionOptions;


+ (BOOL)xxpk_applicationOpenURL:(NSURL *_Nullable)url xoptions:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *_Nullable)options xURLContexts:(NSSet<UIOpenURLContext *> *_Nullable)URLContexts;



+ (void)xxpk_openUserCenterSidebar:(NSString *)type;


+ (void)xxpk_iapRepair;

@property (class, nonatomic, assign, readonly) BOOL xxpk_isBindFacebook;
@property (class, nonatomic, assign, readonly) BOOL xxpk_isBindVK;


+ (void)xxpk_facebookShareWithUrl:(NSString *)url;


+ (void)xxpk_facebookShareWithImgUrl:(NSString *)imgUrl;


+ (void)xxpk_facebookSub;


+ (void)xxpk_facebookInvite;


+ (void)xxpk_facebookBind:(void(^)(NSDictionary *_Nullable userInfo, NSString* errorMsg))handler;


+ (void)xxpk_VKBind:(void(^)(NSDictionary *_Nullable userInfo, NSString* errorMsg))handler;


+ (void)xxpk_logFacebookEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logAppFlyerEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logFirebaseEvent:(NSString *)event params:(NSDictionary *_Nullable)params;
+ (void)xxpk_logAdjustEvent:(NSString *)event params:(NSDictionary *_Nullable)params;


+ (void)xxpk_showRewardedAdForCustomData:(nullable NSString *)customData complate:(void(^)(BOOL result))complate;


+ (void)xxpk_reportlogWithType:(NSString *)xxpk_type xxpk_content:(NSString *)xxpk_content;

@end

NS_ASSUME_NONNULL_END
