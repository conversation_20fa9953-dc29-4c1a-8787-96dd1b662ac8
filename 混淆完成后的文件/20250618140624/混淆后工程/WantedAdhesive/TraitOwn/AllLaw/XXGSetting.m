






#import "XXGSetting.h"
#import "XXGPlayKitConfig.h"

@implementation XXGSetting

+ (NSString *)XXGNotificationNameKeyStartStatusChange {
    return NSStringFromSelector(@selector(XXGNotificationNameKeyStartStatusChange));
}

+ (NSString *)XXGNotificationNameKeyComeinStatusChange {
    return NSStringFromSelector(@selector(XXGNotificationNameKeyComeinStatusChange));
}

+ (XXGPlayKitStartStatus)xxpk_startStatus {
    return [XXGPlayKitConfig shared].xxpk_startStatus;
}

+ (XXGPlayKitComeInStatus)xxpk_comeinStatus {
    return [XXGPlayKitConfig shared].xxpk_comeinStatus;
}

+ (void)xxpk_setCloseButtonHidden:(BOOL)hidden {
    [XXGPlayKitConfig shared].xxpk_closeButtonHidden = hidden;
}

+ (NSString *)xxpk_version {
    return XXGPlayKitConfig.shared.xxpk_body.xxpk_version;
}

+ (void)xxpk_setInitAppId:(NSString *)appid {
    XXGPlayKitConfig.shared.xxpk_startid = appid;
}

+ (void)xxpk_setTestAppId:(NSString *)appid bundleId:(NSString *)bundleId  appVersion:(NSString *)appVersion{
    XXGPlayKitConfig.shared.xxpk_testAppId = appid;
    XXGPlayKitConfig.shared.xxpk_testBundleId = bundleId;
    XXGPlayKitConfig.shared.xxpk_testAppVersion = appVersion;
}
@end
