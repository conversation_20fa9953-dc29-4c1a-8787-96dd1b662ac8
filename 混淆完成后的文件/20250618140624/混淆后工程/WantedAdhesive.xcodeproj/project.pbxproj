// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		1811E3432DCC528B00B17C42 /* XXGAppLovinManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 1811E3412DCC528B00B17C42 /* XXGAppLovinManager.h */; };
		1811E3442DCC528B00B17C42 /* XXGAppLovinManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 1811E3422DCC528B00B17C42 /* XXGAppLovinManager.m */; };
		1811F70B2DD2FD9800B17C42 /* XXGSelectProduct.m in Sources */ = {isa = PBXBuildFile; fileRef = 1811F70A2DD2FD9800B17C42 /* XXGSelectProduct.m */; };
		1811F70C2DD2FD9800B17C42 /* XXGSelectProduct.h in Headers */ = {isa = PBXBuildFile; fileRef = 1811F7092DD2FD9800B17C42 /* XXGSelectProduct.h */; };
		1812EABD2D9114C300B7BB73 /* XXGWKMethodAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 1812EAB92D9114C300B7BB73 /* XXGWKMethodAction.h */; };
		1812EABE2D9114C300B7BB73 /* XXGWKMethodAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 1812EABA2D9114C300B7BB73 /* XXGWKMethodAction.m */; };
		1812EAC32D91593100B7BB73 /* NSURL+XXGAnalyse.h in Headers */ = {isa = PBXBuildFile; fileRef = 1812EABF2D91593100B7BB73 /* NSURL+XXGAnalyse.h */; };
		1812EAC42D91593100B7BB73 /* NSURL+XXGAnalyse.m in Sources */ = {isa = PBXBuildFile; fileRef = 1812EAC02D91593100B7BB73 /* NSURL+XXGAnalyse.m */; };
		1812EAC92D915CC500B7BB73 /* XXGPlayKitCore+Others.h in Headers */ = {isa = PBXBuildFile; fileRef = 1812EAC52D915CC500B7BB73 /* XXGPlayKitCore+Others.h */; };
		1812EACA2D915CC500B7BB73 /* XXGPlayKitCore+Others.m in Sources */ = {isa = PBXBuildFile; fileRef = 1812EAC62D915CC500B7BB73 /* XXGPlayKitCore+Others.m */; };
		181E79242D754987009CF2CC /* XXGExecuteActions.h in Headers */ = {isa = PBXBuildFile; fileRef = 181E79222D754987009CF2CC /* XXGExecuteActions.h */; };
		181E79252D754987009CF2CC /* XXGExecuteActions.m in Sources */ = {isa = PBXBuildFile; fileRef = 181E79232D754987009CF2CC /* XXGExecuteActions.m */; };
		181E79662D75BC2D009CF2CC /* XXGNetworkList.m in Sources */ = {isa = PBXBuildFile; fileRef = 181E79632D75BC2D009CF2CC /* XXGNetworkList.m */; };
		181E79672D75BC2D009CF2CC /* XXGNetworkList.h in Headers */ = {isa = PBXBuildFile; fileRef = 181E79622D75BC2D009CF2CC /* XXGNetworkList.h */; };
		181E797B2D76FF9F009CF2CC /* XXGBoxContent.m in Sources */ = {isa = PBXBuildFile; fileRef = 181E79782D76FF9F009CF2CC /* XXGBoxContent.m */; };
		181E797C2D76FF9F009CF2CC /* XXGBoxContent.h in Headers */ = {isa = PBXBuildFile; fileRef = 181E79772D76FF9F009CF2CC /* XXGBoxContent.h */; };
		181E79882D77EED2009CF2CC /* XXGBoxManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 181E79852D77EED2009CF2CC /* XXGBoxManager.m */; };
		181E79892D77EED2009CF2CC /* XXGBoxManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 181E79842D77EED2009CF2CC /* XXGBoxManager.h */; };
		181E798E2D7845A0009CF2CC /* XXGSkinModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 181E798B2D7845A0009CF2CC /* XXGSkinModel.m */; };
		181E798F2D7845A0009CF2CC /* XXGSkinModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 181E798A2D7845A0009CF2CC /* XXGSkinModel.h */; };
		181E79942D784A7F009CF2CC /* XXGAdaptionCof.m in Sources */ = {isa = PBXBuildFile; fileRef = 181E79912D784A7F009CF2CC /* XXGAdaptionCof.m */; };
		181E79952D784A7F009CF2CC /* XXGAdaptionCof.h in Headers */ = {isa = PBXBuildFile; fileRef = 181E79902D784A7F009CF2CC /* XXGAdaptionCof.h */; };
		182C6D882D8D47C200D3F530 /* XXGFacebookManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 182C6D852D8D47C200D3F530 /* XXGFacebookManager.m */; };
		182C6D892D8D47C200D3F530 /* XXGFacebookManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 182C6D842D8D47C200D3F530 /* XXGFacebookManager.h */; };
		182C6D9C2D8D565B00D3F530 /* XXGThirdManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 182C6D992D8D565B00D3F530 /* XXGThirdManager.m */; };
		182C6D9D2D8D565B00D3F530 /* XXGThirdManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 182C6D982D8D565B00D3F530 /* XXGThirdManager.h */; };
		182C6DA32D8D61C100D3F530 /* NSObject+XXGPerformSelector.m in Sources */ = {isa = PBXBuildFile; fileRef = 182C6DA02D8D61C100D3F530 /* NSObject+XXGPerformSelector.m */; };
		182C6DA42D8D61C100D3F530 /* NSObject+XXGPerformSelector.h in Headers */ = {isa = PBXBuildFile; fileRef = 182C6D9F2D8D61C100D3F530 /* NSObject+XXGPerformSelector.h */; };
		182C6DA72D8D66CF00D3F530 /* XXGExtraParams.m in Sources */ = {isa = PBXBuildFile; fileRef = 182C6DA62D8D66CF00D3F530 /* XXGExtraParams.m */; };
		182C6DA82D8D66CF00D3F530 /* XXGExtraParams.h in Headers */ = {isa = PBXBuildFile; fileRef = 182C6DA52D8D66CF00D3F530 /* XXGExtraParams.h */; };
		182F66992DC090A90039594F /* UIDevice+XXGDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 182F66952DC090A90039594F /* UIDevice+XXGDevice.h */; };
		182F669A2DC090A90039594F /* UIDevice+XXGDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 182F66962DC090A90039594F /* UIDevice+XXGDevice.m */; };
		182F69D12DC0AB150039594F /* XXGAppInfoViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 182F69CF2DC0AB150039594F /* XXGAppInfoViewController.h */; };
		182F69D22DC0AB150039594F /* XXGAppInfoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 182F69D02DC0AB150039594F /* XXGAppInfoViewController.m */; };
		182F6C182DC0F9FB0039594F /* WantedAdhesive.m in Sources */ = {isa = PBXBuildFile; fileRef = 182F6C172DC0F9FB0039594F /* WantedAdhesive.m */; };
		182F6CA32DC1BAA70039594F /* XXGServerInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 182F6CA02DC1BAA70039594F /* XXGServerInfo.m */; };
		182F6CA42DC1BAA70039594F /* XXGServerInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 182F6C9F2DC1BAA70039594F /* XXGServerInfo.h */; };
		182F6CA72DC28C660039594F /* XXGPoopoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 182F6CA62DC28C660039594F /* XXGPoopoManager.m */; };
		182F6CA82DC28C660039594F /* XXGPoopoManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 182F6CA52DC28C660039594F /* XXGPoopoManager.h */; };
		1834F7C92D730E3B00A1C782 /* XXGActionItem.h in Headers */ = {isa = PBXBuildFile; fileRef = 1834F7C72D730E3B00A1C782 /* XXGActionItem.h */; };
		1834F7CA2D730E3B00A1C782 /* XXGActionItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 1834F7C82D730E3B00A1C782 /* XXGActionItem.m */; };
		18401FAA2D8D8A51005D10BB /* XXGAppsFlyerManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18401FA92D8D8A51005D10BB /* XXGAppsFlyerManager.m */; };
		18401FAB2D8D8A51005D10BB /* XXGAppsFlyerManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18401FA82D8D8A51005D10BB /* XXGAppsFlyerManager.h */; };
		18401FB82D8E9506005D10BB /* XXGFirebaseManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18401FB72D8E9506005D10BB /* XXGFirebaseManager.m */; };
		18401FB92D8E9506005D10BB /* XXGFirebaseManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18401FB62D8E9506005D10BB /* XXGFirebaseManager.h */; };
		1849B9302D7877E400A5F887 /* XXGThemeColor.m in Sources */ = {isa = PBXBuildFile; fileRef = 1849B92F2D7877E400A5F887 /* XXGThemeColor.m */; };
		1849B9312D7877E400A5F887 /* XXGThemeColor.h in Headers */ = {isa = PBXBuildFile; fileRef = 1849B92E2D7877E400A5F887 /* XXGThemeColor.h */; };
		1849B9382D78798B00A5F887 /* XXGDockerCof.m in Sources */ = {isa = PBXBuildFile; fileRef = 1849B9352D78798B00A5F887 /* XXGDockerCof.m */; };
		1849B9392D78798B00A5F887 /* XXGDockerCof.h in Headers */ = {isa = PBXBuildFile; fileRef = 1849B9342D78798B00A5F887 /* XXGDockerCof.h */; };
		1849B93C2D7931D700A5F887 /* XXGServiceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 1849B93B2D7931D700A5F887 /* XXGServiceInfo.m */; };
		1849B93D2D7931D700A5F887 /* XXGServiceInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 1849B93A2D7931D700A5F887 /* XXGServiceInfo.h */; };
		1849B9442D79352700A5F887 /* XXGBoxCenterCof.m in Sources */ = {isa = PBXBuildFile; fileRef = 1849B9412D79352700A5F887 /* XXGBoxCenterCof.m */; };
		1849B9452D79352700A5F887 /* XXGBoxCenterCof.h in Headers */ = {isa = PBXBuildFile; fileRef = 1849B9402D79352700A5F887 /* XXGBoxCenterCof.h */; };
		1849B9742D7AE2A100A5F887 /* XXGUIKit.m in Sources */ = {isa = PBXBuildFile; fileRef = 1849B9712D7AE2A100A5F887 /* XXGUIKit.m */; };
		1849B9752D7AE2A100A5F887 /* XXGUIKit.h in Headers */ = {isa = PBXBuildFile; fileRef = 1849B9702D7AE2A100A5F887 /* XXGUIKit.h */; };
		1849B97B2D7AE9A800A5F887 /* XXGBaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1849B9782D7AE9A800A5F887 /* XXGBaseViewController.m */; };
		1849B97C2D7AE9A800A5F887 /* XXGBaseViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 1849B9772D7AE9A800A5F887 /* XXGBaseViewController.h */; };
		1849B9872D7AEA3B00A5F887 /* XXGNavigationController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1849B9842D7AEA3B00A5F887 /* XXGNavigationController.m */; };
		1849B9882D7AEA3B00A5F887 /* XXGNavigationController.h in Headers */ = {isa = PBXBuildFile; fileRef = 1849B9832D7AEA3B00A5F887 /* XXGNavigationController.h */; };
		1849B98B2D7AED6D00A5F887 /* UIViewController+XXGViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1849B98A2D7AED6D00A5F887 /* UIViewController+XXGViewController.m */; };
		1849B98C2D7AED6D00A5F887 /* UIViewController+XXGViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 1849B9892D7AED6D00A5F887 /* UIViewController+XXGViewController.h */; };
		1849B9ED2D7B022600A5F887 /* XXGComeinViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1849B9EA2D7B022600A5F887 /* XXGComeinViewController.m */; };
		1849B9EE2D7B022600A5F887 /* XXGComeinViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 1849B9E92D7B022600A5F887 /* XXGComeinViewController.h */; };
		1863733C2D7164A9000D82AE /* XXGPlayOS.m in Sources */ = {isa = PBXBuildFile; fileRef = 1863733A2D7164A9000D82AE /* XXGPlayOS.m */; };
		1863733D2D7164A9000D82AE /* XXGPlayOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 186373392D7164A9000D82AE /* XXGPlayOS.h */; settings = {ATTRIBUTES = (Public, ); }; };
		186AE8ED2D942364000F1A11 /* XXGVKManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 186AE8EC2D942364000F1A11 /* XXGVKManager.m */; };
		186AE8EE2D942364000F1A11 /* XXGVKManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 186AE8EB2D942364000F1A11 /* XXGVKManager.h */; };
		186AE9D22D96C244000F1A11 /* XXGAdjustManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 186AE9D12D96C244000F1A11 /* XXGAdjustManager.m */; };
		186AE9D32D96C244000F1A11 /* XXGAdjustManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 186AE9D02D96C244000F1A11 /* XXGAdjustManager.h */; };
		1886C6062DE44C4D006C3D99 /* XXGDebugger.h in Headers */ = {isa = PBXBuildFile; fileRef = 1886C6022DE44C4D006C3D99 /* XXGDebugger.h */; };
		1886C6072DE44C4D006C3D99 /* XXGDebugger.m in Sources */ = {isa = PBXBuildFile; fileRef = 1886C6032DE44C4D006C3D99 /* XXGDebugger.m */; };
		1886C6E92DE6F77F006C3D99 /* ZBConsoleDestinatioin.h in Headers */ = {isa = PBXBuildFile; fileRef = 1886C6DD2DE6F77F006C3D99 /* ZBConsoleDestinatioin.h */; };
		1886C6EA2DE6F77F006C3D99 /* ZBLogFormatter.h in Headers */ = {isa = PBXBuildFile; fileRef = 1886C6E32DE6F77F006C3D99 /* ZBLogFormatter.h */; };
		1886C6EB2DE6F77F006C3D99 /* ZBLogViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 1886C6E62DE6F77F006C3D99 /* ZBLogViewController.h */; };
		1886C6EC2DE6F77F006C3D99 /* ZBLogMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 1886C6E52DE6F77F006C3D99 /* ZBLogMacros.h */; };
		1886C6ED2DE6F77F006C3D99 /* ZBBaseDestination.h in Headers */ = {isa = PBXBuildFile; fileRef = 1886C6DB2DE6F77F006C3D99 /* ZBBaseDestination.h */; };
		1886C6EE2DE6F77F006C3D99 /* ZBLog.h in Headers */ = {isa = PBXBuildFile; fileRef = 1886C6E12DE6F77F006C3D99 /* ZBLog.h */; };
		1886C6EF2DE6F77F006C3D99 /* ZBFileDestination.h in Headers */ = {isa = PBXBuildFile; fileRef = 1886C6DF2DE6F77F006C3D99 /* ZBFileDestination.h */; };
		1886C6F02DE6F77F006C3D99 /* ZBObjectiveCBeaver.h in Headers */ = {isa = PBXBuildFile; fileRef = 1886C6E82DE6F77F006C3D99 /* ZBObjectiveCBeaver.h */; };
		1886C6F12DE6F77F006C3D99 /* ZBConsoleDestinatioin.m in Sources */ = {isa = PBXBuildFile; fileRef = 1886C6DE2DE6F77F006C3D99 /* ZBConsoleDestinatioin.m */; };
		1886C6F22DE6F77F006C3D99 /* ZBLogFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 1886C6E42DE6F77F006C3D99 /* ZBLogFormatter.m */; };
		1886C6F32DE6F77F006C3D99 /* ZBFileDestination.m in Sources */ = {isa = PBXBuildFile; fileRef = 1886C6E02DE6F77F006C3D99 /* ZBFileDestination.m */; };
		1886C6F42DE6F77F006C3D99 /* ZBLog.m in Sources */ = {isa = PBXBuildFile; fileRef = 1886C6E22DE6F77F006C3D99 /* ZBLog.m */; };
		1886C6F52DE6F77F006C3D99 /* ZBLogViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1886C6E72DE6F77F006C3D99 /* ZBLogViewController.m */; };
		1886C6F62DE6F77F006C3D99 /* ZBBaseDestination.m in Sources */ = {isa = PBXBuildFile; fileRef = 1886C6DC2DE6F77F006C3D99 /* ZBBaseDestination.m */; };
		188F7C5A2D9BBB5400699DF4 /* XXGMobileTextField.h in Headers */ = {isa = PBXBuildFile; fileRef = 188F7C562D9BBB5400699DF4 /* XXGMobileTextField.h */; };
		188F7C5B2D9BBB5400699DF4 /* XXGMobileTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 188F7C572D9BBB5400699DF4 /* XXGMobileTextField.m */; };
		188F7C662D9E206D00699DF4 /* XXGSaveNamePSViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 188F7C622D9E206D00699DF4 /* XXGSaveNamePSViewController.h */; };
		188F7C672D9E206D00699DF4 /* XXGSaveNamePSViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 188F7C632D9E206D00699DF4 /* XXGSaveNamePSViewController.m */; };
		188F7F0E2DA7EEAC00699DF4 /* XXGMarqueeViewCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 188F7F0C2DA7EEAC00699DF4 /* XXGMarqueeViewCell.h */; };
		188F7F0F2DA7EEAC00699DF4 /* XXGMarqueeViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 188F7F0D2DA7EEAC00699DF4 /* XXGMarqueeViewCell.m */; };
		188F7F172DA7EF9C00699DF4 /* XXGTransparentWindow.h in Headers */ = {isa = PBXBuildFile; fileRef = 188F7F132DA7EF9C00699DF4 /* XXGTransparentWindow.h */; };
		188F7F182DA7EF9C00699DF4 /* XXGTransparentWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = 188F7F142DA7EF9C00699DF4 /* XXGTransparentWindow.m */; };
		188F7F1B2DA7F00F00699DF4 /* XXGOrientationViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 188F7F192DA7F00F00699DF4 /* XXGOrientationViewController.h */; };
		188F7F1C2DA7F00F00699DF4 /* XXGOrientationViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 188F7F1A2DA7F00F00699DF4 /* XXGOrientationViewController.m */; };
		188F80072DACB95200699DF4 /* NSString+XXGMd5.m in Sources */ = {isa = PBXBuildFile; fileRef = 188F80042DACB95200699DF4 /* NSString+XXGMd5.m */; };
		188F80082DACB95200699DF4 /* NSString+XXGMd5.h in Headers */ = {isa = PBXBuildFile; fileRef = 188F80032DACB95200699DF4 /* NSString+XXGMd5.h */; };
		188F80132DACBA3500699DF4 /* NSData+SunHope.m in Sources */ = {isa = PBXBuildFile; fileRef = 188F80102DACBA3500699DF4 /* NSData+SunHope.m */; };
		188F80142DACBA3500699DF4 /* NSData+SunHope.h in Headers */ = {isa = PBXBuildFile; fileRef = 188F800F2DACBA3500699DF4 /* NSData+SunHope.h */; };
		18989DEA2D71C35A00E11C3B /* WantedAdhesive.h in Headers */ = {isa = PBXBuildFile; fileRef = 18989DE92D71C35A00E11C3B /* WantedAdhesive.h */; settings = {ATTRIBUTES = (Public, ); }; };
		18989F2B2D71E0B000E11C3B /* XXGDeviceInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 18989F272D71E0B000E11C3B /* XXGDeviceInfo.h */; };
		18989F2C2D71E0B000E11C3B /* XXGDeviceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 18989F282D71E0B000E11C3B /* XXGDeviceInfo.m */; };
		18989F322D72C10C00E11C3B /* UIColor+XXGColor.h in Headers */ = {isa = PBXBuildFile; fileRef = 18989F2E2D72C10C00E11C3B /* UIColor+XXGColor.h */; };
		18989F332D72C10C00E11C3B /* UIColor+XXGColor.m in Sources */ = {isa = PBXBuildFile; fileRef = 18989F2F2D72C10C00E11C3B /* UIColor+XXGColor.m */; };
		18A2308B2D8EDD2C0015E020 /* XXGPlayProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 18A230892D8EDD2C0015E020 /* XXGPlayProtocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		18DA0BFA2D8A6B1E003C4467 /* XXGSelectPCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0BF82D8A6B1D003C4467 /* XXGSelectPCell.h */; };
		18DA0BFB2D8A6B1E003C4467 /* XXGSelectPCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0BF92D8A6B1D003C4467 /* XXGSelectPCell.m */; };
		18DA0CD62D8BB02E003C4467 /* MQTTCoreDataPersistence.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CB22D8BB02E003C4467 /* MQTTCoreDataPersistence.h */; };
		18DA0CD72D8BB02E003C4467 /* MQTTSessionSynchron.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CC52D8BB02E003C4467 /* MQTTSessionSynchron.h */; };
		18DA0CD82D8BB02E003C4467 /* MQTTSSLSecurityPolicy.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CC72D8BB02E003C4467 /* MQTTSSLSecurityPolicy.h */; };
		18DA0CD92D8BB02E003C4467 /* MQTTPersistence.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CBC2D8BB02E003C4467 /* MQTTPersistence.h */; };
		18DA0CDA2D8BB02E003C4467 /* ReconnectTimer.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CD32D8BB02E003C4467 /* ReconnectTimer.h */; };
		18DA0CDB2D8BB02E003C4467 /* MQTTSSLSecurityPolicyEncoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CCB2D8BB02E003C4467 /* MQTTSSLSecurityPolicyEncoder.h */; };
		18DA0CDC2D8BB02E003C4467 /* MQTTSSLSecurityPolicyTransport.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CCD2D8BB02E003C4467 /* MQTTSSLSecurityPolicyTransport.h */; };
		18DA0CDD2D8BB02E003C4467 /* MQTTProperties.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CBD2D8BB02E003C4467 /* MQTTProperties.h */; };
		18DA0CDE2D8BB02E003C4467 /* MQTTStrict.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CCF2D8BB02E003C4467 /* MQTTStrict.h */; };
		18DA0CDF2D8BB02E003C4467 /* MQTTLog.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CB82D8BB02E003C4467 /* MQTTLog.h */; };
		18DA0CE02D8BB02E003C4467 /* ForegroundReconnection.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CA72D8BB02E003C4467 /* ForegroundReconnection.h */; };
		18DA0CE12D8BB02E003C4467 /* MQTTMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CBA2D8BB02E003C4467 /* MQTTMessage.h */; };
		18DA0CE22D8BB02E003C4467 /* MQTTInMemoryPersistence.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CB62D8BB02E003C4467 /* MQTTInMemoryPersistence.h */; };
		18DA0CE32D8BB02E003C4467 /* MQTTDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CB42D8BB02E003C4467 /* MQTTDecoder.h */; };
		18DA0CE42D8BB02E003C4467 /* GCDTimer.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CA92D8BB02E003C4467 /* GCDTimer.h */; };
		18DA0CE52D8BB02E003C4467 /* MQTTCFSocketEncoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CAD2D8BB02E003C4467 /* MQTTCFSocketEncoder.h */; };
		18DA0CE62D8BB02E003C4467 /* MQTTCFSocketTransport.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CAF2D8BB02E003C4467 /* MQTTCFSocketTransport.h */; };
		18DA0CE72D8BB02E003C4467 /* MQTTTransportProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CD12D8BB02E003C4467 /* MQTTTransportProtocol.h */; };
		18DA0CE82D8BB02E003C4467 /* MQTTClient.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CB12D8BB02E003C4467 /* MQTTClient.h */; };
		18DA0CE92D8BB02E003C4467 /* MQTTSessionLegacy.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CC12D8BB02E003C4467 /* MQTTSessionLegacy.h */; };
		18DA0CEA2D8BB02E003C4467 /* MQTTSession.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CBF2D8BB02E003C4467 /* MQTTSession.h */; };
		18DA0CEB2D8BB02E003C4467 /* MQTTSSLSecurityPolicyDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CC92D8BB02E003C4467 /* MQTTSSLSecurityPolicyDecoder.h */; };
		18DA0CEC2D8BB02E003C4467 /* MQTTCFSocketDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CAB2D8BB02E003C4467 /* MQTTCFSocketDecoder.h */; };
		18DA0CED2D8BB02E003C4467 /* MQTTSessionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0CC32D8BB02E003C4467 /* MQTTSessionManager.h */; };
		18DA0CEE2D8BB02E003C4467 /* MQTTSessionSynchron.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CC62D8BB02E003C4467 /* MQTTSessionSynchron.m */; };
		18DA0CEF2D8BB02E003C4467 /* MQTTDecoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CB52D8BB02E003C4467 /* MQTTDecoder.m */; };
		18DA0CF02D8BB02E003C4467 /* MQTTSessionLegacy.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CC22D8BB02E003C4467 /* MQTTSessionLegacy.m */; };
		18DA0CF12D8BB02E003C4467 /* MQTTSSLSecurityPolicy.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CC82D8BB02E003C4467 /* MQTTSSLSecurityPolicy.m */; };
		18DA0CF22D8BB02E003C4467 /* MQTTMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CBB2D8BB02E003C4467 /* MQTTMessage.m */; };
		18DA0CF32D8BB02E003C4467 /* MQTTSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CC42D8BB02E003C4467 /* MQTTSessionManager.m */; };
		18DA0CF42D8BB02E003C4467 /* ReconnectTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CD42D8BB02E003C4467 /* ReconnectTimer.m */; };
		18DA0CF52D8BB02E003C4467 /* MQTTSSLSecurityPolicyTransport.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CCE2D8BB02E003C4467 /* MQTTSSLSecurityPolicyTransport.m */; };
		18DA0CF62D8BB02E003C4467 /* MQTTSession.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CC02D8BB02E003C4467 /* MQTTSession.m */; };
		18DA0CF72D8BB02E003C4467 /* MQTTCoreDataPersistence.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CB32D8BB02E003C4467 /* MQTTCoreDataPersistence.m */; };
		18DA0CF82D8BB02E003C4467 /* MQTTCFSocketTransport.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CB02D8BB02E003C4467 /* MQTTCFSocketTransport.m */; };
		18DA0CF92D8BB02E003C4467 /* MQTTLog.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CB92D8BB02E003C4467 /* MQTTLog.m */; };
		18DA0CFA2D8BB02E003C4467 /* MQTTSSLSecurityPolicyDecoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CCA2D8BB02E003C4467 /* MQTTSSLSecurityPolicyDecoder.m */; };
		18DA0CFB2D8BB02E003C4467 /* MQTTSSLSecurityPolicyEncoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CCC2D8BB02E003C4467 /* MQTTSSLSecurityPolicyEncoder.m */; };
		18DA0CFC2D8BB02E003C4467 /* MQTTTransportProtocol.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CD22D8BB02E003C4467 /* MQTTTransportProtocol.m */; };
		18DA0CFD2D8BB02E003C4467 /* MQTTInMemoryPersistence.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CB72D8BB02E003C4467 /* MQTTInMemoryPersistence.m */; };
		18DA0CFE2D8BB02E003C4467 /* MQTTStrict.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CD02D8BB02E003C4467 /* MQTTStrict.m */; };
		18DA0CFF2D8BB02E003C4467 /* MQTTCFSocketDecoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CAC2D8BB02E003C4467 /* MQTTCFSocketDecoder.m */; };
		18DA0D002D8BB02E003C4467 /* GCDTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CAA2D8BB02E003C4467 /* GCDTimer.m */; };
		18DA0D012D8BB02E003C4467 /* MQTTCFSocketEncoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CAE2D8BB02E003C4467 /* MQTTCFSocketEncoder.m */; };
		18DA0D022D8BB02E003C4467 /* MQTTProperties.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CBE2D8BB02E003C4467 /* MQTTProperties.m */; };
		18DA0D032D8BB02E003C4467 /* ForegroundReconnection.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0CA82D8BB02E003C4467 /* ForegroundReconnection.m */; };
		18DA0D342D8BB826003C4467 /* XXGMQTTManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0D322D8BB826003C4467 /* XXGMQTTManager.h */; };
		18DA0D352D8BB826003C4467 /* XXGMQTTManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0D332D8BB826003C4467 /* XXGMQTTManager.m */; };
		18DA0D462D8BBA5A003C4467 /* XXGMQTTConnectInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0D442D8BBA5A003C4467 /* XXGMQTTConnectInfo.h */; };
		18DA0D472D8BBA5A003C4467 /* XXGMQTTConnectInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0D452D8BBA5A003C4467 /* XXGMQTTConnectInfo.m */; };
		18DA0E742D8C116E003C4467 /* XXGMQTTTopicInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0E702D8C116E003C4467 /* XXGMQTTTopicInfo.h */; };
		18DA0E752D8C116E003C4467 /* XXGMQTTTopicInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0E712D8C116E003C4467 /* XXGMQTTTopicInfo.m */; };
		18DA0E7B2D8C16C7003C4467 /* XXGLiveBarrageCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0E772D8C16C7003C4467 /* XXGLiveBarrageCell.h */; };
		18DA0E7C2D8C16C7003C4467 /* XXGLiveBarrage.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0E762D8C16C7003C4467 /* XXGLiveBarrage.h */; };
		18DA0E7D2D8C16C7003C4467 /* XXGLiveBarrageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0E782D8C16C7003C4467 /* XXGLiveBarrageCell.m */; };
		18DA0E7E2D8C16C7003C4467 /* XXGLiveBarrage.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0E792D8C16C7003C4467 /* XXGLiveBarrage.m */; };
		18DA0E872D8C1A9E003C4467 /* XXGMarqueeView.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DA0E832D8C1A9E003C4467 /* XXGMarqueeView.h */; };
		18DA0E882D8C1A9E003C4467 /* XXGMarqueeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DA0E842D8C1A9E003C4467 /* XXGMarqueeView.m */; };
		18DD423E2D7C2B1F00E809EC /* ViewController+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42212D7C2B1F00E809EC /* ViewController+MASAdditions.m */; };
		18DD423F2D7C2B1F00E809EC /* MASViewConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42172D7C2B1F00E809EC /* MASViewConstraint.m */; };
		18DD42402D7C2B1F00E809EC /* NSLayoutConstraint+MASDebugAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD421C2D7C2B1F00E809EC /* NSLayoutConstraint+MASDebugAdditions.m */; };
		18DD42412D7C2B1F00E809EC /* MASConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD420C2D7C2B1F00E809EC /* MASConstraint.m */; };
		18DD42422D7C2B1F00E809EC /* MASLayoutConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42112D7C2B1F00E809EC /* MASLayoutConstraint.m */; };
		18DD42432D7C2B1F00E809EC /* NSArray+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42192D7C2B1F00E809EC /* NSArray+MASAdditions.m */; };
		18DD42442D7C2B1F00E809EC /* MASConstraintMaker.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD420F2D7C2B1F00E809EC /* MASConstraintMaker.m */; };
		18DD42452D7C2B1F00E809EC /* MASViewAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42152D7C2B1F00E809EC /* MASViewAttribute.m */; };
		18DD42462D7C2B1F00E809EC /* MASCompositeConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD420A2D7C2B1F00E809EC /* MASCompositeConstraint.m */; };
		18DD42472D7C2B1F00E809EC /* View+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD421E2D7C2B1F00E809EC /* View+MASAdditions.m */; };
		18DD42482D7C2B1F00E809EC /* ViewController+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42202D7C2B1F00E809EC /* ViewController+MASAdditions.h */; };
		18DD42492D7C2B1F00E809EC /* MASCompositeConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42092D7C2B1F00E809EC /* MASCompositeConstraint.h */; };
		18DD424A2D7C2B1F00E809EC /* View+MASShorthandAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD421F2D7C2B1F00E809EC /* View+MASShorthandAdditions.h */; };
		18DD424B2D7C2B1F00E809EC /* NSArray+MASShorthandAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD421A2D7C2B1F00E809EC /* NSArray+MASShorthandAdditions.h */; };
		18DD424C2D7C2B1F00E809EC /* NSArray+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42182D7C2B1F00E809EC /* NSArray+MASAdditions.h */; };
		18DD424D2D7C2B1F00E809EC /* View+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD421D2D7C2B1F00E809EC /* View+MASAdditions.h */; };
		18DD424E2D7C2B1F00E809EC /* MASConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD420B2D7C2B1F00E809EC /* MASConstraint.h */; };
		18DD424F2D7C2B1F00E809EC /* MASViewConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42162D7C2B1F00E809EC /* MASViewConstraint.h */; };
		18DD42502D7C2B1F00E809EC /* MASConstraintMaker.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD420E2D7C2B1F00E809EC /* MASConstraintMaker.h */; };
		18DD42512D7C2B1F00E809EC /* MASUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42132D7C2B1F00E809EC /* MASUtilities.h */; };
		18DD42522D7C2B1F00E809EC /* MASLayoutConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42102D7C2B1F00E809EC /* MASLayoutConstraint.h */; };
		18DD42532D7C2B1F00E809EC /* MASViewAttribute.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42142D7C2B1F00E809EC /* MASViewAttribute.h */; };
		18DD42542D7C2B1F00E809EC /* NSLayoutConstraint+MASDebugAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD421B2D7C2B1F00E809EC /* NSLayoutConstraint+MASDebugAdditions.h */; };
		18DD42552D7C2B1F00E809EC /* MASConstraint+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD420D2D7C2B1F00E809EC /* MASConstraint+Private.h */; };
		18DD42562D7C2B1F00E809EC /* Masonry.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42122D7C2B1F00E809EC /* Masonry.h */; };
		18DD44002D7C3EE700E809EC /* SDMemoryCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42922D7C3EE700E809EC /* SDMemoryCache.m */; };
		18DD44012D7C3EE700E809EC /* SDAnimatedImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42642D7C3EE700E809EC /* SDAnimatedImageView.m */; };
		18DD44022D7C3EE700E809EC /* SDInternalMacros.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD43232D7C3EE700E809EC /* SDInternalMacros.m */; };
		18DD44032D7C3EE700E809EC /* SDAnimatedImage.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD425E2D7C3EE700E809EC /* SDAnimatedImage.m */; };
		18DD44042D7C3EE700E809EC /* NSData+ImageContentType.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD425A2D7C3EE700E809EC /* NSData+ImageContentType.m */; };
		18DD44052D7C3EE700E809EC /* NSButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42582D7C3EE700E809EC /* NSButton+WebCache.m */; };
		18DD44062D7C3EE700E809EC /* SDImageTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42902D7C3EE700E809EC /* SDImageTransformer.m */; };
		18DD44072D7C3EE700E809EC /* SDImageAWebPCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42702D7C3EE700E809EC /* SDImageAWebPCoder.m */; };
		18DD44082D7C3EE700E809EC /* SDAsyncBlockOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD43142D7C3EE700E809EC /* SDAsyncBlockOperation.m */; };
		18DD44092D7C3EE700E809EC /* UIButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42B62D7C3EE700E809EC /* UIButton+WebCache.m */; };
		18DD440A2D7C3EE700E809EC /* SDAnimatedImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42662D7C3EE700E809EC /* SDAnimatedImageView+WebCache.m */; };
		18DD440B2D7C3EE700E809EC /* UIImage+ExtendedCacheData.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42B82D7C3EE700E809EC /* UIImage+ExtendedCacheData.m */; };
		18DD440C2D7C3EE700E809EC /* SDImageLoader.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD428C2D7C3EE700E809EC /* SDImageLoader.m */; };
		18DD440D2D7C3EE700E809EC /* SDWebImageDownloaderDecryptor.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42A02D7C3EE700E809EC /* SDWebImageDownloaderDecryptor.m */; };
		18DD440E2D7C3EE700E809EC /* SDWebImageIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42AA2D7C3EE700E809EC /* SDWebImageIndicator.m */; };
		18DD440F2D7C3EE700E809EC /* SDImageFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42802D7C3EE700E809EC /* SDImageFrame.m */; };
		18DD44102D7C3EE700E809EC /* SDFileAttributeHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD431A2D7C3EE700E809EC /* SDFileAttributeHelper.m */; };
		18DD44112D7C3EE700E809EC /* SDWebImageError.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42A82D7C3EE700E809EC /* SDWebImageError.m */; };
		18DD44122D7C3EE700E809EC /* SDWebImageDownloaderConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD429E2D7C3EE700E809EC /* SDWebImageDownloaderConfig.m */; };
		18DD44132D7C3EE700E809EC /* SDImageLoadersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD428E2D7C3EE700E809EC /* SDImageLoadersManager.m */; };
		18DD44142D7C3EE700E809EC /* UIImage+MemoryCacheCost.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42BE2D7C3EE700E809EC /* UIImage+MemoryCacheCost.m */; };
		18DD44152D7C3EE700E809EC /* UIImageView+HighlightedWebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42C62D7C3EE700E809EC /* UIImageView+HighlightedWebCache.m */; };
		18DD44162D7C3EE700E809EC /* SDImageGraphics.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42842D7C3EE700E809EC /* SDImageGraphics.m */; };
		18DD44172D7C3EE700E809EC /* SDImageCachesManagerOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD431E2D7C3EE700E809EC /* SDImageCachesManagerOperation.m */; };
		18DD44182D7C3EE700E809EC /* SDWebImageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42AC2D7C3EE700E809EC /* SDWebImageManager.m */; };
		18DD44192D7C3EE700E809EC /* SDImageCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD427A2D7C3EE700E809EC /* SDImageCoder.m */; };
		18DD441A2D7C3EE700E809EC /* UIImage+GIF.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42BC2D7C3EE700E809EC /* UIImage+GIF.m */; };
		18DD441B2D7C3EE700E809EC /* SDImageCoderHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD427C2D7C3EE700E809EC /* SDImageCoderHelper.m */; };
		18DD441C2D7C3EE700E809EC /* SDWebImageDownloaderRequestModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42A42D7C3EE700E809EC /* SDWebImageDownloaderRequestModifier.m */; };
		18DD441D2D7C3EE700E809EC /* SDWebImageCacheKeyFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42942D7C3EE700E809EC /* SDWebImageCacheKeyFilter.m */; };
		18DD441E2D7C3EE700E809EC /* SDImageCacheConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42742D7C3EE700E809EC /* SDImageCacheConfig.m */; };
		18DD441F2D7C3EE700E809EC /* SDImageCodersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD427E2D7C3EE700E809EC /* SDImageCodersManager.m */; };
		18DD44202D7C3EE700E809EC /* SDAnimatedImagePlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42602D7C3EE700E809EC /* SDAnimatedImagePlayer.m */; };
		18DD44212D7C3EE700E809EC /* SDImageIOCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD428A2D7C3EE700E809EC /* SDImageIOCoder.m */; };
		18DD44222D7C3EE700E809EC /* NSImage+Compatibility.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD425C2D7C3EE700E809EC /* NSImage+Compatibility.m */; };
		18DD44232D7C3EE700E809EC /* SDWebImageDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD429A2D7C3EE700E809EC /* SDWebImageDefine.m */; };
		18DD44242D7C3EE700E809EC /* UIImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42C82D7C3EE700E809EC /* UIImageView+WebCache.m */; };
		18DD44252D7C3EE700E809EC /* SDWebImageOptionsProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42B02D7C3EE700E809EC /* SDWebImageOptionsProcessor.m */; };
		18DD44262D7C3EE700E809EC /* SDCallbackQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42682D7C3EE700E809EC /* SDCallbackQueue.m */; };
		18DD44272D7C3EE700E809EC /* UIImage+ForceDecode.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42BA2D7C3EE700E809EC /* UIImage+ForceDecode.m */; };
		18DD44282D7C3EE700E809EC /* SDDeviceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD43162D7C3EE700E809EC /* SDDeviceHelper.m */; };
		18DD44292D7C3EE700E809EC /* SDImageGIFCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42822D7C3EE700E809EC /* SDImageGIFCoder.m */; };
		18DD442A2D7C3EE700E809EC /* UIView+WebCacheState.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42CE2D7C3EE700E809EC /* UIView+WebCacheState.m */; };
		18DD442B2D7C3EE700E809EC /* UIImage+Metadata.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42C02D7C3EE700E809EC /* UIImage+Metadata.m */; };
		18DD442C2D7C3EE700E809EC /* UIView+WebCacheOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42CC2D7C3EE700E809EC /* UIView+WebCacheOperation.m */; };
		18DD442D2D7C3EE700E809EC /* SDDiskCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD426A2D7C3EE700E809EC /* SDDiskCache.m */; };
		18DD442E2D7C3EE700E809EC /* SDImageCacheDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42762D7C3EE700E809EC /* SDImageCacheDefine.m */; };
		18DD442F2D7C3EE700E809EC /* SDWeakProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD43262D7C3EE700E809EC /* SDWeakProxy.m */; };
		18DD44302D7C3EE700E809EC /* SDWebImageDownloaderOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42A22D7C3EE700E809EC /* SDWebImageDownloaderOperation.m */; };
		18DD44312D7C3EE700E809EC /* UIColor+SDHexString.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD43292D7C3EE700E809EC /* UIColor+SDHexString.m */; };
		18DD44322D7C3EE700E809EC /* SDWebImageDownloaderResponseModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42A62D7C3EE700E809EC /* SDWebImageDownloaderResponseModifier.m */; };
		18DD44332D7C3EE700E809EC /* SDWebImageOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42AE2D7C3EE700E809EC /* SDWebImageOperation.m */; };
		18DD44342D7C3EE700E809EC /* NSBezierPath+SDRoundedCorners.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD43102D7C3EE700E809EC /* NSBezierPath+SDRoundedCorners.m */; };
		18DD44352D7C3EE700E809EC /* SDImageAPNGCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD426E2D7C3EE700E809EC /* SDImageAPNGCoder.m */; };
		18DD44362D7C3EE700E809EC /* UIImage+Transform.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42C42D7C3EE700E809EC /* UIImage+Transform.m */; };
		18DD44372D7C3EE700E809EC /* SDImageAssetManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD431C2D7C3EE700E809EC /* SDImageAssetManager.m */; };
		18DD44382D7C3EE700E809EC /* SDWebImageTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42B42D7C3EE700E809EC /* SDWebImageTransition.m */; };
		18DD44392D7C3EE700E809EC /* SDImageCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42722D7C3EE700E809EC /* SDImageCache.m */; };
		18DD443A2D7C3EE700E809EC /* SDImageHEICCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42862D7C3EE700E809EC /* SDImageHEICCoder.m */; };
		18DD443B2D7C3EE700E809EC /* SDImageCachesManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42782D7C3EE700E809EC /* SDImageCachesManager.m */; };
		18DD443C2D7C3EE700E809EC /* SDGraphicsImageRenderer.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD426C2D7C3EE700E809EC /* SDGraphicsImageRenderer.m */; };
		18DD443D2D7C3EE700E809EC /* UIView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42CA2D7C3EE700E809EC /* UIView+WebCache.m */; };
		18DD443E2D7C3EE700E809EC /* SDWebImagePrefetcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42B22D7C3EE700E809EC /* SDWebImagePrefetcher.m */; };
		18DD443F2D7C3EE700E809EC /* SDWebImageCacheSerializer.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42962D7C3EE700E809EC /* SDWebImageCacheSerializer.m */; };
		18DD44402D7C3EE700E809EC /* SDDisplayLink.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD43182D7C3EE700E809EC /* SDDisplayLink.m */; };
		18DD44412D7C3EE700E809EC /* SDWebImageDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD429C2D7C3EE700E809EC /* SDWebImageDownloader.m */; };
		18DD44422D7C3EE700E809EC /* SDImageFramePool.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD43202D7C3EE700E809EC /* SDImageFramePool.m */; };
		18DD44432D7C3EE700E809EC /* SDImageIOAnimatedCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42882D7C3EE700E809EC /* SDImageIOAnimatedCoder.m */; };
		18DD44442D7C3EE700E809EC /* UIImage+MultiFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42C22D7C3EE700E809EC /* UIImage+MultiFormat.m */; };
		18DD44452D7C3EE700E809EC /* SDAssociatedObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD43122D7C3EE700E809EC /* SDAssociatedObject.m */; };
		18DD44462D7C3EE700E809EC /* SDAnimatedImageRep.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42622D7C3EE700E809EC /* SDAnimatedImageRep.m */; };
		18DD44472D7C3EE700E809EC /* SDWebImageCompat.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD42982D7C3EE700E809EC /* SDWebImageCompat.m */; };
		18DD44482D7C3EE700E809EC /* UIColor+SDHexString.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD43282D7C3EE700E809EC /* UIColor+SDHexString.h */; };
		18DD444E2D7C3EE700E809EC /* SDImageIOCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42892D7C3EE700E809EC /* SDImageIOCoder.h */; };
		18DD44522D7C3EE700E809EC /* SDFileAttributeHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD43192D7C3EE700E809EC /* SDFileAttributeHelper.h */; };
		18DD44562D7C3EE700E809EC /* SDWebImagePrefetcher.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42B12D7C3EE700E809EC /* SDWebImagePrefetcher.h */; };
		18DD44582D7C3EE700E809EC /* SDAnimatedImageView.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42632D7C3EE700E809EC /* SDAnimatedImageView.h */; };
		18DD44592D7C3EE700E809EC /* NSButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42572D7C3EE700E809EC /* NSButton+WebCache.h */; };
		18DD445A2D7C3EE700E809EC /* SDImageHEICCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42852D7C3EE700E809EC /* SDImageHEICCoder.h */; };
		18DD445B2D7C3EE700E809EC /* SDImageIOAnimatedCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42872D7C3EE700E809EC /* SDImageIOAnimatedCoder.h */; };
		18DD445D2D7C3EE700E809EC /* SDImageCoderHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD427B2D7C3EE700E809EC /* SDImageCoderHelper.h */; };
		18DD445F2D7C3EE700E809EC /* SDAnimatedImagePlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD425F2D7C3EE700E809EC /* SDAnimatedImagePlayer.h */; };
		18DD44602D7C3EE700E809EC /* UIView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42C92D7C3EE700E809EC /* UIView+WebCache.h */; };
		18DD44622D7C3EE700E809EC /* SDWebImageOptionsProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42AF2D7C3EE700E809EC /* SDWebImageOptionsProcessor.h */; };
		18DD44632D7C3EE700E809EC /* SDWebImageTransitionInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD43272D7C3EE700E809EC /* SDWebImageTransitionInternal.h */; };
		18DD44652D7C3EE700E809EC /* SDWeakProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD43252D7C3EE700E809EC /* SDWeakProxy.h */; };
		18DD44662D7C3EE700E809EC /* SDWebImageCompat.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42972D7C3EE700E809EC /* SDWebImageCompat.h */; };
		18DD44672D7C3EE700E809EC /* SDCallbackQueue.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42672D7C3EE700E809EC /* SDCallbackQueue.h */; };
		18DD446E2D7C3EE700E809EC /* SDmetamacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD43242D7C3EE700E809EC /* SDmetamacros.h */; };
		18DD446F2D7C3EE700E809EC /* UIImage+Transform.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42C32D7C3EE700E809EC /* UIImage+Transform.h */; };
		18DD44712D7C3EE700E809EC /* UIImage+MultiFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42C12D7C3EE700E809EC /* UIImage+MultiFormat.h */; };
		18DD44722D7C3EE700E809EC /* SDImageAssetManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD431B2D7C3EE700E809EC /* SDImageAssetManager.h */; };
		18DD44732D7C3EE700E809EC /* SDImageCachesManagerOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD431D2D7C3EE700E809EC /* SDImageCachesManagerOperation.h */; };
		18DD44762D7C3EE700E809EC /* UIImageView+HighlightedWebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42C52D7C3EE700E809EC /* UIImageView+HighlightedWebCache.h */; };
		18DD447A2D7C3EE700E809EC /* SDWebImageDownloaderOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42A12D7C3EE700E809EC /* SDWebImageDownloaderOperation.h */; };
		18DD447D2D7C3EE700E809EC /* SDWebImageDownloaderDecryptor.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD429F2D7C3EE700E809EC /* SDWebImageDownloaderDecryptor.h */; };
		18DD447E2D7C3EE700E809EC /* SDImageAWebPCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD426F2D7C3EE700E809EC /* SDImageAWebPCoder.h */; };
		18DD44832D7C3EE700E809EC /* SDInternalMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD43222D7C3EE700E809EC /* SDInternalMacros.h */; };
		18DD44842D7C3EE700E809EC /* SDAnimatedImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42652D7C3EE700E809EC /* SDAnimatedImageView+WebCache.h */; };
		18DD44852D7C3EE700E809EC /* SDImageCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42792D7C3EE700E809EC /* SDImageCoder.h */; };
		18DD44862D7C3EE700E809EC /* SDWebImageCacheSerializer.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42952D7C3EE700E809EC /* SDWebImageCacheSerializer.h */; };
		18DD44872D7C3EE700E809EC /* SDImageLoadersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD428D2D7C3EE700E809EC /* SDImageLoadersManager.h */; };
		18DD44882D7C3EE700E809EC /* SDDisplayLink.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD43172D7C3EE700E809EC /* SDDisplayLink.h */; };
		18DD44892D7C3EE700E809EC /* SDWebImageIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42A92D7C3EE700E809EC /* SDWebImageIndicator.h */; };
		18DD448B2D7C3EE700E809EC /* SDWebImageDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD429B2D7C3EE700E809EC /* SDWebImageDownloader.h */; };
		18DD448C2D7C3EE700E809EC /* SDAnimatedImageRep.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42612D7C3EE700E809EC /* SDAnimatedImageRep.h */; };
		18DD448D2D7C3EE700E809EC /* SDImageCacheConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42732D7C3EE700E809EC /* SDImageCacheConfig.h */; };
		18DD448F2D7C3EE700E809EC /* SDImageFramePool.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD431F2D7C3EE700E809EC /* SDImageFramePool.h */; };
		18DD44902D7C3EE700E809EC /* SDImageGraphics.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42832D7C3EE700E809EC /* SDImageGraphics.h */; };
		18DD44952D7C3EE700E809EC /* UIImage+GIF.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42BB2D7C3EE700E809EC /* UIImage+GIF.h */; };
		18DD44962D7C3EE700E809EC /* SDAsyncBlockOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD43132D7C3EE700E809EC /* SDAsyncBlockOperation.h */; };
		18DD44972D7C3EE700E809EC /* SDImageCacheDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42752D7C3EE700E809EC /* SDImageCacheDefine.h */; };
		18DD44992D7C3EE700E809EC /* SDWebImageDownloaderResponseModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42A52D7C3EE700E809EC /* SDWebImageDownloaderResponseModifier.h */; };
		18DD449C2D7C3EE700E809EC /* UIView+WebCacheOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42CB2D7C3EE700E809EC /* UIView+WebCacheOperation.h */; };
		18DD449F2D7C3EE700E809EC /* SDWebImageOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42AD2D7C3EE700E809EC /* SDWebImageOperation.h */; };
		18DD44A02D7C3EE700E809EC /* SDGraphicsImageRenderer.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD426B2D7C3EE700E809EC /* SDGraphicsImageRenderer.h */; };
		18DD44A12D7C3EE700E809EC /* SDImageLoader.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD428B2D7C3EE700E809EC /* SDImageLoader.h */; };
		18DD44A22D7C3EE700E809EC /* UIImage+Metadata.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42BF2D7C3EE700E809EC /* UIImage+Metadata.h */; };
		18DD44A32D7C3EE700E809EC /* SDImageGIFCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42812D7C3EE700E809EC /* SDImageGIFCoder.h */; };
		18DD44A42D7C3EE700E809EC /* UIImage+ForceDecode.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42B92D7C3EE700E809EC /* UIImage+ForceDecode.h */; };
		18DD44A52D7C3EE700E809EC /* SDImageFrame.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD427F2D7C3EE700E809EC /* SDImageFrame.h */; };
		18DD44A62D7C3EE700E809EC /* NSImage+Compatibility.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD425B2D7C3EE700E809EC /* NSImage+Compatibility.h */; };
		18DD44A72D7C3EE700E809EC /* SDAnimatedImage.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD425D2D7C3EE700E809EC /* SDAnimatedImage.h */; };
		18DD44A82D7C3EE700E809EC /* SDWebImageManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42AB2D7C3EE700E809EC /* SDWebImageManager.h */; };
		18DD44A92D7C3EE700E809EC /* NSData+ImageContentType.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42592D7C3EE700E809EC /* NSData+ImageContentType.h */; };
		18DD44AB2D7C3EE700E809EC /* SDWebImageDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42992D7C3EE700E809EC /* SDWebImageDefine.h */; };
		18DD44AD2D7C3EE700E809EC /* UIView+WebCacheState.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42CD2D7C3EE700E809EC /* UIView+WebCacheState.h */; };
		18DD44AE2D7C3EE700E809EC /* SDImageTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD428F2D7C3EE700E809EC /* SDImageTransformer.h */; };
		18DD44B02D7C3EE700E809EC /* SDDiskCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42692D7C3EE700E809EC /* SDDiskCache.h */; };
		18DD44B22D7C3EE700E809EC /* SDAssociatedObject.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD43112D7C3EE700E809EC /* SDAssociatedObject.h */; };
		18DD44B32D7C3EE700E809EC /* SDImageIOAnimatedCoderInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD43212D7C3EE700E809EC /* SDImageIOAnimatedCoderInternal.h */; };
		18DD44B62D7C3EE700E809EC /* SDDeviceHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD43152D7C3EE700E809EC /* SDDeviceHelper.h */; };
		18DD44B72D7C3EE700E809EC /* SDImageCodersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD427D2D7C3EE700E809EC /* SDImageCodersManager.h */; };
		18DD44BA2D7C3EE700E809EC /* SDWebImageDownloaderConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD429D2D7C3EE700E809EC /* SDWebImageDownloaderConfig.h */; };
		18DD44BB2D7C3EE700E809EC /* UIImage+ExtendedCacheData.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42B72D7C3EE700E809EC /* UIImage+ExtendedCacheData.h */; };
		18DD44BC2D7C3EE700E809EC /* UIImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42C72D7C3EE700E809EC /* UIImageView+WebCache.h */; };
		18DD44BE2D7C3EE700E809EC /* SDWebImageError.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42A72D7C3EE700E809EC /* SDWebImageError.h */; };
		18DD44C02D7C3EE700E809EC /* SDImageAPNGCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD426D2D7C3EE700E809EC /* SDImageAPNGCoder.h */; };
		18DD44C12D7C3EE700E809EC /* SDWebImageCacheKeyFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42932D7C3EE700E809EC /* SDWebImageCacheKeyFilter.h */; };
		18DD44C32D7C3EE700E809EC /* UIImage+MemoryCacheCost.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42BD2D7C3EE700E809EC /* UIImage+MemoryCacheCost.h */; };
		18DD44C62D7C3EE700E809EC /* SDWebImageDownloaderRequestModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42A32D7C3EE700E809EC /* SDWebImageDownloaderRequestModifier.h */; };
		18DD44C72D7C3EE700E809EC /* SDImageCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42712D7C3EE700E809EC /* SDImageCache.h */; };
		18DD44CA2D7C3EE700E809EC /* SDMemoryCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42912D7C3EE700E809EC /* SDMemoryCache.h */; };
		18DD44CC2D7C3EE700E809EC /* NSBezierPath+SDRoundedCorners.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD430F2D7C3EE700E809EC /* NSBezierPath+SDRoundedCorners.h */; };
		18DD44CD2D7C3EE700E809EC /* UIButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42B52D7C3EE700E809EC /* UIButton+WebCache.h */; };
		18DD44CE2D7C3EE700E809EC /* SDWebImageTransition.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42B32D7C3EE700E809EC /* SDWebImageTransition.h */; };
		18DD44CF2D7C3EE700E809EC /* SDImageCachesManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD42772D7C3EE700E809EC /* SDImageCachesManager.h */; };
		18DD44D22D7C419A00E809EC /* XXGUIDriver.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD44D12D7C419A00E809EC /* XXGUIDriver.m */; };
		18DD44D32D7C419A00E809EC /* XXGUIDriver.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD44D02D7C419A00E809EC /* XXGUIDriver.h */; };
		18DD45142D7EC2E900E809EC /* UIImage+XXGImage.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD45122D7EC2E900E809EC /* UIImage+XXGImage.h */; };
		18DD45152D7EC2E900E809EC /* UIImage+XXGImage.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD45132D7EC2E900E809EC /* UIImage+XXGImage.m */; };
		18DD451C2D7EE51800E809EC /* XXGAccountViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD45182D7EE51800E809EC /* XXGAccountViewController.h */; };
		18DD451D2D7EE51800E809EC /* XXGAccountViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD45192D7EE51800E809EC /* XXGAccountViewController.m */; };
		18DD45202D7EE55700E809EC /* XXGMobileViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD451E2D7EE55700E809EC /* XXGMobileViewController.h */; };
		18DD45212D7EE55700E809EC /* XXGMobileViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD451F2D7EE55700E809EC /* XXGMobileViewController.m */; };
		18DD45262D7EE57500E809EC /* XXGRegistViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD45242D7EE57500E809EC /* XXGRegistViewController.h */; };
		18DD45272D7EE57500E809EC /* XXGRegistViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD45252D7EE57500E809EC /* XXGRegistViewController.m */; };
		18DD452C2D7EE58500E809EC /* XXGSelectAccountViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD452A2D7EE58500E809EC /* XXGSelectAccountViewController.h */; };
		18DD452D2D7EE58500E809EC /* XXGSelectAccountViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD452B2D7EE58500E809EC /* XXGSelectAccountViewController.m */; };
		18DD45322D7EE59D00E809EC /* XXGForgetViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD45302D7EE59D00E809EC /* XXGForgetViewController.h */; };
		18DD45332D7EE59D00E809EC /* XXGForgetViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD45312D7EE59D00E809EC /* XXGForgetViewController.m */; };
		18DD45382D7EE5C200E809EC /* XXGChangeViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD45362D7EE5C200E809EC /* XXGChangeViewController.h */; };
		18DD45392D7EE5C200E809EC /* XXGChangeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD45372D7EE5C200E809EC /* XXGChangeViewController.m */; };
		18DD45402D7EE5D000E809EC /* XXGBindMobileViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD453C2D7EE5D000E809EC /* XXGBindMobileViewController.h */; };
		18DD45412D7EE5D000E809EC /* XXGBindMobileViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD453D2D7EE5D000E809EC /* XXGBindMobileViewController.m */; };
		18DD45442D7EE5DE00E809EC /* XXGSelectPPViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD45422D7EE5DE00E809EC /* XXGSelectPPViewController.h */; };
		18DD45452D7EE5DE00E809EC /* XXGSelectPPViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD45432D7EE5DE00E809EC /* XXGSelectPPViewController.m */; };
		18DD454A2D7EE5ED00E809EC /* XXGRealNameViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD45482D7EE5ED00E809EC /* XXGRealNameViewController.h */; };
		18DD45522D7EE5F800E809EC /* XXGServiceViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD454E2D7EE5F800E809EC /* XXGServiceViewController.h */; };
		18DD45532D7EE5F800E809EC /* XXGServiceViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD454F2D7EE5F800E809EC /* XXGServiceViewController.m */; };
		18DD45582D7EE61500E809EC /* XXGContentTextViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD45542D7EE61500E809EC /* XXGContentTextViewController.h */; };
		18DD45592D7EE61500E809EC /* XXGContentTextViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD45552D7EE61500E809EC /* XXGContentTextViewController.m */; };
		18DD455C2D7F0D2B00E809EC /* XXGProtocolLabel.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD455A2D7F0D2B00E809EC /* XXGProtocolLabel.h */; };
		18DD455D2D7F0D2B00E809EC /* XXGProtocolLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD455B2D7F0D2B00E809EC /* XXGProtocolLabel.m */; };
		18DD45BC2D80518800E809EC /* XXGUIkitProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD45BB2D80518800E809EC /* XXGUIkitProtocol.h */; };
		18DD45C32D8053C000E809EC /* XXGLoadingView.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD45C12D8053C000E809EC /* XXGLoadingView.h */; };
		18DD45C42D8053C000E809EC /* XXGLoadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD45C22D8053C000E809EC /* XXGLoadingView.m */; };
		18DD46762D816D1600E809EC /* XXGSelectAccountCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD46732D816D1600E809EC /* XXGSelectAccountCell.m */; };
		18DD46772D816D1600E809EC /* XXGSelectAccountCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD46722D816D1600E809EC /* XXGSelectAccountCell.h */; };
		18DD467D2D81B43E00E809EC /* XXGWKBaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD467A2D81B43E00E809EC /* XXGWKBaseViewController.m */; };
		18DD467E2D81B43E00E809EC /* XXGWKBaseViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD46792D81B43E00E809EC /* XXGWKBaseViewController.h */; };
		18DD46832D81B4BE00E809EC /* XXGPopupViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD467F2D81B4BE00E809EC /* XXGPopupViewController.h */; };
		18DD46842D81B4BE00E809EC /* XXGPopupViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD46802D81B4BE00E809EC /* XXGPopupViewController.m */; };
		18DD46892D82759300E809EC /* XXGFloatView.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD46852D82759300E809EC /* XXGFloatView.h */; };
		18DD468A2D82759300E809EC /* XXGFloatView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD46862D82759300E809EC /* XXGFloatView.m */; };
		18DD47BC2D850F2A00E809EC /* XXGLocalizedCore.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD47BA2D850F2A00E809EC /* XXGLocalizedCore.h */; };
		18DD47BD2D850F2A00E809EC /* XXGLocalizedCore.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD47BB2D850F2A00E809EC /* XXGLocalizedCore.m */; };
		18DD47CE2D85237800E809EC /* XXGLocaleString.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD47CB2D85237800E809EC /* XXGLocaleString.m */; };
		18DD47CF2D85237800E809EC /* XXGLocaleString.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD47CA2D85237800E809EC /* XXGLocaleString.h */; };
		18DD47E72D8549F600E809EC /* XXGDatasCore.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD47E32D8549F600E809EC /* XXGDatasCore.h */; };
		18DD47E82D8549F600E809EC /* XXGDatasCore.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD47E42D8549F600E809EC /* XXGDatasCore.m */; };
		18DD47ED2D85600300E809EC /* XXGDatasModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD47EA2D85600300E809EC /* XXGDatasModel.m */; };
		18DD47EE2D85600300E809EC /* XXGDatasModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD47E92D85600300E809EC /* XXGDatasModel.h */; };
		18DD47F12D85603600E809EC /* XXGLocalizedModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD47EF2D85603600E809EC /* XXGLocalizedModel.h */; };
		18DD47F22D85603600E809EC /* XXGLocalizedModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD47F02D85603600E809EC /* XXGLocalizedModel.m */; };
		18DD47F72D85736300E809EC /* XXGLocalizedUI.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD47F62D85736300E809EC /* XXGLocalizedUI.m */; };
		18DD47F82D85736300E809EC /* XXGLocalizedUI.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD47F52D85736300E809EC /* XXGLocalizedUI.h */; };
		18DD47FF2D8588B800E809EC /* XXGDatasUI.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD47FB2D8588B800E809EC /* XXGDatasUI.h */; };
		18DD48002D8588B800E809EC /* XXGDatasUI.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD47FC2D8588B800E809EC /* XXGDatasUI.m */; };
		18DD48092D86827900E809EC /* XXGTools.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD48072D86827900E809EC /* XXGTools.h */; };
		18DD480A2D86827900E809EC /* XXGTools.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD48082D86827900E809EC /* XXGTools.m */; };
		18DD480F2D8686E700E809EC /* NSString+URLEncoding.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD480D2D8686E700E809EC /* NSString+URLEncoding.h */; };
		18DD48102D8686E700E809EC /* NSString+URLEncoding.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD480E2D8686E700E809EC /* NSString+URLEncoding.m */; };
		18DD48152D86AF3700E809EC /* XXGSendCodeButton.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD48132D86AF3700E809EC /* XXGSendCodeButton.h */; };
		18DD48162D86AF3700E809EC /* XXGSendCodeButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD48142D86AF3700E809EC /* XXGSendCodeButton.m */; };
		18DD481D2D86C81200E809EC /* XXGCountryCodeSelectorViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD48192D86C81200E809EC /* XXGCountryCodeSelectorViewController.h */; };
		18DD481E2D86C81200E809EC /* XXGCountryCodeSelectorViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD481A2D86C81200E809EC /* XXGCountryCodeSelectorViewController.m */; };
		18DD48242D86C8C600E809EC /* XXGCountry.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD48212D86C8C600E809EC /* XXGCountry.m */; };
		18DD48252D86C8C600E809EC /* XXGCountry.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD48202D86C8C600E809EC /* XXGCountry.h */; };
		18DD482A2D86D0E300E809EC /* XXGCountryCodeButton.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD48262D86D0E300E809EC /* XXGCountryCodeButton.h */; };
		18DD482B2D86D0E300E809EC /* XXGCountryCodeButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD48272D86D0E300E809EC /* XXGCountryCodeButton.m */; };
		18DD48332D86F35D00E809EC /* XXGToast.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD482F2D86F35D00E809EC /* XXGToast.h */; };
		18DD48342D86F35D00E809EC /* XXGToast.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD48302D86F35D00E809EC /* XXGToast.m */; };
		18DD48372D87EA1900E809EC /* XXGUCenterViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD48362D87EA1900E809EC /* XXGUCenterViewController.m */; };
		18DD48382D87EA1900E809EC /* XXGUCenterViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD48352D87EA1900E809EC /* XXGUCenterViewController.h */; };
		18DD48512D8831D100E809EC /* XXGIAPTransactionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD48442D8831D100E809EC /* XXGIAPTransactionModel.m */; };
		18DD48532D8831D100E809EC /* NSError+XXGIAPError.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD483C2D8831D100E809EC /* NSError+XXGIAPError.m */; };
		18DD48542D8831D100E809EC /* XXGIAPConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD484D2D8831D100E809EC /* XXGIAPConfig.m */; };
		18DD48562D8831D100E809EC /* XXGIAPVerifyManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD48412D8831D100E809EC /* XXGIAPVerifyManager.m */; };
		18DD48572D8831D100E809EC /* XXGIAPHelpManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD483F2D8831D100E809EC /* XXGIAPHelpManager.m */; };
		18DD48582D8831D100E809EC /* XXGIAPConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD484C2D8831D100E809EC /* XXGIAPConfig.h */; };
		18DD48592D8831D100E809EC /* XXGIAPHelpManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD483E2D8831D100E809EC /* XXGIAPHelpManager.h */; };
		18DD485A2D8831D100E809EC /* XXGIAPHelp.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD484E2D8831D100E809EC /* XXGIAPHelp.h */; };
		18DD485B2D8831D100E809EC /* XXGIAPVerifyManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD48402D8831D100E809EC /* XXGIAPVerifyManager.h */; };
		18DD485C2D8831D100E809EC /* XXGIAPTransactionModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD48432D8831D100E809EC /* XXGIAPTransactionModel.h */; };
		18DD485E2D8831D100E809EC /* XXGIAPPayProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD484F2D8831D100E809EC /* XXGIAPPayProtocol.h */; };
		18DD48602D8831D100E809EC /* NSError+XXGIAPError.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD483B2D8831D100E809EC /* NSError+XXGIAPError.h */; };
		18DD48792D892A6600E809EC /* XXGIAPManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD48782D892A6600E809EC /* XXGIAPManager.m */; };
		18DD487A2D892A6600E809EC /* XXGIAPManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD48772D892A6600E809EC /* XXGIAPManager.h */; };
		18DD48812D892ECD00E809EC /* XXGProductBody.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD487E2D892ECD00E809EC /* XXGProductBody.m */; };
		18DD48822D892ECD00E809EC /* XXGProductBody.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD487D2D892ECD00E809EC /* XXGProductBody.h */; };
		18DD48872D894CA600E809EC /* XXGSelectProductItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD48842D894CA600E809EC /* XXGSelectProductItem.m */; };
		18DD48882D894CA600E809EC /* XXGSelectProductItem.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD48832D894CA600E809EC /* XXGSelectProductItem.h */; };
		18DD488B2D895D3D00E809EC /* XXGValidateReceiptBody.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD48892D895D3D00E809EC /* XXGValidateReceiptBody.h */; };
		18DD488C2D895D3D00E809EC /* XXGValidateReceiptBody.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD488A2D895D3D00E809EC /* XXGValidateReceiptBody.m */; };
		18DD48912D897DD400E809EC /* XXGRoleBody.m in Sources */ = {isa = PBXBuildFile; fileRef = 18DD48902D897DD400E809EC /* XXGRoleBody.m */; };
		18DD48922D897DD400E809EC /* XXGRoleBody.h in Headers */ = {isa = PBXBuildFile; fileRef = 18DD488F2D897DD400E809EC /* XXGRoleBody.h */; };
		18E064332DC4DC4A00F15793 /* XXGPlayKitCore+Delegates.m in Sources */ = {isa = PBXBuildFile; fileRef = 18E064302DC4DC4A00F15793 /* XXGPlayKitCore+Delegates.m */; };
		18E064342DC4DC4A00F15793 /* XXGPlayKitCore+Delegates.h in Headers */ = {isa = PBXBuildFile; fileRef = 18E0642F2DC4DC4A00F15793 /* XXGPlayKitCore+Delegates.h */; };
		18E064392DC4E29900F15793 /* XXGPlayKitCore+Canal.h in Headers */ = {isa = PBXBuildFile; fileRef = 18E064352DC4E29900F15793 /* XXGPlayKitCore+Canal.h */; };
		18E0643A2DC4E29900F15793 /* XXGPlayKitCore+Canal.m in Sources */ = {isa = PBXBuildFile; fileRef = 18E064362DC4E29900F15793 /* XXGPlayKitCore+Canal.m */; };
		18ED07922D71616E00C8804D /* XXGNetListModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED07012D715DF600C8804D /* XXGNetListModel.h */; };
		18ED07932D71616E00C8804D /* XXGStartBody.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED06FB2D715DF600C8804D /* XXGStartBody.h */; };
		18ED07942D71616E00C8804D /* XXGAlertView.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED071D2D715DF600C8804D /* XXGAlertView.h */; };
		18ED07962D71616E00C8804D /* XXGNetworkMonitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED07172D715DF600C8804D /* XXGNetworkMonitor.h */; };
		18ED07972D71616E00C8804D /* NSString+XXGString.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED07242D715DF600C8804D /* NSString+XXGString.h */; };
		18ED07982D71616E00C8804D /* NSObject+XXGModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED07222D715DF600C8804D /* NSObject+XXGModel.h */; };
		18ED07992D71616E00C8804D /* UICKeyChainStore.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED071A2D715DF600C8804D /* UICKeyChainStore.h */; };
		18ED079B2D71616E00C8804D /* XXGAppInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED07282D715DF600C8804D /* XXGAppInfo.h */; };
		18ED079C2D71616E00C8804D /* XXGSecurityCheckTool.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED072A2D715DF600C8804D /* XXGSecurityCheckTool.h */; };
		18ED079D2D71616E00C8804D /* XXGNetworkCore.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED07152D715DF600C8804D /* XXGNetworkCore.h */; };
		18ED079E2D71616E00C8804D /* XXGSetting.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED072E2D715DF600C8804D /* XXGSetting.h */; settings = {ATTRIBUTES = (Public, ); }; };
		18ED079F2D71616E00C8804D /* XXGPlayKitCore.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED070E2D715DF600C8804D /* XXGPlayKitCore.h */; };
		18ED07A02D71616E00C8804D /* XXGWindowManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED071F2D715DF600C8804D /* XXGWindowManager.h */; };
		18ED07A12D71616E00C8804D /* XXGPlayKitConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED070C2D715DF600C8804D /* XXGPlayKitConfig.h */; };
		18ED07A22D71616E00C8804D /* XXGBaseURL.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED06FF2D715DF600C8804D /* XXGBaseURL.h */; };
		18ED07A32D71616E00C8804D /* XXGNetwork.h in Headers */ = {isa = PBXBuildFile; fileRef = 18ED07032D715DF600C8804D /* XXGNetwork.h */; };
		18ED07A72D71616E00C8804D /* XXGSetting.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED072F2D715DF600C8804D /* XXGSetting.m */; };
		18ED07A82D71616E00C8804D /* XXGPlayKitCore.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED070F2D715DF600C8804D /* XXGPlayKitCore.m */; };
		18ED07A92D71616E00C8804D /* UICKeyChainStore.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED071B2D715DF600C8804D /* UICKeyChainStore.m */; };
		18ED07AB2D71616E00C8804D /* XXGStartBody.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED06FC2D715DF600C8804D /* XXGStartBody.m */; };
		18ED07AC2D71616E00C8804D /* XXGPlayKitConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED070D2D715DF600C8804D /* XXGPlayKitConfig.m */; };
		18ED07AD2D71616E00C8804D /* NSObject+XXGModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED07232D715DF600C8804D /* NSObject+XXGModel.m */; };
		18ED07AE2D71616E00C8804D /* XXGNetworkMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED07182D715DF600C8804D /* XXGNetworkMonitor.m */; };
		18ED07AF2D71616E00C8804D /* XXGBaseURL.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED07002D715DF600C8804D /* XXGBaseURL.m */; };
		18ED07B12D71616E00C8804D /* XXGAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED071E2D715DF600C8804D /* XXGAlertView.m */; };
		18ED07B22D71616E00C8804D /* XXGNetListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED07022D715DF600C8804D /* XXGNetListModel.m */; };
		18ED07B32D71616E00C8804D /* XXGNetworkCore.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED07162D715DF600C8804D /* XXGNetworkCore.m */; };
		18ED07B42D71616E00C8804D /* XXGSecurityCheckTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED072B2D715DF600C8804D /* XXGSecurityCheckTool.m */; };
		18ED07B52D71616E00C8804D /* XXGAppInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED07292D715DF600C8804D /* XXGAppInfo.m */; };
		18ED07B62D71616E00C8804D /* XXGNetwork.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED07042D715DF600C8804D /* XXGNetwork.m */; };
		18ED07B72D71616E00C8804D /* NSString+XXGString.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED07252D715DF600C8804D /* NSString+XXGString.m */; };
		18ED07B82D71616E00C8804D /* XXGWindowManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 18ED07202D715DF600C8804D /* XXGWindowManager.m */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1811E3412DCC528B00B17C42 /* XXGAppLovinManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGAppLovinManager.h; sourceTree = "<group>"; };
		1811E3422DCC528B00B17C42 /* XXGAppLovinManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGAppLovinManager.m; sourceTree = "<group>"; };
		1811F7092DD2FD9800B17C42 /* XXGSelectProduct.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGSelectProduct.h; sourceTree = "<group>"; };
		1811F70A2DD2FD9800B17C42 /* XXGSelectProduct.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGSelectProduct.m; sourceTree = "<group>"; };
		1812EAB92D9114C300B7BB73 /* XXGWKMethodAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGWKMethodAction.h; sourceTree = "<group>"; };
		1812EABA2D9114C300B7BB73 /* XXGWKMethodAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGWKMethodAction.m; sourceTree = "<group>"; };
		1812EABF2D91593100B7BB73 /* NSURL+XXGAnalyse.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSURL+XXGAnalyse.h"; sourceTree = "<group>"; };
		1812EAC02D91593100B7BB73 /* NSURL+XXGAnalyse.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSURL+XXGAnalyse.m"; sourceTree = "<group>"; };
		1812EAC52D915CC500B7BB73 /* XXGPlayKitCore+Others.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XXGPlayKitCore+Others.h"; sourceTree = "<group>"; };
		1812EAC62D915CC500B7BB73 /* XXGPlayKitCore+Others.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XXGPlayKitCore+Others.m"; sourceTree = "<group>"; };
		1812EACD2D915ECA00B7BB73 /* XXGPlayCN.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGPlayCN.h; sourceTree = "<group>"; };
		1812EACE2D915ECA00B7BB73 /* XXGPlayCN.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGPlayCN.m; sourceTree = "<group>"; };
		1812ED852D929A7300B7BB73 /* XXGShanYanManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGShanYanManager.h; sourceTree = "<group>"; };
		1812ED862D929A7300B7BB73 /* XXGShanYanManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGShanYanManager.m; sourceTree = "<group>"; };
		181E79222D754987009CF2CC /* XXGExecuteActions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGExecuteActions.h; sourceTree = "<group>"; };
		181E79232D754987009CF2CC /* XXGExecuteActions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGExecuteActions.m; sourceTree = "<group>"; };
		181E79622D75BC2D009CF2CC /* XXGNetworkList.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGNetworkList.h; sourceTree = "<group>"; };
		181E79632D75BC2D009CF2CC /* XXGNetworkList.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGNetworkList.m; sourceTree = "<group>"; };
		181E79772D76FF9F009CF2CC /* XXGBoxContent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGBoxContent.h; sourceTree = "<group>"; };
		181E79782D76FF9F009CF2CC /* XXGBoxContent.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGBoxContent.m; sourceTree = "<group>"; };
		181E79842D77EED2009CF2CC /* XXGBoxManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGBoxManager.h; sourceTree = "<group>"; };
		181E79852D77EED2009CF2CC /* XXGBoxManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGBoxManager.m; sourceTree = "<group>"; };
		181E798A2D7845A0009CF2CC /* XXGSkinModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGSkinModel.h; sourceTree = "<group>"; };
		181E798B2D7845A0009CF2CC /* XXGSkinModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGSkinModel.m; sourceTree = "<group>"; };
		181E79902D784A7F009CF2CC /* XXGAdaptionCof.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGAdaptionCof.h; sourceTree = "<group>"; };
		181E79912D784A7F009CF2CC /* XXGAdaptionCof.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGAdaptionCof.m; sourceTree = "<group>"; };
		18253AFE2D9AB61700BEA16C /* XXGBDASignalManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGBDASignalManager.h; sourceTree = "<group>"; };
		18253AFF2D9AB61700BEA16C /* XXGBDASignalManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGBDASignalManager.m; sourceTree = "<group>"; };
		182C6D842D8D47C200D3F530 /* XXGFacebookManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGFacebookManager.h; sourceTree = "<group>"; };
		182C6D852D8D47C200D3F530 /* XXGFacebookManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGFacebookManager.m; sourceTree = "<group>"; };
		182C6D982D8D565B00D3F530 /* XXGThirdManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGThirdManager.h; sourceTree = "<group>"; };
		182C6D992D8D565B00D3F530 /* XXGThirdManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGThirdManager.m; sourceTree = "<group>"; };
		182C6D9F2D8D61C100D3F530 /* NSObject+XXGPerformSelector.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSObject+XXGPerformSelector.h"; sourceTree = "<group>"; };
		182C6DA02D8D61C100D3F530 /* NSObject+XXGPerformSelector.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSObject+XXGPerformSelector.m"; sourceTree = "<group>"; };
		182C6DA52D8D66CF00D3F530 /* XXGExtraParams.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGExtraParams.h; sourceTree = "<group>"; };
		182C6DA62D8D66CF00D3F530 /* XXGExtraParams.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGExtraParams.m; sourceTree = "<group>"; };
		182F66952DC090A90039594F /* UIDevice+XXGDevice.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIDevice+XXGDevice.h"; sourceTree = "<group>"; };
		182F66962DC090A90039594F /* UIDevice+XXGDevice.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIDevice+XXGDevice.m"; sourceTree = "<group>"; };
		182F66E62DC09A750039594F /* XXGPlayKitCN.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGPlayKitCN.m; sourceTree = "<group>"; };
		182F69CF2DC0AB150039594F /* XXGAppInfoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGAppInfoViewController.h; sourceTree = "<group>"; };
		182F69D02DC0AB150039594F /* XXGAppInfoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGAppInfoViewController.m; sourceTree = "<group>"; };
		182F6C172DC0F9FB0039594F /* WantedAdhesive.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WantedAdhesive.m; sourceTree = "<group>"; };
		182F6C9F2DC1BAA70039594F /* XXGServerInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGServerInfo.h; sourceTree = "<group>"; };
		182F6CA02DC1BAA70039594F /* XXGServerInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGServerInfo.m; sourceTree = "<group>"; };
		182F6CA52DC28C660039594F /* XXGPoopoManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGPoopoManager.h; sourceTree = "<group>"; };
		182F6CA62DC28C660039594F /* XXGPoopoManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGPoopoManager.m; sourceTree = "<group>"; };
		1834F7C72D730E3B00A1C782 /* XXGActionItem.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGActionItem.h; sourceTree = "<group>"; };
		1834F7C82D730E3B00A1C782 /* XXGActionItem.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGActionItem.m; sourceTree = "<group>"; };
		18401FA82D8D8A51005D10BB /* XXGAppsFlyerManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGAppsFlyerManager.h; sourceTree = "<group>"; };
		18401FA92D8D8A51005D10BB /* XXGAppsFlyerManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGAppsFlyerManager.m; sourceTree = "<group>"; };
		18401FB62D8E9506005D10BB /* XXGFirebaseManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGFirebaseManager.h; sourceTree = "<group>"; };
		18401FB72D8E9506005D10BB /* XXGFirebaseManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGFirebaseManager.m; sourceTree = "<group>"; };
		1849B92E2D7877E400A5F887 /* XXGThemeColor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGThemeColor.h; sourceTree = "<group>"; };
		1849B92F2D7877E400A5F887 /* XXGThemeColor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGThemeColor.m; sourceTree = "<group>"; };
		1849B9342D78798B00A5F887 /* XXGDockerCof.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGDockerCof.h; sourceTree = "<group>"; };
		1849B9352D78798B00A5F887 /* XXGDockerCof.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGDockerCof.m; sourceTree = "<group>"; };
		1849B93A2D7931D700A5F887 /* XXGServiceInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGServiceInfo.h; sourceTree = "<group>"; };
		1849B93B2D7931D700A5F887 /* XXGServiceInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGServiceInfo.m; sourceTree = "<group>"; };
		1849B9402D79352700A5F887 /* XXGBoxCenterCof.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGBoxCenterCof.h; sourceTree = "<group>"; };
		1849B9412D79352700A5F887 /* XXGBoxCenterCof.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGBoxCenterCof.m; sourceTree = "<group>"; };
		1849B9702D7AE2A100A5F887 /* XXGUIKit.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGUIKit.h; sourceTree = "<group>"; };
		1849B9712D7AE2A100A5F887 /* XXGUIKit.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGUIKit.m; sourceTree = "<group>"; };
		1849B9772D7AE9A800A5F887 /* XXGBaseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGBaseViewController.h; sourceTree = "<group>"; };
		1849B9782D7AE9A800A5F887 /* XXGBaseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGBaseViewController.m; sourceTree = "<group>"; };
		1849B9832D7AEA3B00A5F887 /* XXGNavigationController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGNavigationController.h; sourceTree = "<group>"; };
		1849B9842D7AEA3B00A5F887 /* XXGNavigationController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGNavigationController.m; sourceTree = "<group>"; };
		1849B9892D7AED6D00A5F887 /* UIViewController+XXGViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIViewController+XXGViewController.h"; sourceTree = "<group>"; };
		1849B98A2D7AED6D00A5F887 /* UIViewController+XXGViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+XXGViewController.m"; sourceTree = "<group>"; };
		1849B9E92D7B022600A5F887 /* XXGComeinViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGComeinViewController.h; sourceTree = "<group>"; };
		1849B9EA2D7B022600A5F887 /* XXGComeinViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGComeinViewController.m; sourceTree = "<group>"; };
		186373392D7164A9000D82AE /* XXGPlayOS.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGPlayOS.h; sourceTree = "<group>"; };
		1863733A2D7164A9000D82AE /* XXGPlayOS.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGPlayOS.m; sourceTree = "<group>"; };
		186AE8EB2D942364000F1A11 /* XXGVKManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGVKManager.h; sourceTree = "<group>"; };
		186AE8EC2D942364000F1A11 /* XXGVKManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGVKManager.m; sourceTree = "<group>"; };
		186AE9D02D96C244000F1A11 /* XXGAdjustManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGAdjustManager.h; sourceTree = "<group>"; };
		186AE9D12D96C244000F1A11 /* XXGAdjustManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGAdjustManager.m; sourceTree = "<group>"; };
		1886C6022DE44C4D006C3D99 /* XXGDebugger.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGDebugger.h; sourceTree = "<group>"; };
		1886C6032DE44C4D006C3D99 /* XXGDebugger.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGDebugger.m; sourceTree = "<group>"; };
		1886C6DB2DE6F77F006C3D99 /* ZBBaseDestination.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZBBaseDestination.h; sourceTree = "<group>"; };
		1886C6DC2DE6F77F006C3D99 /* ZBBaseDestination.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZBBaseDestination.m; sourceTree = "<group>"; };
		1886C6DD2DE6F77F006C3D99 /* ZBConsoleDestinatioin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZBConsoleDestinatioin.h; sourceTree = "<group>"; };
		1886C6DE2DE6F77F006C3D99 /* ZBConsoleDestinatioin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZBConsoleDestinatioin.m; sourceTree = "<group>"; };
		1886C6DF2DE6F77F006C3D99 /* ZBFileDestination.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZBFileDestination.h; sourceTree = "<group>"; };
		1886C6E02DE6F77F006C3D99 /* ZBFileDestination.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZBFileDestination.m; sourceTree = "<group>"; };
		1886C6E12DE6F77F006C3D99 /* ZBLog.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZBLog.h; sourceTree = "<group>"; };
		1886C6E22DE6F77F006C3D99 /* ZBLog.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZBLog.m; sourceTree = "<group>"; };
		1886C6E32DE6F77F006C3D99 /* ZBLogFormatter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZBLogFormatter.h; sourceTree = "<group>"; };
		1886C6E42DE6F77F006C3D99 /* ZBLogFormatter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZBLogFormatter.m; sourceTree = "<group>"; };
		1886C6E52DE6F77F006C3D99 /* ZBLogMacros.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZBLogMacros.h; sourceTree = "<group>"; };
		1886C6E62DE6F77F006C3D99 /* ZBLogViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZBLogViewController.h; sourceTree = "<group>"; };
		1886C6E72DE6F77F006C3D99 /* ZBLogViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZBLogViewController.m; sourceTree = "<group>"; };
		1886C6E82DE6F77F006C3D99 /* ZBObjectiveCBeaver.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZBObjectiveCBeaver.h; sourceTree = "<group>"; };
		188F7C562D9BBB5400699DF4 /* XXGMobileTextField.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGMobileTextField.h; sourceTree = "<group>"; };
		188F7C572D9BBB5400699DF4 /* XXGMobileTextField.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGMobileTextField.m; sourceTree = "<group>"; };
		188F7C622D9E206D00699DF4 /* XXGSaveNamePSViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGSaveNamePSViewController.h; sourceTree = "<group>"; };
		188F7C632D9E206D00699DF4 /* XXGSaveNamePSViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGSaveNamePSViewController.m; sourceTree = "<group>"; };
		188F7F0C2DA7EEAC00699DF4 /* XXGMarqueeViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGMarqueeViewCell.h; sourceTree = "<group>"; };
		188F7F0D2DA7EEAC00699DF4 /* XXGMarqueeViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGMarqueeViewCell.m; sourceTree = "<group>"; };
		188F7F132DA7EF9C00699DF4 /* XXGTransparentWindow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGTransparentWindow.h; sourceTree = "<group>"; };
		188F7F142DA7EF9C00699DF4 /* XXGTransparentWindow.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGTransparentWindow.m; sourceTree = "<group>"; };
		188F7F192DA7F00F00699DF4 /* XXGOrientationViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGOrientationViewController.h; sourceTree = "<group>"; };
		188F7F1A2DA7F00F00699DF4 /* XXGOrientationViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGOrientationViewController.m; sourceTree = "<group>"; };
		188F80032DACB95200699DF4 /* NSString+XXGMd5.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+XXGMd5.h"; sourceTree = "<group>"; };
		188F80042DACB95200699DF4 /* NSString+XXGMd5.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+XXGMd5.m"; sourceTree = "<group>"; };
		188F800F2DACBA3500699DF4 /* NSData+SunHope.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSData+SunHope.h"; sourceTree = "<group>"; };
		188F80102DACBA3500699DF4 /* NSData+SunHope.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSData+SunHope.m"; sourceTree = "<group>"; };
		18989DE92D71C35A00E11C3B /* WantedAdhesive.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WantedAdhesive.h; sourceTree = "<group>"; };
		18989DEB2D71C39D00E11C3B /* XXGPlayKitCN.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGPlayKitCN.h; sourceTree = "<group>"; };
		18989F272D71E0B000E11C3B /* XXGDeviceInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGDeviceInfo.h; sourceTree = "<group>"; };
		18989F282D71E0B000E11C3B /* XXGDeviceInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGDeviceInfo.m; sourceTree = "<group>"; };
		18989F2E2D72C10C00E11C3B /* UIColor+XXGColor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIColor+XXGColor.h"; sourceTree = "<group>"; };
		18989F2F2D72C10C00E11C3B /* UIColor+XXGColor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIColor+XXGColor.m"; sourceTree = "<group>"; };
		18A230892D8EDD2C0015E020 /* XXGPlayProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGPlayProtocol.h; sourceTree = "<group>"; };
		18DA0BF82D8A6B1D003C4467 /* XXGSelectPCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGSelectPCell.h; sourceTree = "<group>"; };
		18DA0BF92D8A6B1D003C4467 /* XXGSelectPCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGSelectPCell.m; sourceTree = "<group>"; };
		18DA0CA72D8BB02E003C4467 /* ForegroundReconnection.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ForegroundReconnection.h; sourceTree = "<group>"; };
		18DA0CA82D8BB02E003C4467 /* ForegroundReconnection.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ForegroundReconnection.m; sourceTree = "<group>"; };
		18DA0CA92D8BB02E003C4467 /* GCDTimer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GCDTimer.h; sourceTree = "<group>"; };
		18DA0CAA2D8BB02E003C4467 /* GCDTimer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GCDTimer.m; sourceTree = "<group>"; };
		18DA0CAB2D8BB02E003C4467 /* MQTTCFSocketDecoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCFSocketDecoder.h; sourceTree = "<group>"; };
		18DA0CAC2D8BB02E003C4467 /* MQTTCFSocketDecoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCFSocketDecoder.m; sourceTree = "<group>"; };
		18DA0CAD2D8BB02E003C4467 /* MQTTCFSocketEncoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCFSocketEncoder.h; sourceTree = "<group>"; };
		18DA0CAE2D8BB02E003C4467 /* MQTTCFSocketEncoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCFSocketEncoder.m; sourceTree = "<group>"; };
		18DA0CAF2D8BB02E003C4467 /* MQTTCFSocketTransport.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCFSocketTransport.h; sourceTree = "<group>"; };
		18DA0CB02D8BB02E003C4467 /* MQTTCFSocketTransport.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCFSocketTransport.m; sourceTree = "<group>"; };
		18DA0CB12D8BB02E003C4467 /* MQTTClient.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTClient.h; sourceTree = "<group>"; };
		18DA0CB22D8BB02E003C4467 /* MQTTCoreDataPersistence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCoreDataPersistence.h; sourceTree = "<group>"; };
		18DA0CB32D8BB02E003C4467 /* MQTTCoreDataPersistence.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCoreDataPersistence.m; sourceTree = "<group>"; };
		18DA0CB42D8BB02E003C4467 /* MQTTDecoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTDecoder.h; sourceTree = "<group>"; };
		18DA0CB52D8BB02E003C4467 /* MQTTDecoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTDecoder.m; sourceTree = "<group>"; };
		18DA0CB62D8BB02E003C4467 /* MQTTInMemoryPersistence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTInMemoryPersistence.h; sourceTree = "<group>"; };
		18DA0CB72D8BB02E003C4467 /* MQTTInMemoryPersistence.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTInMemoryPersistence.m; sourceTree = "<group>"; };
		18DA0CB82D8BB02E003C4467 /* MQTTLog.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTLog.h; sourceTree = "<group>"; };
		18DA0CB92D8BB02E003C4467 /* MQTTLog.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTLog.m; sourceTree = "<group>"; };
		18DA0CBA2D8BB02E003C4467 /* MQTTMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTMessage.h; sourceTree = "<group>"; };
		18DA0CBB2D8BB02E003C4467 /* MQTTMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTMessage.m; sourceTree = "<group>"; };
		18DA0CBC2D8BB02E003C4467 /* MQTTPersistence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTPersistence.h; sourceTree = "<group>"; };
		18DA0CBD2D8BB02E003C4467 /* MQTTProperties.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTProperties.h; sourceTree = "<group>"; };
		18DA0CBE2D8BB02E003C4467 /* MQTTProperties.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTProperties.m; sourceTree = "<group>"; };
		18DA0CBF2D8BB02E003C4467 /* MQTTSession.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSession.h; sourceTree = "<group>"; };
		18DA0CC02D8BB02E003C4467 /* MQTTSession.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSession.m; sourceTree = "<group>"; };
		18DA0CC12D8BB02E003C4467 /* MQTTSessionLegacy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSessionLegacy.h; sourceTree = "<group>"; };
		18DA0CC22D8BB02E003C4467 /* MQTTSessionLegacy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSessionLegacy.m; sourceTree = "<group>"; };
		18DA0CC32D8BB02E003C4467 /* MQTTSessionManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSessionManager.h; sourceTree = "<group>"; };
		18DA0CC42D8BB02E003C4467 /* MQTTSessionManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSessionManager.m; sourceTree = "<group>"; };
		18DA0CC52D8BB02E003C4467 /* MQTTSessionSynchron.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSessionSynchron.h; sourceTree = "<group>"; };
		18DA0CC62D8BB02E003C4467 /* MQTTSessionSynchron.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSessionSynchron.m; sourceTree = "<group>"; };
		18DA0CC72D8BB02E003C4467 /* MQTTSSLSecurityPolicy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicy.h; sourceTree = "<group>"; };
		18DA0CC82D8BB02E003C4467 /* MQTTSSLSecurityPolicy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicy.m; sourceTree = "<group>"; };
		18DA0CC92D8BB02E003C4467 /* MQTTSSLSecurityPolicyDecoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicyDecoder.h; sourceTree = "<group>"; };
		18DA0CCA2D8BB02E003C4467 /* MQTTSSLSecurityPolicyDecoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicyDecoder.m; sourceTree = "<group>"; };
		18DA0CCB2D8BB02E003C4467 /* MQTTSSLSecurityPolicyEncoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicyEncoder.h; sourceTree = "<group>"; };
		18DA0CCC2D8BB02E003C4467 /* MQTTSSLSecurityPolicyEncoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicyEncoder.m; sourceTree = "<group>"; };
		18DA0CCD2D8BB02E003C4467 /* MQTTSSLSecurityPolicyTransport.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicyTransport.h; sourceTree = "<group>"; };
		18DA0CCE2D8BB02E003C4467 /* MQTTSSLSecurityPolicyTransport.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicyTransport.m; sourceTree = "<group>"; };
		18DA0CCF2D8BB02E003C4467 /* MQTTStrict.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTStrict.h; sourceTree = "<group>"; };
		18DA0CD02D8BB02E003C4467 /* MQTTStrict.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTStrict.m; sourceTree = "<group>"; };
		18DA0CD12D8BB02E003C4467 /* MQTTTransportProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTTransportProtocol.h; sourceTree = "<group>"; };
		18DA0CD22D8BB02E003C4467 /* MQTTTransportProtocol.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTTransportProtocol.m; sourceTree = "<group>"; };
		18DA0CD32D8BB02E003C4467 /* ReconnectTimer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReconnectTimer.h; sourceTree = "<group>"; };
		18DA0CD42D8BB02E003C4467 /* ReconnectTimer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReconnectTimer.m; sourceTree = "<group>"; };
		18DA0D322D8BB826003C4467 /* XXGMQTTManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGMQTTManager.h; sourceTree = "<group>"; };
		18DA0D332D8BB826003C4467 /* XXGMQTTManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGMQTTManager.m; sourceTree = "<group>"; };
		18DA0D442D8BBA5A003C4467 /* XXGMQTTConnectInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGMQTTConnectInfo.h; sourceTree = "<group>"; };
		18DA0D452D8BBA5A003C4467 /* XXGMQTTConnectInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGMQTTConnectInfo.m; sourceTree = "<group>"; };
		18DA0E702D8C116E003C4467 /* XXGMQTTTopicInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGMQTTTopicInfo.h; sourceTree = "<group>"; };
		18DA0E712D8C116E003C4467 /* XXGMQTTTopicInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGMQTTTopicInfo.m; sourceTree = "<group>"; };
		18DA0E762D8C16C7003C4467 /* XXGLiveBarrage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGLiveBarrage.h; sourceTree = "<group>"; };
		18DA0E772D8C16C7003C4467 /* XXGLiveBarrageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGLiveBarrageCell.h; sourceTree = "<group>"; };
		18DA0E782D8C16C7003C4467 /* XXGLiveBarrageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGLiveBarrageCell.m; sourceTree = "<group>"; };
		18DA0E792D8C16C7003C4467 /* XXGLiveBarrage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGLiveBarrage.m; sourceTree = "<group>"; };
		18DA0E832D8C1A9E003C4467 /* XXGMarqueeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGMarqueeView.h; sourceTree = "<group>"; };
		18DA0E842D8C1A9E003C4467 /* XXGMarqueeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGMarqueeView.m; sourceTree = "<group>"; };
		18DD42092D7C2B1F00E809EC /* MASCompositeConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASCompositeConstraint.h; sourceTree = "<group>"; };
		18DD420A2D7C2B1F00E809EC /* MASCompositeConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASCompositeConstraint.m; sourceTree = "<group>"; };
		18DD420B2D7C2B1F00E809EC /* MASConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASConstraint.h; sourceTree = "<group>"; };
		18DD420C2D7C2B1F00E809EC /* MASConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASConstraint.m; sourceTree = "<group>"; };
		18DD420D2D7C2B1F00E809EC /* MASConstraint+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "MASConstraint+Private.h"; sourceTree = "<group>"; };
		18DD420E2D7C2B1F00E809EC /* MASConstraintMaker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASConstraintMaker.h; sourceTree = "<group>"; };
		18DD420F2D7C2B1F00E809EC /* MASConstraintMaker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASConstraintMaker.m; sourceTree = "<group>"; };
		18DD42102D7C2B1F00E809EC /* MASLayoutConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASLayoutConstraint.h; sourceTree = "<group>"; };
		18DD42112D7C2B1F00E809EC /* MASLayoutConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASLayoutConstraint.m; sourceTree = "<group>"; };
		18DD42122D7C2B1F00E809EC /* Masonry.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Masonry.h; sourceTree = "<group>"; };
		18DD42132D7C2B1F00E809EC /* MASUtilities.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASUtilities.h; sourceTree = "<group>"; };
		18DD42142D7C2B1F00E809EC /* MASViewAttribute.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASViewAttribute.h; sourceTree = "<group>"; };
		18DD42152D7C2B1F00E809EC /* MASViewAttribute.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASViewAttribute.m; sourceTree = "<group>"; };
		18DD42162D7C2B1F00E809EC /* MASViewConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASViewConstraint.h; sourceTree = "<group>"; };
		18DD42172D7C2B1F00E809EC /* MASViewConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASViewConstraint.m; sourceTree = "<group>"; };
		18DD42182D7C2B1F00E809EC /* NSArray+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASAdditions.h"; sourceTree = "<group>"; };
		18DD42192D7C2B1F00E809EC /* NSArray+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSArray+MASAdditions.m"; sourceTree = "<group>"; };
		18DD421A2D7C2B1F00E809EC /* NSArray+MASShorthandAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		18DD421B2D7C2B1F00E809EC /* NSLayoutConstraint+MASDebugAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSLayoutConstraint+MASDebugAdditions.h"; sourceTree = "<group>"; };
		18DD421C2D7C2B1F00E809EC /* NSLayoutConstraint+MASDebugAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSLayoutConstraint+MASDebugAdditions.m"; sourceTree = "<group>"; };
		18DD421D2D7C2B1F00E809EC /* View+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "View+MASAdditions.h"; sourceTree = "<group>"; };
		18DD421E2D7C2B1F00E809EC /* View+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "View+MASAdditions.m"; sourceTree = "<group>"; };
		18DD421F2D7C2B1F00E809EC /* View+MASShorthandAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "View+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		18DD42202D7C2B1F00E809EC /* ViewController+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "ViewController+MASAdditions.h"; sourceTree = "<group>"; };
		18DD42212D7C2B1F00E809EC /* ViewController+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "ViewController+MASAdditions.m"; sourceTree = "<group>"; };
		18DD42572D7C3EE700E809EC /* NSButton+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSButton+WebCache.h"; sourceTree = "<group>"; };
		18DD42582D7C3EE700E809EC /* NSButton+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSButton+WebCache.m"; sourceTree = "<group>"; };
		18DD42592D7C3EE700E809EC /* NSData+ImageContentType.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSData+ImageContentType.h"; sourceTree = "<group>"; };
		18DD425A2D7C3EE700E809EC /* NSData+ImageContentType.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSData+ImageContentType.m"; sourceTree = "<group>"; };
		18DD425B2D7C3EE700E809EC /* NSImage+Compatibility.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSImage+Compatibility.h"; sourceTree = "<group>"; };
		18DD425C2D7C3EE700E809EC /* NSImage+Compatibility.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSImage+Compatibility.m"; sourceTree = "<group>"; };
		18DD425D2D7C3EE700E809EC /* SDAnimatedImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImage.h; sourceTree = "<group>"; };
		18DD425E2D7C3EE700E809EC /* SDAnimatedImage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImage.m; sourceTree = "<group>"; };
		18DD425F2D7C3EE700E809EC /* SDAnimatedImagePlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImagePlayer.h; sourceTree = "<group>"; };
		18DD42602D7C3EE700E809EC /* SDAnimatedImagePlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImagePlayer.m; sourceTree = "<group>"; };
		18DD42612D7C3EE700E809EC /* SDAnimatedImageRep.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImageRep.h; sourceTree = "<group>"; };
		18DD42622D7C3EE700E809EC /* SDAnimatedImageRep.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImageRep.m; sourceTree = "<group>"; };
		18DD42632D7C3EE700E809EC /* SDAnimatedImageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImageView.h; sourceTree = "<group>"; };
		18DD42642D7C3EE700E809EC /* SDAnimatedImageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImageView.m; sourceTree = "<group>"; };
		18DD42652D7C3EE700E809EC /* SDAnimatedImageView+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SDAnimatedImageView+WebCache.h"; sourceTree = "<group>"; };
		18DD42662D7C3EE700E809EC /* SDAnimatedImageView+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "SDAnimatedImageView+WebCache.m"; sourceTree = "<group>"; };
		18DD42672D7C3EE700E809EC /* SDCallbackQueue.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDCallbackQueue.h; sourceTree = "<group>"; };
		18DD42682D7C3EE700E809EC /* SDCallbackQueue.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDCallbackQueue.m; sourceTree = "<group>"; };
		18DD42692D7C3EE700E809EC /* SDDiskCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDDiskCache.h; sourceTree = "<group>"; };
		18DD426A2D7C3EE700E809EC /* SDDiskCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDDiskCache.m; sourceTree = "<group>"; };
		18DD426B2D7C3EE700E809EC /* SDGraphicsImageRenderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDGraphicsImageRenderer.h; sourceTree = "<group>"; };
		18DD426C2D7C3EE700E809EC /* SDGraphicsImageRenderer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDGraphicsImageRenderer.m; sourceTree = "<group>"; };
		18DD426D2D7C3EE700E809EC /* SDImageAPNGCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageAPNGCoder.h; sourceTree = "<group>"; };
		18DD426E2D7C3EE700E809EC /* SDImageAPNGCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageAPNGCoder.m; sourceTree = "<group>"; };
		18DD426F2D7C3EE700E809EC /* SDImageAWebPCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageAWebPCoder.h; sourceTree = "<group>"; };
		18DD42702D7C3EE700E809EC /* SDImageAWebPCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageAWebPCoder.m; sourceTree = "<group>"; };
		18DD42712D7C3EE700E809EC /* SDImageCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCache.h; sourceTree = "<group>"; };
		18DD42722D7C3EE700E809EC /* SDImageCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCache.m; sourceTree = "<group>"; };
		18DD42732D7C3EE700E809EC /* SDImageCacheConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCacheConfig.h; sourceTree = "<group>"; };
		18DD42742D7C3EE700E809EC /* SDImageCacheConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCacheConfig.m; sourceTree = "<group>"; };
		18DD42752D7C3EE700E809EC /* SDImageCacheDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCacheDefine.h; sourceTree = "<group>"; };
		18DD42762D7C3EE700E809EC /* SDImageCacheDefine.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCacheDefine.m; sourceTree = "<group>"; };
		18DD42772D7C3EE700E809EC /* SDImageCachesManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCachesManager.h; sourceTree = "<group>"; };
		18DD42782D7C3EE700E809EC /* SDImageCachesManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCachesManager.m; sourceTree = "<group>"; };
		18DD42792D7C3EE700E809EC /* SDImageCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCoder.h; sourceTree = "<group>"; };
		18DD427A2D7C3EE700E809EC /* SDImageCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCoder.m; sourceTree = "<group>"; };
		18DD427B2D7C3EE700E809EC /* SDImageCoderHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCoderHelper.h; sourceTree = "<group>"; };
		18DD427C2D7C3EE700E809EC /* SDImageCoderHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCoderHelper.m; sourceTree = "<group>"; };
		18DD427D2D7C3EE700E809EC /* SDImageCodersManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCodersManager.h; sourceTree = "<group>"; };
		18DD427E2D7C3EE700E809EC /* SDImageCodersManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCodersManager.m; sourceTree = "<group>"; };
		18DD427F2D7C3EE700E809EC /* SDImageFrame.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageFrame.h; sourceTree = "<group>"; };
		18DD42802D7C3EE700E809EC /* SDImageFrame.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageFrame.m; sourceTree = "<group>"; };
		18DD42812D7C3EE700E809EC /* SDImageGIFCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageGIFCoder.h; sourceTree = "<group>"; };
		18DD42822D7C3EE700E809EC /* SDImageGIFCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageGIFCoder.m; sourceTree = "<group>"; };
		18DD42832D7C3EE700E809EC /* SDImageGraphics.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageGraphics.h; sourceTree = "<group>"; };
		18DD42842D7C3EE700E809EC /* SDImageGraphics.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageGraphics.m; sourceTree = "<group>"; };
		18DD42852D7C3EE700E809EC /* SDImageHEICCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageHEICCoder.h; sourceTree = "<group>"; };
		18DD42862D7C3EE700E809EC /* SDImageHEICCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageHEICCoder.m; sourceTree = "<group>"; };
		18DD42872D7C3EE700E809EC /* SDImageIOAnimatedCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageIOAnimatedCoder.h; sourceTree = "<group>"; };
		18DD42882D7C3EE700E809EC /* SDImageIOAnimatedCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageIOAnimatedCoder.m; sourceTree = "<group>"; };
		18DD42892D7C3EE700E809EC /* SDImageIOCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageIOCoder.h; sourceTree = "<group>"; };
		18DD428A2D7C3EE700E809EC /* SDImageIOCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageIOCoder.m; sourceTree = "<group>"; };
		18DD428B2D7C3EE700E809EC /* SDImageLoader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageLoader.h; sourceTree = "<group>"; };
		18DD428C2D7C3EE700E809EC /* SDImageLoader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageLoader.m; sourceTree = "<group>"; };
		18DD428D2D7C3EE700E809EC /* SDImageLoadersManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageLoadersManager.h; sourceTree = "<group>"; };
		18DD428E2D7C3EE700E809EC /* SDImageLoadersManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageLoadersManager.m; sourceTree = "<group>"; };
		18DD428F2D7C3EE700E809EC /* SDImageTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageTransformer.h; sourceTree = "<group>"; };
		18DD42902D7C3EE700E809EC /* SDImageTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageTransformer.m; sourceTree = "<group>"; };
		18DD42912D7C3EE700E809EC /* SDMemoryCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDMemoryCache.h; sourceTree = "<group>"; };
		18DD42922D7C3EE700E809EC /* SDMemoryCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDMemoryCache.m; sourceTree = "<group>"; };
		18DD42932D7C3EE700E809EC /* SDWebImageCacheKeyFilter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageCacheKeyFilter.h; sourceTree = "<group>"; };
		18DD42942D7C3EE700E809EC /* SDWebImageCacheKeyFilter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCacheKeyFilter.m; sourceTree = "<group>"; };
		18DD42952D7C3EE700E809EC /* SDWebImageCacheSerializer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageCacheSerializer.h; sourceTree = "<group>"; };
		18DD42962D7C3EE700E809EC /* SDWebImageCacheSerializer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCacheSerializer.m; sourceTree = "<group>"; };
		18DD42972D7C3EE700E809EC /* SDWebImageCompat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageCompat.h; sourceTree = "<group>"; };
		18DD42982D7C3EE700E809EC /* SDWebImageCompat.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCompat.m; sourceTree = "<group>"; };
		18DD42992D7C3EE700E809EC /* SDWebImageDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDefine.h; sourceTree = "<group>"; };
		18DD429A2D7C3EE700E809EC /* SDWebImageDefine.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDefine.m; sourceTree = "<group>"; };
		18DD429B2D7C3EE700E809EC /* SDWebImageDownloader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloader.h; sourceTree = "<group>"; };
		18DD429C2D7C3EE700E809EC /* SDWebImageDownloader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloader.m; sourceTree = "<group>"; };
		18DD429D2D7C3EE700E809EC /* SDWebImageDownloaderConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderConfig.h; sourceTree = "<group>"; };
		18DD429E2D7C3EE700E809EC /* SDWebImageDownloaderConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderConfig.m; sourceTree = "<group>"; };
		18DD429F2D7C3EE700E809EC /* SDWebImageDownloaderDecryptor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderDecryptor.h; sourceTree = "<group>"; };
		18DD42A02D7C3EE700E809EC /* SDWebImageDownloaderDecryptor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderDecryptor.m; sourceTree = "<group>"; };
		18DD42A12D7C3EE700E809EC /* SDWebImageDownloaderOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderOperation.h; sourceTree = "<group>"; };
		18DD42A22D7C3EE700E809EC /* SDWebImageDownloaderOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderOperation.m; sourceTree = "<group>"; };
		18DD42A32D7C3EE700E809EC /* SDWebImageDownloaderRequestModifier.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderRequestModifier.h; sourceTree = "<group>"; };
		18DD42A42D7C3EE700E809EC /* SDWebImageDownloaderRequestModifier.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderRequestModifier.m; sourceTree = "<group>"; };
		18DD42A52D7C3EE700E809EC /* SDWebImageDownloaderResponseModifier.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderResponseModifier.h; sourceTree = "<group>"; };
		18DD42A62D7C3EE700E809EC /* SDWebImageDownloaderResponseModifier.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderResponseModifier.m; sourceTree = "<group>"; };
		18DD42A72D7C3EE700E809EC /* SDWebImageError.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageError.h; sourceTree = "<group>"; };
		18DD42A82D7C3EE700E809EC /* SDWebImageError.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageError.m; sourceTree = "<group>"; };
		18DD42A92D7C3EE700E809EC /* SDWebImageIndicator.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageIndicator.h; sourceTree = "<group>"; };
		18DD42AA2D7C3EE700E809EC /* SDWebImageIndicator.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageIndicator.m; sourceTree = "<group>"; };
		18DD42AB2D7C3EE700E809EC /* SDWebImageManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageManager.h; sourceTree = "<group>"; };
		18DD42AC2D7C3EE700E809EC /* SDWebImageManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageManager.m; sourceTree = "<group>"; };
		18DD42AD2D7C3EE700E809EC /* SDWebImageOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageOperation.h; sourceTree = "<group>"; };
		18DD42AE2D7C3EE700E809EC /* SDWebImageOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageOperation.m; sourceTree = "<group>"; };
		18DD42AF2D7C3EE700E809EC /* SDWebImageOptionsProcessor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageOptionsProcessor.h; sourceTree = "<group>"; };
		18DD42B02D7C3EE700E809EC /* SDWebImageOptionsProcessor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageOptionsProcessor.m; sourceTree = "<group>"; };
		18DD42B12D7C3EE700E809EC /* SDWebImagePrefetcher.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImagePrefetcher.h; sourceTree = "<group>"; };
		18DD42B22D7C3EE700E809EC /* SDWebImagePrefetcher.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImagePrefetcher.m; sourceTree = "<group>"; };
		18DD42B32D7C3EE700E809EC /* SDWebImageTransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageTransition.h; sourceTree = "<group>"; };
		18DD42B42D7C3EE700E809EC /* SDWebImageTransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageTransition.m; sourceTree = "<group>"; };
		18DD42B52D7C3EE700E809EC /* UIButton+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIButton+WebCache.h"; sourceTree = "<group>"; };
		18DD42B62D7C3EE700E809EC /* UIButton+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIButton+WebCache.m"; sourceTree = "<group>"; };
		18DD42B72D7C3EE700E809EC /* UIImage+ExtendedCacheData.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+ExtendedCacheData.h"; sourceTree = "<group>"; };
		18DD42B82D7C3EE700E809EC /* UIImage+ExtendedCacheData.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+ExtendedCacheData.m"; sourceTree = "<group>"; };
		18DD42B92D7C3EE700E809EC /* UIImage+ForceDecode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+ForceDecode.h"; sourceTree = "<group>"; };
		18DD42BA2D7C3EE700E809EC /* UIImage+ForceDecode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+ForceDecode.m"; sourceTree = "<group>"; };
		18DD42BB2D7C3EE700E809EC /* UIImage+GIF.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+GIF.h"; sourceTree = "<group>"; };
		18DD42BC2D7C3EE700E809EC /* UIImage+GIF.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+GIF.m"; sourceTree = "<group>"; };
		18DD42BD2D7C3EE700E809EC /* UIImage+MemoryCacheCost.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+MemoryCacheCost.h"; sourceTree = "<group>"; };
		18DD42BE2D7C3EE700E809EC /* UIImage+MemoryCacheCost.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+MemoryCacheCost.m"; sourceTree = "<group>"; };
		18DD42BF2D7C3EE700E809EC /* UIImage+Metadata.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+Metadata.h"; sourceTree = "<group>"; };
		18DD42C02D7C3EE700E809EC /* UIImage+Metadata.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Metadata.m"; sourceTree = "<group>"; };
		18DD42C12D7C3EE700E809EC /* UIImage+MultiFormat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+MultiFormat.h"; sourceTree = "<group>"; };
		18DD42C22D7C3EE700E809EC /* UIImage+MultiFormat.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+MultiFormat.m"; sourceTree = "<group>"; };
		18DD42C32D7C3EE700E809EC /* UIImage+Transform.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+Transform.h"; sourceTree = "<group>"; };
		18DD42C42D7C3EE700E809EC /* UIImage+Transform.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Transform.m"; sourceTree = "<group>"; };
		18DD42C52D7C3EE700E809EC /* UIImageView+HighlightedWebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImageView+HighlightedWebCache.h"; sourceTree = "<group>"; };
		18DD42C62D7C3EE700E809EC /* UIImageView+HighlightedWebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+HighlightedWebCache.m"; sourceTree = "<group>"; };
		18DD42C72D7C3EE700E809EC /* UIImageView+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImageView+WebCache.h"; sourceTree = "<group>"; };
		18DD42C82D7C3EE700E809EC /* UIImageView+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+WebCache.m"; sourceTree = "<group>"; };
		18DD42C92D7C3EE700E809EC /* UIView+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCache.h"; sourceTree = "<group>"; };
		18DD42CA2D7C3EE700E809EC /* UIView+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCache.m"; sourceTree = "<group>"; };
		18DD42CB2D7C3EE700E809EC /* UIView+WebCacheOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCacheOperation.h"; sourceTree = "<group>"; };
		18DD42CC2D7C3EE700E809EC /* UIView+WebCacheOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCacheOperation.m"; sourceTree = "<group>"; };
		18DD42CD2D7C3EE700E809EC /* UIView+WebCacheState.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCacheState.h"; sourceTree = "<group>"; };
		18DD42CE2D7C3EE700E809EC /* UIView+WebCacheState.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCacheState.m"; sourceTree = "<group>"; };
		18DD430F2D7C3EE700E809EC /* NSBezierPath+SDRoundedCorners.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSBezierPath+SDRoundedCorners.h"; sourceTree = "<group>"; };
		18DD43102D7C3EE700E809EC /* NSBezierPath+SDRoundedCorners.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSBezierPath+SDRoundedCorners.m"; sourceTree = "<group>"; };
		18DD43112D7C3EE700E809EC /* SDAssociatedObject.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAssociatedObject.h; sourceTree = "<group>"; };
		18DD43122D7C3EE700E809EC /* SDAssociatedObject.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAssociatedObject.m; sourceTree = "<group>"; };
		18DD43132D7C3EE700E809EC /* SDAsyncBlockOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAsyncBlockOperation.h; sourceTree = "<group>"; };
		18DD43142D7C3EE700E809EC /* SDAsyncBlockOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAsyncBlockOperation.m; sourceTree = "<group>"; };
		18DD43152D7C3EE700E809EC /* SDDeviceHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDDeviceHelper.h; sourceTree = "<group>"; };
		18DD43162D7C3EE700E809EC /* SDDeviceHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDDeviceHelper.m; sourceTree = "<group>"; };
		18DD43172D7C3EE700E809EC /* SDDisplayLink.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDDisplayLink.h; sourceTree = "<group>"; };
		18DD43182D7C3EE700E809EC /* SDDisplayLink.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDDisplayLink.m; sourceTree = "<group>"; };
		18DD43192D7C3EE700E809EC /* SDFileAttributeHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDFileAttributeHelper.h; sourceTree = "<group>"; };
		18DD431A2D7C3EE700E809EC /* SDFileAttributeHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDFileAttributeHelper.m; sourceTree = "<group>"; };
		18DD431B2D7C3EE700E809EC /* SDImageAssetManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageAssetManager.h; sourceTree = "<group>"; };
		18DD431C2D7C3EE700E809EC /* SDImageAssetManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageAssetManager.m; sourceTree = "<group>"; };
		18DD431D2D7C3EE700E809EC /* SDImageCachesManagerOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCachesManagerOperation.h; sourceTree = "<group>"; };
		18DD431E2D7C3EE700E809EC /* SDImageCachesManagerOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCachesManagerOperation.m; sourceTree = "<group>"; };
		18DD431F2D7C3EE700E809EC /* SDImageFramePool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageFramePool.h; sourceTree = "<group>"; };
		18DD43202D7C3EE700E809EC /* SDImageFramePool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageFramePool.m; sourceTree = "<group>"; };
		18DD43212D7C3EE700E809EC /* SDImageIOAnimatedCoderInternal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageIOAnimatedCoderInternal.h; sourceTree = "<group>"; };
		18DD43222D7C3EE700E809EC /* SDInternalMacros.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDInternalMacros.h; sourceTree = "<group>"; };
		18DD43232D7C3EE700E809EC /* SDInternalMacros.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDInternalMacros.m; sourceTree = "<group>"; };
		18DD43242D7C3EE700E809EC /* SDmetamacros.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDmetamacros.h; sourceTree = "<group>"; };
		18DD43252D7C3EE700E809EC /* SDWeakProxy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWeakProxy.h; sourceTree = "<group>"; };
		18DD43262D7C3EE700E809EC /* SDWeakProxy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWeakProxy.m; sourceTree = "<group>"; };
		18DD43272D7C3EE700E809EC /* SDWebImageTransitionInternal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageTransitionInternal.h; sourceTree = "<group>"; };
		18DD43282D7C3EE700E809EC /* UIColor+SDHexString.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIColor+SDHexString.h"; sourceTree = "<group>"; };
		18DD43292D7C3EE700E809EC /* UIColor+SDHexString.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIColor+SDHexString.m"; sourceTree = "<group>"; };
		18DD44D02D7C419A00E809EC /* XXGUIDriver.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGUIDriver.h; sourceTree = "<group>"; };
		18DD44D12D7C419A00E809EC /* XXGUIDriver.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGUIDriver.m; sourceTree = "<group>"; };
		18DD45122D7EC2E900E809EC /* UIImage+XXGImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+XXGImage.h"; sourceTree = "<group>"; };
		18DD45132D7EC2E900E809EC /* UIImage+XXGImage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+XXGImage.m"; sourceTree = "<group>"; };
		18DD45182D7EE51800E809EC /* XXGAccountViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGAccountViewController.h; sourceTree = "<group>"; };
		18DD45192D7EE51800E809EC /* XXGAccountViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGAccountViewController.m; sourceTree = "<group>"; };
		18DD451E2D7EE55700E809EC /* XXGMobileViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGMobileViewController.h; sourceTree = "<group>"; };
		18DD451F2D7EE55700E809EC /* XXGMobileViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGMobileViewController.m; sourceTree = "<group>"; };
		18DD45242D7EE57500E809EC /* XXGRegistViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGRegistViewController.h; sourceTree = "<group>"; };
		18DD45252D7EE57500E809EC /* XXGRegistViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGRegistViewController.m; sourceTree = "<group>"; };
		18DD452A2D7EE58500E809EC /* XXGSelectAccountViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGSelectAccountViewController.h; sourceTree = "<group>"; };
		18DD452B2D7EE58500E809EC /* XXGSelectAccountViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGSelectAccountViewController.m; sourceTree = "<group>"; };
		18DD45302D7EE59D00E809EC /* XXGForgetViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGForgetViewController.h; sourceTree = "<group>"; };
		18DD45312D7EE59D00E809EC /* XXGForgetViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGForgetViewController.m; sourceTree = "<group>"; };
		18DD45362D7EE5C200E809EC /* XXGChangeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGChangeViewController.h; sourceTree = "<group>"; };
		18DD45372D7EE5C200E809EC /* XXGChangeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGChangeViewController.m; sourceTree = "<group>"; };
		18DD453C2D7EE5D000E809EC /* XXGBindMobileViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGBindMobileViewController.h; sourceTree = "<group>"; };
		18DD453D2D7EE5D000E809EC /* XXGBindMobileViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGBindMobileViewController.m; sourceTree = "<group>"; };
		18DD45422D7EE5DE00E809EC /* XXGSelectPPViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGSelectPPViewController.h; sourceTree = "<group>"; };
		18DD45432D7EE5DE00E809EC /* XXGSelectPPViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGSelectPPViewController.m; sourceTree = "<group>"; };
		18DD45482D7EE5ED00E809EC /* XXGRealNameViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGRealNameViewController.h; sourceTree = "<group>"; };
		18DD45492D7EE5ED00E809EC /* XXGRealNameViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGRealNameViewController.m; sourceTree = "<group>"; };
		18DD454E2D7EE5F800E809EC /* XXGServiceViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGServiceViewController.h; sourceTree = "<group>"; };
		18DD454F2D7EE5F800E809EC /* XXGServiceViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGServiceViewController.m; sourceTree = "<group>"; };
		18DD45542D7EE61500E809EC /* XXGContentTextViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGContentTextViewController.h; sourceTree = "<group>"; };
		18DD45552D7EE61500E809EC /* XXGContentTextViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGContentTextViewController.m; sourceTree = "<group>"; };
		18DD455A2D7F0D2B00E809EC /* XXGProtocolLabel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGProtocolLabel.h; sourceTree = "<group>"; };
		18DD455B2D7F0D2B00E809EC /* XXGProtocolLabel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGProtocolLabel.m; sourceTree = "<group>"; };
		18DD45BB2D80518800E809EC /* XXGUIkitProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGUIkitProtocol.h; sourceTree = "<group>"; };
		18DD45C12D8053C000E809EC /* XXGLoadingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGLoadingView.h; sourceTree = "<group>"; };
		18DD45C22D8053C000E809EC /* XXGLoadingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGLoadingView.m; sourceTree = "<group>"; };
		18DD46722D816D1600E809EC /* XXGSelectAccountCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGSelectAccountCell.h; sourceTree = "<group>"; };
		18DD46732D816D1600E809EC /* XXGSelectAccountCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGSelectAccountCell.m; sourceTree = "<group>"; };
		18DD46792D81B43E00E809EC /* XXGWKBaseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGWKBaseViewController.h; sourceTree = "<group>"; };
		18DD467A2D81B43E00E809EC /* XXGWKBaseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGWKBaseViewController.m; sourceTree = "<group>"; };
		18DD467F2D81B4BE00E809EC /* XXGPopupViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGPopupViewController.h; sourceTree = "<group>"; };
		18DD46802D81B4BE00E809EC /* XXGPopupViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGPopupViewController.m; sourceTree = "<group>"; };
		18DD46852D82759300E809EC /* XXGFloatView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGFloatView.h; sourceTree = "<group>"; };
		18DD46862D82759300E809EC /* XXGFloatView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGFloatView.m; sourceTree = "<group>"; };
		18DD47BA2D850F2A00E809EC /* XXGLocalizedCore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGLocalizedCore.h; sourceTree = "<group>"; };
		18DD47BB2D850F2A00E809EC /* XXGLocalizedCore.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGLocalizedCore.m; sourceTree = "<group>"; };
		18DD47CA2D85237800E809EC /* XXGLocaleString.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGLocaleString.h; sourceTree = "<group>"; };
		18DD47CB2D85237800E809EC /* XXGLocaleString.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGLocaleString.m; sourceTree = "<group>"; };
		18DD47E32D8549F600E809EC /* XXGDatasCore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGDatasCore.h; sourceTree = "<group>"; };
		18DD47E42D8549F600E809EC /* XXGDatasCore.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGDatasCore.m; sourceTree = "<group>"; };
		18DD47E92D85600300E809EC /* XXGDatasModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGDatasModel.h; sourceTree = "<group>"; };
		18DD47EA2D85600300E809EC /* XXGDatasModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGDatasModel.m; sourceTree = "<group>"; };
		18DD47EF2D85603600E809EC /* XXGLocalizedModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGLocalizedModel.h; sourceTree = "<group>"; };
		18DD47F02D85603600E809EC /* XXGLocalizedModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGLocalizedModel.m; sourceTree = "<group>"; };
		18DD47F52D85736300E809EC /* XXGLocalizedUI.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGLocalizedUI.h; sourceTree = "<group>"; };
		18DD47F62D85736300E809EC /* XXGLocalizedUI.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGLocalizedUI.m; sourceTree = "<group>"; };
		18DD47FB2D8588B800E809EC /* XXGDatasUI.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGDatasUI.h; sourceTree = "<group>"; };
		18DD47FC2D8588B800E809EC /* XXGDatasUI.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGDatasUI.m; sourceTree = "<group>"; };
		18DD48072D86827900E809EC /* XXGTools.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGTools.h; sourceTree = "<group>"; };
		18DD48082D86827900E809EC /* XXGTools.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGTools.m; sourceTree = "<group>"; };
		18DD480D2D8686E700E809EC /* NSString+URLEncoding.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+URLEncoding.h"; sourceTree = "<group>"; };
		18DD480E2D8686E700E809EC /* NSString+URLEncoding.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+URLEncoding.m"; sourceTree = "<group>"; };
		18DD48132D86AF3700E809EC /* XXGSendCodeButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGSendCodeButton.h; sourceTree = "<group>"; };
		18DD48142D86AF3700E809EC /* XXGSendCodeButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGSendCodeButton.m; sourceTree = "<group>"; };
		18DD48192D86C81200E809EC /* XXGCountryCodeSelectorViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGCountryCodeSelectorViewController.h; sourceTree = "<group>"; };
		18DD481A2D86C81200E809EC /* XXGCountryCodeSelectorViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGCountryCodeSelectorViewController.m; sourceTree = "<group>"; };
		18DD48202D86C8C600E809EC /* XXGCountry.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGCountry.h; sourceTree = "<group>"; };
		18DD48212D86C8C600E809EC /* XXGCountry.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGCountry.m; sourceTree = "<group>"; };
		18DD48262D86D0E300E809EC /* XXGCountryCodeButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGCountryCodeButton.h; sourceTree = "<group>"; };
		18DD48272D86D0E300E809EC /* XXGCountryCodeButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGCountryCodeButton.m; sourceTree = "<group>"; };
		18DD482F2D86F35D00E809EC /* XXGToast.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGToast.h; sourceTree = "<group>"; };
		18DD48302D86F35D00E809EC /* XXGToast.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGToast.m; sourceTree = "<group>"; };
		18DD48352D87EA1900E809EC /* XXGUCenterViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGUCenterViewController.h; sourceTree = "<group>"; };
		18DD48362D87EA1900E809EC /* XXGUCenterViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGUCenterViewController.m; sourceTree = "<group>"; };
		18DD483B2D8831D100E809EC /* NSError+XXGIAPError.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSError+XXGIAPError.h"; sourceTree = "<group>"; };
		18DD483C2D8831D100E809EC /* NSError+XXGIAPError.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSError+XXGIAPError.m"; sourceTree = "<group>"; };
		18DD483E2D8831D100E809EC /* XXGIAPHelpManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGIAPHelpManager.h; sourceTree = "<group>"; };
		18DD483F2D8831D100E809EC /* XXGIAPHelpManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGIAPHelpManager.m; sourceTree = "<group>"; };
		18DD48402D8831D100E809EC /* XXGIAPVerifyManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGIAPVerifyManager.h; sourceTree = "<group>"; };
		18DD48412D8831D100E809EC /* XXGIAPVerifyManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGIAPVerifyManager.m; sourceTree = "<group>"; };
		18DD48432D8831D100E809EC /* XXGIAPTransactionModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGIAPTransactionModel.h; sourceTree = "<group>"; };
		18DD48442D8831D100E809EC /* XXGIAPTransactionModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGIAPTransactionModel.m; sourceTree = "<group>"; };
		18DD484C2D8831D100E809EC /* XXGIAPConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGIAPConfig.h; sourceTree = "<group>"; };
		18DD484D2D8831D100E809EC /* XXGIAPConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGIAPConfig.m; sourceTree = "<group>"; };
		18DD484E2D8831D100E809EC /* XXGIAPHelp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGIAPHelp.h; sourceTree = "<group>"; };
		18DD484F2D8831D100E809EC /* XXGIAPPayProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGIAPPayProtocol.h; sourceTree = "<group>"; };
		18DD48772D892A6600E809EC /* XXGIAPManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGIAPManager.h; sourceTree = "<group>"; };
		18DD48782D892A6600E809EC /* XXGIAPManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGIAPManager.m; sourceTree = "<group>"; };
		18DD487D2D892ECD00E809EC /* XXGProductBody.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGProductBody.h; sourceTree = "<group>"; };
		18DD487E2D892ECD00E809EC /* XXGProductBody.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGProductBody.m; sourceTree = "<group>"; };
		18DD48832D894CA600E809EC /* XXGSelectProductItem.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGSelectProductItem.h; sourceTree = "<group>"; };
		18DD48842D894CA600E809EC /* XXGSelectProductItem.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGSelectProductItem.m; sourceTree = "<group>"; };
		18DD48892D895D3D00E809EC /* XXGValidateReceiptBody.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGValidateReceiptBody.h; sourceTree = "<group>"; };
		18DD488A2D895D3D00E809EC /* XXGValidateReceiptBody.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGValidateReceiptBody.m; sourceTree = "<group>"; };
		18DD488F2D897DD400E809EC /* XXGRoleBody.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGRoleBody.h; sourceTree = "<group>"; };
		18DD48902D897DD400E809EC /* XXGRoleBody.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGRoleBody.m; sourceTree = "<group>"; };
		18E0642F2DC4DC4A00F15793 /* XXGPlayKitCore+Delegates.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XXGPlayKitCore+Delegates.h"; sourceTree = "<group>"; };
		18E064302DC4DC4A00F15793 /* XXGPlayKitCore+Delegates.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XXGPlayKitCore+Delegates.m"; sourceTree = "<group>"; };
		18E064352DC4E29900F15793 /* XXGPlayKitCore+Canal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XXGPlayKitCore+Canal.h"; sourceTree = "<group>"; };
		18E064362DC4E29900F15793 /* XXGPlayKitCore+Canal.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XXGPlayKitCore+Canal.m"; sourceTree = "<group>"; };
		18ED06FB2D715DF600C8804D /* XXGStartBody.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGStartBody.h; sourceTree = "<group>"; };
		18ED06FC2D715DF600C8804D /* XXGStartBody.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGStartBody.m; sourceTree = "<group>"; };
		18ED06FF2D715DF600C8804D /* XXGBaseURL.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGBaseURL.h; sourceTree = "<group>"; };
		18ED07002D715DF600C8804D /* XXGBaseURL.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGBaseURL.m; sourceTree = "<group>"; };
		18ED07012D715DF600C8804D /* XXGNetListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGNetListModel.h; sourceTree = "<group>"; };
		18ED07022D715DF600C8804D /* XXGNetListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGNetListModel.m; sourceTree = "<group>"; };
		18ED07032D715DF600C8804D /* XXGNetwork.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGNetwork.h; sourceTree = "<group>"; };
		18ED07042D715DF600C8804D /* XXGNetwork.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGNetwork.m; sourceTree = "<group>"; };
		18ED070C2D715DF600C8804D /* XXGPlayKitConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGPlayKitConfig.h; sourceTree = "<group>"; };
		18ED070D2D715DF600C8804D /* XXGPlayKitConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGPlayKitConfig.m; sourceTree = "<group>"; };
		18ED070E2D715DF600C8804D /* XXGPlayKitCore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGPlayKitCore.h; sourceTree = "<group>"; };
		18ED070F2D715DF600C8804D /* XXGPlayKitCore.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGPlayKitCore.m; sourceTree = "<group>"; };
		18ED07152D715DF600C8804D /* XXGNetworkCore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGNetworkCore.h; sourceTree = "<group>"; };
		18ED07162D715DF600C8804D /* XXGNetworkCore.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGNetworkCore.m; sourceTree = "<group>"; };
		18ED07172D715DF600C8804D /* XXGNetworkMonitor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGNetworkMonitor.h; sourceTree = "<group>"; };
		18ED07182D715DF600C8804D /* XXGNetworkMonitor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGNetworkMonitor.m; sourceTree = "<group>"; };
		18ED071A2D715DF600C8804D /* UICKeyChainStore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UICKeyChainStore.h; sourceTree = "<group>"; };
		18ED071B2D715DF600C8804D /* UICKeyChainStore.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UICKeyChainStore.m; sourceTree = "<group>"; };
		18ED071D2D715DF600C8804D /* XXGAlertView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGAlertView.h; sourceTree = "<group>"; };
		18ED071E2D715DF600C8804D /* XXGAlertView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGAlertView.m; sourceTree = "<group>"; };
		18ED071F2D715DF600C8804D /* XXGWindowManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGWindowManager.h; sourceTree = "<group>"; };
		18ED07202D715DF600C8804D /* XXGWindowManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGWindowManager.m; sourceTree = "<group>"; };
		18ED07222D715DF600C8804D /* NSObject+XXGModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSObject+XXGModel.h"; sourceTree = "<group>"; };
		18ED07232D715DF600C8804D /* NSObject+XXGModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSObject+XXGModel.m"; sourceTree = "<group>"; };
		18ED07242D715DF600C8804D /* NSString+XXGString.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+XXGString.h"; sourceTree = "<group>"; };
		18ED07252D715DF600C8804D /* NSString+XXGString.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+XXGString.m"; sourceTree = "<group>"; };
		18ED07282D715DF600C8804D /* XXGAppInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGAppInfo.h; sourceTree = "<group>"; };
		18ED07292D715DF600C8804D /* XXGAppInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGAppInfo.m; sourceTree = "<group>"; };
		18ED072A2D715DF600C8804D /* XXGSecurityCheckTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGSecurityCheckTool.h; sourceTree = "<group>"; };
		18ED072B2D715DF600C8804D /* XXGSecurityCheckTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGSecurityCheckTool.m; sourceTree = "<group>"; };
		18ED072E2D715DF600C8804D /* XXGSetting.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGSetting.h; sourceTree = "<group>"; };
		18ED072F2D715DF600C8804D /* XXGSetting.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGSetting.m; sourceTree = "<group>"; };
		18ED07BE2D71616E00C8804D /* WantedAdhesive.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = WantedAdhesive.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		18ED07B92D71616E00C8804D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1812EAD12D9163A400B7BB73 /* Its */ = {
			isa = PBXGroup;
			children = (
				1812ED852D929A7300B7BB73 /* XXGShanYanManager.h */,
				1812ED862D929A7300B7BB73 /* XXGShanYanManager.m */,
				18253AFE2D9AB61700BEA16C /* XXGBDASignalManager.h */,
				18253AFF2D9AB61700BEA16C /* XXGBDASignalManager.m */,
			);
			path = Its;
			sourceTree = "<group>";
		};
		181E79832D77EEB6009CF2CC /* Protein */ = {
			isa = PBXGroup;
			children = (
				1812EAD12D9163A400B7BB73 /* Its */,
				182C6D832D8D47B400D3F530 /* Use */,
				182C6D982D8D565B00D3F530 /* XXGThirdManager.h */,
				182C6D992D8D565B00D3F530 /* XXGThirdManager.m */,
				181E79842D77EED2009CF2CC /* XXGBoxManager.h */,
				181E79852D77EED2009CF2CC /* XXGBoxManager.m */,
				18DD48772D892A6600E809EC /* XXGIAPManager.h */,
				18DD48782D892A6600E809EC /* XXGIAPManager.m */,
				18DA0D322D8BB826003C4467 /* XXGMQTTManager.h */,
				18DA0D332D8BB826003C4467 /* XXGMQTTManager.m */,
			);
			path = Protein;
			sourceTree = "<group>";
		};
		182C6D832D8D47B400D3F530 /* Use */ = {
			isa = PBXGroup;
			children = (
				182C6D842D8D47C200D3F530 /* XXGFacebookManager.h */,
				182C6D852D8D47C200D3F530 /* XXGFacebookManager.m */,
				18401FA82D8D8A51005D10BB /* XXGAppsFlyerManager.h */,
				18401FA92D8D8A51005D10BB /* XXGAppsFlyerManager.m */,
				18401FB62D8E9506005D10BB /* XXGFirebaseManager.h */,
				18401FB72D8E9506005D10BB /* XXGFirebaseManager.m */,
				186AE8EB2D942364000F1A11 /* XXGVKManager.h */,
				186AE8EC2D942364000F1A11 /* XXGVKManager.m */,
				186AE9D02D96C244000F1A11 /* XXGAdjustManager.h */,
				186AE9D12D96C244000F1A11 /* XXGAdjustManager.m */,
				182F6CA52DC28C660039594F /* XXGPoopoManager.h */,
				182F6CA62DC28C660039594F /* XXGPoopoManager.m */,
				1811E3412DCC528B00B17C42 /* XXGAppLovinManager.h */,
				1811E3422DCC528B00B17C42 /* XXGAppLovinManager.m */,
			);
			path = Use;
			sourceTree = "<group>";
		};
		1849B9762D7AE5F000A5F887 /* Taps */ = {
			isa = PBXGroup;
			children = (
				188F7F192DA7F00F00699DF4 /* XXGOrientationViewController.h */,
				188F7F1A2DA7F00F00699DF4 /* XXGOrientationViewController.m */,
				1849B9772D7AE9A800A5F887 /* XXGBaseViewController.h */,
				1849B9782D7AE9A800A5F887 /* XXGBaseViewController.m */,
				1849B9832D7AEA3B00A5F887 /* XXGNavigationController.h */,
				1849B9842D7AEA3B00A5F887 /* XXGNavigationController.m */,
				18ED071F2D715DF600C8804D /* XXGWindowManager.h */,
				18ED07202D715DF600C8804D /* XXGWindowManager.m */,
				18DD44D02D7C419A00E809EC /* XXGUIDriver.h */,
				18DD44D12D7C419A00E809EC /* XXGUIDriver.m */,
				18DD45BB2D80518800E809EC /* XXGUIkitProtocol.h */,
				18DD47F52D85736300E809EC /* XXGLocalizedUI.h */,
				18DD47F62D85736300E809EC /* XXGLocalizedUI.m */,
				18DD47FB2D8588B800E809EC /* XXGDatasUI.h */,
				18DD47FC2D8588B800E809EC /* XXGDatasUI.m */,
			);
			path = Taps;
			sourceTree = "<group>";
		};
		186373362D7164A2000D82AE /* NetRows */ = {
			isa = PBXGroup;
			children = (
				18989DEB2D71C39D00E11C3B /* XXGPlayKitCN.h */,
				182F66E62DC09A750039594F /* XXGPlayKitCN.m */,
				1812EACD2D915ECA00B7BB73 /* XXGPlayCN.h */,
				1812EACE2D915ECA00B7BB73 /* XXGPlayCN.m */,
			);
			path = NetRows;
			sourceTree = "<group>";
		};
		1863733B2D7164A9000D82AE /* DidMost */ = {
			isa = PBXGroup;
			children = (
				18989DE92D71C35A00E11C3B /* WantedAdhesive.h */,
				182F6C172DC0F9FB0039594F /* WantedAdhesive.m */,
				186373392D7164A9000D82AE /* XXGPlayOS.h */,
				1863733A2D7164A9000D82AE /* XXGPlayOS.m */,
			);
			path = DidMost;
			sourceTree = "<group>";
		};
		1886C5F52DE44250006C3D99 /* LateCapWalk */ = {
			isa = PBXGroup;
			children = (
				1886C6022DE44C4D006C3D99 /* XXGDebugger.h */,
				1886C6032DE44C4D006C3D99 /* XXGDebugger.m */,
			);
			path = LateCapWalk;
			sourceTree = "<group>";
		};
		1886C6B02DE6E820006C3D99 /* AngularNoneBatchAllowableCircular */ = {
			isa = PBXGroup;
			children = (
				1886C6DB2DE6F77F006C3D99 /* ZBBaseDestination.h */,
				1886C6DC2DE6F77F006C3D99 /* ZBBaseDestination.m */,
				1886C6DD2DE6F77F006C3D99 /* ZBConsoleDestinatioin.h */,
				1886C6DE2DE6F77F006C3D99 /* ZBConsoleDestinatioin.m */,
				1886C6DF2DE6F77F006C3D99 /* ZBFileDestination.h */,
				1886C6E02DE6F77F006C3D99 /* ZBFileDestination.m */,
				1886C6E12DE6F77F006C3D99 /* ZBLog.h */,
				1886C6E22DE6F77F006C3D99 /* ZBLog.m */,
				1886C6E32DE6F77F006C3D99 /* ZBLogFormatter.h */,
				1886C6E42DE6F77F006C3D99 /* ZBLogFormatter.m */,
				1886C6E52DE6F77F006C3D99 /* ZBLogMacros.h */,
				1886C6E62DE6F77F006C3D99 /* ZBLogViewController.h */,
				1886C6E72DE6F77F006C3D99 /* ZBLogViewController.m */,
				1886C6E82DE6F77F006C3D99 /* ZBObjectiveCBeaver.h */,
			);
			path = AngularNoneBatchAllowableCircular;
			sourceTree = "<group>";
		};
		188F7F122DA7EF6600699DF4 /* NotRadixHead */ = {
			isa = PBXGroup;
			children = (
				18DD46852D82759300E809EC /* XXGFloatView.h */,
				18DD46862D82759300E809EC /* XXGFloatView.m */,
				188F7F132DA7EF9C00699DF4 /* XXGTransparentWindow.h */,
				188F7F142DA7EF9C00699DF4 /* XXGTransparentWindow.m */,
			);
			path = NotRadixHead;
			sourceTree = "<group>";
		};
		18989F2D2D72AB2B00E11C3B /* TraitOwn */ = {
			isa = PBXGroup;
			children = (
				18ED075E2D715E4500C8804D /* AllLaw */,
				18ED07102D715DF600C8804D /* MinTied */,
				18ED06FE2D715DF600C8804D /* EarBag */,
				18ED07052D715DF600C8804D /* EndBlue */,
				181E79832D77EEB6009CF2CC /* Protein */,
			);
			path = TraitOwn;
			sourceTree = "<group>";
		};
		18989F342D72C12300E11C3B /* BrushSub */ = {
			isa = PBXGroup;
			children = (
				18ED07222D715DF600C8804D /* NSObject+XXGModel.h */,
				18ED07232D715DF600C8804D /* NSObject+XXGModel.m */,
				18ED07242D715DF600C8804D /* NSString+XXGString.h */,
				18ED07252D715DF600C8804D /* NSString+XXGString.m */,
				18989F2E2D72C10C00E11C3B /* UIColor+XXGColor.h */,
				18989F2F2D72C10C00E11C3B /* UIColor+XXGColor.m */,
				1849B9892D7AED6D00A5F887 /* UIViewController+XXGViewController.h */,
				1849B98A2D7AED6D00A5F887 /* UIViewController+XXGViewController.m */,
				18DD45122D7EC2E900E809EC /* UIImage+XXGImage.h */,
				18DD45132D7EC2E900E809EC /* UIImage+XXGImage.m */,
				18DD480D2D8686E700E809EC /* NSString+URLEncoding.h */,
				18DD480E2D8686E700E809EC /* NSString+URLEncoding.m */,
				182C6D9F2D8D61C100D3F530 /* NSObject+XXGPerformSelector.h */,
				182C6DA02D8D61C100D3F530 /* NSObject+XXGPerformSelector.m */,
				1812EABF2D91593100B7BB73 /* NSURL+XXGAnalyse.h */,
				1812EAC02D91593100B7BB73 /* NSURL+XXGAnalyse.m */,
				188F80032DACB95200699DF4 /* NSString+XXGMd5.h */,
				188F80042DACB95200699DF4 /* NSString+XXGMd5.m */,
				188F800F2DACBA3500699DF4 /* NSData+SunHope.h */,
				188F80102DACBA3500699DF4 /* NSData+SunHope.m */,
				182F66952DC090A90039594F /* UIDevice+XXGDevice.h */,
				182F66962DC090A90039594F /* UIDevice+XXGDevice.m */,
			);
			path = BrushSub;
			sourceTree = "<group>";
		};
		18DA0CD52D8BB02E003C4467 /* MQTTClient */ = {
			isa = PBXGroup;
			children = (
				18DA0CA72D8BB02E003C4467 /* ForegroundReconnection.h */,
				18DA0CA82D8BB02E003C4467 /* ForegroundReconnection.m */,
				18DA0CA92D8BB02E003C4467 /* GCDTimer.h */,
				18DA0CAA2D8BB02E003C4467 /* GCDTimer.m */,
				18DA0CAB2D8BB02E003C4467 /* MQTTCFSocketDecoder.h */,
				18DA0CAC2D8BB02E003C4467 /* MQTTCFSocketDecoder.m */,
				18DA0CAD2D8BB02E003C4467 /* MQTTCFSocketEncoder.h */,
				18DA0CAE2D8BB02E003C4467 /* MQTTCFSocketEncoder.m */,
				18DA0CAF2D8BB02E003C4467 /* MQTTCFSocketTransport.h */,
				18DA0CB02D8BB02E003C4467 /* MQTTCFSocketTransport.m */,
				18DA0CB12D8BB02E003C4467 /* MQTTClient.h */,
				18DA0CB22D8BB02E003C4467 /* MQTTCoreDataPersistence.h */,
				18DA0CB32D8BB02E003C4467 /* MQTTCoreDataPersistence.m */,
				18DA0CB42D8BB02E003C4467 /* MQTTDecoder.h */,
				18DA0CB52D8BB02E003C4467 /* MQTTDecoder.m */,
				18DA0CB62D8BB02E003C4467 /* MQTTInMemoryPersistence.h */,
				18DA0CB72D8BB02E003C4467 /* MQTTInMemoryPersistence.m */,
				18DA0CB82D8BB02E003C4467 /* MQTTLog.h */,
				18DA0CB92D8BB02E003C4467 /* MQTTLog.m */,
				18DA0CBA2D8BB02E003C4467 /* MQTTMessage.h */,
				18DA0CBB2D8BB02E003C4467 /* MQTTMessage.m */,
				18DA0CBC2D8BB02E003C4467 /* MQTTPersistence.h */,
				18DA0CBD2D8BB02E003C4467 /* MQTTProperties.h */,
				18DA0CBE2D8BB02E003C4467 /* MQTTProperties.m */,
				18DA0CBF2D8BB02E003C4467 /* MQTTSession.h */,
				18DA0CC02D8BB02E003C4467 /* MQTTSession.m */,
				18DA0CC12D8BB02E003C4467 /* MQTTSessionLegacy.h */,
				18DA0CC22D8BB02E003C4467 /* MQTTSessionLegacy.m */,
				18DA0CC32D8BB02E003C4467 /* MQTTSessionManager.h */,
				18DA0CC42D8BB02E003C4467 /* MQTTSessionManager.m */,
				18DA0CC52D8BB02E003C4467 /* MQTTSessionSynchron.h */,
				18DA0CC62D8BB02E003C4467 /* MQTTSessionSynchron.m */,
				18DA0CC72D8BB02E003C4467 /* MQTTSSLSecurityPolicy.h */,
				18DA0CC82D8BB02E003C4467 /* MQTTSSLSecurityPolicy.m */,
				18DA0CC92D8BB02E003C4467 /* MQTTSSLSecurityPolicyDecoder.h */,
				18DA0CCA2D8BB02E003C4467 /* MQTTSSLSecurityPolicyDecoder.m */,
				18DA0CCB2D8BB02E003C4467 /* MQTTSSLSecurityPolicyEncoder.h */,
				18DA0CCC2D8BB02E003C4467 /* MQTTSSLSecurityPolicyEncoder.m */,
				18DA0CCD2D8BB02E003C4467 /* MQTTSSLSecurityPolicyTransport.h */,
				18DA0CCE2D8BB02E003C4467 /* MQTTSSLSecurityPolicyTransport.m */,
				18DA0CCF2D8BB02E003C4467 /* MQTTStrict.h */,
				18DA0CD02D8BB02E003C4467 /* MQTTStrict.m */,
				18DA0CD12D8BB02E003C4467 /* MQTTTransportProtocol.h */,
				18DA0CD22D8BB02E003C4467 /* MQTTTransportProtocol.m */,
				18DA0CD32D8BB02E003C4467 /* ReconnectTimer.h */,
				18DA0CD42D8BB02E003C4467 /* ReconnectTimer.m */,
			);
			path = MQTTClient;
			sourceTree = "<group>";
		};
		18DA0E7A2D8C16C7003C4467 /* ClipEchoYou */ = {
			isa = PBXGroup;
			children = (
				18DA0E832D8C1A9E003C4467 /* XXGMarqueeView.h */,
				18DA0E842D8C1A9E003C4467 /* XXGMarqueeView.m */,
				18DA0E772D8C16C7003C4467 /* XXGLiveBarrageCell.h */,
				18DA0E782D8C16C7003C4467 /* XXGLiveBarrageCell.m */,
				18DA0E762D8C16C7003C4467 /* XXGLiveBarrage.h */,
				18DA0E792D8C16C7003C4467 /* XXGLiveBarrage.m */,
				188F7F0C2DA7EEAC00699DF4 /* XXGMarqueeViewCell.h */,
				188F7F0D2DA7EEAC00699DF4 /* XXGMarqueeViewCell.m */,
			);
			path = ClipEchoYou;
			sourceTree = "<group>";
		};
		18DD42002D7BFAC300E809EC /* ArrayUtterance */ = {
			isa = PBXGroup;
			children = (
				1849B9E92D7B022600A5F887 /* XXGComeinViewController.h */,
				1849B9EA2D7B022600A5F887 /* XXGComeinViewController.m */,
				18DD45182D7EE51800E809EC /* XXGAccountViewController.h */,
				18DD45192D7EE51800E809EC /* XXGAccountViewController.m */,
				18DD451E2D7EE55700E809EC /* XXGMobileViewController.h */,
				18DD451F2D7EE55700E809EC /* XXGMobileViewController.m */,
				18DD45242D7EE57500E809EC /* XXGRegistViewController.h */,
				18DD45252D7EE57500E809EC /* XXGRegistViewController.m */,
				18DD452A2D7EE58500E809EC /* XXGSelectAccountViewController.h */,
				18DD452B2D7EE58500E809EC /* XXGSelectAccountViewController.m */,
				18DD45302D7EE59D00E809EC /* XXGForgetViewController.h */,
				18DD45312D7EE59D00E809EC /* XXGForgetViewController.m */,
				18DD45362D7EE5C200E809EC /* XXGChangeViewController.h */,
				18DD45372D7EE5C200E809EC /* XXGChangeViewController.m */,
				18DD453C2D7EE5D000E809EC /* XXGBindMobileViewController.h */,
				18DD453D2D7EE5D000E809EC /* XXGBindMobileViewController.m */,
				18DD45422D7EE5DE00E809EC /* XXGSelectPPViewController.h */,
				18DD45432D7EE5DE00E809EC /* XXGSelectPPViewController.m */,
				18DD45482D7EE5ED00E809EC /* XXGRealNameViewController.h */,
				18DD45492D7EE5ED00E809EC /* XXGRealNameViewController.m */,
				18DD454E2D7EE5F800E809EC /* XXGServiceViewController.h */,
				18DD454F2D7EE5F800E809EC /* XXGServiceViewController.m */,
				18DD45542D7EE61500E809EC /* XXGContentTextViewController.h */,
				18DD45552D7EE61500E809EC /* XXGContentTextViewController.m */,
				188F7C622D9E206D00699DF4 /* XXGSaveNamePSViewController.h */,
				188F7C632D9E206D00699DF4 /* XXGSaveNamePSViewController.m */,
				182F69CF2DC0AB150039594F /* XXGAppInfoViewController.h */,
				182F69D02DC0AB150039594F /* XXGAppInfoViewController.m */,
			);
			path = ArrayUtterance;
			sourceTree = "<group>";
		};
		18DD42012D7BFADC00E809EC /* Race */ = {
			isa = PBXGroup;
			children = (
				18DD481F2D86C87300E809EC /* ProceedSoloist */,
				188F7F122DA7EF6600699DF4 /* NotRadixHead */,
				18DA0E7A2D8C16C7003C4467 /* ClipEchoYou */,
				18ED071D2D715DF600C8804D /* XXGAlertView.h */,
				18ED071E2D715DF600C8804D /* XXGAlertView.m */,
				18DD455A2D7F0D2B00E809EC /* XXGProtocolLabel.h */,
				18DD455B2D7F0D2B00E809EC /* XXGProtocolLabel.m */,
				18DD45C12D8053C000E809EC /* XXGLoadingView.h */,
				18DD45C22D8053C000E809EC /* XXGLoadingView.m */,
				18DD46722D816D1600E809EC /* XXGSelectAccountCell.h */,
				18DD46732D816D1600E809EC /* XXGSelectAccountCell.m */,
				18DD48132D86AF3700E809EC /* XXGSendCodeButton.h */,
				18DD48142D86AF3700E809EC /* XXGSendCodeButton.m */,
				18DD482F2D86F35D00E809EC /* XXGToast.h */,
				18DD48302D86F35D00E809EC /* XXGToast.m */,
				18DA0BF82D8A6B1D003C4467 /* XXGSelectPCell.h */,
				18DA0BF92D8A6B1D003C4467 /* XXGSelectPCell.m */,
				188F7C562D9BBB5400699DF4 /* XXGMobileTextField.h */,
				188F7C572D9BBB5400699DF4 /* XXGMobileTextField.m */,
			);
			path = Race;
			sourceTree = "<group>";
		};
		18DD42222D7C2B1F00E809EC /* Masonry */ = {
			isa = PBXGroup;
			children = (
				18DD42092D7C2B1F00E809EC /* MASCompositeConstraint.h */,
				18DD420A2D7C2B1F00E809EC /* MASCompositeConstraint.m */,
				18DD420B2D7C2B1F00E809EC /* MASConstraint.h */,
				18DD420C2D7C2B1F00E809EC /* MASConstraint.m */,
				18DD420D2D7C2B1F00E809EC /* MASConstraint+Private.h */,
				18DD420E2D7C2B1F00E809EC /* MASConstraintMaker.h */,
				18DD420F2D7C2B1F00E809EC /* MASConstraintMaker.m */,
				18DD42102D7C2B1F00E809EC /* MASLayoutConstraint.h */,
				18DD42112D7C2B1F00E809EC /* MASLayoutConstraint.m */,
				18DD42122D7C2B1F00E809EC /* Masonry.h */,
				18DD42132D7C2B1F00E809EC /* MASUtilities.h */,
				18DD42142D7C2B1F00E809EC /* MASViewAttribute.h */,
				18DD42152D7C2B1F00E809EC /* MASViewAttribute.m */,
				18DD42162D7C2B1F00E809EC /* MASViewConstraint.h */,
				18DD42172D7C2B1F00E809EC /* MASViewConstraint.m */,
				18DD42182D7C2B1F00E809EC /* NSArray+MASAdditions.h */,
				18DD42192D7C2B1F00E809EC /* NSArray+MASAdditions.m */,
				18DD421A2D7C2B1F00E809EC /* NSArray+MASShorthandAdditions.h */,
				18DD421B2D7C2B1F00E809EC /* NSLayoutConstraint+MASDebugAdditions.h */,
				18DD421C2D7C2B1F00E809EC /* NSLayoutConstraint+MASDebugAdditions.m */,
				18DD421D2D7C2B1F00E809EC /* View+MASAdditions.h */,
				18DD421E2D7C2B1F00E809EC /* View+MASAdditions.m */,
				18DD421F2D7C2B1F00E809EC /* View+MASShorthandAdditions.h */,
				18DD42202D7C2B1F00E809EC /* ViewController+MASAdditions.h */,
				18DD42212D7C2B1F00E809EC /* ViewController+MASAdditions.m */,
			);
			path = Masonry;
			sourceTree = "<group>";
		};
		18DD42CF2D7C3EE700E809EC /* Core */ = {
			isa = PBXGroup;
			children = (
				18DD42572D7C3EE700E809EC /* NSButton+WebCache.h */,
				18DD42582D7C3EE700E809EC /* NSButton+WebCache.m */,
				18DD42592D7C3EE700E809EC /* NSData+ImageContentType.h */,
				18DD425A2D7C3EE700E809EC /* NSData+ImageContentType.m */,
				18DD425B2D7C3EE700E809EC /* NSImage+Compatibility.h */,
				18DD425C2D7C3EE700E809EC /* NSImage+Compatibility.m */,
				18DD425D2D7C3EE700E809EC /* SDAnimatedImage.h */,
				18DD425E2D7C3EE700E809EC /* SDAnimatedImage.m */,
				18DD425F2D7C3EE700E809EC /* SDAnimatedImagePlayer.h */,
				18DD42602D7C3EE700E809EC /* SDAnimatedImagePlayer.m */,
				18DD42612D7C3EE700E809EC /* SDAnimatedImageRep.h */,
				18DD42622D7C3EE700E809EC /* SDAnimatedImageRep.m */,
				18DD42632D7C3EE700E809EC /* SDAnimatedImageView.h */,
				18DD42642D7C3EE700E809EC /* SDAnimatedImageView.m */,
				18DD42652D7C3EE700E809EC /* SDAnimatedImageView+WebCache.h */,
				18DD42662D7C3EE700E809EC /* SDAnimatedImageView+WebCache.m */,
				18DD42672D7C3EE700E809EC /* SDCallbackQueue.h */,
				18DD42682D7C3EE700E809EC /* SDCallbackQueue.m */,
				18DD42692D7C3EE700E809EC /* SDDiskCache.h */,
				18DD426A2D7C3EE700E809EC /* SDDiskCache.m */,
				18DD426B2D7C3EE700E809EC /* SDGraphicsImageRenderer.h */,
				18DD426C2D7C3EE700E809EC /* SDGraphicsImageRenderer.m */,
				18DD426D2D7C3EE700E809EC /* SDImageAPNGCoder.h */,
				18DD426E2D7C3EE700E809EC /* SDImageAPNGCoder.m */,
				18DD426F2D7C3EE700E809EC /* SDImageAWebPCoder.h */,
				18DD42702D7C3EE700E809EC /* SDImageAWebPCoder.m */,
				18DD42712D7C3EE700E809EC /* SDImageCache.h */,
				18DD42722D7C3EE700E809EC /* SDImageCache.m */,
				18DD42732D7C3EE700E809EC /* SDImageCacheConfig.h */,
				18DD42742D7C3EE700E809EC /* SDImageCacheConfig.m */,
				18DD42752D7C3EE700E809EC /* SDImageCacheDefine.h */,
				18DD42762D7C3EE700E809EC /* SDImageCacheDefine.m */,
				18DD42772D7C3EE700E809EC /* SDImageCachesManager.h */,
				18DD42782D7C3EE700E809EC /* SDImageCachesManager.m */,
				18DD42792D7C3EE700E809EC /* SDImageCoder.h */,
				18DD427A2D7C3EE700E809EC /* SDImageCoder.m */,
				18DD427B2D7C3EE700E809EC /* SDImageCoderHelper.h */,
				18DD427C2D7C3EE700E809EC /* SDImageCoderHelper.m */,
				18DD427D2D7C3EE700E809EC /* SDImageCodersManager.h */,
				18DD427E2D7C3EE700E809EC /* SDImageCodersManager.m */,
				18DD427F2D7C3EE700E809EC /* SDImageFrame.h */,
				18DD42802D7C3EE700E809EC /* SDImageFrame.m */,
				18DD42812D7C3EE700E809EC /* SDImageGIFCoder.h */,
				18DD42822D7C3EE700E809EC /* SDImageGIFCoder.m */,
				18DD42832D7C3EE700E809EC /* SDImageGraphics.h */,
				18DD42842D7C3EE700E809EC /* SDImageGraphics.m */,
				18DD42852D7C3EE700E809EC /* SDImageHEICCoder.h */,
				18DD42862D7C3EE700E809EC /* SDImageHEICCoder.m */,
				18DD42872D7C3EE700E809EC /* SDImageIOAnimatedCoder.h */,
				18DD42882D7C3EE700E809EC /* SDImageIOAnimatedCoder.m */,
				18DD42892D7C3EE700E809EC /* SDImageIOCoder.h */,
				18DD428A2D7C3EE700E809EC /* SDImageIOCoder.m */,
				18DD428B2D7C3EE700E809EC /* SDImageLoader.h */,
				18DD428C2D7C3EE700E809EC /* SDImageLoader.m */,
				18DD428D2D7C3EE700E809EC /* SDImageLoadersManager.h */,
				18DD428E2D7C3EE700E809EC /* SDImageLoadersManager.m */,
				18DD428F2D7C3EE700E809EC /* SDImageTransformer.h */,
				18DD42902D7C3EE700E809EC /* SDImageTransformer.m */,
				18DD42912D7C3EE700E809EC /* SDMemoryCache.h */,
				18DD42922D7C3EE700E809EC /* SDMemoryCache.m */,
				18DD42932D7C3EE700E809EC /* SDWebImageCacheKeyFilter.h */,
				18DD42942D7C3EE700E809EC /* SDWebImageCacheKeyFilter.m */,
				18DD42952D7C3EE700E809EC /* SDWebImageCacheSerializer.h */,
				18DD42962D7C3EE700E809EC /* SDWebImageCacheSerializer.m */,
				18DD42972D7C3EE700E809EC /* SDWebImageCompat.h */,
				18DD42982D7C3EE700E809EC /* SDWebImageCompat.m */,
				18DD42992D7C3EE700E809EC /* SDWebImageDefine.h */,
				18DD429A2D7C3EE700E809EC /* SDWebImageDefine.m */,
				18DD429B2D7C3EE700E809EC /* SDWebImageDownloader.h */,
				18DD429C2D7C3EE700E809EC /* SDWebImageDownloader.m */,
				18DD429D2D7C3EE700E809EC /* SDWebImageDownloaderConfig.h */,
				18DD429E2D7C3EE700E809EC /* SDWebImageDownloaderConfig.m */,
				18DD429F2D7C3EE700E809EC /* SDWebImageDownloaderDecryptor.h */,
				18DD42A02D7C3EE700E809EC /* SDWebImageDownloaderDecryptor.m */,
				18DD42A12D7C3EE700E809EC /* SDWebImageDownloaderOperation.h */,
				18DD42A22D7C3EE700E809EC /* SDWebImageDownloaderOperation.m */,
				18DD42A32D7C3EE700E809EC /* SDWebImageDownloaderRequestModifier.h */,
				18DD42A42D7C3EE700E809EC /* SDWebImageDownloaderRequestModifier.m */,
				18DD42A52D7C3EE700E809EC /* SDWebImageDownloaderResponseModifier.h */,
				18DD42A62D7C3EE700E809EC /* SDWebImageDownloaderResponseModifier.m */,
				18DD42A72D7C3EE700E809EC /* SDWebImageError.h */,
				18DD42A82D7C3EE700E809EC /* SDWebImageError.m */,
				18DD42A92D7C3EE700E809EC /* SDWebImageIndicator.h */,
				18DD42AA2D7C3EE700E809EC /* SDWebImageIndicator.m */,
				18DD42AB2D7C3EE700E809EC /* SDWebImageManager.h */,
				18DD42AC2D7C3EE700E809EC /* SDWebImageManager.m */,
				18DD42AD2D7C3EE700E809EC /* SDWebImageOperation.h */,
				18DD42AE2D7C3EE700E809EC /* SDWebImageOperation.m */,
				18DD42AF2D7C3EE700E809EC /* SDWebImageOptionsProcessor.h */,
				18DD42B02D7C3EE700E809EC /* SDWebImageOptionsProcessor.m */,
				18DD42B12D7C3EE700E809EC /* SDWebImagePrefetcher.h */,
				18DD42B22D7C3EE700E809EC /* SDWebImagePrefetcher.m */,
				18DD42B32D7C3EE700E809EC /* SDWebImageTransition.h */,
				18DD42B42D7C3EE700E809EC /* SDWebImageTransition.m */,
				18DD42B52D7C3EE700E809EC /* UIButton+WebCache.h */,
				18DD42B62D7C3EE700E809EC /* UIButton+WebCache.m */,
				18DD42B72D7C3EE700E809EC /* UIImage+ExtendedCacheData.h */,
				18DD42B82D7C3EE700E809EC /* UIImage+ExtendedCacheData.m */,
				18DD42B92D7C3EE700E809EC /* UIImage+ForceDecode.h */,
				18DD42BA2D7C3EE700E809EC /* UIImage+ForceDecode.m */,
				18DD42BB2D7C3EE700E809EC /* UIImage+GIF.h */,
				18DD42BC2D7C3EE700E809EC /* UIImage+GIF.m */,
				18DD42BD2D7C3EE700E809EC /* UIImage+MemoryCacheCost.h */,
				18DD42BE2D7C3EE700E809EC /* UIImage+MemoryCacheCost.m */,
				18DD42BF2D7C3EE700E809EC /* UIImage+Metadata.h */,
				18DD42C02D7C3EE700E809EC /* UIImage+Metadata.m */,
				18DD42C12D7C3EE700E809EC /* UIImage+MultiFormat.h */,
				18DD42C22D7C3EE700E809EC /* UIImage+MultiFormat.m */,
				18DD42C32D7C3EE700E809EC /* UIImage+Transform.h */,
				18DD42C42D7C3EE700E809EC /* UIImage+Transform.m */,
				18DD42C52D7C3EE700E809EC /* UIImageView+HighlightedWebCache.h */,
				18DD42C62D7C3EE700E809EC /* UIImageView+HighlightedWebCache.m */,
				18DD42C72D7C3EE700E809EC /* UIImageView+WebCache.h */,
				18DD42C82D7C3EE700E809EC /* UIImageView+WebCache.m */,
				18DD42C92D7C3EE700E809EC /* UIView+WebCache.h */,
				18DD42CA2D7C3EE700E809EC /* UIView+WebCache.m */,
				18DD42CB2D7C3EE700E809EC /* UIView+WebCacheOperation.h */,
				18DD42CC2D7C3EE700E809EC /* UIView+WebCacheOperation.m */,
				18DD42CD2D7C3EE700E809EC /* UIView+WebCacheState.h */,
				18DD42CE2D7C3EE700E809EC /* UIView+WebCacheState.m */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		18DD432A2D7C3EE700E809EC /* Private */ = {
			isa = PBXGroup;
			children = (
				18DD430F2D7C3EE700E809EC /* NSBezierPath+SDRoundedCorners.h */,
				18DD43102D7C3EE700E809EC /* NSBezierPath+SDRoundedCorners.m */,
				18DD43112D7C3EE700E809EC /* SDAssociatedObject.h */,
				18DD43122D7C3EE700E809EC /* SDAssociatedObject.m */,
				18DD43132D7C3EE700E809EC /* SDAsyncBlockOperation.h */,
				18DD43142D7C3EE700E809EC /* SDAsyncBlockOperation.m */,
				18DD43152D7C3EE700E809EC /* SDDeviceHelper.h */,
				18DD43162D7C3EE700E809EC /* SDDeviceHelper.m */,
				18DD43172D7C3EE700E809EC /* SDDisplayLink.h */,
				18DD43182D7C3EE700E809EC /* SDDisplayLink.m */,
				18DD43192D7C3EE700E809EC /* SDFileAttributeHelper.h */,
				18DD431A2D7C3EE700E809EC /* SDFileAttributeHelper.m */,
				18DD431B2D7C3EE700E809EC /* SDImageAssetManager.h */,
				18DD431C2D7C3EE700E809EC /* SDImageAssetManager.m */,
				18DD431D2D7C3EE700E809EC /* SDImageCachesManagerOperation.h */,
				18DD431E2D7C3EE700E809EC /* SDImageCachesManagerOperation.m */,
				18DD431F2D7C3EE700E809EC /* SDImageFramePool.h */,
				18DD43202D7C3EE700E809EC /* SDImageFramePool.m */,
				18DD43212D7C3EE700E809EC /* SDImageIOAnimatedCoderInternal.h */,
				18DD43222D7C3EE700E809EC /* SDInternalMacros.h */,
				18DD43232D7C3EE700E809EC /* SDInternalMacros.m */,
				18DD43242D7C3EE700E809EC /* SDmetamacros.h */,
				18DD43252D7C3EE700E809EC /* SDWeakProxy.h */,
				18DD43262D7C3EE700E809EC /* SDWeakProxy.m */,
				18DD43272D7C3EE700E809EC /* SDWebImageTransitionInternal.h */,
				18DD43282D7C3EE700E809EC /* UIColor+SDHexString.h */,
				18DD43292D7C3EE700E809EC /* UIColor+SDHexString.m */,
			);
			path = Private;
			sourceTree = "<group>";
		};
		18DD432D2D7C3EE700E809EC /* SDWebImage */ = {
			isa = PBXGroup;
			children = (
				18DD42CF2D7C3EE700E809EC /* Core */,
				18DD432A2D7C3EE700E809EC /* Private */,
			);
			path = SDWebImage;
			sourceTree = "<group>";
		};
		18DD46782D81B3A800E809EC /* PurpleLazyChapterSpaUnderage */ = {
			isa = PBXGroup;
			children = (
				18DD46792D81B43E00E809EC /* XXGWKBaseViewController.h */,
				18DD467A2D81B43E00E809EC /* XXGWKBaseViewController.m */,
				18DD467F2D81B4BE00E809EC /* XXGPopupViewController.h */,
				18DD46802D81B4BE00E809EC /* XXGPopupViewController.m */,
				18DD48352D87EA1900E809EC /* XXGUCenterViewController.h */,
				18DD48362D87EA1900E809EC /* XXGUCenterViewController.m */,
			);
			path = PurpleLazyChapterSpaUnderage;
			sourceTree = "<group>";
		};
		18DD47D02D85325400E809EC /* TouchDefined */ = {
			isa = PBXGroup;
			children = (
				18DD47CA2D85237800E809EC /* XXGLocaleString.h */,
				18DD47CB2D85237800E809EC /* XXGLocaleString.m */,
				18DD47EF2D85603600E809EC /* XXGLocalizedModel.h */,
				18DD47F02D85603600E809EC /* XXGLocalizedModel.m */,
				18DD47E92D85600300E809EC /* XXGDatasModel.h */,
				18DD47EA2D85600300E809EC /* XXGDatasModel.m */,
			);
			path = TouchDefined;
			sourceTree = "<group>";
		};
		18DD481F2D86C87300E809EC /* ProceedSoloist */ = {
			isa = PBXGroup;
			children = (
				18DD48192D86C81200E809EC /* XXGCountryCodeSelectorViewController.h */,
				18DD481A2D86C81200E809EC /* XXGCountryCodeSelectorViewController.m */,
				18DD48202D86C8C600E809EC /* XXGCountry.h */,
				18DD48212D86C8C600E809EC /* XXGCountry.m */,
				18DD48262D86D0E300E809EC /* XXGCountryCodeButton.h */,
				18DD48272D86D0E300E809EC /* XXGCountryCodeButton.m */,
			);
			path = ProceedSoloist;
			sourceTree = "<group>";
		};
		18DD483D2D8831D100E809EC /* Infer */ = {
			isa = PBXGroup;
			children = (
				18DD483B2D8831D100E809EC /* NSError+XXGIAPError.h */,
				18DD483C2D8831D100E809EC /* NSError+XXGIAPError.m */,
			);
			path = Infer;
			sourceTree = "<group>";
		};
		18DD48422D8831D100E809EC /* GrowWay */ = {
			isa = PBXGroup;
			children = (
				18DD483E2D8831D100E809EC /* XXGIAPHelpManager.h */,
				18DD483F2D8831D100E809EC /* XXGIAPHelpManager.m */,
				18DD48402D8831D100E809EC /* XXGIAPVerifyManager.h */,
				18DD48412D8831D100E809EC /* XXGIAPVerifyManager.m */,
			);
			path = GrowWay;
			sourceTree = "<group>";
		};
		18DD48452D8831D100E809EC /* Build */ = {
			isa = PBXGroup;
			children = (
				18DD48432D8831D100E809EC /* XXGIAPTransactionModel.h */,
				18DD48442D8831D100E809EC /* XXGIAPTransactionModel.m */,
			);
			path = Build;
			sourceTree = "<group>";
		};
		18DD48502D8831D100E809EC /* PongMen */ = {
			isa = PBXGroup;
			children = (
				18DD483D2D8831D100E809EC /* Infer */,
				18DD48422D8831D100E809EC /* GrowWay */,
				18DD48452D8831D100E809EC /* Build */,
				18DD484C2D8831D100E809EC /* XXGIAPConfig.h */,
				18DD484D2D8831D100E809EC /* XXGIAPConfig.m */,
				18DD484E2D8831D100E809EC /* XXGIAPHelp.h */,
				18DD484F2D8831D100E809EC /* XXGIAPPayProtocol.h */,
			);
			path = PongMen;
			sourceTree = "<group>";
		};
		18ED06B12D715C2D00C8804D = {
			isa = PBXGroup;
			children = (
				18ED07302D715DF600C8804D /* WantedAdhesive */,
				18ED06BC2D715C2D00C8804D /* Products */,
			);
			sourceTree = "<group>";
		};
		18ED06BC2D715C2D00C8804D /* Products */ = {
			isa = PBXGroup;
			children = (
				18ED07BE2D71616E00C8804D /* WantedAdhesive.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		18ED06FA2D715DF600C8804D /* Bar */ = {
			isa = PBXGroup;
			children = (
				18DD47BA2D850F2A00E809EC /* XXGLocalizedCore.h */,
				18DD47BB2D850F2A00E809EC /* XXGLocalizedCore.m */,
				18DD47E32D8549F600E809EC /* XXGDatasCore.h */,
				18DD47E42D8549F600E809EC /* XXGDatasCore.m */,
				18ED06FB2D715DF600C8804D /* XXGStartBody.h */,
				18ED06FC2D715DF600C8804D /* XXGStartBody.m */,
				18989F272D71E0B000E11C3B /* XXGDeviceInfo.h */,
				18989F282D71E0B000E11C3B /* XXGDeviceInfo.m */,
				18DD487D2D892ECD00E809EC /* XXGProductBody.h */,
				18DD487E2D892ECD00E809EC /* XXGProductBody.m */,
				18DD48892D895D3D00E809EC /* XXGValidateReceiptBody.h */,
				18DD488A2D895D3D00E809EC /* XXGValidateReceiptBody.m */,
				18DD488F2D897DD400E809EC /* XXGRoleBody.h */,
				18DD48902D897DD400E809EC /* XXGRoleBody.m */,
			);
			path = Bar;
			sourceTree = "<group>";
		};
		18ED06FD2D715DF600C8804D /* Are */ = {
			isa = PBXGroup;
			children = (
				1834F7C72D730E3B00A1C782 /* XXGActionItem.h */,
				1834F7C82D730E3B00A1C782 /* XXGActionItem.m */,
				181E79772D76FF9F009CF2CC /* XXGBoxContent.h */,
				181E79782D76FF9F009CF2CC /* XXGBoxContent.m */,
				181E798A2D7845A0009CF2CC /* XXGSkinModel.h */,
				181E798B2D7845A0009CF2CC /* XXGSkinModel.m */,
				1849B92E2D7877E400A5F887 /* XXGThemeColor.h */,
				1849B92F2D7877E400A5F887 /* XXGThemeColor.m */,
				1849B9342D78798B00A5F887 /* XXGDockerCof.h */,
				1849B9352D78798B00A5F887 /* XXGDockerCof.m */,
				1849B93A2D7931D700A5F887 /* XXGServiceInfo.h */,
				1849B93B2D7931D700A5F887 /* XXGServiceInfo.m */,
				1849B9402D79352700A5F887 /* XXGBoxCenterCof.h */,
				1849B9412D79352700A5F887 /* XXGBoxCenterCof.m */,
				181E79902D784A7F009CF2CC /* XXGAdaptionCof.h */,
				181E79912D784A7F009CF2CC /* XXGAdaptionCof.m */,
				18DD48832D894CA600E809EC /* XXGSelectProductItem.h */,
				18DD48842D894CA600E809EC /* XXGSelectProductItem.m */,
				1811F7092DD2FD9800B17C42 /* XXGSelectProduct.h */,
				1811F70A2DD2FD9800B17C42 /* XXGSelectProduct.m */,
				18DA0D442D8BBA5A003C4467 /* XXGMQTTConnectInfo.h */,
				18DA0D452D8BBA5A003C4467 /* XXGMQTTConnectInfo.m */,
				18DA0E702D8C116E003C4467 /* XXGMQTTTopicInfo.h */,
				18DA0E712D8C116E003C4467 /* XXGMQTTTopicInfo.m */,
				182C6DA52D8D66CF00D3F530 /* XXGExtraParams.h */,
				182C6DA62D8D66CF00D3F530 /* XXGExtraParams.m */,
				182F6C9F2DC1BAA70039594F /* XXGServerInfo.h */,
				182F6CA02DC1BAA70039594F /* XXGServerInfo.m */,
			);
			path = Are;
			sourceTree = "<group>";
		};
		18ED06FE2D715DF600C8804D /* EarBag */ = {
			isa = PBXGroup;
			children = (
				18ED06FA2D715DF600C8804D /* Bar */,
				18ED06FD2D715DF600C8804D /* Are */,
			);
			path = EarBag;
			sourceTree = "<group>";
		};
		18ED07052D715DF600C8804D /* EndBlue */ = {
			isa = PBXGroup;
			children = (
				18ED06FF2D715DF600C8804D /* XXGBaseURL.h */,
				18ED07002D715DF600C8804D /* XXGBaseURL.m */,
				18ED07012D715DF600C8804D /* XXGNetListModel.h */,
				18ED07022D715DF600C8804D /* XXGNetListModel.m */,
				18ED07032D715DF600C8804D /* XXGNetwork.h */,
				18ED07042D715DF600C8804D /* XXGNetwork.m */,
				181E79622D75BC2D009CF2CC /* XXGNetworkList.h */,
				181E79632D75BC2D009CF2CC /* XXGNetworkList.m */,
			);
			path = EndBlue;
			sourceTree = "<group>";
		};
		18ED07102D715DF600C8804D /* MinTied */ = {
			isa = PBXGroup;
			children = (
				18ED070C2D715DF600C8804D /* XXGPlayKitConfig.h */,
				18ED070D2D715DF600C8804D /* XXGPlayKitConfig.m */,
				18ED070E2D715DF600C8804D /* XXGPlayKitCore.h */,
				18ED070F2D715DF600C8804D /* XXGPlayKitCore.m */,
				18E064352DC4E29900F15793 /* XXGPlayKitCore+Canal.h */,
				18E064362DC4E29900F15793 /* XXGPlayKitCore+Canal.m */,
				18E0642F2DC4DC4A00F15793 /* XXGPlayKitCore+Delegates.h */,
				18E064302DC4DC4A00F15793 /* XXGPlayKitCore+Delegates.m */,
				1812EAC52D915CC500B7BB73 /* XXGPlayKitCore+Others.h */,
				1812EAC62D915CC500B7BB73 /* XXGPlayKitCore+Others.m */,
				181E79222D754987009CF2CC /* XXGExecuteActions.h */,
				181E79232D754987009CF2CC /* XXGExecuteActions.m */,
				1812EAB92D9114C300B7BB73 /* XXGWKMethodAction.h */,
				1812EABA2D9114C300B7BB73 /* XXGWKMethodAction.m */,
			);
			path = MinTied;
			sourceTree = "<group>";
		};
		18ED07192D715DF600C8804D /* AtomAge */ = {
			isa = PBXGroup;
			children = (
				18ED07152D715DF600C8804D /* XXGNetworkCore.h */,
				18ED07162D715DF600C8804D /* XXGNetworkCore.m */,
				18ED07172D715DF600C8804D /* XXGNetworkMonitor.h */,
				18ED07182D715DF600C8804D /* XXGNetworkMonitor.m */,
			);
			path = AtomAge;
			sourceTree = "<group>";
		};
		18ED071C2D715DF600C8804D /* EyeBarriersPublicCapsBorder */ = {
			isa = PBXGroup;
			children = (
				18ED071A2D715DF600C8804D /* UICKeyChainStore.h */,
				18ED071B2D715DF600C8804D /* UICKeyChainStore.m */,
			);
			path = EyeBarriersPublicCapsBorder;
			sourceTree = "<group>";
		};
		18ED07212D715DF600C8804D /* HisAlive */ = {
			isa = PBXGroup;
			children = (
				1849B9702D7AE2A100A5F887 /* XXGUIKit.h */,
				1849B9712D7AE2A100A5F887 /* XXGUIKit.m */,
				1849B9762D7AE5F000A5F887 /* Taps */,
				18DD42012D7BFADC00E809EC /* Race */,
				18DD42002D7BFAC300E809EC /* ArrayUtterance */,
				18DD46782D81B3A800E809EC /* PurpleLazyChapterSpaUnderage */,
			);
			path = HisAlive;
			sourceTree = "<group>";
		};
		18ED072C2D715DF600C8804D /* Sessions */ = {
			isa = PBXGroup;
			children = (
				18DD48072D86827900E809EC /* XXGTools.h */,
				18DD48082D86827900E809EC /* XXGTools.m */,
				1886C6B02DE6E820006C3D99 /* AngularNoneBatchAllowableCircular */,
				1886C5F52DE44250006C3D99 /* LateCapWalk */,
				18DA0CD52D8BB02E003C4467 /* MQTTClient */,
				18DD48502D8831D100E809EC /* PongMen */,
				18DD47D02D85325400E809EC /* TouchDefined */,
				18DD432D2D7C3EE700E809EC /* SDWebImage */,
				18DD42222D7C2B1F00E809EC /* Masonry */,
				18989F342D72C12300E11C3B /* BrushSub */,
				18ED07192D715DF600C8804D /* AtomAge */,
				18ED071C2D715DF600C8804D /* EyeBarriersPublicCapsBorder */,
				18ED07282D715DF600C8804D /* XXGAppInfo.h */,
				18ED07292D715DF600C8804D /* XXGAppInfo.m */,
				18ED072A2D715DF600C8804D /* XXGSecurityCheckTool.h */,
				18ED072B2D715DF600C8804D /* XXGSecurityCheckTool.m */,
			);
			path = Sessions;
			sourceTree = "<group>";
		};
		18ED07302D715DF600C8804D /* WantedAdhesive */ = {
			isa = PBXGroup;
			children = (
				18989F2D2D72AB2B00E11C3B /* TraitOwn */,
				18ED072C2D715DF600C8804D /* Sessions */,
				18ED07212D715DF600C8804D /* HisAlive */,
			);
			path = WantedAdhesive;
			sourceTree = "<group>";
		};
		18ED075E2D715E4500C8804D /* AllLaw */ = {
			isa = PBXGroup;
			children = (
				186373362D7164A2000D82AE /* NetRows */,
				1863733B2D7164A9000D82AE /* DidMost */,
				18ED072E2D715DF600C8804D /* XXGSetting.h */,
				18ED072F2D715DF600C8804D /* XXGSetting.m */,
				18A230892D8EDD2C0015E020 /* XXGPlayProtocol.h */,
			);
			path = AllLaw;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		18ED07912D71616E00C8804D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				18ED07922D71616E00C8804D /* XXGNetListModel.h in Headers */,
				181E79952D784A7F009CF2CC /* XXGAdaptionCof.h in Headers */,
				182F6CA82DC28C660039594F /* XXGPoopoManager.h in Headers */,
				18DD481D2D86C81200E809EC /* XXGCountryCodeSelectorViewController.h in Headers */,
				18DD47F82D85736300E809EC /* XXGLocalizedUI.h in Headers */,
				18DD452C2D7EE58500E809EC /* XXGSelectAccountViewController.h in Headers */,
				18DD45582D7EE61500E809EC /* XXGContentTextViewController.h in Headers */,
				188F80082DACB95200699DF4 /* NSString+XXGMd5.h in Headers */,
				18DD46772D816D1600E809EC /* XXGSelectAccountCell.h in Headers */,
				18ED07932D71616E00C8804D /* XXGStartBody.h in Headers */,
				1849B9392D78798B00A5F887 /* XXGDockerCof.h in Headers */,
				1849B93D2D7931D700A5F887 /* XXGServiceInfo.h in Headers */,
				1849B9752D7AE2A100A5F887 /* XXGUIKit.h in Headers */,
				18DA0BFA2D8A6B1E003C4467 /* XXGSelectPCell.h in Headers */,
				18ED07942D71616E00C8804D /* XXGAlertView.h in Headers */,
				18ED07962D71616E00C8804D /* XXGNetworkMonitor.h in Headers */,
				1849B98C2D7AED6D00A5F887 /* UIViewController+XXGViewController.h in Headers */,
				181E79892D77EED2009CF2CC /* XXGBoxManager.h in Headers */,
				18DD45442D7EE5DE00E809EC /* XXGSelectPPViewController.h in Headers */,
				18DD48332D86F35D00E809EC /* XXGToast.h in Headers */,
				18ED07972D71616E00C8804D /* NSString+XXGString.h in Headers */,
				18ED07982D71616E00C8804D /* NSObject+XXGModel.h in Headers */,
				181E798F2D7845A0009CF2CC /* XXGSkinModel.h in Headers */,
				18989F2B2D71E0B000E11C3B /* XXGDeviceInfo.h in Headers */,
				181E79672D75BC2D009CF2CC /* XXGNetworkList.h in Headers */,
				182F6CA42DC1BAA70039594F /* XXGServerInfo.h in Headers */,
				18ED07992D71616E00C8804D /* UICKeyChainStore.h in Headers */,
				182C6DA42D8D61C100D3F530 /* NSObject+XXGPerformSelector.h in Headers */,
				18989DEA2D71C35A00E11C3B /* WantedAdhesive.h in Headers */,
				1849B9312D7877E400A5F887 /* XXGThemeColor.h in Headers */,
				1849B9882D7AEA3B00A5F887 /* XXGNavigationController.h in Headers */,
				18ED079B2D71616E00C8804D /* XXGAppInfo.h in Headers */,
				18DD487A2D892A6600E809EC /* XXGIAPManager.h in Headers */,
				182C6D9D2D8D565B00D3F530 /* XXGThirdManager.h in Headers */,
				18ED079C2D71616E00C8804D /* XXGSecurityCheckTool.h in Headers */,
				18ED079D2D71616E00C8804D /* XXGNetworkCore.h in Headers */,
				18DD47FF2D8588B800E809EC /* XXGDatasUI.h in Headers */,
				18DD454A2D7EE5ED00E809EC /* XXGRealNameViewController.h in Headers */,
				1849B9452D79352700A5F887 /* XXGBoxCenterCof.h in Headers */,
				18ED079E2D71616E00C8804D /* XXGSetting.h in Headers */,
				18DD48582D8831D100E809EC /* XXGIAPConfig.h in Headers */,
				186AE8EE2D942364000F1A11 /* XXGVKManager.h in Headers */,
				18DD48592D8831D100E809EC /* XXGIAPHelpManager.h in Headers */,
				18DD485A2D8831D100E809EC /* XXGIAPHelp.h in Headers */,
				18DD485B2D8831D100E809EC /* XXGIAPVerifyManager.h in Headers */,
				18DD485C2D8831D100E809EC /* XXGIAPTransactionModel.h in Headers */,
				18DD485E2D8831D100E809EC /* XXGIAPPayProtocol.h in Headers */,
				18DD48602D8831D100E809EC /* NSError+XXGIAPError.h in Headers */,
				18ED079F2D71616E00C8804D /* XXGPlayKitCore.h in Headers */,
				1849B9EE2D7B022600A5F887 /* XXGComeinViewController.h in Headers */,
				18ED07A02D71616E00C8804D /* XXGWindowManager.h in Headers */,
				18989F322D72C10C00E11C3B /* UIColor+XXGColor.h in Headers */,
				181E79242D754987009CF2CC /* XXGExecuteActions.h in Headers */,
				18DD45382D7EE5C200E809EC /* XXGChangeViewController.h in Headers */,
				18E064342DC4DC4A00F15793 /* XXGPlayKitCore+Delegates.h in Headers */,
				181E797C2D76FF9F009CF2CC /* XXGBoxContent.h in Headers */,
				1834F7C92D730E3B00A1C782 /* XXGActionItem.h in Headers */,
				1812EABD2D9114C300B7BB73 /* XXGWKMethodAction.h in Headers */,
				18ED07A12D71616E00C8804D /* XXGPlayKitConfig.h in Headers */,
				18DD45C32D8053C000E809EC /* XXGLoadingView.h in Headers */,
				18DD44482D7C3EE700E809EC /* UIColor+SDHexString.h in Headers */,
				18DD48092D86827900E809EC /* XXGTools.h in Headers */,
				18DD444E2D7C3EE700E809EC /* SDImageIOCoder.h in Headers */,
				18DD47BC2D850F2A00E809EC /* XXGLocalizedCore.h in Headers */,
				18DD44522D7C3EE700E809EC /* SDFileAttributeHelper.h in Headers */,
				18DD44562D7C3EE700E809EC /* SDWebImagePrefetcher.h in Headers */,
				18DD44582D7C3EE700E809EC /* SDAnimatedImageView.h in Headers */,
				18DD44592D7C3EE700E809EC /* NSButton+WebCache.h in Headers */,
				18DD467E2D81B43E00E809EC /* XXGWKBaseViewController.h in Headers */,
				18DD445A2D7C3EE700E809EC /* SDImageHEICCoder.h in Headers */,
				18DD48152D86AF3700E809EC /* XXGSendCodeButton.h in Headers */,
				18DD46832D81B4BE00E809EC /* XXGPopupViewController.h in Headers */,
				18DD445B2D7C3EE700E809EC /* SDImageIOAnimatedCoder.h in Headers */,
				18DD445D2D7C3EE700E809EC /* SDImageCoderHelper.h in Headers */,
				18DD445F2D7C3EE700E809EC /* SDAnimatedImagePlayer.h in Headers */,
				18DA0E742D8C116E003C4467 /* XXGMQTTTopicInfo.h in Headers */,
				18DD44602D7C3EE700E809EC /* UIView+WebCache.h in Headers */,
				18DD45BC2D80518800E809EC /* XXGUIkitProtocol.h in Headers */,
				18DD455C2D7F0D2B00E809EC /* XXGProtocolLabel.h in Headers */,
				1886C6062DE44C4D006C3D99 /* XXGDebugger.h in Headers */,
				18DD44622D7C3EE700E809EC /* SDWebImageOptionsProcessor.h in Headers */,
				18DD44632D7C3EE700E809EC /* SDWebImageTransitionInternal.h in Headers */,
				18DD44652D7C3EE700E809EC /* SDWeakProxy.h in Headers */,
				18DA0D342D8BB826003C4467 /* XXGMQTTManager.h in Headers */,
				18DD44662D7C3EE700E809EC /* SDWebImageCompat.h in Headers */,
				18DD44672D7C3EE700E809EC /* SDCallbackQueue.h in Headers */,
				18DD446E2D7C3EE700E809EC /* SDmetamacros.h in Headers */,
				18DD446F2D7C3EE700E809EC /* UIImage+Transform.h in Headers */,
				18DD44712D7C3EE700E809EC /* UIImage+MultiFormat.h in Headers */,
				18DD44722D7C3EE700E809EC /* SDImageAssetManager.h in Headers */,
				18DD44732D7C3EE700E809EC /* SDImageCachesManagerOperation.h in Headers */,
				18DA0D462D8BBA5A003C4467 /* XXGMQTTConnectInfo.h in Headers */,
				18DD44762D7C3EE700E809EC /* UIImageView+HighlightedWebCache.h in Headers */,
				18DD447A2D7C3EE700E809EC /* SDWebImageDownloaderOperation.h in Headers */,
				18DD447D2D7C3EE700E809EC /* SDWebImageDownloaderDecryptor.h in Headers */,
				18DD45202D7EE55700E809EC /* XXGMobileViewController.h in Headers */,
				18DD447E2D7C3EE700E809EC /* SDImageAWebPCoder.h in Headers */,
				18DD44832D7C3EE700E809EC /* SDInternalMacros.h in Headers */,
				18DD44842D7C3EE700E809EC /* SDAnimatedImageView+WebCache.h in Headers */,
				18DD44852D7C3EE700E809EC /* SDImageCoder.h in Headers */,
				18DD44862D7C3EE700E809EC /* SDWebImageCacheSerializer.h in Headers */,
				18DD44872D7C3EE700E809EC /* SDImageLoadersManager.h in Headers */,
				18DD48382D87EA1900E809EC /* XXGUCenterViewController.h in Headers */,
				186AE9D32D96C244000F1A11 /* XXGAdjustManager.h in Headers */,
				188F7F0E2DA7EEAC00699DF4 /* XXGMarqueeViewCell.h in Headers */,
				18DD44882D7C3EE700E809EC /* SDDisplayLink.h in Headers */,
				1811E3432DCC528B00B17C42 /* XXGAppLovinManager.h in Headers */,
				182F69D12DC0AB150039594F /* XXGAppInfoViewController.h in Headers */,
				18DD44892D7C3EE700E809EC /* SDWebImageIndicator.h in Headers */,
				18DD448B2D7C3EE700E809EC /* SDWebImageDownloader.h in Headers */,
				18DD45522D7EE5F800E809EC /* XXGServiceViewController.h in Headers */,
				18DD448C2D7C3EE700E809EC /* SDAnimatedImageRep.h in Headers */,
				18DD448D2D7C3EE700E809EC /* SDImageCacheConfig.h in Headers */,
				18DD448F2D7C3EE700E809EC /* SDImageFramePool.h in Headers */,
				1886C6E92DE6F77F006C3D99 /* ZBConsoleDestinatioin.h in Headers */,
				1886C6EA2DE6F77F006C3D99 /* ZBLogFormatter.h in Headers */,
				1886C6EB2DE6F77F006C3D99 /* ZBLogViewController.h in Headers */,
				1886C6EC2DE6F77F006C3D99 /* ZBLogMacros.h in Headers */,
				1886C6ED2DE6F77F006C3D99 /* ZBBaseDestination.h in Headers */,
				1886C6EE2DE6F77F006C3D99 /* ZBLog.h in Headers */,
				1886C6EF2DE6F77F006C3D99 /* ZBFileDestination.h in Headers */,
				1886C6F02DE6F77F006C3D99 /* ZBObjectiveCBeaver.h in Headers */,
				18DD44902D7C3EE700E809EC /* SDImageGraphics.h in Headers */,
				188F80142DACBA3500699DF4 /* NSData+SunHope.h in Headers */,
				18DD47CF2D85237800E809EC /* XXGLocaleString.h in Headers */,
				18DD44952D7C3EE700E809EC /* UIImage+GIF.h in Headers */,
				18DD47E72D8549F600E809EC /* XXGDatasCore.h in Headers */,
				18DD44962D7C3EE700E809EC /* SDAsyncBlockOperation.h in Headers */,
				18DD44972D7C3EE700E809EC /* SDImageCacheDefine.h in Headers */,
				18DD44992D7C3EE700E809EC /* SDWebImageDownloaderResponseModifier.h in Headers */,
				18DD449C2D7C3EE700E809EC /* UIView+WebCacheOperation.h in Headers */,
				18DD449F2D7C3EE700E809EC /* SDWebImageOperation.h in Headers */,
				18DD47EE2D85600300E809EC /* XXGDatasModel.h in Headers */,
				18DD451C2D7EE51800E809EC /* XXGAccountViewController.h in Headers */,
				18DD44A02D7C3EE700E809EC /* SDGraphicsImageRenderer.h in Headers */,
				18E064392DC4E29900F15793 /* XXGPlayKitCore+Canal.h in Headers */,
				1811F70C2DD2FD9800B17C42 /* XXGSelectProduct.h in Headers */,
				18401FB92D8E9506005D10BB /* XXGFirebaseManager.h in Headers */,
				18DD482A2D86D0E300E809EC /* XXGCountryCodeButton.h in Headers */,
				18DD44A12D7C3EE700E809EC /* SDImageLoader.h in Headers */,
				18DD44A22D7C3EE700E809EC /* UIImage+Metadata.h in Headers */,
				188F7C662D9E206D00699DF4 /* XXGSaveNamePSViewController.h in Headers */,
				18DD47F12D85603600E809EC /* XXGLocalizedModel.h in Headers */,
				18DD44A32D7C3EE700E809EC /* SDImageGIFCoder.h in Headers */,
				18DD44A42D7C3EE700E809EC /* UIImage+ForceDecode.h in Headers */,
				18DA0E7B2D8C16C7003C4467 /* XXGLiveBarrageCell.h in Headers */,
				18DA0E7C2D8C16C7003C4467 /* XXGLiveBarrage.h in Headers */,
				18DD44A52D7C3EE700E809EC /* SDImageFrame.h in Headers */,
				18DD45142D7EC2E900E809EC /* UIImage+XXGImage.h in Headers */,
				18DA0CD62D8BB02E003C4467 /* MQTTCoreDataPersistence.h in Headers */,
				18DA0CD72D8BB02E003C4467 /* MQTTSessionSynchron.h in Headers */,
				18DA0CD82D8BB02E003C4467 /* MQTTSSLSecurityPolicy.h in Headers */,
				18DA0CD92D8BB02E003C4467 /* MQTTPersistence.h in Headers */,
				18DA0CDA2D8BB02E003C4467 /* ReconnectTimer.h in Headers */,
				18DA0CDB2D8BB02E003C4467 /* MQTTSSLSecurityPolicyEncoder.h in Headers */,
				18DA0CDC2D8BB02E003C4467 /* MQTTSSLSecurityPolicyTransport.h in Headers */,
				18DA0CDD2D8BB02E003C4467 /* MQTTProperties.h in Headers */,
				1812EAC32D91593100B7BB73 /* NSURL+XXGAnalyse.h in Headers */,
				18DA0CDE2D8BB02E003C4467 /* MQTTStrict.h in Headers */,
				18DA0CDF2D8BB02E003C4467 /* MQTTLog.h in Headers */,
				18DA0CE02D8BB02E003C4467 /* ForegroundReconnection.h in Headers */,
				18DA0CE12D8BB02E003C4467 /* MQTTMessage.h in Headers */,
				18DA0CE22D8BB02E003C4467 /* MQTTInMemoryPersistence.h in Headers */,
				18DA0CE32D8BB02E003C4467 /* MQTTDecoder.h in Headers */,
				18401FAB2D8D8A51005D10BB /* XXGAppsFlyerManager.h in Headers */,
				188F7F172DA7EF9C00699DF4 /* XXGTransparentWindow.h in Headers */,
				18DA0CE42D8BB02E003C4467 /* GCDTimer.h in Headers */,
				18DA0CE52D8BB02E003C4467 /* MQTTCFSocketEncoder.h in Headers */,
				18DA0CE62D8BB02E003C4467 /* MQTTCFSocketTransport.h in Headers */,
				182F66992DC090A90039594F /* UIDevice+XXGDevice.h in Headers */,
				18DA0CE72D8BB02E003C4467 /* MQTTTransportProtocol.h in Headers */,
				18DA0CE82D8BB02E003C4467 /* MQTTClient.h in Headers */,
				18DA0CE92D8BB02E003C4467 /* MQTTSessionLegacy.h in Headers */,
				18DA0CEA2D8BB02E003C4467 /* MQTTSession.h in Headers */,
				18DA0CEB2D8BB02E003C4467 /* MQTTSSLSecurityPolicyDecoder.h in Headers */,
				18DA0CEC2D8BB02E003C4467 /* MQTTCFSocketDecoder.h in Headers */,
				18DA0CED2D8BB02E003C4467 /* MQTTSessionManager.h in Headers */,
				18DD44D32D7C419A00E809EC /* XXGUIDriver.h in Headers */,
				18DD44A62D7C3EE700E809EC /* NSImage+Compatibility.h in Headers */,
				18DD44A72D7C3EE700E809EC /* SDAnimatedImage.h in Headers */,
				18DD44A82D7C3EE700E809EC /* SDWebImageManager.h in Headers */,
				188F7C5A2D9BBB5400699DF4 /* XXGMobileTextField.h in Headers */,
				1812EAC92D915CC500B7BB73 /* XXGPlayKitCore+Others.h in Headers */,
				18DD44A92D7C3EE700E809EC /* NSData+ImageContentType.h in Headers */,
				18DD44AB2D7C3EE700E809EC /* SDWebImageDefine.h in Headers */,
				18DD44AD2D7C3EE700E809EC /* UIView+WebCacheState.h in Headers */,
				18DD44AE2D7C3EE700E809EC /* SDImageTransformer.h in Headers */,
				18DD44B02D7C3EE700E809EC /* SDDiskCache.h in Headers */,
				18DD45322D7EE59D00E809EC /* XXGForgetViewController.h in Headers */,
				18DD44B22D7C3EE700E809EC /* SDAssociatedObject.h in Headers */,
				18DD44B32D7C3EE700E809EC /* SDImageIOAnimatedCoderInternal.h in Headers */,
				18A2308B2D8EDD2C0015E020 /* XXGPlayProtocol.h in Headers */,
				18DD480F2D8686E700E809EC /* NSString+URLEncoding.h in Headers */,
				18DD44B62D7C3EE700E809EC /* SDDeviceHelper.h in Headers */,
				18DD44B72D7C3EE700E809EC /* SDImageCodersManager.h in Headers */,
				18DD44BA2D7C3EE700E809EC /* SDWebImageDownloaderConfig.h in Headers */,
				182C6D892D8D47C200D3F530 /* XXGFacebookManager.h in Headers */,
				18DD48922D897DD400E809EC /* XXGRoleBody.h in Headers */,
				18DD44BB2D7C3EE700E809EC /* UIImage+ExtendedCacheData.h in Headers */,
				18DD44BC2D7C3EE700E809EC /* UIImageView+WebCache.h in Headers */,
				18DD44BE2D7C3EE700E809EC /* SDWebImageError.h in Headers */,
				18DD44C02D7C3EE700E809EC /* SDImageAPNGCoder.h in Headers */,
				18DD44C12D7C3EE700E809EC /* SDWebImageCacheKeyFilter.h in Headers */,
				18DD45262D7EE57500E809EC /* XXGRegistViewController.h in Headers */,
				18DA0E872D8C1A9E003C4467 /* XXGMarqueeView.h in Headers */,
				18DD44C32D7C3EE700E809EC /* UIImage+MemoryCacheCost.h in Headers */,
				18DD44C62D7C3EE700E809EC /* SDWebImageDownloaderRequestModifier.h in Headers */,
				18DD44C72D7C3EE700E809EC /* SDImageCache.h in Headers */,
				18DD44CA2D7C3EE700E809EC /* SDMemoryCache.h in Headers */,
				18DD44CC2D7C3EE700E809EC /* NSBezierPath+SDRoundedCorners.h in Headers */,
				18DD44CD2D7C3EE700E809EC /* UIButton+WebCache.h in Headers */,
				18DD48882D894CA600E809EC /* XXGSelectProductItem.h in Headers */,
				18DD44CE2D7C3EE700E809EC /* SDWebImageTransition.h in Headers */,
				18DD44CF2D7C3EE700E809EC /* SDImageCachesManager.h in Headers */,
				18ED07A22D71616E00C8804D /* XXGBaseURL.h in Headers */,
				18ED07A32D71616E00C8804D /* XXGNetwork.h in Headers */,
				188F7F1B2DA7F00F00699DF4 /* XXGOrientationViewController.h in Headers */,
				18DD42482D7C2B1F00E809EC /* ViewController+MASAdditions.h in Headers */,
				18DD42492D7C2B1F00E809EC /* MASCompositeConstraint.h in Headers */,
				18DD424A2D7C2B1F00E809EC /* View+MASShorthandAdditions.h in Headers */,
				18DD424B2D7C2B1F00E809EC /* NSArray+MASShorthandAdditions.h in Headers */,
				18DD424C2D7C2B1F00E809EC /* NSArray+MASAdditions.h in Headers */,
				18DD424D2D7C2B1F00E809EC /* View+MASAdditions.h in Headers */,
				18DD424E2D7C2B1F00E809EC /* MASConstraint.h in Headers */,
				18DD424F2D7C2B1F00E809EC /* MASViewConstraint.h in Headers */,
				18DD42502D7C2B1F00E809EC /* MASConstraintMaker.h in Headers */,
				18DD48252D86C8C600E809EC /* XXGCountry.h in Headers */,
				18DD42512D7C2B1F00E809EC /* MASUtilities.h in Headers */,
				18DD42522D7C2B1F00E809EC /* MASLayoutConstraint.h in Headers */,
				18DD42532D7C2B1F00E809EC /* MASViewAttribute.h in Headers */,
				18DD42542D7C2B1F00E809EC /* NSLayoutConstraint+MASDebugAdditions.h in Headers */,
				18DD42552D7C2B1F00E809EC /* MASConstraint+Private.h in Headers */,
				18DD488B2D895D3D00E809EC /* XXGValidateReceiptBody.h in Headers */,
				18DD46892D82759300E809EC /* XXGFloatView.h in Headers */,
				18DD42562D7C2B1F00E809EC /* Masonry.h in Headers */,
				1863733D2D7164A9000D82AE /* XXGPlayOS.h in Headers */,
				18DD45402D7EE5D000E809EC /* XXGBindMobileViewController.h in Headers */,
				182C6DA82D8D66CF00D3F530 /* XXGExtraParams.h in Headers */,
				1849B97C2D7AE9A800A5F887 /* XXGBaseViewController.h in Headers */,
				18DD48822D892ECD00E809EC /* XXGProductBody.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		18ED07902D71616E00C8804D /* WantedAdhesive */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18ED07BB2D71616E00C8804D /* Build configuration list for PBXNativeTarget "WantedAdhesive" */;
			buildPhases = (
				18ED07912D71616E00C8804D /* Headers */,
				18ED07A62D71616E00C8804D /* Sources */,
				18ED07B92D71616E00C8804D /* Frameworks */,
				18ED07BA2D71616E00C8804D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = WantedAdhesive;
			packageProductDependencies = (
			);
			productName = WantedAdhesive;
			productReference = 18ED07BE2D71616E00C8804D /* WantedAdhesive.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		18ED06B22D715C2D00C8804D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					18ED06BA2D715C2D00C8804D = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 18ED06B52D715C2D00C8804D /* Build configuration list for PBXProject "WantedAdhesive" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 18ED06B12D715C2D00C8804D;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 18ED06BC2D715C2D00C8804D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				18ED07902D71616E00C8804D /* WantedAdhesive */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		18ED07BA2D71616E00C8804D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		18ED07A62D71616E00C8804D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				181E797B2D76FF9F009CF2CC /* XXGBoxContent.m in Sources */,
				18ED07A72D71616E00C8804D /* XXGSetting.m in Sources */,
				18DD45212D7EE55700E809EC /* XXGMobileViewController.m in Sources */,
				18DD423E2D7C2B1F00E809EC /* ViewController+MASAdditions.m in Sources */,
				18DD48812D892ECD00E809EC /* XXGProductBody.m in Sources */,
				18DD423F2D7C2B1F00E809EC /* MASViewConstraint.m in Sources */,
				18E0643A2DC4E29900F15793 /* XXGPlayKitCore+Canal.m in Sources */,
				18DD42402D7C2B1F00E809EC /* NSLayoutConstraint+MASDebugAdditions.m in Sources */,
				18DA0BFB2D8A6B1E003C4467 /* XXGSelectPCell.m in Sources */,
				18DD42412D7C2B1F00E809EC /* MASConstraint.m in Sources */,
				18DD468A2D82759300E809EC /* XXGFloatView.m in Sources */,
				18DD42422D7C2B1F00E809EC /* MASLayoutConstraint.m in Sources */,
				18DD44002D7C3EE700E809EC /* SDMemoryCache.m in Sources */,
				18DD44012D7C3EE700E809EC /* SDAnimatedImageView.m in Sources */,
				18DD47E82D8549F600E809EC /* XXGDatasCore.m in Sources */,
				18DD488C2D895D3D00E809EC /* XXGValidateReceiptBody.m in Sources */,
				18DD44022D7C3EE700E809EC /* SDInternalMacros.m in Sources */,
				182C6D9C2D8D565B00D3F530 /* XXGThirdManager.m in Sources */,
				18DD44032D7C3EE700E809EC /* SDAnimatedImage.m in Sources */,
				18DD44042D7C3EE700E809EC /* NSData+ImageContentType.m in Sources */,
				18DD44052D7C3EE700E809EC /* NSButton+WebCache.m in Sources */,
				18DD44062D7C3EE700E809EC /* SDImageTransformer.m in Sources */,
				188F7C672D9E206D00699DF4 /* XXGSaveNamePSViewController.m in Sources */,
				18DD44072D7C3EE700E809EC /* SDImageAWebPCoder.m in Sources */,
				18DD44082D7C3EE700E809EC /* SDAsyncBlockOperation.m in Sources */,
				18DD44092D7C3EE700E809EC /* UIButton+WebCache.m in Sources */,
				18DD440A2D7C3EE700E809EC /* SDAnimatedImageView+WebCache.m in Sources */,
				18DD46842D81B4BE00E809EC /* XXGPopupViewController.m in Sources */,
				18DD440B2D7C3EE700E809EC /* UIImage+ExtendedCacheData.m in Sources */,
				18DD440C2D7C3EE700E809EC /* SDImageLoader.m in Sources */,
				18DD440D2D7C3EE700E809EC /* SDWebImageDownloaderDecryptor.m in Sources */,
				18DD45152D7EC2E900E809EC /* UIImage+XXGImage.m in Sources */,
				18DD48372D87EA1900E809EC /* XXGUCenterViewController.m in Sources */,
				18DD440E2D7C3EE700E809EC /* SDWebImageIndicator.m in Sources */,
				18DD440F2D7C3EE700E809EC /* SDImageFrame.m in Sources */,
				18DD44102D7C3EE700E809EC /* SDFileAttributeHelper.m in Sources */,
				18DD44112D7C3EE700E809EC /* SDWebImageError.m in Sources */,
				18DD44122D7C3EE700E809EC /* SDWebImageDownloaderConfig.m in Sources */,
				18DD44132D7C3EE700E809EC /* SDImageLoadersManager.m in Sources */,
				18DD44142D7C3EE700E809EC /* UIImage+MemoryCacheCost.m in Sources */,
				18DD44152D7C3EE700E809EC /* UIImageView+HighlightedWebCache.m in Sources */,
				18DD44162D7C3EE700E809EC /* SDImageGraphics.m in Sources */,
				18DD44172D7C3EE700E809EC /* SDImageCachesManagerOperation.m in Sources */,
				18DD44182D7C3EE700E809EC /* SDWebImageManager.m in Sources */,
				18DD44192D7C3EE700E809EC /* SDImageCoder.m in Sources */,
				18DD48162D86AF3700E809EC /* XXGSendCodeButton.m in Sources */,
				18DD441A2D7C3EE700E809EC /* UIImage+GIF.m in Sources */,
				18DD45452D7EE5DE00E809EC /* XXGSelectPPViewController.m in Sources */,
				18DD441B2D7C3EE700E809EC /* SDImageCoderHelper.m in Sources */,
				182F669A2DC090A90039594F /* UIDevice+XXGDevice.m in Sources */,
				188F7F1C2DA7F00F00699DF4 /* XXGOrientationViewController.m in Sources */,
				18DD47F72D85736300E809EC /* XXGLocalizedUI.m in Sources */,
				18DD441C2D7C3EE700E809EC /* SDWebImageDownloaderRequestModifier.m in Sources */,
				18DD467D2D81B43E00E809EC /* XXGWKBaseViewController.m in Sources */,
				18401FB82D8E9506005D10BB /* XXGFirebaseManager.m in Sources */,
				18DD441D2D7C3EE700E809EC /* SDWebImageCacheKeyFilter.m in Sources */,
				18DD45532D7EE5F800E809EC /* XXGServiceViewController.m in Sources */,
				18DD47F22D85603600E809EC /* XXGLocalizedModel.m in Sources */,
				18DD45332D7EE59D00E809EC /* XXGForgetViewController.m in Sources */,
				18DD45272D7EE57500E809EC /* XXGRegistViewController.m in Sources */,
				18DD441E2D7C3EE700E809EC /* SDImageCacheConfig.m in Sources */,
				18DD441F2D7C3EE700E809EC /* SDImageCodersManager.m in Sources */,
				18DD44202D7C3EE700E809EC /* SDAnimatedImagePlayer.m in Sources */,
				188F80132DACBA3500699DF4 /* NSData+SunHope.m in Sources */,
				18DD48792D892A6600E809EC /* XXGIAPManager.m in Sources */,
				18DD44212D7C3EE700E809EC /* SDImageIOCoder.m in Sources */,
				18DD48242D86C8C600E809EC /* XXGCountry.m in Sources */,
				18DD482B2D86D0E300E809EC /* XXGCountryCodeButton.m in Sources */,
				18DD44222D7C3EE700E809EC /* NSImage+Compatibility.m in Sources */,
				18DD44232D7C3EE700E809EC /* SDWebImageDefine.m in Sources */,
				18DD44242D7C3EE700E809EC /* UIImageView+WebCache.m in Sources */,
				18DD45392D7EE5C200E809EC /* XXGChangeViewController.m in Sources */,
				1811F70B2DD2FD9800B17C42 /* XXGSelectProduct.m in Sources */,
				18DD48002D8588B800E809EC /* XXGDatasUI.m in Sources */,
				18DD44252D7C3EE700E809EC /* SDWebImageOptionsProcessor.m in Sources */,
				18401FAA2D8D8A51005D10BB /* XXGAppsFlyerManager.m in Sources */,
				18DD44262D7C3EE700E809EC /* SDCallbackQueue.m in Sources */,
				18DD44272D7C3EE700E809EC /* UIImage+ForceDecode.m in Sources */,
				1812EAC42D91593100B7BB73 /* NSURL+XXGAnalyse.m in Sources */,
				18DD44282D7C3EE700E809EC /* SDDeviceHelper.m in Sources */,
				18DD44292D7C3EE700E809EC /* SDImageGIFCoder.m in Sources */,
				18DD442A2D7C3EE700E809EC /* UIView+WebCacheState.m in Sources */,
				18DD442B2D7C3EE700E809EC /* UIImage+Metadata.m in Sources */,
				18DD442C2D7C3EE700E809EC /* UIView+WebCacheOperation.m in Sources */,
				188F7F0F2DA7EEAC00699DF4 /* XXGMarqueeViewCell.m in Sources */,
				18DD442D2D7C3EE700E809EC /* SDDiskCache.m in Sources */,
				18DD442E2D7C3EE700E809EC /* SDImageCacheDefine.m in Sources */,
				18DD45592D7EE61500E809EC /* XXGContentTextViewController.m in Sources */,
				182C6DA32D8D61C100D3F530 /* NSObject+XXGPerformSelector.m in Sources */,
				1812EABE2D9114C300B7BB73 /* XXGWKMethodAction.m in Sources */,
				18DD442F2D7C3EE700E809EC /* SDWeakProxy.m in Sources */,
				186AE8ED2D942364000F1A11 /* XXGVKManager.m in Sources */,
				18DD44302D7C3EE700E809EC /* SDWebImageDownloaderOperation.m in Sources */,
				18DD44312D7C3EE700E809EC /* UIColor+SDHexString.m in Sources */,
				188F7C5B2D9BBB5400699DF4 /* XXGMobileTextField.m in Sources */,
				18DD44322D7C3EE700E809EC /* SDWebImageDownloaderResponseModifier.m in Sources */,
				18DD44332D7C3EE700E809EC /* SDWebImageOperation.m in Sources */,
				18DD48102D8686E700E809EC /* NSString+URLEncoding.m in Sources */,
				18DD44342D7C3EE700E809EC /* NSBezierPath+SDRoundedCorners.m in Sources */,
				18DD481E2D86C81200E809EC /* XXGCountryCodeSelectorViewController.m in Sources */,
				18DD44352D7C3EE700E809EC /* SDImageAPNGCoder.m in Sources */,
				186AE9D22D96C244000F1A11 /* XXGAdjustManager.m in Sources */,
				18DD44362D7C3EE700E809EC /* UIImage+Transform.m in Sources */,
				18DA0E752D8C116E003C4467 /* XXGMQTTTopicInfo.m in Sources */,
				18DD44372D7C3EE700E809EC /* SDImageAssetManager.m in Sources */,
				18DD44382D7C3EE700E809EC /* SDWebImageTransition.m in Sources */,
				1886C6072DE44C4D006C3D99 /* XXGDebugger.m in Sources */,
				18DD44392D7C3EE700E809EC /* SDImageCache.m in Sources */,
				18DD443A2D7C3EE700E809EC /* SDImageHEICCoder.m in Sources */,
				18DD443B2D7C3EE700E809EC /* SDImageCachesManager.m in Sources */,
				18DD451D2D7EE51800E809EC /* XXGAccountViewController.m in Sources */,
				18DA0CEE2D8BB02E003C4467 /* MQTTSessionSynchron.m in Sources */,
				188F80072DACB95200699DF4 /* NSString+XXGMd5.m in Sources */,
				18DA0CEF2D8BB02E003C4467 /* MQTTDecoder.m in Sources */,
				18DA0CF02D8BB02E003C4467 /* MQTTSessionLegacy.m in Sources */,
				18DA0CF12D8BB02E003C4467 /* MQTTSSLSecurityPolicy.m in Sources */,
				18DA0CF22D8BB02E003C4467 /* MQTTMessage.m in Sources */,
				18DA0CF32D8BB02E003C4467 /* MQTTSessionManager.m in Sources */,
				18DA0CF42D8BB02E003C4467 /* ReconnectTimer.m in Sources */,
				18DA0CF52D8BB02E003C4467 /* MQTTSSLSecurityPolicyTransport.m in Sources */,
				18DA0CF62D8BB02E003C4467 /* MQTTSession.m in Sources */,
				18DA0CF72D8BB02E003C4467 /* MQTTCoreDataPersistence.m in Sources */,
				18DA0CF82D8BB02E003C4467 /* MQTTCFSocketTransport.m in Sources */,
				18DA0CF92D8BB02E003C4467 /* MQTTLog.m in Sources */,
				18DA0CFA2D8BB02E003C4467 /* MQTTSSLSecurityPolicyDecoder.m in Sources */,
				18DA0CFB2D8BB02E003C4467 /* MQTTSSLSecurityPolicyEncoder.m in Sources */,
				18DA0CFC2D8BB02E003C4467 /* MQTTTransportProtocol.m in Sources */,
				18DA0CFD2D8BB02E003C4467 /* MQTTInMemoryPersistence.m in Sources */,
				18DA0CFE2D8BB02E003C4467 /* MQTTStrict.m in Sources */,
				18DA0CFF2D8BB02E003C4467 /* MQTTCFSocketDecoder.m in Sources */,
				1812EACA2D915CC500B7BB73 /* XXGPlayKitCore+Others.m in Sources */,
				18DA0D002D8BB02E003C4467 /* GCDTimer.m in Sources */,
				18DA0D012D8BB02E003C4467 /* MQTTCFSocketEncoder.m in Sources */,
				18DA0D022D8BB02E003C4467 /* MQTTProperties.m in Sources */,
				18DA0D032D8BB02E003C4467 /* ForegroundReconnection.m in Sources */,
				18DD443C2D7C3EE700E809EC /* SDGraphicsImageRenderer.m in Sources */,
				18DD443D2D7C3EE700E809EC /* UIView+WebCache.m in Sources */,
				18DD443E2D7C3EE700E809EC /* SDWebImagePrefetcher.m in Sources */,
				18DD44D22D7C419A00E809EC /* XXGUIDriver.m in Sources */,
				18DD443F2D7C3EE700E809EC /* SDWebImageCacheSerializer.m in Sources */,
				18DD44402D7C3EE700E809EC /* SDDisplayLink.m in Sources */,
				18DD44412D7C3EE700E809EC /* SDWebImageDownloader.m in Sources */,
				18DD44422D7C3EE700E809EC /* SDImageFramePool.m in Sources */,
				18DD44432D7C3EE700E809EC /* SDImageIOAnimatedCoder.m in Sources */,
				18DD44442D7C3EE700E809EC /* UIImage+MultiFormat.m in Sources */,
				18DD44452D7C3EE700E809EC /* SDAssociatedObject.m in Sources */,
				18DD44462D7C3EE700E809EC /* SDAnimatedImageRep.m in Sources */,
				1811E3442DCC528B00B17C42 /* XXGAppLovinManager.m in Sources */,
				18DD44472D7C3EE700E809EC /* SDWebImageCompat.m in Sources */,
				1886C6F12DE6F77F006C3D99 /* ZBConsoleDestinatioin.m in Sources */,
				1886C6F22DE6F77F006C3D99 /* ZBLogFormatter.m in Sources */,
				1886C6F32DE6F77F006C3D99 /* ZBFileDestination.m in Sources */,
				1886C6F42DE6F77F006C3D99 /* ZBLog.m in Sources */,
				1886C6F52DE6F77F006C3D99 /* ZBLogViewController.m in Sources */,
				1886C6F62DE6F77F006C3D99 /* ZBBaseDestination.m in Sources */,
				18DD42432D7C2B1F00E809EC /* NSArray+MASAdditions.m in Sources */,
				18DD42442D7C2B1F00E809EC /* MASConstraintMaker.m in Sources */,
				182F6CA32DC1BAA70039594F /* XXGServerInfo.m in Sources */,
				18DD42452D7C2B1F00E809EC /* MASViewAttribute.m in Sources */,
				18DD42462D7C2B1F00E809EC /* MASCompositeConstraint.m in Sources */,
				18DD42472D7C2B1F00E809EC /* View+MASAdditions.m in Sources */,
				1849B9ED2D7B022600A5F887 /* XXGComeinViewController.m in Sources */,
				18DD45412D7EE5D000E809EC /* XXGBindMobileViewController.m in Sources */,
				18DD455D2D7F0D2B00E809EC /* XXGProtocolLabel.m in Sources */,
				1849B97B2D7AE9A800A5F887 /* XXGBaseViewController.m in Sources */,
				18DD48512D8831D100E809EC /* XXGIAPTransactionModel.m in Sources */,
				18DD48532D8831D100E809EC /* NSError+XXGIAPError.m in Sources */,
				18DD48542D8831D100E809EC /* XXGIAPConfig.m in Sources */,
				18DA0E882D8C1A9E003C4467 /* XXGMarqueeView.m in Sources */,
				18DA0D352D8BB826003C4467 /* XXGMQTTManager.m in Sources */,
				18DD48562D8831D100E809EC /* XXGIAPVerifyManager.m in Sources */,
				18DA0D472D8BBA5A003C4467 /* XXGMQTTConnectInfo.m in Sources */,
				182F6C182DC0F9FB0039594F /* WantedAdhesive.m in Sources */,
				18DD48572D8831D100E809EC /* XXGIAPHelpManager.m in Sources */,
				18DD46762D816D1600E809EC /* XXGSelectAccountCell.m in Sources */,
				18ED07A82D71616E00C8804D /* XXGPlayKitCore.m in Sources */,
				181E79882D77EED2009CF2CC /* XXGBoxManager.m in Sources */,
				1834F7CA2D730E3B00A1C782 /* XXGActionItem.m in Sources */,
				18DD45C42D8053C000E809EC /* XXGLoadingView.m in Sources */,
				18ED07A92D71616E00C8804D /* UICKeyChainStore.m in Sources */,
				1849B9742D7AE2A100A5F887 /* XXGUIKit.m in Sources */,
				18DD480A2D86827900E809EC /* XXGTools.m in Sources */,
				18ED07AB2D71616E00C8804D /* XXGStartBody.m in Sources */,
				182C6D882D8D47C200D3F530 /* XXGFacebookManager.m in Sources */,
				18ED07AC2D71616E00C8804D /* XXGPlayKitConfig.m in Sources */,
				182F69D22DC0AB150039594F /* XXGAppInfoViewController.m in Sources */,
				181E79942D784A7F009CF2CC /* XXGAdaptionCof.m in Sources */,
				18ED07AD2D71616E00C8804D /* NSObject+XXGModel.m in Sources */,
				18DD452D2D7EE58500E809EC /* XXGSelectAccountViewController.m in Sources */,
				1849B9872D7AEA3B00A5F887 /* XXGNavigationController.m in Sources */,
				1849B98B2D7AED6D00A5F887 /* UIViewController+XXGViewController.m in Sources */,
				18DA0E7D2D8C16C7003C4467 /* XXGLiveBarrageCell.m in Sources */,
				182C6DA72D8D66CF00D3F530 /* XXGExtraParams.m in Sources */,
				18DA0E7E2D8C16C7003C4467 /* XXGLiveBarrage.m in Sources */,
				1849B9442D79352700A5F887 /* XXGBoxCenterCof.m in Sources */,
				18ED07AE2D71616E00C8804D /* XXGNetworkMonitor.m in Sources */,
				18ED07AF2D71616E00C8804D /* XXGBaseURL.m in Sources */,
				18DD47BD2D850F2A00E809EC /* XXGLocalizedCore.m in Sources */,
				18989F332D72C10C00E11C3B /* UIColor+XXGColor.m in Sources */,
				18ED07B12D71616E00C8804D /* XXGAlertView.m in Sources */,
				18ED07B22D71616E00C8804D /* XXGNetListModel.m in Sources */,
				18DD48872D894CA600E809EC /* XXGSelectProductItem.m in Sources */,
				18DD48342D86F35D00E809EC /* XXGToast.m in Sources */,
				18ED07B32D71616E00C8804D /* XXGNetworkCore.m in Sources */,
				1849B9302D7877E400A5F887 /* XXGThemeColor.m in Sources */,
				182F6CA72DC28C660039594F /* XXGPoopoManager.m in Sources */,
				181E798E2D7845A0009CF2CC /* XXGSkinModel.m in Sources */,
				18ED07B42D71616E00C8804D /* XXGSecurityCheckTool.m in Sources */,
				181E79662D75BC2D009CF2CC /* XXGNetworkList.m in Sources */,
				18E064332DC4DC4A00F15793 /* XXGPlayKitCore+Delegates.m in Sources */,
				18ED07B52D71616E00C8804D /* XXGAppInfo.m in Sources */,
				18ED07B62D71616E00C8804D /* XXGNetwork.m in Sources */,
				18ED07B72D71616E00C8804D /* NSString+XXGString.m in Sources */,
				18ED07B82D71616E00C8804D /* XXGWindowManager.m in Sources */,
				18DD47CE2D85237800E809EC /* XXGLocaleString.m in Sources */,
				181E79252D754987009CF2CC /* XXGExecuteActions.m in Sources */,
				18DD47ED2D85600300E809EC /* XXGDatasModel.m in Sources */,
				188F7F182DA7EF9C00699DF4 /* XXGTransparentWindow.m in Sources */,
				1849B9382D78798B00A5F887 /* XXGDockerCof.m in Sources */,
				1863733C2D7164A9000D82AE /* XXGPlayOS.m in Sources */,
				18989F2C2D71E0B000E11C3B /* XXGDeviceInfo.m in Sources */,
				1849B93C2D7931D700A5F887 /* XXGServiceInfo.m in Sources */,
				18DD48912D897DD400E809EC /* XXGRoleBody.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		18ED06CF2D715C2D00C8804D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		18ED06D02D715C2D00C8804D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		18ED07BC2D71616E00C8804D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = NO;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"XXGPLAYKITOS_TARGET=1",
					"XXGPLAYKIT_DEBUG=1",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.WantedAdhesive;
				PRODUCT_NAME = WantedAdhesive;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		18ED07BD2D71616E00C8804D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = NO;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"XXGPLAYKITOS_TARGET=1",
					"XXGPLAYKIT_DEBUG=1",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.WantedAdhesive;
				PRODUCT_NAME = WantedAdhesive;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		18ED06B52D715C2D00C8804D /* Build configuration list for PBXProject "WantedAdhesive" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18ED06CF2D715C2D00C8804D /* Debug */,
				18ED06D02D715C2D00C8804D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18ED07BB2D71616E00C8804D /* Build configuration list for PBXNativeTarget "WantedAdhesive" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18ED07BC2D71616E00C8804D /* Debug */,
				18ED07BD2D71616E00C8804D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 18ED06B22D715C2D00C8804D /* Project object */;
}
