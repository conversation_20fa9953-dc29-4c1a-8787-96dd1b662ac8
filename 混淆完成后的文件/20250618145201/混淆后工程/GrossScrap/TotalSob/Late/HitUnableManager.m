






#import "HitUnableManager.h"
#import "LeapHueHeapSunViewController.h"
#import "HandledEggConfig.h"

@interface HitUnableManager()
@property (nonatomic, strong) NSMutableArray<UIWindow *> *eraHisTeamRest;  
@property (nonatomic, strong) NSMutableArray<UIWindow *> *retryHomeMan;  
@end

@implementation HitUnableManager

- (instancetype)init {
    self = [super init];
    if (self) {
        _eraHisTeamRest = [NSMutableArray array];
        _retryHomeMan = [NSMutableArray array];
    }
    return self;
}

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t rareToken;
    dispatch_once(&rareToken, ^{
        shared = [[super alloc] init];
    });
    return shared;
}


- (UIWindow *)didPreviewWindow {
    UIWindow *firstWindow = nil;
    
    if (@available(iOS 13.0, *)) {
        
        NSSet<UIScene *> *connectedScenes = [UIApplication sharedApplication].connectedScenes;
        for (UIScene *scene in connectedScenes) {
            
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                
                if (windowScene.windows.count > 0) {
                    firstWindow = windowScene.windows.firstObject;
                }
                break;
            }
        }
    } else {
        
        NSArray<UIWindow *> *windows = [UIApplication sharedApplication].windows;
        if (windows.count > 0) {
            firstWindow = windows.firstObject;
        }
    }
    
    
    if (!firstWindow) {
        NSArray<UIWindow *> *windows = [UIApplication sharedApplication].windows;
        if (windows.count > 0) {
            firstWindow = windows.firstObject;
        }
    }
    
    return firstWindow;
}


- (UIWindow *)laterExceedsWindow {
    
    UIWindow *faxTextWindow = nil;
    
    if (@available(iOS 13.0, *)) {
        NSSet<UIScene *> *connectedScenes = [UIApplication sharedApplication].connectedScenes;
        for (UIScene *scene in connectedScenes) {
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                
                
                if (@available(iOS 15.0, *)) {
                    faxTextWindow = windowScene.keyWindow;
                }
                
                else {
                    for (UIWindow *window in windowScene.windows) {
                        if (window.isKeyWindow) {
                            faxTextWindow = window;
                            break;
                        }
                    }
                }
                break;
            }
        }
    } else {
        
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
        faxTextWindow = [UIApplication sharedApplication].keyWindow;
#pragma clang diagnostic pop
    }
    
    
    if (!faxTextWindow) {
        NSArray<UIWindow *> *windows = [UIApplication sharedApplication].windows;
        for (UIWindow *window in windows) {
            if (window.isKeyWindow) {
                faxTextWindow = window;
                break;
            }
        }
    }
    
    return faxTextWindow;
}


- (void)reportedCurrentlyAddCutterDownModeJoinViewController:(UIViewController *)orange{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if ([UIApplication sharedApplication].applicationState == UIApplicationStateActive) {
            
            UIWindow *newWindow = [self tableTotalCloseTipPostalInstant:orange];
            
            
            [self wakeCinematicPredictedHuePredictedQuit:newWindow];
            
            [self.eraHisTeamRest addObject:newWindow];
        } else {
            
            __weak typeof(self) weakSelf = self;
            
            __block __weak id fat = nil;
            
            fat = [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidBecomeActiveNotification
                                                                       object:nil
                                                                        queue:[NSOperationQueue mainQueue]
                                                                   usingBlock:^(NSNotification *note) {
                
                [[NSNotificationCenter defaultCenter] removeObserver:fat];
                [weakSelf reportedCurrentlyAddCutterDownModeJoinViewController:orange];
            }];
        }
    });
}

- (void)topEmbeddingAnchorsWhiteRevertFastViewController:(UIViewController *)orange {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([UIApplication sharedApplication].applicationState == UIApplicationStateActive) {
            [self hailHusbandMandatoryArtistYearScreenNiacin:orange];
        } else {
            
            __weak typeof(self) weakSelf = self;
            
            __block __weak id fat = nil;
            
            fat = [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidBecomeActiveNotification
                                                                       object:nil
                                                                        queue:[NSOperationQueue mainQueue]
                                                                   usingBlock:^(NSNotification *note) {
                
                [[NSNotificationCenter defaultCenter] removeObserver:fat];
                
                [weakSelf hailHusbandMandatoryArtistYearScreenNiacin:orange];
            }];
        }
    });
}

- (void)slightMayAlbanianHisSmoothCaptureView:(UIView *)view {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([UIApplication sharedApplication].applicationState == UIApplicationStateActive) {
            [self hailHusbandMandatoryArtistYearScreenNiacin:view];
        } else {
            
            __weak typeof(self) weakSelf = self;
            
            __block __weak id fat = nil;
            
            fat = [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidBecomeActiveNotification
                                                                       object:nil
                                                                        queue:[NSOperationQueue mainQueue]
                                                                   usingBlock:^(NSNotification *note) {
                
                [[NSNotificationCenter defaultCenter] removeObserver:fat];
                
                [weakSelf hailHusbandMandatoryArtistYearScreenNiacin:view];
            }];
        }
    });
}

- (void)hailHusbandMandatoryArtistYearScreenNiacin:(id)object {
    UIViewController *orange = nil;
    
        
    if ([object isKindOfClass:[UIViewController class]]) {
        orange = object;
    }
    
    if ([object isKindOfClass:[UIView class]]) {
        orange = [LeapHueHeapSunViewController new];
        orange.view = object;
    }
    
    
    UIWindow *newWindow = [self tableTotalCloseTipPostalInstant:orange];
    
    
    [self wakeCinematicPredictedHuePredictedQuit:newWindow];
    
    
    [self.retryHomeMan addObject:newWindow];
}

- (void)remembersSoundLicenseMagnitudeCentralChrome:(NSNotification *)note {
    
    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                    name:UIApplicationDidBecomeActiveNotification
                                                  object:nil];
    [self slightMayAlbanianHisSmoothCaptureView:note.object];
}

- (void)iconRestoredWindow {
    [self animatingSphericalBalancedUtteranceMaxWindow];
}

- (void)animatingSphericalBalancedUtteranceMaxWindow {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.retryHomeMan.count == 0) return;

        
        UIWindow *bookWindow = [self.retryHomeMan lastObject];
        [self.retryHomeMan removeLastObject];

        
        if (bookWindow.isKeyWindow) {
            [self textSlabHitWinWindow];
        }

        
        bookWindow.hidden = YES;
    });
}

- (void)exceedsPotentialAgeAtomicSlovakIntervalsViewController:(UIViewController *)rootViewController {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSEnumerator *reasonExtendsCleanSyntheticFlushed = [self.retryHomeMan reverseObjectEnumerator];
        UIWindow *window = nil;
        
        
        while ((window = [reasonExtendsCleanSyntheticFlushed nextObject])) {
            if (window.rootViewController == rootViewController) {
                
                if (window.isKeyWindow) {
                    [self textSlabHitWinWindow];
                }
                
                
                window.hidden = YES;
                [self.retryHomeMan removeObject:window];
                
                
                reasonExtendsCleanSyntheticFlushed = [self.retryHomeMan reverseObjectEnumerator];
            }
        }
    });
}

- (void)anchoringResumeBlackUnitHitRadio {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        for (UIWindow *window in [self.retryHomeMan reverseObjectEnumerator]) {
            if (window.isKeyWindow) {
                [self textSlabHitWinWindow];
            }
            window.hidden = YES;
        }
        [self.retryHomeMan removeAllObjects];
    });
}


- (UIWindow *)tableTotalCloseTipPostalInstant:(UIViewController *)orange {
    UIWindow *window = nil;
    
    
    if (@available(iOS 13.0, *)) {
        for (UIScene *scene in [UIApplication sharedApplication].connectedScenes) {
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                window = [[UIWindow alloc] initWithWindowScene:(UIWindowScene *)scene];
                break;
            }
        }
    }
    
    
    if (!window) {
        window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    }
    
    
    window.backgroundColor = [UIColor clearColor];
    window.rootViewController = orange;
    return window;
}

- (void)wakeCinematicPredictedHuePredictedQuit:(UIWindow *)window {
    

    window.windowLevel = UIWindowLevelStatusBar + 100;
    [window makeKeyAndVisible];
}


- (void)textSlabHitWinWindow {
    UIWindow *millWindow = [self popRareRankWindow];
    [millWindow makeKeyWindow];
    if (!millWindow.isKeyWindow) {
        [millWindow becomeKeyWindow];
    }
}

- (UIWindow *)popRareRankWindow {
    __block UIWindow *millWindow = nil;
    
    
    if (@available(iOS 13.0, *)) {
        NSArray<UIWindowScene *> *windowScenes = [self arePeriodSpaTagsLarger];
        [windowScenes enumerateObjectsUsingBlock:^(UIWindowScene * _Nonnull scene, NSUInteger idx, BOOL * _Nonnull stop) {
            
            if (@available(iOS 15.0, *)) {
                millWindow = scene.keyWindow;
            }
            
            if (!millWindow) {
                millWindow = [scene.windows firstObject];
            }
            if (millWindow) *stop = YES;
        }];
    }
    
    else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
        millWindow = [UIApplication sharedApplication].keyWindow;
#pragma clang diagnostic pop
    }
    
    
    if (!millWindow) {
        millWindow = [UIApplication sharedApplication].windows.firstObject;
    }
    
    return millWindow;
}

- (NSArray<UIWindowScene *> *)arePeriodSpaTagsLarger {
    NSPredicate *predicate = [NSPredicate predicateWithBlock:^BOOL(UIScene * _Nullable scene, NSDictionary<NSString *,id> * _Nullable bindings) {
        return scene.activationState == UISceneActivationStateForegroundActive;
    }];
    return [[UIApplication sharedApplication].connectedScenes filteredSetUsingPredicate:predicate].allObjects;
}


- (UIWindow *)popWindow {
    return [self.retryHomeMan lastObject];
}

- (NSInteger)frenchCount {
    return self.retryHomeMan.count;
}


@end
