






#import "LoopsEyeRomanController.h"
#import "YoungerViewController.h"

@interface LoopsEyeRomanController ()

@end

@implementation LoopsEyeRomanController


- (BOOL)shouldAutorotate {
    return self.topViewController.shouldAutorotate;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return self.topViewController.supportedInterfaceOrientations;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.interactivePopGestureRecognizer.enabled = NO;
    [self setNavigationBarHidden:YES];
    self.view.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:.3];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    
    CGPoint point = [[touches anyObject] locationInView:self.view];
    
    UIView *icyView = self.topViewController.view;
    
    point = [icyView.layer convertPoint:point fromLayer:self.view.layer];
    
    YoungerViewController *vc = (YoungerViewController *)self.topViewController;
    if (![icyView.layer containsPoint:point]) {
        [vc capablePolarFairEventFactories:touches withEvent:event];
    }else{  
        [super touchesBegan:touches withEvent:event];
    }
}

- (void)pushViewController:(YoungerViewController *)viewController animated:(BOOL)animated
{
    if (self.childViewControllers.count > 0) {
        viewController.notEraserButton.hidden = NO;
    }else {
        viewController.notEraserButton.hidden = YES;
    }
    [super pushViewController:viewController animated:animated];
    
}
- (void)dealloc {
    
}
@end
