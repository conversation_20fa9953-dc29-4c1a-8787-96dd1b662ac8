






#import "YoungerViewController.h"

@interface YoungerViewController ()

@end

@implementation YoungerViewController

- (UIButton *)notEraserButton
{
    if (!_notEraserButton) {
        _notEraserButton = [[UIButton alloc] init];
        [_notEraserButton setTitle:ShapeWinNet.redoFinalTag.chargeEachDiscardedSecondaryFullPeople forState:UIControlStateNormal];
        [_notEraserButton setTitleColor:[ShapeWinNet startCropColor] forState:UIControlStateNormal];
        [_notEraserButton addTarget:self action:@selector(stickyIndoorClampMismatchStepperAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _notEraserButton;
}

- (UIButton *)earStepsonButton
{
    if (!_earStepsonButton) {
        _earStepsonButton = [[UIButton alloc] init];
        [_earStepsonButton setTitle:ShapeWinNet.redoFinalTag.installFreestyleOrdinaryAllAlignSeason forState:UIControlStateNormal];
        [_earStepsonButton setTitleColor:[ShapeWinNet startCropColor] forState:UIControlStateNormal];
        [_earStepsonButton addTarget:self action:@selector(arcadeOriginInactiveAppearingSignAction:) forControlEvents:UIControlEventTouchUpInside];
        _earStepsonButton.hidden = [ShapeWinNet routeForbidItsDepthImpactFocusing];
    }
    return _earStepsonButton;
}

- (void)stickyIndoorClampMismatchStepperAction:(UIButton *)sender {
    if(self.navigationController.viewControllers.count > 1) {
        [self.view endEditing:YES];
        [self.navigationController popViewControllerAnimated:NO];
    }else {
        [self arcadeOriginInactiveAppearingSignAction:sender];
        [self dismissViewControllerAnimated:NO completion:nil];
    }
}

- (void)arcadeOriginInactiveAppearingSignAction:(UIButton *)sender {
    [[HitUnableManager shared] iconRestoredWindow];
    [ShapeWinNet arcadeOriginInactiveAppearingSignAction];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.view.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
    self.view.layer.cornerRadius = 2;
    self.view.backgroundColor = [ShapeWinNet stylizeAlcoholCollapsesEdgePolicyColor];
    [self.view addSubview:self.notEraserButton];
    [self.view addSubview:self.earStepsonButton];
    
    CGFloat rowSize = ShapeWinNet.redoFinalTag.briefKinMask;
    [_notEraserButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.view).offset(ShapeWinNet.redoFinalTag.maxBrownCan);
        make.size.mas_equalTo(CGSizeMake(rowSize, rowSize));
    }];
    [_earStepsonButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(ShapeWinNet.redoFinalTag.maxBrownCan);
        make.right.equalTo(self.view).offset(-ShapeWinNet.redoFinalTag.maxBrownCan);
        make.size.mas_equalTo(CGSizeMake(rowSize, rowSize));
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(decipherTradAppearSnowDifferentMinFrame:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(sumMenUnorderedPreserveTrashPositions:) name:UIKeyboardWillHideNotification object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.view.superview);
        make.size.mas_equalTo([ShapeWinNet millHueMenBlueNearestArtSize]);
    }];
}


- (void)decipherTradAppearSnowDifferentMinFrame:(NSNotification *)notification {
    
    CGFloat duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] floatValue];
    
    
    CGRect keyboardFrame = [notification.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    
    UIWindow *keyWindow = [HitUnableManager shared].laterExceedsWindow;
    if (![keyWindow isMemberOfClass:NSClassFromString(ShapeWinNet.redoFinalTag.attitudeExposeHeaderBringOpacityTamil)]) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
        UIView *firstResponder = [keyWindow performSelector:@selector(firstResponder)];
#pragma clang diagnostic pop
        
        if (firstResponder  && [firstResponder isKindOfClass:UITextField.class]) {

            CGRect ageRect = [keyWindow convertRect:firstResponder.frame fromView:firstResponder.superview];
            
            if ((ageRect.origin.y + ageRect.size.height) > keyboardFrame.origin.y) {
                CGFloat strip = ((ageRect.origin.y + ageRect.size.height) - keyboardFrame.origin.y) + 10;
                
                faceWas(self);
                [UIView animateWithDuration:duration animations:^{
                    ampereIll(self);
                    self.navigationController.view.transform = CGAffineTransformMakeTranslation(0, -strip);
                }];
            }
        }
    }
}


- (void)sumMenUnorderedPreserveTrashPositions:(NSNotification *)notification{
    CGFloat duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] floatValue];
    faceWas(self);
    [UIView animateWithDuration:duration animations:^{
        ampereIll(self);
        self.navigationController.view.transform = CGAffineTransformIdentity;
    }];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super touchesEnded:touches withEvent:event];
    [self.view endEditing:YES];
}

- (void)capablePolarFairEventFactories:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [self.view endEditing:YES];
}

- (void)dealloc {
    
    [self.view endEditing:YES];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillHideNotification object:nil];
}

@end
