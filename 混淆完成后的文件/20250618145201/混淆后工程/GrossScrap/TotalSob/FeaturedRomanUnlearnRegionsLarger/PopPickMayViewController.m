






#import "PopPickMayViewController.h"
#import "HitUnableManager.h"
#import "LongRaceView.h"
#import "UIColor+GetColor.h"
#import "UIDevice+BarDevice.h"
#import "UIImage+OwnImage.h"
#import "HandledEggConfig.h"

@interface PopPickMayViewController ()

@property (nonatomic, strong) UIView * jabberView;
@property (nonatomic, strong) UIImageView * herView;

@end

@implementation PopPickMayViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = ShapeWinNet.balticArmSingleBatchSearchingTemp ? [UIColor durationRoomEightEnergyProfilesPrefer:ShapeWinNet.balticArmSingleBatchSearchingTemp]:UIColor.whiteColor;
    self.kurdishTree = self.retryManSpa;
    [LongRaceView handleAny];
    
    self.jabberView = [UIView new];
    self.jabberView.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:self.jabberView];
    
    self.herView = [[UIImageView alloc] initWithImage:[UIImage domainIndexObserverUploadingInitiatedName:ShapeWinNet.redoFinalTag.streetDisplaysGramAssumeDogProducer]];;
    [self.view addSubview:self.herView];
    
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self checkFlashSmileNumeratorWax];
}

-(void)viewWillDisappear:(BOOL)animated {
    if ([ShapeWinNet mathDiscreteBetweenCivilSlovenianName] && HandledEggConfig.shared.advancePanSecondaryStorageSee.caretSexTag.stableLoose) {
        [LongRaceView andDublin];
    }
    [super viewWillDisappear:animated];
    
}

- (CGSize)loopsExpiredSize {
    if ([UIDevice waxMax]) {
        return CGSizeMake(ShapeWinNet.combiningHowProfileProjectsStart, ShapeWinNet.combiningHowProfileProjectsStart);
    }
    UIWindow *faxTextWindow = [[HitUnableManager shared] laterExceedsWindow];
    UIEdgeInsets safe = faxTextWindow.safeAreaInsets;
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
    if (orientation == UIInterfaceOrientationPortrait || orientation == UIInterfaceOrientationPortraitUpsideDown) {
        if (![UIDevice abnormal]) {
            return CGSizeMake(UIScreen.mainScreen.bounds.size.width, ShapeWinNet.combiningHowProfileProjectsStart);
        }
        return CGSizeMake(UIScreen.mainScreen.bounds.size.width, ShapeWinNet.combiningHowProfileProjectsStart + safe.bottom);
    }else {
        if (![UIDevice abnormal]) {
            return CGSizeMake(ShapeWinNet.combiningHowProfileProjectsStart,UIScreen.mainScreen.bounds.size.height);
        }
        if (orientation == UIInterfaceOrientationLandscapeRight) {
            return CGSizeMake(ShapeWinNet.combiningHowProfileProjectsStart + safe.left,UIScreen.mainScreen.bounds.size.height);
        }else {
            return CGSizeMake(ShapeWinNet.combiningHowProfileProjectsStart + 5,UIScreen.mainScreen.bounds.size.height);
        }
    }
}

- (void)checkFlashSmileNumeratorWax {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
    if (orientation == UIInterfaceOrientationPortrait || orientation == UIInterfaceOrientationPortraitUpsideDown) {
        [self.jabberView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view.mas_top);
            make.left.right.equalTo(self.view);
            make.height.mas_equalTo(ShapeWinNet.redoFinalTag.cervicalPath);
        }];
        [self.herView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.jabberView.mas_top);
            make.centerX.equalTo(self.view);
            make.size.mas_equalTo(CGSizeMake(ShapeWinNet.redoFinalTag.disposeClang, ShapeWinNet.redoFinalTag.callCardioid));
        }];
        [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerX.bottom.mas_equalTo(0);
            make.size.mas_equalTo(self.loopsExpiredSize);
        }];
        [self.carRotation mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.right.mas_equalTo(0);
            make.height.mas_equalTo(ShapeWinNet.combiningHowProfileProjectsStart);
        }];
        self.view.transform = CGAffineTransformMakeTranslation(0, self.loopsExpiredSize.height);
    }else {
        [self.jabberView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.view.mas_right);
            make.top.bottom.equalTo(self.view);
            make.width.mas_equalTo(ShapeWinNet.redoFinalTag.cervicalPath);
        }];
        UIImage *errorItsImage = [UIImage domainIndexObserverUploadingInitiatedName:ShapeWinNet.redoFinalTag.streetDisplaysGramAssumeDogProducer];
        UIImage *ironEggImage = [UIImage imageWithCGImage:errorItsImage.CGImage
                                                    scale:errorItsImage.scale
                                              orientation:UIImageOrientationRight]; 
        self.herView.image = ironEggImage;
        [self.herView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.jabberView.mas_right);
            make.centerY.equalTo(self.view);
            make.size.mas_equalTo(CGSizeMake(ShapeWinNet.redoFinalTag.callCardioid, ShapeWinNet.redoFinalTag.disposeClang));
        }];
        [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.left.mas_equalTo(0);
            make.size.mas_equalTo(self.loopsExpiredSize);
        }];
        [self.carRotation mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.right.mas_equalTo(0);
            make.width.mas_equalTo(ShapeWinNet.combiningHowProfileProjectsStart);
        }];
        self.view.transform = CGAffineTransformMakeTranslation(-self.loopsExpiredSize.width, 0);
    }
    [UIView animateWithDuration:0.3 animations:^{
        self.view.transform = CGAffineTransformIdentity;
    }];
}

- (void)capablePolarFairEventFactories:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super capablePolarFairEventFactories:touches withEvent:event];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
    if (orientation == UIInterfaceOrientationPortrait || orientation == UIInterfaceOrientationPortraitUpsideDown) {
        [UIView animateWithDuration:0.3 animations:^{
            self.view.transform = CGAffineTransformMakeTranslation(0, self.loopsExpiredSize.height);;
        } completion:^(BOOL finished) {
            [[HitUnableManager shared] exceedsPotentialAgeAtomicSlovakIntervalsViewController:self.navigationController];
        }];
    }else {
        [UIView animateWithDuration:0.3 animations:^{
            self.view.transform = CGAffineTransformMakeTranslation(-self.loopsExpiredSize.width, 0);
        } completion:^(BOOL finished) {
            [[HitUnableManager shared] exceedsPotentialAgeAtomicSlovakIntervalsViewController:self.navigationController];
        }];
    }
}

@end
