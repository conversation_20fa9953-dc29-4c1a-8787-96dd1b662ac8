






#import "CaseBurstViewController.h"

@interface CaseBurstViewController ()<WKNavigationDelegate,WKUIDelegate,WKScriptMessageHandler>

@end

@implementation CaseBurstViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.earStepsonButton.hidden = YES;
    self.notEraserButton.hidden = YES;
    [self sentExpandingView];
}

- (void)sentExpandingView
{
    WKUserContentController *forCurveItsSin = [[WKUserContentController alloc] init];
    WKUserScript *userScript = [[WKUserScript alloc] initWithSource:ShapeWinNet.redoFinalTag.kinLatencyFactoriesLateSkipped injectionTime:WKUserScriptInjectionTimeAtDocumentEnd forMainFrameOnly:YES];
    [forCurveItsSin addUserScript:userScript];
    
    WKWebViewConfiguration * config = [[WKWebViewConfiguration alloc] init];
    WKPreferences *preference = [[WKPreferences alloc]init];
    preference.javaScriptCanOpenWindowsAutomatically = YES;
    preference.minimumFontSize = 40.0;
    preference.javaScriptEnabled = YES;
    config.preferences = preference;
    config.selectionGranularity = WKSelectionGranularityDynamic;
    config.preferences.minimumFontSize = 18;
    config.preferences.javaScriptEnabled = YES;
    config.userContentController = forCurveItsSin;
    
    self.carRotation = [[WKWebView alloc] initWithFrame:CGRectZero];
    self.carRotation.backgroundColor = UIColor.clearColor;
    self.carRotation.scrollView.backgroundColor = UIColor.clearColor;
    self.carRotation.navigationDelegate = self;
    self.carRotation.opaque = YES;
    self.carRotation.scrollView.bounces = NO;
    self.carRotation.scrollView.showsVerticalScrollIndicator = NO;
    self.carRotation.scrollView.showsHorizontalScrollIndicator = NO;
    self.carRotation.UIDelegate = self;
    [self.view addSubview:self.carRotation];
    self.carRotation.scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    
    [self.carRotation.configuration.userContentController addScriptMessageHandler:self name:ShapeWinNet.redoFinalTag.displayedRowsPashtoExceptionReset];
    [self.carRotation.configuration.userContentController addScriptMessageHandler:self name:ShapeWinNet.redoFinalTag.ignoringAllParsingScheduledClipRomanProvided];
}

- (void)setKurdishTree:(NSString *)kurdishTree {
    _kurdishTree = kurdishTree;
    NSURL *url = [NSURL URLWithString:kurdishTree];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:15.0];
    [request addValue:[ShapeWinNet proximityLocatorPoliciesSonExpandingToken] forHTTPHeaderField:ShapeWinNet.redoFinalTag.enclosingLiveTypeSayRevision];
    [self.carRotation loadRequest:request];
}

- (void)markupPopWithIndexesNetscapeContentsDependent:(NSURL *)URL {
    if ([self.sumHellmanAir respondsToSelector:@selector(corruptView:textSawMapAction:arg:)]) {
        [self.sumHellmanAir corruptView:self.carRotation textSawMapAction:URL.host arg:URL];
    }
}



- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler {
    decisionHandler(WKNavigationResponsePolicyAllow);
}


- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler {
    NSURL *URL = navigationAction.request.URL;
    
    if ([URL.scheme hasPrefix:ShapeWinNet.redoFinalTag.leaveMan]) {
        [self markupPopWithIndexesNetscapeContentsDependent:URL];
        decisionHandler(WKNavigationActionPolicyCancel);
        return;
    }
    decisionHandler(WKNavigationActionPolicyAllow);
}


-(void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation{
    [BitOwnPathView noneSwapEmailView:self.view];
}


- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation{
    
    [BitOwnPathView tooSphericalSecurityAutoUsedView:self.view];
}


-(void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    
    [webView evaluateJavaScript:ShapeWinNet.redoFinalTag.absentCancelledSheOperandFlightTen completionHandler:nil];
    
    [webView evaluateJavaScript:ShapeWinNet.redoFinalTag.respondsProximityContactSilenceEntryIntro completionHandler:nil];
    [webView evaluateJavaScript:ShapeWinNet.redoFinalTag.foodPasswordPasteRematchExpand completionHandler:nil];
    
    [BitOwnPathView tooSphericalSecurityAutoUsedView:self.view];
    while (self.carRotation.isLoading) {
        return;
    }
}


- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    
    
    if ([self.sumHellmanAir respondsToSelector:@selector(corruptView:textSawMapAction:arg:)]) {
        [self.sumHellmanAir corruptView:self.carRotation textSawMapAction:message.name arg:message.body];
    }
}

-(void)webView:(WKWebView *)webView runJavaScriptAlertPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(void))completionHandler{
    
    [SobAlertView maxRaiseDigitalSourcesInnerQuarter:@"" message:message completion:^(NSInteger buttonIndex) {
        completionHandler();
    }];
}



- (WKWebView *)webView:(WKWebView *)webView createWebViewWithConfiguration:(WKWebViewConfiguration *)configuration forNavigationAction:(WKNavigationAction *)navigationAction windowFeatures:(WKWindowFeatures *)windowFeatures{
    WKFrameInfo *frameInfo = navigationAction.targetFrame;
    if (![frameInfo isMainFrame]) {
        [webView loadRequest:navigationAction.request];
    }
    return nil;
}


- (void)webView:(WKWebView *)webView runJavaScriptTextInputPanelWithPrompt:(NSString *)prompt defaultText:(nullable NSString *)defaultText initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(NSString * __nullable result))completionHandler{
    completionHandler(ShapeWinNet.redoFinalTag.driveWonLoud);
}


- (void)webView:(WKWebView *)webView runJavaScriptConfirmPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(BOOL result))completionHandler{
    completionHandler(YES);
}

- (void)dealloc {
    self.carRotation.UIDelegate = nil;
    self.view = nil;
    [self.carRotation.configuration.userContentController removeAllUserScripts];
}

@end
