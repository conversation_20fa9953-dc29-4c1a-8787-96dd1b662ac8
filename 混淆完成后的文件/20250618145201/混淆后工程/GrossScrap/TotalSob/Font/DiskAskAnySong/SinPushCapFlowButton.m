






#import "SinPushCapFlowButton.h"
#import "ShapeWinNet.h"
#import "UIImage+OwnImage.h"
#import "LoopsEyeRomanController.h"
#import "UIImage+OwnImage.h"
#import "UIColor+GetColor.h"

@implementation SinPushCapFlowButton

- (instancetype)initFlowItsFlipViewController:(UIViewController *)viewController {
    self = [super init];
    if (self) {
        self.eyeSubmitSayViewController = viewController;
        [self garbageBadQuoteBackupMath];
    }
    return self;
}


- (void)garbageBadQuoteBackupMath {
    
    NSArray *dateSegmentsSlovakGigahertzIrish = [VerboseDigestSignUnorderedSynthesis napPointFunctionFoodCurrency:[CellTapCan class]];
    
    
    NSString *currentCountryCode = [[NSLocale currentLocale] objectForKey:NSLocaleCountryCode];
    
    __block CellTapCan *matchedCountry = nil;
    [dateSegmentsSlovakGigahertzIrish enumerateObjectsUsingBlock:^(CellTapCan *country, NSUInteger idx, BOOL *stop) {
        if ([country.pageAlphabetCode caseInsensitiveCompare:currentCountryCode] == NSOrderedSame) {
            matchedCountry = country;
            *stop = YES; 
        }
    }];
    self.listenersSlowBurstTelephonyPreparing = matchedCountry;
    
    
    NSString *title = [NSString stringWithFormat:@"%@%@",ShapeWinNet.redoFinalTag.optClickBegin, matchedCountry.utilitiesCode];
    [self setTitle:title forState:UIControlStateNormal];
    
    
    UIImage *errorItsImage = [UIImage domainIndexObserverUploadingInitiatedName:ShapeWinNet.redoFinalTag.cousinAltitudeElapsedSkipPartlyFiber];
    
    
    CGSize targetImageSize = CGSizeMake(13, 13); 
    
    
    UIImage *scaledImage = [self chestPerformedImage:errorItsImage sugarAnySize:targetImageSize];
    
    
    [self setImage:scaledImage forState:UIControlStateNormal];
    [self setImage:[scaledImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateHighlighted]; 
    
    
    self.imageView.contentMode = UIViewContentModeScaleAspectFit;
    
    
    self.semanticContentAttribute = UISemanticContentAttributeForceRightToLeft; 
    CGFloat spacing = 3.0; 
    self.imageEdgeInsets = UIEdgeInsetsMake(0, spacing, 0, -spacing);  
    self.titleEdgeInsets = UIEdgeInsetsMake(0, -spacing, 0, spacing);   
    
    
    [self setBackgroundImage:[UIImage lateGenreCloudColor:[ShapeWinNet.exposuresColor personBoxOccurredHebrewGoogleMalformed:8]] forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage lateGenreCloudColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]]
                   forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont systemFontOfSize:16];
    self.layer.cornerRadius = 2.f;
    self.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMinXMaxYCorner;
    self.layer.masksToBounds = YES;
    
    
    self.contentEdgeInsets = UIEdgeInsetsMake(8, 12, 8, 12); 
    
    
    [self sizeToFit];
    
    
    [self addTarget:self action:@selector(eventHitEyeClicked) forControlEvents:UIControlEventTouchUpInside];
}


- (UIImage *)chestPerformedImage:(UIImage *)image sugarAnySize:(CGSize)targetSize {
    
    UIGraphicsBeginImageContextWithOptions(targetSize, NO, 0.0);
    
    
    CGFloat zipTabular = targetSize.width / image.size.width;
    CGFloat soloFetched = targetSize.height / image.size.height;
    CGFloat scaleFactor = MIN(zipTabular, soloFetched);
    
    
    CGRect tapCarRect = CGRectMake(0, 0,
                                  image.size.width * scaleFactor,
                                  image.size.height * scaleFactor);
    
    
    CGPoint origin = CGPointMake((targetSize.width - tapCarRect.size.width) / 2.0,
                               (targetSize.height - tapCarRect.size.height) / 2.0);
    [image drawInRect:CGRectMake(origin.x, origin.y,
                                tapCarRect.size.width,
                                tapCarRect.size.height)];
    
    UIImage *bedImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return bedImage;
}


- (void)eventHitEyeClicked {
    FloaterBendTerabytesCopperTaggingReasonViewController *vc = [FloaterBendTerabytesCopperTaggingReasonViewController new];
    vc.nowKazakhDelegate = self;
    [self.eyeSubmitSayViewController.navigationController pushViewController:vc animated:NO];
}

- (void)infinityChangeFloatingFocusPhotoAnimatorBendSubset:(CellTapCan *)country {
    NSString *title = [NSString stringWithFormat:@"%@%@",ShapeWinNet.redoFinalTag.optClickBegin, country.utilitiesCode];
    [self setTitle:title forState:UIControlStateNormal];
    self.listenersSlowBurstTelephonyPreparing = country;
}

- (void)dealloc {
    
}
@end
