






#import "SlavicTapTextField.h"
#import "ShapeWinNet.h"
#import "Masonry.h"
#import "NSString+StickySay.h"
#import "SinPushCapFlowButton.h"

@interface SlavicTapTextField()

@property (nonatomic,strong) SinPushCapFlowButton *momentaryKazakhDocumentsHairPopButton;

@end

@implementation SlavicTapTextField

- (instancetype)initWithController:(UIViewController *)vc
{
    self = [super init];
    if (self) {
        self.layer.borderColor = [ShapeWinNet exposuresColor].CGColor;
        self.layer.borderWidth = 0.6;
        self.layer.cornerRadius = 2;
        

        self.momentaryKazakhDocumentsHairPopButton = [[SinPushCapFlowButton alloc] initFlowItsFlipViewController:vc];
        [self addSubview:self.momentaryKazakhDocumentsHairPopButton];
        [self.momentaryKazakhDocumentsHairPopButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(0);
            make.left.mas_equalTo(0);
            make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
        }];
        
        [self.momentaryKazakhDocumentsHairPopButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
        
        
        self.whoRedDigitTextField = [ShapeWinNet notEmbeddingBelowCentersZeroUnion];
        self.whoRedDigitTextField.layer.borderWidth = 0;
        self.whoRedDigitTextField.layer.cornerRadius = 2.f;
        self.whoRedDigitTextField.layer.maskedCorners = kCALayerMaxXMaxYCorner;
        self.whoRedDigitTextField.layer.masksToBounds = YES;
        [self addSubview:self.whoRedDigitTextField];
        [self.whoRedDigitTextField mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(0);
make.left.mas_equalTo(self.momentaryKazakhDocumentsHairPopButton.mas_right);

            make.right.mas_equalTo(0);
            make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
        }];
    }
    return self;
}

- (NSString *)fileBigHandPub {
return [NSString stringWithFormat:@"%@%@",ShapeWinNet.redoFinalTag.optClickBegin,[self.momentaryKazakhDocumentsHairPopButton.listenersSlowBurstTelephonyPreparing.utilitiesCode stringByReplacingOccurrencesOfString:@" " withString:@""]];
    return @"";
}

- (NSString *)tempFooterMusicianTriggersExpand {
    return self.whoRedDigitTextField.text.uplinkUseDetailedTeamVolatile ? [NSString stringWithFormat:@"%@%@",self.fileBigHandPub,self.whoRedDigitTextField.text] : @"";
}
@end
