






#import "LongRaceView.h"
#import "UIImageView+WebCache.h"
#import "UIImage+OwnImage.h"
#import "ShapeWinNet.h"
#import "NSString+StickySay.h"
#import "SobAlertView.h"
#import "TildeScanPlaneWindow.h"
#import "LeapHueHeapSunViewController.h"
#import "UIDevice+BarDevice.h"
#import "HitUnableManager.h"

@interface LongRaceView()  <UIGestureRecognizerDelegate> {
    CGPoint deprecateFactoredPurchasedYearSlabEmbedding;
    BOOL senseOnlineCleanSumMeal;
    BOOL undoFemaleChallengeExposeSelectedBypassed; 
    BOOL outIntersectExternalHigherColored; 
}


@property (nonatomic, strong) TildeScanPlaneWindow *iconNapMidWindow;
@property (nonatomic, weak) UIWindow *earlierPriceWindow;


@property (nonatomic, strong) UIImageView *dueSquareView;
@property (nonatomic, strong) UIView *drainDueWasView;


@property (nonatomic, strong) UIView *outletMayView;
@property (nonatomic, strong) UILabel *runUpsideLabel;
@property (nonatomic, assign) BOOL shrinkMusicLocalityAndCat;


@property (nonatomic, strong) NSTimer *sinMixHighSawTimer;
@property (nonatomic, assign) UIEdgeInsets offsetsCreatingCubicUnderlineResponder;
@property (nonatomic, assign) CGRect stoneConfirmHierarchyModernDueCriteria;


@property (nonatomic, strong) UIImage *engravedPubImage;
@property (nonatomic, copy) NSString *normalAudiencesMinimalGaspBaltic;
@property (nonatomic, strong) UIImage *sinhaleseImage;
@property (nonatomic, assign) CGFloat menuWetOddCrop;
@property (nonatomic, assign) WhoMirroringEdge nowRepeatKinEdge;
@property (nonatomic, assign) NSTimeInterval formatsLimitSheDecomposeIndexes;
@property (nonatomic, assign) BOOL alertControlFatSizeMinIntervals;
@end

@implementation LongRaceView


+ (instancetype)shared {
    static LongRaceView *instance = nil;
    static dispatch_once_t rareToken;
    dispatch_once(&rareToken, ^{
        instance = [[super alloc] initWithFrame:CGRectZero];
        [instance ordinalReferenceParserDolbyRead];
    });
    return instance;
}

- (UIView *)drainDueWasView {
    if (!_drainDueWasView) {
        _drainDueWasView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 8, 8)];
        _drainDueWasView.backgroundColor = UIColor.redColor;
        _drainDueWasView.layer.cornerRadius = 4;
        _drainDueWasView.hidden = YES;
    }
    return _drainDueWasView;
}

- (void)ordinalReferenceParserDolbyRead {
    self.menuWetOddCrop = 10.0;
    self.formatsLimitSheDecomposeIndexes = 3.0;
    self.alertControlFatSizeMinIntervals = YES;
    
    
    self.dueSquareView = [[UIImageView alloc] init];
    self.dueSquareView.contentMode = UIViewContentModeScaleAspectFit;
    [self addSubview:self.dueSquareView];
    
    self.outletMayView = [[UIView alloc] init];
    self.outletMayView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.7];
    self.outletMayView.layer.cornerRadius = 20;
    self.outletMayView.layer.masksToBounds = YES;
    self.outletMayView.alpha = 0.0;
    
    self.runUpsideLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 80, 40)];
    self.runUpsideLabel.text = ShapeWinNet.daySeeWaistUse.sensorMealRedefinedCategoryBengaliHeadphone;
    self.runUpsideLabel.numberOfLines = 0;
    self.runUpsideLabel.textColor = [UIColor whiteColor];
    self.runUpsideLabel.textAlignment = NSTextAlignmentCenter;
    self.runUpsideLabel.font = [UIFont systemFontOfSize:14];
    [self.outletMayView addSubview:self.runUpsideLabel];
    
    
    UIPanGestureRecognizer *net = [[UIPanGestureRecognizer alloc]
                                   initWithTarget:self
                                   action:@selector(walkOnceSayAge:)];
    net.delegate = self;
    [self addGestureRecognizer:net];
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]
                                   initWithTarget:self
                                   action:@selector(markupFeedback)];
    [self addGestureRecognizer:tap];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(eventualSubTraveledCharacterAskLogical)
                                                 name:UIApplicationDidChangeStatusBarOrientationNotification
                                               object:nil];
#pragma clang diagnostic pop
}

- (void)setTeamPluralJson:(NSDictionary *)teamPluralJson {
    _teamPluralJson = teamPluralJson;
    if (teamPluralJson && self.iconNapMidWindow != nil) {
        NSString *action = teamPluralJson[ShapeWinNet.redoFinalTag.scopeWhileTail];
        if ([action isEqualToString:ShapeWinNet.redoFinalTag.cubeWhileOpt]) {
            self.drainDueWasView.hidden = NO;
        }else if ([action isEqualToString:ShapeWinNet.redoFinalTag.supportsMark]) {
            self.drainDueWasView.hidden = YES;
        }else if ([action isEqualToString:ShapeWinNet.redoFinalTag.touchIcyLikeInfinityIcon]) {
            self.drainDueWasView.hidden = NO;
        }
    }
}


+ (void)andDublin {
    [self.shared levelWaitClampFreestylePutAnonymousImage:[UIImage domainIndexObserverUploadingInitiatedName:ShapeWinNet.redoFinalTag.pointMemoryBeginningSystemRecognize] nowTable:ShapeWinNet.pathSexProducedAdjustPreserves roleImage:nil];
}

+ (void)tooValueSliceImage:(UIImage *)image {
    [[self shared] levelWaitClampFreestylePutAnonymousImage:image roleImage:nil];
}

+ (void)reflectLoadImage:(UIImage *)normalImage roleImage:(nullable UIImage *)roleImage {
    LongRaceView *instance = [self shared];
    instance.engravedPubImage = normalImage;
    instance.normalAudiencesMinimalGaspBaltic = nil;
    instance.sinhaleseImage = roleImage;
    instance.dueSquareView.image = normalImage;
}

+ (void)handleAny {
    [[self shared] recursiveGivenBitsPhoneLexicon];
}

+ (BOOL)darkArtLowPick {
    return [self shared].iconNapMidWindow != nil;
}


- (void)levelWaitClampFreestylePutAnonymousImage:(UIImage *)image roleImage:(nullable UIImage *)roleImage {
    [self levelWaitClampFreestylePutAnonymousImage:image nowTable:nil roleImage:roleImage];
}

- (void)levelWaitClampFreestylePutAnonymousImage:(UIImage *)image nowTable:(NSString *)nowTable roleImage:(nullable UIImage *)roleImage {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.engravedPubImage = image;
        self.normalAudiencesMinimalGaspBaltic = nowTable;
        self.sinhaleseImage = roleImage;
        
        if (!self.iconNapMidWindow) {
            [self sideMetadataAlignAttributeStoodWindow];
            [self eventualSelfStreamsClaimIcon];
            [self doubleDingbatsWrapperInterestDecodeHeader];
            [self evaluateExtrasClusterEarlyRenameSigmoid]; 
        }
        
        [self.iconNapMidWindow makeKeyAndVisible];
        [self.earlierPriceWindow makeKeyWindow];
        
        [self evictSucceededSafeJobAboveLargestAnimation:YES];
        [self finderExpiredArteryRetriedSequenceTimer];
    });
}

- (void)recursiveGivenBitsPhoneLexicon {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.iconNapMidWindow resignKeyWindow];
        self.iconNapMidWindow.hidden = YES;
        self.iconNapMidWindow = nil;
    });
}


- (void)sideMetadataAlignAttributeStoodWindow {
    
    self.earlierPriceWindow = [self endDescenderSockCanLocalizedWindow];
    
    
    TildeScanPlaneWindow *window = nil;
    
    
    if (@available(iOS 13.0, *)) {
        for (UIScene *scene in [UIApplication sharedApplication].connectedScenes) {
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                window = [[TildeScanPlaneWindow alloc] initWithWindowScene:(UIWindowScene *)scene];
                break;
            }
        }
    }
    
    
    if (!window) {
        window = [[TildeScanPlaneWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    }
    
    
    window.backgroundColor = [UIColor clearColor];
    window.clipsToBounds = YES; 
    window.windowLevel = UIWindowLevelAlert + 1000;
    window.backgroundColor = [UIColor clearColor];
    window.rootViewController = [[LeapHueHeapSunViewController alloc] init];
    window.hidden = NO;
    self.iconNapMidWindow = window;
    
    
    [self.iconNapMidWindow resignKeyWindow];
    [self.earlierPriceWindow makeKeyWindow];
    
    
    [self addSubview:self.drainDueWasView];
    
    
    self.frame = CGRectMake(0, 0, 60, 60);
    if (self.normalAudiencesMinimalGaspBaltic) {
        [self.dueSquareView sd_setImageWithURL:[NSURL URLWithString:self.normalAudiencesMinimalGaspBaltic] placeholderImage
                                              :[UIImage domainIndexObserverUploadingInitiatedName:ShapeWinNet.redoFinalTag.pointMemoryBeginningSystemRecognize]
                                       options:(SDWebImageDelayPlaceholder)];
    }else {
        self.dueSquareView.image = self.engravedPubImage;
    }
    self.dueSquareView.frame = self.bounds;
    
    [self.iconNapMidWindow addSubview:self];
    [self.iconNapMidWindow addSubview:self.outletMayView];
}


- (void)evaluateExtrasClusterEarlyRenameSigmoid {
    CGRect curveMuteAny = self.stoneConfirmHierarchyModernDueCriteria;
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [UIApplication sharedApplication].statusBarOrientation;
#pragma clang diagnostic pop
    
    if (UIInterfaceOrientationIsLandscape(orientation)) {
        CGFloat north = 180;
        self.outletMayView.frame = CGRectMake(
         (curveMuteAny.size.width - north)/2,
          curveMuteAny.size.height - north/2,
          north,
          north
        );
        self.outletMayView.layer.masksToBounds = YES;
        self.outletMayView.layer.cornerRadius = north/2;
        self.runUpsideLabel.center = CGPointMake(north/2, north/4);
    }
    
    else {
        CGFloat north = 240;
        self.outletMayView.frame = CGRectMake(

          (curveMuteAny.size.width - north/2),
          curveMuteAny.size.height - north/2,
          north,
          north
        );
        self.outletMayView.layer.masksToBounds = YES;
        self.outletMayView.layer.cornerRadius = north/2;
        self.runUpsideLabel.center = CGPointMake(north/3, north/4);
    }
}


- (void)markupFeedback {
    if (self.teamPluralJson) {
        !self.pageMealHandler ?: self.pageMealHandler(self.teamPluralJson[ShapeWinNet.redoFinalTag.unboundEasy]);
        if ([self.teamPluralJson[ShapeWinNet.redoFinalTag.scopeWhileTail] isEqualToString:ShapeWinNet.redoFinalTag.touchIcyLikeInfinityIcon]) {
            self.drainDueWasView.hidden = YES;
            _teamPluralJson = nil;
        }
    }else {
        !self.pageMealHandler ?: self.pageMealHandler(nil);
    }
}

- (void)walkOnceSayAge:(UIPanGestureRecognizer *)gesture {
    if (senseOnlineCleanSumMeal) return;
        
    CGPoint translation = [gesture translationInView:self.superview];
    
    switch (gesture.state) {
        case UIGestureRecognizerStateBegan:
            deprecateFactoredPurchasedYearSlabEmbedding = self.center;
            _dueSquareView.alpha = 1;
            [self boundMakeParagraphReplyAdditive];
            undoFemaleChallengeExposeSelectedBypassed = NO; 
            outIntersectExternalHigherColored = NO; 
            
            
            [self.layer removeAllAnimations];
            [self.outletMayView.layer removeAllAnimations];
            
            
            self.outletMayView.alpha = 0.0;
            self.outletMayView.transform = CGAffineTransformIdentity;
            break;
            
        case UIGestureRecognizerStateChanged:{
            
            self.center = [self reactorStrongClickSpacingJouleMarkSixteenCenter:
                           CGPointMake(deprecateFactoredPurchasedYearSlabEmbedding.x + translation.x,
                                       deprecateFactoredPurchasedYearSlabEmbedding.y + translation.y)];
            
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
            
            BOOL TrashNapRaw = UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation);
#pragma clang diagnostic pop
            CGRect hintFrame = self.outletMayView.frame;
            CGRect touchArea = CGRectInset(hintFrame, -280, TrashNapRaw?-100:-280); 
            BOOL isInHideArea = CGRectContainsPoint(touchArea, self.center);
            
            
            
            
            if (isInHideArea != outIntersectExternalHigherColored) {
                outIntersectExternalHigherColored = isInHideArea;
                
                
                [UIView animateWithDuration:0.3
                                      delay:0
                                    options:UIViewAnimationOptionBeginFromCurrentState
                                 animations:^{
                    self.outletMayView.alpha = isInHideArea ? 1.0 : 0.0;
                    self.outletMayView.transform = isInHideArea ? CGAffineTransformMakeScale(1.2, 1.2) : CGAffineTransformIdentity;
                } completion:nil];
            }
            
            
            isInHideArea = CGRectContainsPoint(CGRectInset(hintFrame, 0, 0), self.center);
            if (isInHideArea && !undoFemaleChallengeExposeSelectedBypassed) {
                UIImpactFeedbackGenerator *applying = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleMedium];
                [applying prepare]; 
                [applying impactOccurred];
                undoFemaleChallengeExposeSelectedBypassed = YES;
                
                
                [UIView animateWithDuration:0.3
                                      delay:0
                                    options:UIViewAnimationOptionBeginFromCurrentState
                                 animations:^{
                    self.outletMayView.transform = CGAffineTransformMakeScale(1.3, 1.3);
                } completion:nil];
            } else if (!isInHideArea) {
                if (undoFemaleChallengeExposeSelectedBypassed) {
                    self.outletMayView.transform = CGAffineTransformMakeScale(1.2, 1.2);
                }
                undoFemaleChallengeExposeSelectedBypassed = NO;
            }
            
            
            touchArea = CGRectInset(hintFrame, 0, 0);
            _shrinkMusicLocalityAndCat = CGRectContainsPoint(touchArea, self.center);
            break;
        }
            
        case UIGestureRecognizerStateEnded:
        case UIGestureRecognizerStateCancelled: {
            
            [UIView animateWithDuration:0.3 animations:^{
                self.outletMayView.alpha = 0.0;
                self.outletMayView.transform = CGAffineTransformIdentity;
            }];
            
            if (_shrinkMusicLocalityAndCat) {
                [SobAlertView maxRaiseDigitalSourcesInnerQuarter:nil message:ShapeWinNet.daySeeWaistUse.programPaddleEndsPlugPaymentsFocal lowerDogOnce:@[ShapeWinNet.daySeeWaistUse.ignoringDay, ShapeWinNet.daySeeWaistUse.dueChar] completion:^(NSInteger buttonIndex) {
                    if (buttonIndex ==1) {
                        [self recursiveGivenBitsPhoneLexicon];
                    }else {
                        [self evictSucceededSafeJobAboveLargestAnimation:YES];
                        [self finderExpiredArteryRetriedSequenceTimer];
                    }
                }];
            } else {
                [self evictSucceededSafeJobAboveLargestAnimation:YES];
                [self finderExpiredArteryRetriedSequenceTimer];
            }
            undoFemaleChallengeExposeSelectedBypassed = NO;
            outIntersectExternalHigherColored = NO;
            break;
        }

        default: break;
    }
}


- (void)evictSucceededSafeJobAboveLargestAnimation:(BOOL)animate {
    if (!_alertControlFatSizeMinIntervals) return;
    
    
    if (senseOnlineCleanSumMeal && animate) return;
    
    CGRect sameFrame = [self bringVoiceSpeechSecureMatchFrame];
    CGPoint center = self.center;
    
    CGFloat cube = sameFrame.origin.x;
    CGFloat trap = sameFrame.origin.x + sameFrame.size.width;
    CGFloat plus = sameFrame.origin.y;
    CGFloat logo = sameFrame.origin.y + sameFrame.size.height;
    
    
    WhoMirroringEdge targetEdge = ProxyMenProfilesPressedChatSmall;
    CGFloat minDistance = CGFLOAT_MAX;
    
    
    CGFloat toLeft = center.x - cube;
    CGFloat toRight = trap - center.x;
    CGFloat toTop = center.y - plus;
    CGFloat toBottom = logo - center.y;
    
    NSArray *distances = @[@(toLeft), @(toRight), @(toTop), @(toBottom)];
    NSArray *edges = @[@(WorldMenWaySourceEncryptedMirrored), @(AskAdjustAlcoholKazakhStrictConstruct),
                       @(DragPhoneticHeaderLessLongest), @(DomainIncomingMailCarCubeNotify)];
    
    for (NSInteger i = 0; i < distances.count; i++) {
        CGFloat distance = [distances[i] floatValue];
        if (distance < minDistance) {
            minDistance = distance;
            targetEdge = [edges[i] integerValue];
        }
    }
    
    
    if (targetEdge == self.nowRepeatKinEdge) {
        CGPoint serviceCenter = self.center;
        CGPoint originCenter = [self purpleTradDecoderHeadphoneInitiallyEncipherEdge:targetEdge];
        CGFloat distance = hypot(serviceCenter.x - originCenter.x, serviceCenter.y - originCenter.y);
        if (distance < 5.0) { 
            return;
        }
    }
    
    self.nowRepeatKinEdge = targetEdge;
    
    
    CGPoint originCenter = [self purpleTradDecoderHeadphoneInitiallyEncipherEdge:targetEdge];
    CGPoint forceCenter = [self beatPreparingMixEsperantoOffMergeFloaterEdge:targetEdge];
    
    
    senseOnlineCleanSumMeal = YES;
    
    
    [CATransaction begin];
    [CATransaction setCompletionBlock:^{
        self->senseOnlineCleanSumMeal = NO;
    }];
    
    [UIView animateWithDuration:animate ? 0.3 : 0
                     animations:^{
        self.center = originCenter;
        self.drainDueWasView.center = forceCenter;
    }];
    
    [CATransaction commit];
}


- (CGPoint)purpleTradDecoderHeadphoneInitiallyEncipherEdge:(WhoMirroringEdge)edge {
    CGRect sameFrame = [self bringVoiceSpeechSecureMatchFrame];
    CGPoint center = self.center;
    
    CGFloat cube = sameFrame.origin.x;
    CGFloat trap = sameFrame.origin.x + sameFrame.size.width;
    CGFloat plus = sameFrame.origin.y;
    CGFloat logo = sameFrame.origin.y + sameFrame.size.height;
    
    CGPoint originCenter = center;
    
    switch (edge) {
        case WorldMenWaySourceEncryptedMirrored:
            originCenter.x = cube + self.bounds.size.width/2 + _menuWetOddCrop;
            break;
        case AskAdjustAlcoholKazakhStrictConstruct:
            originCenter.x = trap - self.bounds.size.width/2 - _menuWetOddCrop;
            break;
        case DragPhoneticHeaderLessLongest:
            originCenter.y = plus + self.bounds.size.height/2 + _menuWetOddCrop;
            break;
        case DomainIncomingMailCarCubeNotify:
            originCenter.y = logo - self.bounds.size.height/2 - _menuWetOddCrop;
            break;
        default:
            break;
    }
    
    
    return [self reactorStrongClickSpacingJouleMarkSixteenCenter:originCenter];
}


- (CGPoint)beatPreparingMixEsperantoOffMergeFloaterEdge:(WhoMirroringEdge)edge {
    CGPoint forceCenter = CGPointMake(0, 0);
    
    switch (edge) {
        case WorldMenWaySourceEncryptedMirrored:
            forceCenter.x = self.bounds.size.width;
            break;
        case AskAdjustAlcoholKazakhStrictConstruct:
            
            break;
        case DragPhoneticHeaderLessLongest:
            forceCenter.x = self.bounds.size.width;
            forceCenter.y = self.bounds.size.height;
            break;
        case DomainIncomingMailCarCubeNotify:
            forceCenter.x = self.bounds.size.width;
            break;
        default:
            break;
    }
    
    return forceCenter;
}


- (void)finderExpiredArteryRetriedSequenceTimer {
    if (_formatsLimitSheDecomposeIndexes <= 0) return;
    
    [self boundMakeParagraphReplyAdditive];
    _sinMixHighSawTimer = [NSTimer scheduledTimerWithTimeInterval:_formatsLimitSheDecomposeIndexes
                                                     target:self
                                                   selector:@selector(equalAlignmentStripSecretRomanian)
                                                   userInfo:nil
                                                    repeats:NO];
}

- (void)boundMakeParagraphReplyAdditive {
    [_sinMixHighSawTimer invalidate];
    _sinMixHighSawTimer = nil;
}

- (void)equalAlignmentStripSecretRomanian {
    [UIView animateWithDuration:0.3 animations:^{
        self.dueSquareView.alpha = 0.5;
        
        CGRect frame = self.frame;
        switch (self.nowRepeatKinEdge) {
            case WorldMenWaySourceEncryptedMirrored:
                frame.origin.x -= self.menuWetOddCrop;
                break;
            case AskAdjustAlcoholKazakhStrictConstruct:
                frame.origin.x += self.menuWetOddCrop;
                break;
            case DragPhoneticHeaderLessLongest:
                frame.origin.y -= self.menuWetOddCrop;
                break;
            case DomainIncomingMailCarCubeNotify:
                frame.origin.y += self.menuWetOddCrop;
                break;
            default:
                break;
        }
        self.frame = frame;
    }];
}


- (void)eventualSubTraveledCharacterAskLogical {
    [self eventualSelfStreamsClaimIcon];
    [self evaluateExtrasClusterEarlyRenameSigmoid]; 
    [self evictSucceededSafeJobAboveLargestAnimation:YES];
}


- (void)eventualSelfStreamsClaimIcon {
    UIWindow *keyWindow = HitUnableManager.shared.laterExceedsWindow; //self.earlierPriceWindow;
    UIEdgeInsets safeArea = UIEdgeInsetsZero;
    if (![UIDevice abnormal]) {
        safeArea = UIEdgeInsetsZero;
    }else if([UIDevice waxMax]) {
        safeArea = UIEdgeInsetsMake(0, 0, 20, 0);
    }else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
        UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
        safeArea = keyWindow.safeAreaInsets;
        switch (orientation) {
            case UIInterfaceOrientationPortrait:
                safeArea = UIEdgeInsetsMake(safeArea.top-10, 5, 15, 5);
                break;
            case UIInterfaceOrientationPortraitUpsideDown:
                safeArea = UIEdgeInsetsMake(15, 5, safeArea.bottom-10, 5);
                break;
            case UIInterfaceOrientationLandscapeRight:
                safeArea = UIEdgeInsetsMake(5, safeArea.right-10, 15, 5);
                break;
            case UIInterfaceOrientationLandscapeLeft:
                safeArea = UIEdgeInsetsMake(5, 5, 15, safeArea.left-10);
                break;
            case UIInterfaceOrientationUnknown:
            default:
                safeArea = safeArea;
        }
    }
    
    self.offsetsCreatingCubicUnderlineResponder = safeArea;
    self.stoneConfirmHierarchyModernDueCriteria = keyWindow.bounds;
}

- (CGRect)bringVoiceSpeechSecureMatchFrame {
    
    return CGRectMake(
        self.stoneConfirmHierarchyModernDueCriteria.origin.x + self.offsetsCreatingCubicUnderlineResponder.left,
        self.stoneConfirmHierarchyModernDueCriteria.origin.y + self.offsetsCreatingCubicUnderlineResponder.top,
        self.stoneConfirmHierarchyModernDueCriteria.size.width - (self.offsetsCreatingCubicUnderlineResponder.left + self.offsetsCreatingCubicUnderlineResponder.right),
        self.stoneConfirmHierarchyModernDueCriteria.size.height - (self.offsetsCreatingCubicUnderlineResponder.top + self.offsetsCreatingCubicUnderlineResponder.bottom)
    );
}


- (void)doubleDingbatsWrapperInterestDecodeHeader {
    NSString *applierShake = [[NSUserDefaults standardUserDefaults] valueForKey:ShapeWinNet.redoFinalTag.holdJustResponsesSequencerExtrasFeat];
    if (applierShake) {
        self.center = CGPointFromString(applierShake);
    }else {
        
        CGRect sameFrame = [self bringVoiceSpeechSecureMatchFrame];
        self.center = CGPointMake(sameFrame.origin.x + sameFrame.size.width - self.bounds.size.width/2 - _menuWetOddCrop,
                                  sameFrame.origin.y + sameFrame.size.height/2);
    }
}


- (UIWindow *)endDescenderSockCanLocalizedWindow {
    if (@available(iOS 13.0, *)) {
        NSSet<UIScene *> *scenes = [UIApplication sharedApplication].connectedScenes;
        for (UIScene *scene in scenes) {
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                return windowScene.windows.firstObject;
            }
        }
    }
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    return [UIApplication sharedApplication].keyWindow;
#pragma clang diagnostic pop
}

- (CGPoint)reactorStrongClickSpacingJouleMarkSixteenCenter:(CGPoint)proposedCenter {
    CGRect sameFrame = [self bringVoiceSpeechSecureMatchFrame];
    CGSize obtainSize = self.bounds.size;
    
    CGFloat cube = sameFrame.origin.x + obtainSize.width/2;
    CGFloat trap = sameFrame.origin.x + sameFrame.size.width - obtainSize.width/2;
    CGFloat plus = sameFrame.origin.y + obtainSize.height/2;
    CGFloat logo = sameFrame.origin.y + sameFrame.size.height - obtainSize.height/2;
    
    return CGPointMake(
        MAX(cube, MIN(proposedCenter.x, trap)),
        MAX(plus, MIN(proposedCenter.y, logo))
    );
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
