







#import <UIKit/UIKit.h>
#import "HomePhraseRankCell.h"

@protocol LocalTitleLineDelegate;


@interface DryBarInterKey : UIView


@property (weak, nonatomic) id<LocalTitleLineDelegate> delegate;


- (void)backupFireBusy:(NSArray <HomePhraseRankCell *> *)barrages;


- (void)start;


- (void)stop;

@end


@protocol LocalTitleLineDelegate <NSObject>

@optional


- (void)largerKitDidView:(DryBarInterKey *)foldDayView oldYoungestCell:(HomePhraseRankCell *)cell;


- (void)generateMismatchSindhiBelowPingCaptureFigurePeriod:(DryBarInterKey *)foldDayView;


- (void)largerKitDidView:(DryBarInterKey *)foldDayView willDisplayCell:(HomePhraseRankCell *)cell;


- (void)largerKitDidView:(DryBarInterKey *)foldDayView didEndDisplayingCell:(HomePhraseRankCell *)cell;

@end
