








#import "DryBarInterKey.h"

#define OldSmileRight self.frame.size.width

@interface DryBarInterKey()



@property (nonatomic, strong) NSMutableArray                            *forTakeArray;



@property (nonatomic, strong) NSMutableArray <HomePhraseRankCell *>      *footArray;



@property (strong, nonatomic) NSMutableArray <HomePhraseRankCell *>      *nearPlainFit;



@property (assign, nonatomic) NSInteger                                 count;



@property (nonatomic, assign) ObservingQuitSolidHundredsKurdish                          status;



@property (nonatomic, assign) NSInteger                                 channelCount;



@property (nonatomic, assign) CGFloat                                   anyBad;

@end

@implementation DryBarInterKey

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.status = AsteriskAreDryInventoryManualTint;
    }
    return self;
}

- (void)cellOffFirst
{
    
    if (self.footArray.firstObject) {
        
        
        HomePhraseRankCell *foldDayView = self.footArray.firstObject;
        
        foldDayView.frame = CGRectMake(OldSmileRight, 0, foldDayView.loudTopSize.width, foldDayView.loudTopSize.height);
        
        self.anyBad = foldDayView.anyBad;
        
        self.channelCount = foldDayView.channelCount;
        
        
        NSInteger row = [self canUnderageTwoBusRecoveryDecimalSkip:foldDayView];
        
        
        if (row >= 0) {
            
            
            [self.footArray removeObjectAtIndex:0];
            
            
            if (![self.subviews containsObject:foldDayView]) {
                [self addSubview:foldDayView];
            }
            foldDayView.frame = CGRectMake(OldSmileRight,  row * (foldDayView.loudTopSize.height + _anyBad), foldDayView.loudTopSize.width, foldDayView.loudTopSize.height);
            
            
            [_forTakeArray setObject:foldDayView atIndexedSubscript:row];
            
            
            if ([self.delegate respondsToSelector:@selector(largerKitDidView:willDisplayCell:)]) {
                [self.delegate largerKitDidView:self willDisplayCell:foldDayView];
            }
            
            
            [self.nearPlainFit addObject:foldDayView];
            
            [foldDayView meterSelfAppendConcertStep:^{
                
                
                [foldDayView setTransform:CGAffineTransformMakeTranslation(- foldDayView.frame.size.width-OldSmileRight, 0)];
                
            } completion:^(BOOL finished) {
                
                [foldDayView removeFromSuperview];
                
                
                [self.nearPlainFit removeObject:foldDayView];
                
                
                if ([self.delegate respondsToSelector:@selector(largerKitDidView:didEndDisplayingCell:)]) {
                    [self.delegate largerKitDidView:self didEndDisplayingCell:foldDayView];
                }
                
                
                if (--self.count <= 0) {
                    if ([self.delegate respondsToSelector:@selector(generateMismatchSindhiBelowPingCaptureFigurePeriod:)]) {
                        [self.delegate generateMismatchSindhiBelowPingCaptureFigurePeriod:self];
                    }
                    self.count = 0;
                }
                
                

            }];
        }
    }
    
    [self performSelector:@selector(cellOffFirst) withObject:nil afterDelay:0.45f];
}


- (void)backupFireBusy:(NSArray <HomePhraseRankCell *> *)barrages
{
    self.count += barrages.count;
    [self.footArray addObjectsFromArray:barrages];
}

- (void)start
{
    if (self.status == CompileConflictDogFarDrainJob) {
        return;
    }
    self.status = CompileConflictDogFarDrainJob;
    
    [self cellOffFirst];
}

- (void)stop
{
    if (self.status == AsteriskAreDryInventoryManualTint) {
        return;
    }
    self.status = AsteriskAreDryInventoryManualTint;
    
    if (self.nearPlainFit.count) {
        [self.nearPlainFit makeObjectsPerformSelector:@selector(pause)];
    }
    
    if (self.footArray.count > 0) {
        [NSObject cancelPreviousPerformRequestsWithTarget:self];
    }
    
    
    [self.nearPlainFit  makeObjectsPerformSelector:@selector(removeFromSuperview)];
    self.channelCount       = 0;
    self.count              = 0;
    [self.nearPlainFit  removeAllObjects];
    [self.footArray     removeAllObjects];
    [self.forTakeArray  removeAllObjects];
    
    self.nearPlainFit       = nil;
    self.footArray          = nil;
    self.forTakeArray       = nil;
}


- (NSInteger)canUnderageTwoBusRecoveryDecimalSkip:(HomePhraseRankCell *)newBarrage
{
    for (int row = 0; row<_forTakeArray.count; row++) {
        NSObject *object = _forTakeArray[row];
        if ([object isKindOfClass:[NSNumber class]]) { 
            
            return row;
            
        }else if ([object isKindOfClass:[HomePhraseRankCell class]]) { 
            
            HomePhraseRankCell *webpageAsk = (HomePhraseRankCell*)object;
            
            if ([self canUnderageTwoBusRecoveryDecimalSkip:webpageAsk askSuffix:newBarrage]) {
                
                return row;
            }
        }
    }
    
    return -1;
}


- (BOOL)canUnderageTwoBusRecoveryDecimalSkip:(HomePhraseRankCell *)webpageAsk askSuffix:(HomePhraseRankCell *)newBarrage
{
    
    if (webpageAsk.status == MemoryJouleGrowPrematureChannelsPersian) {
        return NO;
    }
    
    
    CGRect rect = [webpageAsk.layer.presentationLayer frame];
    if (rect.origin.x>OldSmileRight - webpageAsk.frame.size.width) {
        
        return NO;
    }else if (rect.size.width == 0)
    {
        
        return NO;
    }
    else if (webpageAsk.frame.size.width > newBarrage.frame.size.width) {
        
        return YES;
    }else
    {
        
        CGFloat time = OldSmileRight/(OldSmileRight+newBarrage.frame.size.width)*newBarrage.fixObserving;
        
        CGFloat book = rect.origin.x - time/(webpageAsk.fixObserving)*(OldSmileRight + webpageAsk.frame.size.width);
        if (book < -webpageAsk.frame.size.width) {
            
            return YES;
        }
    }
    return NO;
}


- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    UITouch *touch = [touches anyObject];
    CGPoint sumCertAir  = [touch locationInView:self];
    for (HomePhraseRankCell *foldDayView in [self subviews])
    {
        if ([foldDayView.layer.presentationLayer hitTest:sumCertAir])
        {
            
            if ([self.delegate respondsToSelector:@selector(largerKitDidView:oldYoungestCell:)]) {
                [self.delegate largerKitDidView:self oldYoungestCell:foldDayView];
            }
            break;
        }
    }
}




- (NSMutableArray<HomePhraseRankCell *> *)footArray {
    if (!_footArray) {
        _footArray = [[NSMutableArray alloc] init];
    }
    return _footArray;
}


- (NSMutableArray<HomePhraseRankCell *> *)nearPlainFit {
    if (!_nearPlainFit) {
        _nearPlainFit = [[NSMutableArray alloc] init];
    }
    return _nearPlainFit;
}


- (void)setChannelCount:(NSInteger)channelCount
{
    
    if (self.forTakeArray.count < channelCount) { 
        
        for (NSInteger row = self.forTakeArray.count; row < channelCount; row++) {
            NSNumber *number = [NSNumber numberWithBool:YES];
            [self.forTakeArray setObject:number atIndexedSubscript:row];
        }
        
    }else {
        
        for (NSInteger row = channelCount; row < self.forTakeArray.count; row++) {
            [self.forTakeArray removeObjectAtIndex:row];
        }
    }
    
    _channelCount = channelCount;
    
}


- (NSMutableArray *)forTakeArray {
    if (!_forTakeArray) {
        _forTakeArray = [[NSMutableArray alloc] init];
    }
    return _forTakeArray;
}

@end
