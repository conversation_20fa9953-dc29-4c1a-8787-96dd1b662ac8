






#import "SonAssetWinNetCell.h"
#import "FigureGetSonInfo.h"
#import "Masonry.h"
#import "UIColor+GetColor.h"

@implementation SonAssetWinNetCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        self.backgroundColor = [UIColor clearColor];
        
        self.clipsToBounds = YES;
        
        [self addSubview:self.loseCapLabel];
        
        [self.loseCapLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsMake(0, 4, 0, 0));
        }];
        
    }
    return self;
}

- (void)setModel:(FigureGetSonInfo *)model
{
    _model = model;
    _loseCapLabel.text = model.usePhrasePan;
    _loseCapLabel.font = [UIFont systemFontOfSize:model.reduceLocalityWeightsTextureSobNordic];
    _loseCapLabel.textColor = [UIColor durationRoomEightEnergyProfilesPrefer:model.striationTicketsPrefersWillEarlierVariance];
    self.backgroundColor = [[UIColor durationRoomEightEnergyProfilesPrefer:model.reclaimSayHelperAttempterRareMolar] colorWithAlphaComponent:model.elementSummaryCauseFollowerMemoryFocal];
    self.layer.cornerRadius = 2;
}

- (UILabel *)loseCapLabel {
    if (!_loseCapLabel) {
        _loseCapLabel = [[UILabel alloc] init];
        _loseCapLabel.backgroundColor = [UIColor clearColor];
        _loseCapLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _loseCapLabel;
}

@end
