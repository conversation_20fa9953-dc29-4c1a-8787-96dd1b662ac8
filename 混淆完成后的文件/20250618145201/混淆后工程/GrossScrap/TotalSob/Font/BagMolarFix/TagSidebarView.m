

#import "TagSidebarView.h"
#import "SonAssetWinNetCell.h"

@implementation TagSidebarView

- (void)widgetBriefSourcesKashmiriWritingMoleModel:(FigureGetSonInfo *)model {
    
    NSMutableArray *canon = [NSMutableArray new];
    for (int i = 0; i<model.copticCall; i++) {
        SonAssetWinNetCell *cell = [[SonAssetWinNetCell alloc]init];
        cell.fixObserving = model.infoBedBox;
        cell.channelCount = 1;
        cell.anyBad = 6;
        cell.greatSeeCanBad = CGFLOAT_MIN;
        CGRect awayRect = [model.usePhrasePan boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:[NSDictionary dictionaryWithObject:[UIFont systemFontOfSize:model.reduceLocalityWeightsTextureSobNordic] forKey:NSFontAttributeName] context:nil];
        cell.loudTopSize = CGSizeMake(awayRect.size.width+8, awayRect.size.height+4);
        cell.model = model;
        [canon addObject:cell];
    }

    [self backupFireBusy:canon];
    
}

@end
