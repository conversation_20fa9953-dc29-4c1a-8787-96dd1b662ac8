







#import "HomePhraseRankCell.h"
#define faceWas(obj) __weak typeof(obj) weak##obj = obj;
#define ampereIll(obj) __strong typeof(obj) obj = weak##obj;

@interface HomePhraseRankCell()



@property (nonatomic, strong) NSTimer *timer;

@property (nonatomic, assign) BOOL darwinDeferred;

@end

@implementation HomePhraseRankCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:CGRectZero];
    if (self) {
        
        _loudTopSize = CGSizeMake(200, 40);
        _fixObserving = 4;
        _model = nil;
        _channelCount = 3;
        _anyBad = 0;
        _greatSeeCanBad = 0;
        _status = AsteriskAreDryInventoryManualTint;
        _darwinDeferred = NO;
        
    }
    return self;
}

- (void)towerHealthSignUnderlineQuitValue
{
    CGFloat lawRotor = [[self.layer presentationLayer] frame].origin.x;
    CGFloat barrageWidth = self.frame.size.width;
    
    
    CGFloat speed = (self.superview.frame.size.width + barrageWidth) / self.fixObserving;
    
    
    CGFloat beginExitTime = barrageWidth / speed;
    
    if (_greatSeeCanBad > 0) {
        self.status = MemoryJouleGrowPrematureChannelsPersian;
        if (-1< lawRotor < 1) {
            
            if (_darwinDeferred) { return;}
            _darwinDeferred = YES;
            [self pause];
            [self performSelector:@selector(resume) withObject:nil afterDelay:_greatSeeCanBad];
            [self performSelector:@selector(bypassStatus) withObject:nil afterDelay:_greatSeeCanBad - beginExitTime];
        }
    }
}
- (void)bypassStatus
{
    self.status = CompileConflictDogFarDrainJob;
}

- (void)meterSelfAppendConcertStep:(void(^)(void))animations completion:(void(^)(BOOL))completion
{
    self.status = CompileConflictDogFarDrainJob;
    
    _timer = [NSTimer timerWithTimeInterval:0.01 target:self selector:@selector(towerHealthSignUnderlineQuitValue) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:_timer forMode:NSRunLoopCommonModes];
    
    
    faceWas(self);
    [UIView animateWithDuration:self.fixObserving delay:0 options:(UIViewAnimationOptionCurveLinear | UIViewAnimationOptionAllowUserInteraction) animations:^{
        
        if (animations) {
            animations();
        }
        
    } completion:^(BOOL finished) {
        ampereIll(self);
        self->_status = CompileConflictDogFarDrainJob;
        
        if (completion) {
            completion(finished);
        }
        
        if(self->_timer) {
            [self->_timer invalidate];
            self->_timer = nil;
        }
        
    }];
}

- (void)pause
{
    
    CFTimeInterval badgeTime = [self.layer convertTime:CACurrentMediaTime() fromLayer:nil];
    
    
    self.layer.timeOffset = badgeTime;
    
    
    self.layer.speed = 0;
}

- (void)resume
{
    
    CFTimeInterval badgeTime = self.layer.timeOffset;
    
    CFTimeInterval gopherRedirect = CACurrentMediaTime() - badgeTime;
    
    self.layer.timeOffset = 0;
    
    self.layer.beginTime = gopherRedirect;
    
    self.layer.speed = 1;
}


@end
