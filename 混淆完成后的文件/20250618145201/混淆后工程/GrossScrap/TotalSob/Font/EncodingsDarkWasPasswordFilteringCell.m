






#import "EncodingsDarkWasPasswordFilteringCell.h"
#import "ShapeWinNet.h"
#import "Masonry.h"

@implementation EncodingsDarkWasPasswordFilteringCell

-(id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self){
        
        self.backgroundColor = UIColor.whiteColor;
        self.contentView.backgroundColor = UIColor.whiteColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        
        self.warpMinRetView = [UIImageView new];
        self.warpMinRetView.tintColor = [ShapeWinNet exposuresColor];
        self.warpMinRetView.layer.cornerRadius = 15;
        [self.contentView addSubview:self.warpMinRetView];
        [self.warpMinRetView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(ShapeWinNet.redoFinalTag.cervicalPath);
            make.centerY.mas_equalTo(self);
            make.width.height.mas_equalTo(ShapeWinNet.redoFinalTag.briefKinMask);
        }];
        
        
        self.viewYearName = [UILabel new];
        self.viewYearName.font = [UIFont boldSystemFontOfSize:16];
        self.viewYearName.textColor = UIColor.darkGrayColor;
        [self.contentView addSubview:self.viewYearName];
        [self.viewYearName mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.warpMinRetView.mas_right).offset(ShapeWinNet.redoFinalTag.cervicalPath);
            make.bottom.equalTo(self.contentView.mas_centerY);
        }];
        
        
        self.pipeEyeLastTime = [UILabel new];
        self.pipeEyeLastTime.font =  [UIFont systemFontOfSize:11];
        self.pipeEyeLastTime.textColor = UIColor.grayColor;
        [self.contentView addSubview:self.pipeEyeLastTime];
        [self.pipeEyeLastTime mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_centerY).offset(ShapeWinNet.redoFinalTag.buddyMayPan);
            make.left.equalTo(self.viewYearName);
        }];
    }
    return self;
}

@end
