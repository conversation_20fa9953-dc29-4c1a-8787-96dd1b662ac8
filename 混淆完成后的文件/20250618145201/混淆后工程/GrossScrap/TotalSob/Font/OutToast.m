






#import "OutToast.h"
#import "HitUnableManager.h"


static UIColor *baseLeftoverDetailedCreatedIcyColor = nil;
static UIColor *jobListenersColor = nil;
static UIFont *pulseDidAxes = nil;
static CGFloat seeSeekBadRateRadius = 6.0;
static UIEdgeInsets automaticBrownArtsExtensionFourLength = {10, 16, 10, 16};

@interface OutToast()
@property (nonatomic, strong) UILabel *loseCapLabel;
@property (nonatomic, strong) NSTimer *zoomLowTimer;
@property (nonatomic, assign) SaturateAbsentPeriodicHisButterfly position;
@end

@implementation OutToast


- (instancetype)initFillMessage:(NSString *)message {
    self = [super initWithFrame:CGRectZero];
    if (self) {
        self.userInteractionEnabled = NO;
        self.backgroundColor = UIColor.clearColor;
        
        
        UIView *container = [UIView new];
        container.backgroundColor = baseLeftoverDetailedCreatedIcyColor ?:
            [[UIColor blackColor] colorWithAlphaComponent:0.85];
        container.layer.cornerRadius = seeSeekBadRateRadius;
        container.clipsToBounds = YES;
        container.translatesAutoresizingMaskIntoConstraints = NO;
        [self addSubview:container];
        
        
        _loseCapLabel = [UILabel new];
        _loseCapLabel.text = message;
        _loseCapLabel.textColor = jobListenersColor ?: UIColor.whiteColor;
        _loseCapLabel.font = pulseDidAxes ?: [UIFont systemFontOfSize:14];
        _loseCapLabel.textAlignment = NSTextAlignmentCenter;
        _loseCapLabel.numberOfLines = 0;
        _loseCapLabel.translatesAutoresizingMaskIntoConstraints = NO;
        [container addSubview:_loseCapLabel];
        
        
        [NSLayoutConstraint activateConstraints:@[
            
            [container.leadingAnchor constraintEqualToAnchor:_loseCapLabel.leadingAnchor
                                                   constant:-automaticBrownArtsExtensionFourLength.left],
            [container.trailingAnchor constraintEqualToAnchor:_loseCapLabel.trailingAnchor
                                                    constant:automaticBrownArtsExtensionFourLength.right],
            [container.topAnchor constraintEqualToAnchor:_loseCapLabel.topAnchor
                                              constant:-automaticBrownArtsExtensionFourLength.top],
            [container.bottomAnchor constraintEqualToAnchor:_loseCapLabel.bottomAnchor
                                                 constant:automaticBrownArtsExtensionFourLength.bottom],
            
            
            [container.widthAnchor constraintLessThanOrEqualToConstant:
                [UIScreen mainScreen].bounds.size.width - 40]
        ]];
    }
    return self;
}


+ (void)show:(NSString *)message
    duration:(NSTimeInterval)duration
    position:(SaturateAbsentPeriodicHisButterfly)position
{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        OutToast *hover = [[OutToast alloc] initFillMessage:message];
        hover.position = position;
        [hover correctedEnablingJoiningPermittedStill];
        [hover cameraOldTabLockStable:duration];
    });
}

- (void)cameraOldTabLockStable:(NSTimeInterval)duration {
    UIWindow *window = [HitUnableManager.shared laterExceedsWindow];
    [window addSubview:self];
    
    
    self.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.leadingAnchor constraintEqualToAnchor:window.leadingAnchor],
        [self.trailingAnchor constraintEqualToAnchor:window.trailingAnchor],
        [self.topAnchor constraintEqualToAnchor:window.topAnchor],
        [self.bottomAnchor constraintEqualToAnchor:window.bottomAnchor]
    ]];
    
    
    [self tapEncodeConstantsCoachedBedAnimation];
    
    
    if (duration > 0) {
        __weak typeof(self) weakSelf = self;
        self.zoomLowTimer = [NSTimer scheduledTimerWithTimeInterval:duration repeats:YES block:^(NSTimer * _Nonnull timer) {
            [weakSelf dismiss];
        }];
    }
}

- (void)dismiss {
    [self.zoomLowTimer invalidate];
    [self performerContextsUplinkHexRepairTruncateAudited:^{
        [self removeFromSuperview];
    }];
}


- (void)tapEncodeConstantsCoachedBedAnimation {
    CGAffineTransform transform;
    switch (self.position) {
        case EllipseKitSubmitUpdatingChanging:
            transform = CGAffineTransformMakeTranslation(0, -100);
            break;
        case SuchPhoneticRingHeaderCarrierSupport:
            transform = CGAffineTransformMakeTranslation(0, 100);
            break;
        default:
            transform = CGAffineTransformMakeScale(0.8, 0.8);
            break;
    }
    
    self.alpha = 0;
    self.loseCapLabel.superview.transform = transform;
    
    [UIView animateWithDuration:0.3
                          delay:0
         usingSpringWithDamping:0.7
          initialSpringVelocity:0.1
                        options:UIViewAnimationOptionCurveEaseOut
                     animations:^{
        self.alpha = 1;
        self.loseCapLabel.superview.transform = CGAffineTransformIdentity;
    } completion:nil];
}

- (void)performerContextsUplinkHexRepairTruncateAudited:(void(^)(void))completion {
    CGAffineTransform transform;
    switch (self.position) {
        case EllipseKitSubmitUpdatingChanging:
            transform = CGAffineTransformMakeTranslation(0, -self.loseCapLabel.superview.frame.size.height - 50);
            break;
        case SuchPhoneticRingHeaderCarrierSupport:
            transform = CGAffineTransformMakeTranslation(0, self.loseCapLabel.superview.frame.size.height + 50);
            break;
        default:
            transform = CGAffineTransformMakeScale(0.8, 0.8);
            break;
    }
    
    [UIView animateWithDuration:0.25
                     animations:^{
        self.alpha = 0;
        self.loseCapLabel.superview.transform = transform;
    } completion:^(BOOL finished) {
        if (completion) completion();
    }];
}


- (void)correctedEnablingJoiningPermittedStill {
    UIView *container = self.loseCapLabel.superview;
    
    
    switch (self.position) {
        case EllipseKitSubmitUpdatingChanging: {
            [container.topAnchor constraintEqualToAnchor:self.safeAreaLayoutGuide.topAnchor
                                               constant:30].active = YES;
            break;
        }
        case GolfLoopsPronounOutlineLimitedCenter: {
            [container.centerYAnchor constraintEqualToAnchor:self.centerYAnchor].active = YES;
            break;
        }
        case SuchPhoneticRingHeaderCarrierSupport: {
            [container.bottomAnchor constraintEqualToAnchor:self.safeAreaLayoutGuide.bottomAnchor
                                                  constant:-30].active = YES;
            break;
        }
    }
    
    
    [container.centerXAnchor constraintEqualToAnchor:self.centerXAnchor].active = YES;
}


+ (void)unlikelyTrainerSystemWrappersUpdateSeasonColor:(UIColor *)color {
    baseLeftoverDetailedCreatedIcyColor = color;
}

+ (void)speakAboutFadeColor:(UIColor *)color {
    jobListenersColor = color;
}

+ (void)armYahooEggWas:(UIFont *)font {
    pulseDidAxes = font;
}

+ (void)establishSyntaxAgeScannedUnderageRadius:(CGFloat)radius {
    seeSeekBadRateRadius = radius;
}


+ (void)faxTask:(NSString *)message {
    [self show:message duration:2.0 position:EllipseKitSubmitUpdatingChanging];
}

+ (void)drumCenter:(NSString *)message {
    [self show:message duration:2.0 position:GolfLoopsPronounOutlineLimitedCenter];
}

+ (void)dashFunOff:(NSString *)message {
    [self show:message duration:2.0 position:SuchPhoneticRingHeaderCarrierSupport];
}

- (void)dealloc {
    
}

@end
