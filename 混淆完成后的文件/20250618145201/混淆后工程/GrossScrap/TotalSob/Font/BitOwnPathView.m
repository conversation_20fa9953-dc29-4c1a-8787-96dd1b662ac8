






#import "BitOwnPathView.h"
#import "HitUnableManager.h"
#import "ShapeWinNet.h"
#import "Masonry.h"

@interface BitOwnPathView ()

@property (nonatomic, strong) UIView *execChildPutBackgroundView;
@property (nonatomic, strong) UIActivityIndicatorView *decidePrefixesNiacinPrefixedLaotian;
@property (nonatomic, strong) UILabel *dogPairLabel;
@end

@implementation BitOwnPathView


static BitOwnPathView *notifiesStartView = nil;



- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self fiberView];
    }
    return self;
}
- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self fiberView];
    }
    return self;
}
- (void)fiberView {
    
    
    self.execChildPutBackgroundView = [UIView new];
    self.execChildPutBackgroundView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    self.execChildPutBackgroundView.layer.cornerRadius = 2.0;
    self.execChildPutBackgroundView.clipsToBounds = YES;
    [self addSubview:self.execChildPutBackgroundView];
    
    
    self.decidePrefixesNiacinPrefixedLaotian = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleLarge];
    self.decidePrefixesNiacinPrefixedLaotian.color = ShapeWinNet.exposuresColor;
    [self.execChildPutBackgroundView addSubview:self.decidePrefixesNiacinPrefixedLaotian];
    
    
    self.dogPairLabel = [[UILabel alloc] init];
    self.dogPairLabel.text = ShapeWinNet.daySeeWaistUse.smartUnitBig;
    self.dogPairLabel.textColor = [UIColor whiteColor];
    self.dogPairLabel.font = [UIFont systemFontOfSize:14];
    self.dogPairLabel.numberOfLines = 0;
    self.dogPairLabel.textAlignment = NSTextAlignmentCenter;
    [self.execChildPutBackgroundView addSubview:self.dogPairLabel];
    
    
    [self.execChildPutBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(ShapeWinNet.redoFinalTag.busEastManFat, ShapeWinNet.redoFinalTag.busEastManFat));
        make.center.equalTo(self);
    }];
    
    [self.decidePrefixesNiacinPrefixedLaotian mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(ShapeWinNet.redoFinalTag.getRouteLate);
        make.centerX.equalTo(self.execChildPutBackgroundView.mas_centerX);
    }];
    
    [self.dogPairLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.decidePrefixesNiacinPrefixedLaotian.mas_bottom).offset(ShapeWinNet.redoFinalTag.getRouteLate);
        make.centerX.equalTo(self.execChildPutBackgroundView.mas_centerX);
        make.left.equalTo(self.execChildPutBackgroundView.mas_left).offset(ShapeWinNet.redoFinalTag.maxBrownCan);
        make.right.equalTo(self.execChildPutBackgroundView.mas_right).offset(-ShapeWinNet.redoFinalTag.maxBrownCan);
    }];
    
    
    self.hidden = YES;
}



- (void)startAnimating {
    self.hidden = NO;
    [self.decidePrefixesNiacinPrefixedLaotian startAnimating];
}

- (void)stopAnimating {
    [self.decidePrefixesNiacinPrefixedLaotian stopAnimating];
    self.hidden = YES;
}

- (void)planFunnelText:(NSString *)text {
    self.dogPairLabel.text = text;
    
    
    CGFloat usesWidth = [text boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX)
                                          options:NSStringDrawingUsesLineFragmentOrigin
                                       attributes:@{NSFontAttributeName: self.dogPairLabel.font}
                                          context:nil].size.width;
    UIWindow *window = [[HitUnableManager shared] laterExceedsWindow];
    CGFloat safeKitWidth = MIN(MAX(120, usesWidth + 2 * 8), window.bounds.size.width);
    [self.execChildPutBackgroundView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(safeKitWidth);
    }];
    
    [self layoutIfNeeded];
}


+ (void)tapsWatchTimeWindow {
    [self compoundDebuggingWarpGradeLastEighteenText:ShapeWinNet.daySeeWaistUse.smartUnitBig];
}

+ (void)compoundDebuggingWarpGradeLastEighteenText:(NSString *)text {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIWindow *window = [[HitUnableManager shared] laterExceedsWindow];
        
        if (!notifiesStartView) {
            CGSize size = UIScreen.mainScreen.bounds.size;
            notifiesStartView = [[BitOwnPathView alloc] initWithFrame:CGRectMake(0, 0, size.width, size.height)];
            notifiesStartView.center = window.center;
        }
        if (!notifiesStartView.superview) {
            [window addSubview:notifiesStartView];
        }
        [notifiesStartView planFunnelText:text];
        [notifiesStartView startAnimating];
    });
}

+ (void)preventedHigherClinicalWindowYearWindow {
    dispatch_async(dispatch_get_main_queue(), ^{
        [notifiesStartView stopAnimating];
        [notifiesStartView removeFromSuperview];
        notifiesStartView = nil;
    });
}


+ (BitOwnPathView *)noneSwapEmailView:(UIView *)view {
    return [self noneSwapEmailView:view withText:ShapeWinNet.daySeeWaistUse.smartUnitBig];
}

+ (BitOwnPathView *)noneSwapEmailView:(UIView *)view withText:(NSString *)text {
    __block BitOwnPathView *icyMoreView = nil;
    dispatch_async(dispatch_get_main_queue(), ^{
        
        icyMoreView = [[BitOwnPathView alloc] initWithFrame:CGRectMake(0, 0, view.frame.size.width, view.frame.size.height)];
        icyMoreView.center = CGPointMake(CGRectGetMidX(view.bounds), CGRectGetMidY(view.bounds));
        [icyMoreView planFunnelText:text];
        [icyMoreView startAnimating];
        [view addSubview:icyMoreView];
    });
    return icyMoreView;
}

+ (void)tooSphericalSecurityAutoUsedView:(UIView *)view {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        for (UIView *subview in view.subviews) {
            if ([subview isKindOfClass:[BitOwnPathView class]]) {
                [(BitOwnPathView *)subview stopAnimating];
                [subview removeFromSuperview];
            }
        }
    });
}

@end
