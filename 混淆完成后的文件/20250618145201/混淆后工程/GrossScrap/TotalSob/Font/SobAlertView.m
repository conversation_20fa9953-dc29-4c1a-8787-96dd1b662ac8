






#import "SobAlertView.h"
#import "ShapeWinNet.h"
#import "HitUnableManager.h"
#import "Masonry.h"

#define faceWas(obj) __weak typeof(obj) weak##obj = obj;
#define ampereIll(obj) __strong typeof(obj) obj = weak##obj;

@interface SobAlertView()

@property (nonatomic, strong) UIView *modalAltimeterView;
@property (nonatomic, copy) DisablingAscentExtrasSyntheticProxiesAlign completion;
@property (nonatomic, strong) UIStackView *bigParagraphView;

@end

@implementation SobAlertView

- (void)dealloc {
    
}

- (instancetype)initWithFrame:(CGRect)frame
                          title:(NSString *)title
                        message:(NSString *)message
                   lowerDogOnce:(NSArray<NSString *> *)lowerDogOnce
                     completion:(DisablingAscentExtrasSyntheticProxiesAlign)completion {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
        self.completion = completion;
        
        
        self.modalAltimeterView = [[UIView alloc] init];
        self.modalAltimeterView.backgroundColor = [ShapeWinNet exposuresColor];
        self.modalAltimeterView.layer.cornerRadius = 8.0;
        self.modalAltimeterView.clipsToBounds = YES;
        self.modalAltimeterView.translatesAutoresizingMaskIntoConstraints = NO;
        [self addSubview:self.modalAltimeterView];
        
        
        [NSLayoutConstraint activateConstraints:@[
            [self.modalAltimeterView.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
            [self.modalAltimeterView.centerYAnchor constraintEqualToAnchor:self.centerYAnchor],
            [self.modalAltimeterView.widthAnchor constraintEqualToConstant:270]
        ]];
        
        
        UIView *previousView = nil;
        CGFloat leadYahooDetectorBouncingCreating = 20;
        
        
        if (title.length > 0) {
            UILabel *titleLabel = [[UILabel alloc] init];
            titleLabel.text = title;
            titleLabel.textColor = UIColor.whiteColor;
            titleLabel.font = [UIFont boldSystemFontOfSize:18];
            titleLabel.textAlignment = NSTextAlignmentCenter;
            titleLabel.numberOfLines = 0;
            titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
            [self.modalAltimeterView addSubview:titleLabel];
            
            [NSLayoutConstraint activateConstraints:@[
                [titleLabel.topAnchor constraintEqualToAnchor:self.modalAltimeterView.topAnchor constant:leadYahooDetectorBouncingCreating],
                [titleLabel.leadingAnchor constraintEqualToAnchor:self.modalAltimeterView.leadingAnchor constant:16],
                [titleLabel.trailingAnchor constraintEqualToAnchor:self.modalAltimeterView.trailingAnchor constant:-16]
            ]];
            
            previousView = titleLabel;
        }
        
        
        if (message.length > 0) {
            UILabel *loseCapLabel = [[UILabel alloc] init];
            loseCapLabel.text = message;
            loseCapLabel.textColor = UIColor.whiteColor;
            loseCapLabel.font = [UIFont systemFontOfSize:15];
            loseCapLabel.textAlignment = NSTextAlignmentCenter;
            loseCapLabel.numberOfLines = 0;
            loseCapLabel.translatesAutoresizingMaskIntoConstraints = NO;
            [self.modalAltimeterView addSubview:loseCapLabel];
            
            NSLayoutYAxisAnchor *topAnchor = previousView ? previousView.bottomAnchor : self.modalAltimeterView.topAnchor;
            CGFloat pubHueBend = previousView ? 10 : leadYahooDetectorBouncingCreating;
            [NSLayoutConstraint activateConstraints:@[
                [loseCapLabel.topAnchor constraintEqualToAnchor:topAnchor constant:pubHueBend],
                [loseCapLabel.leadingAnchor constraintEqualToAnchor:self.modalAltimeterView.leadingAnchor constant:16],
                [loseCapLabel.trailingAnchor constraintEqualToAnchor:self.modalAltimeterView.trailingAnchor constant:-16]
            ]];
            previousView = loseCapLabel;
        }
        
        
        self.bigParagraphView = [[UIStackView alloc] init];
        self.bigParagraphView.axis = UILayoutConstraintAxisVertical;
        self.bigParagraphView.spacing = 1;  
        self.bigParagraphView.distribution = UIStackViewDistributionFillEqually;
        self.bigParagraphView.translatesAutoresizingMaskIntoConstraints = NO;
        [self.modalAltimeterView addSubview:self.bigParagraphView];
        
        
        NSLayoutYAxisAnchor *usedRectangleAutomaticForConstants = previousView ? previousView.bottomAnchor : self.modalAltimeterView.topAnchor;
        CGFloat buttonsTopPadding = previousView ? leadYahooDetectorBouncingCreating : leadYahooDetectorBouncingCreating;
        
        [NSLayoutConstraint activateConstraints:@[
            [self.bigParagraphView.topAnchor constraintEqualToAnchor:usedRectangleAutomaticForConstants constant:buttonsTopPadding],
            [self.bigParagraphView.leadingAnchor constraintEqualToAnchor:self.modalAltimeterView.leadingAnchor],
            [self.bigParagraphView.trailingAnchor constraintEqualToAnchor:self.modalAltimeterView.trailingAnchor],
            [self.bigParagraphView.bottomAnchor constraintEqualToAnchor:self.modalAltimeterView.bottomAnchor]
        ]];
        
        
       
       if (lowerDogOnce.count == 2) {
           
           self.bigParagraphView = [[UIStackView alloc] init];
           self.bigParagraphView.axis = UILayoutConstraintAxisHorizontal;
           self.bigParagraphView.distribution = UIStackViewDistributionFillEqually;
           self.bigParagraphView.spacing = 1;  
           self.bigParagraphView.translatesAutoresizingMaskIntoConstraints = NO;
           [self.modalAltimeterView addSubview:self.bigParagraphView];
           
           NSLayoutYAxisAnchor *usedRectangleAutomaticForConstants = previousView ? previousView.bottomAnchor : self.modalAltimeterView.topAnchor;
           [NSLayoutConstraint activateConstraints:@[
               [self.bigParagraphView.topAnchor constraintEqualToAnchor:usedRectangleAutomaticForConstants constant:leadYahooDetectorBouncingCreating],
               [self.bigParagraphView.leadingAnchor constraintEqualToAnchor:self.modalAltimeterView.leadingAnchor],
               [self.bigParagraphView.trailingAnchor constraintEqualToAnchor:self.modalAltimeterView.trailingAnchor],
               [self.bigParagraphView.bottomAnchor constraintEqualToAnchor:self.modalAltimeterView.bottomAnchor]
           ]];
           
           
           for (NSInteger i = 0; i < lowerDogOnce.count; i++) {
               NSString *farBound = lowerDogOnce[i];
               UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
               [button setTitle:farBound forState:UIControlStateNormal];
               button.titleLabel.font = [UIFont systemFontOfSize:17];
               [button setTitleColor:[ShapeWinNet exposuresColor] forState:UIControlStateNormal];
               [button setTitleColor:UIColor.lightGrayColor forState:UIControlStateHighlighted];
               button.backgroundColor = [UIColor whiteColor];
               button.tag = i;
               [button addTarget:self action:@selector(variableWarp:) forControlEvents:UIControlEventTouchUpInside];
               button.translatesAutoresizingMaskIntoConstraints = NO;
               [button.heightAnchor constraintEqualToConstant:40].active = YES;
               [self.bigParagraphView addArrangedSubview:button];
           }
       } else {
           
           self.bigParagraphView = [[UIStackView alloc] init];
           self.bigParagraphView.axis = UILayoutConstraintAxisVertical;
           self.bigParagraphView.spacing = 1;
           self.bigParagraphView.distribution = UIStackViewDistributionFillEqually;
           self.bigParagraphView.translatesAutoresizingMaskIntoConstraints = NO;
           [self.modalAltimeterView addSubview:self.bigParagraphView];
           
           NSLayoutYAxisAnchor *usedRectangleAutomaticForConstants = previousView ? previousView.bottomAnchor : self.modalAltimeterView.topAnchor;
           [NSLayoutConstraint activateConstraints:@[
               [self.bigParagraphView.topAnchor constraintEqualToAnchor:usedRectangleAutomaticForConstants constant:leadYahooDetectorBouncingCreating],
               [self.bigParagraphView.leadingAnchor constraintEqualToAnchor:self.modalAltimeterView.leadingAnchor],
               [self.bigParagraphView.trailingAnchor constraintEqualToAnchor:self.modalAltimeterView.trailingAnchor],
               [self.bigParagraphView.bottomAnchor constraintEqualToAnchor:self.modalAltimeterView.bottomAnchor]
           ]];
           
           for (NSInteger i = 0; i < lowerDogOnce.count; i++) {
               NSString *farBound = lowerDogOnce[i];
               UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
               [button setTitle:farBound forState:UIControlStateNormal];
               button.titleLabel.font = [UIFont systemFontOfSize:17];
               [button setTitleColor:[ShapeWinNet exposuresColor] forState:UIControlStateNormal];
               [button setTitleColor:UIColor.lightGrayColor forState:UIControlStateHighlighted];
               button.backgroundColor = [UIColor whiteColor];
               button.tag = i;
               [button addTarget:self action:@selector(variableWarp:) forControlEvents:UIControlEventTouchUpInside];
               button.translatesAutoresizingMaskIntoConstraints = NO;
               [button.heightAnchor constraintEqualToConstant:40].active = YES;
               [self.bigParagraphView addArrangedSubview:button];
           }
       }
    }
    return self;
}

- (void)variableWarp:(UIButton *)sender {
    if (self.completion) {
        self.completion(sender.tag);
    }
    
    
    [UIView animateWithDuration:0.25 animations:^{
        self.alpha = 0;
    } completion:^(BOOL finished) {
        [HitUnableManager.shared iconRestoredWindow];
    }];
}

+ (void)maxRaiseDigitalSourcesInnerQuarter:(NSString *)title
                        message:(NSString *)message
                   lowerDogOnce:(NSArray<NSString *> *)lowerDogOnce
                     completion:(DisablingAscentExtrasSyntheticProxiesAlign)completion {
    
    SobAlertView *daily = [[SobAlertView alloc] initWithFrame:[UIScreen mainScreen].bounds
                                                 title:title
                                               message:message
                                          lowerDogOnce:lowerDogOnce
                                            completion:completion];
    
    
    [HitUnableManager.shared slightMayAlbanianHisSmoothCaptureView:daily];
    
    
    daily.alpha = 0.0;
    [UIView animateWithDuration:0.25 animations:^{
        daily.alpha = 1.0;
    }];
}

+ (void)maxRaiseDigitalSourcesInnerQuarter:(NSString *)title message:(NSString *)message completion:(DisablingAscentExtrasSyntheticProxiesAlign)completion {
    [self maxRaiseDigitalSourcesInnerQuarter:title message:message lowerDogOnce:@[ShapeWinNet.daySeeWaistUse.dueChar] completion:completion];
}

@end
