






#import "XXGProtocolLabel.h"
#import "ShapeWinNet.h"
#import "UIImage+OwnImage.h"

@implementation XXGProtocolLabel

+ (XXGProtocolLabel *)farSeePartnerLabel {
    return [self farSeePartnerLabel:YES];
}

+ (XXGProtocolLabel *)farSeePartnerLabel:(BOOL)isCheckBox {
    
    XXGProtocolLabel *label = [[XXGProtocolLabel alloc] init];
    label.numberOfLines = 0;
    label.textAlignment = NSTextAlignmentCenter;
    label.textColor = [UIColor lightGrayColor];
    label.font = [UIFont systemFontOfSize:12];
    label.userInteractionEnabled = YES; 

    NSAttributedString *permittedLaunchingFarSubjectWill = nil;
    if (isCheckBox) {
        
        NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
        UIImage *flowArtsImage = [[UIImage domainIndexObserverUploadingInitiatedName:ShapeWinNet.redoFinalTag.googleManMindfulLabeledMicroShutdown] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        attachment.image = flowArtsImage; 
        
        attachment.bounds = CGRectMake(0, -5, 20, 20);
        permittedLaunchingFarSubjectWill = [NSAttributedString attributedStringWithAttachment:attachment];
    }

    
    NSString *text = ShapeWinNet.daySeeWaistUse.unifyPenMean;
    NSMutableAttributedString *backWayItemFar = [[NSMutableAttributedString alloc] initWithString:text];
    
    
    NSRange endArmHowOwner = [text rangeOfString:ShapeWinNet.daySeeWaistUse.levelTenMidPin];
    if (endArmHowOwner.location != NSNotFound) {
        [backWayItemFar addAttribute:NSForegroundColorAttributeName value:[ShapeWinNet exposuresColor] range:endArmHowOwner];
        [backWayItemFar addAttribute:NSUnderlineStyleAttributeName value:@(NSUnderlineStyleSingle) range:endArmHowOwner];
    }

    
    NSMutableAttributedString *partiallyCreditSmoothedMajorDesired = [[NSMutableAttributedString alloc] init];
    if (permittedLaunchingFarSubjectWill) {
        [partiallyCreditSmoothedMajorDesired appendAttributedString:permittedLaunchingFarSubjectWill];
    }
    [partiallyCreditSmoothedMajorDesired appendAttributedString:backWayItemFar];
    
    label.attributedText = partiallyCreditSmoothedMajorDesired;
    
    
    UITapGestureRecognizer *onceDidEnd = [[UITapGestureRecognizer alloc] initWithTarget:label action:@selector(launchSyntaxSequencerSwapFully:)];
    [label addGestureRecognizer:onceDidEnd];
    
    return label;
}

- (void)setBezelOffCutFor:(BOOL)bezelOffCutFor {
    _bezelOffCutFor = !bezelOffCutFor;
    [self rotorAdvertiseGradeMinimumDragWayLabel:self];
}

- (void)launchSyntaxSequencerSwapFully:(UITapGestureRecognizer *)onceDidEnd {
    XXGProtocolLabel *label = (XXGProtocolLabel *)onceDidEnd.view;
    if (!label.attributedText) return;
    
    
    NSTextStorage *textStorage = [[NSTextStorage alloc] initWithAttributedString:label.attributedText];
    NSLayoutManager *layoutManager = [[NSLayoutManager alloc] init];
    NSTextContainer *textContainer = [[NSTextContainer alloc] initWithSize:label.bounds.size];
    
    textContainer.lineFragmentPadding = 0;
    textContainer.maximumNumberOfLines = label.numberOfLines;
    textContainer.lineBreakMode = label.lineBreakMode;
    
    [textStorage addLayoutManager:layoutManager];
    [layoutManager addTextContainer:textContainer];
    
    
    [layoutManager ensureLayoutForTextContainer:textContainer];
    
    
    CGPoint mapCapExact = [onceDidEnd locationInView:label];
    CGRect usedRect = [layoutManager usedRectForTextContainer:textContainer];
    CGPoint textContainerOrigin = CGPointMake(
        (label.bounds.size.width - usedRect.size.width) / 2,   
        (label.bounds.size.height - usedRect.size.height) / 2  
    );
    
    
    CGPoint locationInTextContainer = CGPointMake(
        mapCapExact.x - textContainerOrigin.x,
        mapCapExact.y - textContainerOrigin.y
    );
    
    
    __block BOOL isImageTapped = NO;
    [label.attributedText enumerateAttribute:NSAttachmentAttributeName
                                    inRange:NSMakeRange(0, label.attributedText.length)
                                    options:0
                                 usingBlock:^(id value, NSRange range, BOOL *stop) {
        if ([value isKindOfClass:[NSTextAttachment class]]) {
            
            NSRange glyphRange;
            [layoutManager glyphRangeForCharacterRange:range actualCharacterRange:&glyphRange];
            
            
            CGRect insetRect = [layoutManager boundingRectForGlyphRange:glyphRange
                                                      inTextContainer:textContainer];
            
            
            CGRect bendPartialRect = CGRectOffset(insetRect, textContainerOrigin.x, textContainerOrigin.y);
            
            
            if (CGRectContainsPoint(bendPartialRect, mapCapExact)) {
                isImageTapped = YES;
                *stop = YES;
            }
        }
    }];
    
    if (isImageTapped) {
        
        
        [self rotorAdvertiseGradeMinimumDragWayLabel:label];
        return;
    }
    
    
    NSUInteger characterIndex = [layoutManager characterIndexForPoint:locationInTextContainer
                                                    inTextContainer:textContainer
                           fractionOfDistanceBetweenInsertionPoints:NULL];
    
    NSString *fullText = label.attributedText.string;
    NSRange endArmHowOwner = [fullText rangeOfString:ShapeWinNet.daySeeWaistUse.levelTenMidPin];
    
    if (characterIndex != NSNotFound && NSLocationInRange(characterIndex, endArmHowOwner)) {
        
        
        if (self.glyphAverageLibrariesLegacyWrappedFace) {
            self.glyphAverageLibrariesLegacyWrappedFace();
        }
    }
}


- (void)rotorAdvertiseGradeMinimumDragWayLabel:(XXGProtocolLabel *)label {
    NSMutableAttributedString *attributedText = [label.attributedText mutableCopy];
    __block BOOL bodyPolicy = NO;
    
    [attributedText enumerateAttribute:NSAttachmentAttributeName
                             inRange:NSMakeRange(0, attributedText.length)
                             options:0
                          usingBlock:^(NSTextAttachment *oldAttachment, NSRange range, BOOL *stop) {
        if (![oldAttachment isKindOfClass:[NSTextAttachment class]]) return;
        
        
        BOOL proxyThin = !_bezelOffCutFor;
        
        
        NSTextAttachment *checksumCubic = [[NSTextAttachment alloc] init];
        
        
        UIColor *anyWasColor = proxyThin ? [ShapeWinNet exposuresColor]: UIColor.lightGrayColor;
        UIImage *errorItsImage = [UIImage domainIndexObserverUploadingInitiatedName:proxyThin ? ShapeWinNet.redoFinalTag.menHangResponsesNumeratorGenerateSocket :ShapeWinNet.redoFinalTag.googleManMindfulLabeledMicroShutdown];
        
        
        checksumCubic.image = [[errorItsImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate]
                                imageWithTintColor:anyWasColor];
        checksumCubic.bounds = oldAttachment.bounds;
        
        
        [attributedText removeAttribute:NSAttachmentAttributeName range:range];
        [attributedText addAttribute:NSAttachmentAttributeName value:checksumCubic range:range];
        
        _bezelOffCutFor = proxyThin;
        bodyPolicy = YES;
        *stop = YES;
    }];
    
    if (bodyPolicy) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [UIView transitionWithView:label
                              duration:0.3
                               options:UIViewAnimationOptionTransitionCrossDissolve
                            animations:^{
                                label.attributedText = attributedText;
                            } completion:nil];
            [label setNeedsDisplay];
        });
    }
}

@end
