






#import "CheckSmoothButton.h"
#import "ShapeWinNet.h"
#import "UIImage+OwnImage.h"

@interface CheckSmoothButton ()


@property (nonatomic, strong) NSTimer *burnBodyNumberTimer;

@property (nonatomic, assign) NSInteger portraitsPushBigGroupedSurfacePartial;

@property (nonatomic, copy) NSString *theMarkFatOffOwn;

@end

@implementation CheckSmoothButton

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self garbageBadQuoteBackupMath];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self garbageBadQuoteBackupMath];
    }
    return self;
}


- (void)garbageBadQuoteBackupMath {
    
    self.unchangedConsumerSubmitRhythmQualifierPerson = 60;
    self.theMarkFatOffOwn = ShapeWinNet.daySeeWaistUse.succeedLyricistUsedAwayAppearsCubicCode;
    [self setTitle:self.theMarkFatOffOwn forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage lateGenreCloudColor:ShapeWinNet.exposuresColor] forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage lateGenreCloudColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]] forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont systemFontOfSize:16];
    self.layer.cornerRadius = 2.f;
    self.layer.masksToBounds = YES;
    
    self.contentEdgeInsets = UIEdgeInsetsMake(0, 5, 0, 5);
    
    [self sizeToFit];
    
    
    [self addTarget:self action:@selector(eventHitEyeClicked) forControlEvents:UIControlEventTouchUpInside];
}


- (void)eventHitEyeClicked {
    [self somaliDefinedMixerExportedLocales];
    if (self.eyeWhoNodeCapAction) {
        self.eyeWhoNodeCapAction();
    }
}


- (void)somaliDefinedMixerExportedLocales {
    self.enabled = NO;
    self.portraitsPushBigGroupedSurfacePartial = self.unchangedConsumerSubmitRhythmQualifierPerson;
    [self wetMaskBurstFlipPerformerTornado];
    
    
    self.burnBodyNumberTimer = [NSTimer scheduledTimerWithTimeInterval:1.0
                                                                 target:self
                                                               selector:@selector(accessingWidthLegacyIcelandicToo:)
                                                               userInfo:nil
                                                                repeats:YES];
}


- (void)accessingWidthLegacyIcelandicToo:(NSTimer *)timer {
    self.portraitsPushBigGroupedSurfacePartial--;
    if (self.portraitsPushBigGroupedSurfacePartial <= 0) {
        [self insertedIncludingDecipherFalloffCall];
    } else {
        [self wetMaskBurstFlipPerformerTornado];
    }
}


- (void)wetMaskBurstFlipPerformerTornado {
    NSString *title = [NSString stringWithFormat:@"%@(%ld)",ShapeWinNet.daySeeWaistUse.entitledExposeMustCatalogAssistantOurCode, (long)self.portraitsPushBigGroupedSurfacePartial];
    [self setTitle:title forState:UIControlStateDisabled];
}


- (void)insertedIncludingDecipherFalloffCall {
    [self.burnBodyNumberTimer invalidate];
    self.burnBodyNumberTimer = nil;
    self.enabled = YES;
    [self setTitle:self.theMarkFatOffOwn forState:UIControlStateNormal];
}

- (void)dealloc {
    
    [self.burnBodyNumberTimer invalidate];
    self.burnBodyNumberTimer = nil;
}

@end
