






#import "KoreanSeeViewController.h"
#import "XXGProtocolLabel.h"
#import "GroupedCollectViewController.h"

@interface KoreanSeeViewController ()

@property (nonatomic, strong) UITextField *drawLeaseHitTextField;
@property (nonatomic, strong) UITextField *askMilesSlideTextField;
@property (nonatomic,strong) XXGProtocolLabel *commonSchemeLabel;
@end



@implementation KoreanSeeViewController

- (XXGProtocolLabel *)commonSchemeLabel {
    if (!_commonSchemeLabel) {
        _commonSchemeLabel = [XXGProtocolLabel farSeePartnerLabel:NO];
    }
    return _commonSchemeLabel;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.drawLeaseHitTextField = [ShapeWinNet tryMeasureSeventeenReservedExpansionAccount];
    [self.view addSubview:self.drawLeaseHitTextField];
    [self.drawLeaseHitTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(ShapeWinNet.redoFinalTag.earAllowPong);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    
    
    self.askMilesSlideTextField = [ShapeWinNet waxWaitingMicroFourDensityPassword:NO];
    [self.view addSubview:self.askMilesSlideTextField];
    [self.askMilesSlideTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.drawLeaseHitTextField.mas_bottom).offset(ShapeWinNet.redoFinalTag.penSawAmount);
        make.left.right.equalTo(self.drawLeaseHitTextField);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    UIButton *abortButton = self.askMilesSlideTextField.rightView.subviews.firstObject;
    [abortButton addTarget:self action:@selector(showingInlandShortGreaterManagerLineHandler:) forControlEvents:(UIControlEventTouchUpInside)];

    
    UIButton *typeAscendingButton = [ShapeWinNet lightOcclusionLeaseCancelExclusiveColor:ShapeWinNet.daySeeWaistUse.decryptedTied];
    [typeAscendingButton addTarget:self action:@selector(listExactnessProminentOccurFilteringDueAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:typeAscendingButton];
    [typeAscendingButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.askMilesSlideTextField.mas_bottom).offset(ShapeWinNet.redoFinalTag.penSawAmount);
        make.left.right.equalTo(self.askMilesSlideTextField);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.tapsVoiceLaw);
    }];
    
    [self.view addSubview:self.commonSchemeLabel];
    faceWas(self);
    [self.commonSchemeLabel setGlyphAverageLibrariesLegacyWrappedFace:^{
        ampereIll(self);
        [self lossyRangeMuteHandoverCutSafariAction];
    }];
    [self.commonSchemeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(-ShapeWinNet.redoFinalTag.findDryFlash);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.getRouteLate);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.getRouteLate);
    }];
}

- (void)showingInlandShortGreaterManagerLineHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.askMilesSlideTextField.secureTextEntry = !self.askMilesSlideTextField.isSecureTextEntry;
}

- (void)listExactnessProminentOccurFilteringDueAction:(UIButton *)sender {
    if (self.drawLeaseHitTextField.text.length < ShapeWinNet.redoFinalTag.missingStop) {
        [SobAlertView maxRaiseDigitalSourcesInnerQuarter:ShapeWinNet.daySeeWaistUse.cursorDry message:ShapeWinNet.daySeeWaistUse.dayJustifiedBurnFormatFireDeviation completion:nil];
        return;
    }
    if (self.askMilesSlideTextField.text.length < ShapeWinNet.redoFinalTag.missingStop) {
        [SobAlertView maxRaiseDigitalSourcesInnerQuarter:ShapeWinNet.daySeeWaistUse.cursorDry message:ShapeWinNet.daySeeWaistUse.iconSonWordRepublicTelephotoReject completion:nil];
        return;
    }
    if ([self.sumHellmanAir respondsToSelector:@selector(fourthMeterSerializeCropTeluguOcclusionEvaluateName:bedKey:completion:)]) {
        [BitOwnPathView tapsWatchTimeWindow];
        [self.sumHellmanAir fourthMeterSerializeCropTeluguOcclusionEvaluateName:self.drawLeaseHitTextField.text bedKey:self.askMilesSlideTextField.text completion:^(id object) {
            [BitOwnPathView preventedHigherClinicalWindowYearWindow];
        }];
    }
}


- (void)lossyRangeMuteHandoverCutSafariAction {
    GroupedCollectViewController *modeGrouping = [GroupedCollectViewController new];
    modeGrouping.retryManSpa = @(YES);
    modeGrouping.sumHellmanAir = self.sumHellmanAir;
    [modeGrouping setBinaryAppearsInstallQueryEntries:^(BOOL result) {
        self.commonSchemeLabel.bezelOffCutFor = result;
    }];
    [self.navigationController pushViewController:modeGrouping animated:NO];
}

@end
