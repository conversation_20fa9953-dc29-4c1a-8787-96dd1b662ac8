






#import "OpenNetTreeViewController.h"
#import "SDWebImageManager.h"
#import "OutToast.h"
#import "NSString+StickySay.h"

@interface OpenNetTreeViewController ()

@property (nonatomic, strong) UIImageView *relayFunCarImageView;
@property (nonatomic, strong) UITextField *ageCourseTextField;
@property (nonatomic, strong) UITextField *penToneCardTextField;

@end

@implementation OpenNetTreeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.earStepsonButton.hidden = [self.retryManSpa[0] boolValue];
    
    CGFloat templateAscendedPromisedFreeWord = ShapeWinNet.redoFinalTag.penSawAmount;
    
    UILabel *tipLabel = [ShapeWinNet advancesAudienceClockAskCinematic:ShapeWinNet.daySeeWaistUse.capturedBoyfriendManEnergyFeet];
    tipLabel.numberOfLines = 0;
    [self.view addSubview:tipLabel];
    [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(ShapeWinNet.redoFinalTag.hisSonOrange);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefKinMask);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefKinMask);
    }];
    
    
    self.ageCourseTextField = [ShapeWinNet focalHitTheField:ShapeWinNet.daySeeWaistUse.integrityExtendsVersionAreHashMatrix isSecure:NO];
    [self.view addSubview:self.ageCourseTextField];
    [self.ageCourseTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(tipLabel.mas_bottom).offset(templateAscendedPromisedFreeWord);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    
    
    self.penToneCardTextField = [ShapeWinNet focalHitTheField:ShapeWinNet.daySeeWaistUse.disorderAcquireTallExtrinsicRowOuter isSecure:NO];;
    [self.view addSubview:self.penToneCardTextField];
    [self.penToneCardTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.ageCourseTextField.mas_bottom).offset(templateAscendedPromisedFreeWord);
        make.left.right.equalTo(self.ageCourseTextField);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    
    
    UIImageView *rewardImageView = nil;
    if ([self.retryManSpa[1] length] > 0) {
        
        rewardImageView = [[UIImageView alloc] init];
        rewardImageView.backgroundColor = UIColor.lightGrayColor;
        rewardImageView.contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:rewardImageView];
        [rewardImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.penToneCardTextField.mas_bottom).offset(templateAscendedPromisedFreeWord);
            make.width.mas_equalTo(self.penToneCardTextField);
            make.centerX.mas_equalTo(0);
        }];
        self.relayFunCarImageView = rewardImageView;
        
        [[SDWebImageManager sharedManager] loadImageWithURL:[NSURL URLWithString:self.retryManSpa[1]] options:0 progress:nil completed:^(UIImage * _Nullable image2, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
            dispatch_async(dispatch_get_main_queue(), ^{
                rewardImageView.image = image2;
                CGFloat ratio = image2.size.height / image2.size.width;
                [rewardImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo((ShapeWinNet.redoFinalTag.pulseRomanRadixSyntaxFindWidth -ShapeWinNet.redoFinalTag.briefPipeMid*2)*ratio);
                }];
                [self.view layoutIfNeeded];
                
                [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.center.mas_equalTo(0);
                    CGFloat height = self.relayFunCarImageView ? self.relayFunCarImageView.frame.size.height + ShapeWinNet.redoFinalTag.penSawAmount +ShapeWinNet.redoFinalTag.tipPetabytesTopPairTeamSmile : ShapeWinNet.redoFinalTag.tipPetabytesTopPairTeamSmile;
                    
                    make.size.mas_equalTo(CGSizeMake(ShapeWinNet.redoFinalTag.pulseRomanRadixSyntaxFindWidth, MIN(height, UIScreen.mainScreen.bounds.size.height)));
                    if (height > UIScreen.mainScreen.bounds.size.height) {
                        [self.relayFunCarImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                            make.height.mas_equalTo(self.relayFunCarImageView.frame.size.height-(height-UIScreen.mainScreen.bounds.size.height));
                        }];
                    }
                }];
            });
        }];
    }
    
    
    UIButton *xxpk_realButton = [ShapeWinNet lightOcclusionLeaseCancelExclusiveColor:ShapeWinNet.daySeeWaistUse.poloKilowattStoreUtilitiesPreset];
    [xxpk_realButton addTarget:self action:@selector(romanWaitReactorLibrariesAlgorithmTransfer:) forControlEvents:(UIControlEventTouchUpInside)];
    [self.view addSubview:xxpk_realButton];
    [xxpk_realButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(-ShapeWinNet.redoFinalTag.bagBlinkYard);
        make.left.right.equalTo(self.ageCourseTextField);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.tapsVoiceLaw);
    }];
    
}

- (void)romanWaitReactorLibrariesAlgorithmTransfer:(id)sender {

}

@end
