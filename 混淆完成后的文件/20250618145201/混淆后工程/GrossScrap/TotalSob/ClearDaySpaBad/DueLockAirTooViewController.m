






#import "DueLockAirTooViewController.h"
#import "OutToast.h"
#import "MarkupInfo.h"

@interface DueLockAirTooViewController ()

@property (nonatomic, strong) UIImageView *dueSquareView;
@property (nonatomic, strong) UIButton *fourteenBigButton;
@property (nonatomic, strong) UIView *loseBloodView;
@property (nonatomic, strong) UILabel *tintNodeLabel;
@property (nonatomic, strong) UITextField *drawLeaseHitTextField;
@property (nonatomic, strong) UITextField *askMilesSlideTextField;
@end

@implementation DueLockAirTooViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.earStepsonButton.hidden = YES;
    
    if ([MarkupInfo resetLookHighlightMolarTrustImage]) {
        self.dueSquareView = [[UIImageView alloc] initWithImage:[MarkupInfo resetLookHighlightMolarTrustImage]];
        [self.view addSubview:self.dueSquareView];
        self.dueSquareView.hidden = YES;
        [self.dueSquareView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(ShapeWinNet.redoFinalTag.tapsVoiceLaw);
            make.left.equalTo(self.notEraserButton.mas_right);
            make.top.equalTo(self.view).offset(ShapeWinNet.redoFinalTag.cervicalPath);
        }];
    }
    
    self.loseBloodView = [ShapeWinNet loseBloodView];
    self.loseBloodView.hidden = YES;
    [self.view addSubview:self.loseBloodView];
    [self.loseBloodView mas_makeConstraints:^(MASConstraintMaker *make) {
        if ([MarkupInfo resetLookHighlightMolarTrustImage]) {
            make.centerY.equalTo(self.dueSquareView);
            make.left.equalTo(self.dueSquareView.mas_right).offset(ShapeWinNet.redoFinalTag.maxBrownCan);
        }else {
            make.top.equalTo(self.view).offset(ShapeWinNet.redoFinalTag.cervicalPath);
            make.left.equalTo(self.notEraserButton.mas_right).offset(ShapeWinNet.redoFinalTag.maxBrownCan);
        }
        make.right.equalTo(self.earStepsonButton.mas_left);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.tapsVoiceLaw);
    }];
    
    self.tintNodeLabel = [ShapeWinNet advancesAudienceClockAskCinematic:ShapeWinNet.daySeeWaistUse.warnCreamyClockwiseMalformedFixing];
    self.tintNodeLabel.numberOfLines = 0;
    [self.view addSubview:self.tintNodeLabel];
    [self.tintNodeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(ShapeWinNet.redoFinalTag.jobEggArmpit);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefKinMask);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefKinMask);
    }];
    
    
    self.drawLeaseHitTextField = [ShapeWinNet tryMeasureSeventeenReservedExpansionAccount];
    self.drawLeaseHitTextField.enabled = NO;
    self.drawLeaseHitTextField.text = self.retryManSpa[ShapeWinNet.redoFinalTag.manyTooSignName];
    [self advisoryBurnShelfLandscapePostWelshView:self.drawLeaseHitTextField text:ShapeWinNet.daySeeWaistUse.japaneseIncludesOccurredKilometerCarrier];
    [self.view addSubview:self.drawLeaseHitTextField];
    [self.drawLeaseHitTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.tintNodeLabel.mas_bottom).offset(ShapeWinNet.redoFinalTag.penSawAmount);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    
    
    self.askMilesSlideTextField = [ShapeWinNet tryMeasureSeventeenReservedExpansionAccount];
    self.askMilesSlideTextField.enabled = NO;
    self.askMilesSlideTextField.text = self.retryManSpa[ShapeWinNet.redoFinalTag.pulseListenKey];
    [self advisoryBurnShelfLandscapePostWelshView:self.askMilesSlideTextField text:ShapeWinNet.daySeeWaistUse.returningPrimeInviteeNowTop];
    [self.view addSubview:self.askMilesSlideTextField];
    [self.askMilesSlideTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.drawLeaseHitTextField.mas_bottom).offset(ShapeWinNet.redoFinalTag.penSawAmount);
        make.left.right.equalTo(self.drawLeaseHitTextField);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    
    
    UIButton *xxpk_saveButton = [ShapeWinNet lightOcclusionLeaseCancelExclusiveColor:ShapeWinNet.daySeeWaistUse.transitHerGeneralTrackContacts];
    [xxpk_saveButton addTarget:self action:@selector(diastolicGeneratorCentralCanadianOddAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_saveButton];
    [xxpk_saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.askMilesSlideTextField.mas_bottom).offset(ShapeWinNet.redoFinalTag.penSawAmount);
        make.left.right.equalTo(self.askMilesSlideTextField);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.tapsVoiceLaw);
    }];
    
    
    self.fourteenBigButton = [ShapeWinNet directlyScrollsLockingAlignDisk:ShapeWinNet.daySeeWaistUse.actualThousandsAndYouAirHundred];
    [self.fourteenBigButton setContentEdgeInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
    [self.fourteenBigButton addTarget:self action:@selector(secondaryDesignZeroTextLaterAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.fourteenBigButton];
    [self.fourteenBigButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(xxpk_saveButton.mas_bottom).offset(ShapeWinNet.redoFinalTag.fileBikeSub);
        make.centerX.equalTo(self.view);
    }];
}

- (void)advisoryBurnShelfLandscapePostWelshView:(UITextField *)textField text:(NSString *)text
{
    CGRect frame = {{0,0},CGSizeMake(ShapeWinNet.redoFinalTag.tapsVoiceLaw, ShapeWinNet.redoFinalTag.outCreateSin)};
    UILabel *leftview = [[UILabel alloc] initWithFrame:frame];
    leftview.text = text;
    leftview.textColor = UIColor.redColor;
    textField.leftViewMode = UITextFieldViewModeAlways;
    textField.leftView = leftview;
}

- (void)secondaryDesignZeroTextLaterAction:(UIButton *)sender {
    [[HitUnableManager shared] exceedsPotentialAgeAtomicSlovakIntervalsViewController:self.navigationController];
}

- (void)diastolicGeneratorCentralCanadianOddAction:(UIButton *)sender {
    sender.hidden = YES;
    self.tintNodeLabel.hidden = YES;
    self.loseBloodView.hidden = NO;
    self.dueSquareView.hidden = NO;
    self.fourteenBigButton.hidden = YES;
    
    [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.view.superview);
        make.size.mas_equalTo(CGSizeMake(ShapeWinNet.redoFinalTag.pulseRomanRadixSyntaxFindWidth, ShapeWinNet.redoFinalTag.tipPetabytesTopPairTeamSmile-ShapeWinNet.redoFinalTag.getRouteLate));
    }];
    [self.drawLeaseHitTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.loseBloodView.mas_bottom).offset(ShapeWinNet.redoFinalTag.penSawAmount);
    }];
    [self.view layoutIfNeeded];
    
    BOOL smile = [[[[NSBundle mainBundle] infoDictionary] allKeys] containsObject:ShapeWinNet.redoFinalTag.clipPreservedStatementPerformedPubSnapColumn];
    if (!smile) {
        self.earStepsonButton.hidden = NO;
        [SobAlertView maxRaiseDigitalSourcesInnerQuarter:nil message:ShapeWinNet.daySeeWaistUse.campaignPotentialBinShoulderNegative completion:nil];
        return;
    }
    CGSize size = self.view.frame.size;
    size.height -= ShapeWinNet.redoFinalTag.borderFourth;
    UIGraphicsBeginImageContextWithOptions(size, NO, 0.0);
    [self.view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    UIImageWriteToSavedPhotosAlbum(image, self, @selector(image:youngestFastestWindowAmpereRemainderDecide:contextInfo:), (__bridge void *)self);
}

- (void)image:(UIImage *)image youngestFastestWindowAmpereRemainderDecide:(NSError *)error contextInfo:(void *)contextInfo
{
    
    if(!error){
        [[HitUnableManager shared] exceedsPotentialAgeAtomicSlovakIntervalsViewController:self.navigationController];
        [OutToast faxTask:ShapeWinNet.daySeeWaistUse.musicianGenderNodeProvidingPrint];
    }else {
        self.earStepsonButton.hidden = NO;
        [SobAlertView maxRaiseDigitalSourcesInnerQuarter:nil message:ShapeWinNet.daySeeWaistUse.campaignPotentialBinShoulderNegative completion:nil];
    }
}

@end
