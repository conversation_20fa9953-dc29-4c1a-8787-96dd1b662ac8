






#import "LikeAsleepViewController.h"
#import "HandledEggConfig.h"
#import "NSObject+WayModel.h"
#import "UIColor+GetColor.h"
#import "OutToast.h"

@interface LikeAsleepViewController ()<UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray<NSDictionary *> *shuffleGenerateDetectionWorkspaceFiller; 
@property (nonatomic, strong) NSArray<NSArray<NSString *> *> *chatNapPairWax; 
@property (nonatomic, strong) NSMutableArray<NSString *> *sectionTitles; 

@end

@implementation LikeAsleepViewController


- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    _shuffleGenerateDetectionWorkspaceFiller = [NSMutableArray array];
    _chatNapPairWax = @[];
    _sectionTitles = [NSMutableArray array];
    
    [self certHerFarView];
}

- (void)viewWillAppear:(BOOL)animated {
    
    UIEdgeInsets oceanBlend = [[HitUnableManager shared] laterExceedsWindow].safeAreaInsets;
    
    oceanBlend.top    += 10;
    oceanBlend.left   += 10;
    oceanBlend.bottom += 10;
    oceanBlend.right  += 10;

    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(oceanBlend);
    }];
}


- (void)certHerFarView {
    _tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStyleGrouped];
    _tableView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    _tableView.dataSource = self;
    _tableView.delegate = self;
    _tableView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:_tableView];
    [_tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.notEraserButton.mas_bottom);
        make.left.right.bottom.equalTo(self.view);
    }];
    
    
    [_tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:NSStringFromClass(self.class)];
}


- (NSArray<NSString *> *)editorialCheckDictionary:(NSDictionary *)dict {
    return [[dict allKeys] sortedArrayUsingSelector:@selector(caseInsensitiveCompare:)];
}

- (void)birthSleepOldInfo:(NSDictionary *)info withTitle:(NSString *)title {
    if (!info || ![info isKindOfClass:[NSDictionary class]]) {
        return;
    }
    
    
    dispatch_async(dispatch_get_main_queue(), ^{
        @synchronized (self) {
            
            [self->_shuffleGenerateDetectionWorkspaceFiller addObject:[info copy]];
            NSArray *cleanChunk = [self editorialCheckDictionary:info];
            self->_chatNapPairWax = [self->_chatNapPairWax arrayByAddingObject:cleanChunk];
            [self->_sectionTitles addObject:title];
            
            
            [self.tableView reloadData];
        }
    });
}


- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return _shuffleGenerateDetectionWorkspaceFiller.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return _chatNapPairWax[section].count;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section {
    return _sectionTitles[section];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(self.class) forIndexPath:indexPath];
    
    NSString *key;
    id value;
    NSInteger sectionIndex = indexPath.section;
    key = _chatNapPairWax[sectionIndex][indexPath.row];
    value = _shuffleGenerateDetectionWorkspaceFiller[sectionIndex][key];
    BOOL arranged = [value isKindOfClass:[NSDictionary class]] || [value isKindOfClass:[NSArray class]];
    cell.backgroundColor = [UIColor clearColor];
    
    
    for (UIView *subview in cell.contentView.subviews) {
        [subview removeFromSuperview];
    }
    
    
    UILabel *butLabel = [[UILabel alloc] init];
    butLabel.font = [UIFont monospacedSystemFontOfSize:14 weight:UIFontWeightMedium];
    butLabel.textColor = [UIColor darkGrayColor];
    butLabel.text = key;
    butLabel.numberOfLines = 0;
    [cell.contentView addSubview:butLabel];
    
    
    UILabel *guideLabel = [[UILabel alloc] init];
    guideLabel.font = [UIFont monospacedSystemFontOfSize:14 weight:UIFontWeightRegular];
    guideLabel.textColor = [UIColor blackColor];
    guideLabel.numberOfLines = 0;
    guideLabel.textAlignment = NSTextAlignmentRight;
    [cell.contentView addSubview:guideLabel];
    
    
    [butLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(cell.contentView).offset(ShapeWinNet.redoFinalTag.tailInfinity);
        make.top.equalTo(cell.contentView).offset(ShapeWinNet.redoFinalTag.cervicalPath);
        make.bottom.equalTo(cell.contentView).offset(-ShapeWinNet.redoFinalTag.cervicalPath);
        make.width.equalTo(cell.contentView.mas_width).multipliedBy(arranged?ShapeWinNet.redoFinalTag.phasePinTrad:ShapeWinNet.redoFinalTag.promptLoading);
    }];
    
    [guideLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(cell.contentView).offset(-ShapeWinNet.redoFinalTag.tailInfinity);
        make.top.equalTo(cell.contentView).offset(ShapeWinNet.redoFinalTag.cervicalPath);
        make.bottom.equalTo(cell.contentView).offset(-ShapeWinNet.redoFinalTag.cervicalPath);
        make.left.equalTo(butLabel.mas_right).offset(ShapeWinNet.redoFinalTag.cervicalPath);
    }];
    
    
    if (arranged) {
        cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    } else {
        guideLabel.text = [value description];
        cell.accessoryType = UITableViewCellAccessoryNone;
    }
    
    return cell;
}


- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    id value;
    NSString *key;
    
    NSInteger sectionIndex = indexPath.section;
    key = _chatNapPairWax[sectionIndex][indexPath.row];
    value = _shuffleGenerateDetectionWorkspaceFiller[sectionIndex][key];
    
    
    if ([value isKindOfClass:[NSDictionary class]]) {
        [self bagVeryPinDictionary:value withTitle:key];
    } else if ([value isKindOfClass:[NSArray class]]) {
        [self limitedMixArray:value withTitle:key];
    } else {
        
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        [pasteboard setString:[value description]];
        [OutToast faxTask:ShapeWinNet.redoFinalTag.linkCompoundBuffersExpectAtomicFolder];
    }
}


- (void)bagVeryPinDictionary:(NSDictionary *)dict withTitle:(NSString *)title {
    LikeAsleepViewController *wayAvail = [[LikeAsleepViewController alloc] init];
    [self.navigationController pushViewController:wayAvail animated:NO];
    [wayAvail birthSleepOldInfo:dict withTitle:title];
}

- (void)limitedMixArray:(NSArray *)array withTitle:(NSString *)title {
    
    NSMutableDictionary *chunkDict = [NSMutableDictionary dictionary];
    for (NSInteger i = 0; i < array.count; i++) {
        chunkDict[[NSString stringWithFormat:@"[%ld]", (long)i]] = array[i];
    }
    
    LikeAsleepViewController *wayAvail = [[LikeAsleepViewController alloc] init];
    [self.navigationController pushViewController:wayAvail animated:NO];
    [wayAvail birthSleepOldInfo:chunkDict withTitle:[NSString stringWithFormat:@"%@ (Array)", title]];
}

@end
