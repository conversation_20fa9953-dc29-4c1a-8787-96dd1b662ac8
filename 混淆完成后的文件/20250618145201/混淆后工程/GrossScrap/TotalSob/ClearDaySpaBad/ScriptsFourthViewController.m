






#import "ScriptsFourthViewController.h"
#import "CheckSmoothButton.h"
#import "OutToast.h"
#import "SlavicTapTextField.h"
#import "NSString+StickySay.h"
@import WebKit;

@interface ScriptsFourthViewController ()

@property (nonatomic, strong) SlavicTapTextField *whoRedDigitTextField;
@property (nonatomic, strong) UITextField *newsstandTextField;
@property (nonatomic, strong) CheckSmoothButton *unloadSunButton;

@end

@implementation ScriptsFourthViewController


- (CheckSmoothButton *)unloadSunButton {
    if (!_unloadSunButton) {
        _unloadSunButton = [[CheckSmoothButton alloc] init];
    }
    return _unloadSunButton;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.earStepsonButton.hidden = [self.retryManSpa[0] boolValue];
    
    UILabel *tipLabel = [ShapeWinNet advancesAudienceClockAskCinematic:ShapeWinNet.daySeeWaistUse.tabStreamMouthMountedJobCap];
    tipLabel.numberOfLines = 0;
    [self.view addSubview:tipLabel];
    [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(ShapeWinNet.redoFinalTag.briefKinMask);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefKinMask);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefKinMask);
    }];
    
    
    self.whoRedDigitTextField = [[SlavicTapTextField alloc] initWithController:self];
    [self.view addSubview:self.whoRedDigitTextField];
    [self.whoRedDigitTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(tipLabel.mas_bottom).offset(ShapeWinNet.redoFinalTag.penSawAmount);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    
    
    self.newsstandTextField = [ShapeWinNet digestSerialInsideUndoMeasureIllegalCode];
    [self.view addSubview:self.newsstandTextField];
    [self.newsstandTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.whoRedDigitTextField.mas_bottom).offset(ShapeWinNet.redoFinalTag.bagBlinkYard);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    
    faceWas(self);
    self.unloadSunButton.eyeWhoNodeCapAction = ^{
        ampereIll(self);
        NSString *utilitiesCode = self.whoRedDigitTextField.fileBigHandPub;
        NSString *tiedSockSex = self.whoRedDigitTextField.tempFooterMusicianTriggersExpand;
        if (self.whoRedDigitTextField.whoRedDigitTextField.text.withinOptWet) {
            [self.unloadSunButton insertedIncludingDecipherFalloffCall];
            [SobAlertView maxRaiseDigitalSourcesInnerQuarter:ShapeWinNet.daySeeWaistUse.cursorDry message:ShapeWinNet.daySeeWaistUse.disablingMenBitRowsSnapDisallow completion:nil];
            return;
        }
        if ([self.sumHellmanAir respondsToSelector:@selector(sheJustCheckoutProxyPartnerFitReservedType:overdueBlur:dropCode:completion:)]) {
            [BitOwnPathView tapsWatchTimeWindow];
            [self.sumHellmanAir sheJustCheckoutProxyPartnerFitReservedType:ShapeWinNet.redoFinalTag.lowOnlySoloTag overdueBlur:tiedSockSex dropCode:utilitiesCode completion:^(id object) {
                [BitOwnPathView preventedHigherClinicalWindowYearWindow];
                if ([object boolValue]) {
                    [OutToast dashFunOff:ShapeWinNet.daySeeWaistUse.exactnessHourlyOutBinCutDayCode];
                }else {
                    [self.unloadSunButton insertedIncludingDecipherFalloffCall];
                }
            }];
        }
    };
    [self.view addSubview:self.unloadSunButton];
    [self.unloadSunButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.newsstandTextField);
        make.height.equalTo(self.newsstandTextField);
        make.left.equalTo(self.newsstandTextField.mas_right).offset(ShapeWinNet.redoFinalTag.cervicalPath);
        make.right.equalTo(self.whoRedDigitTextField);
    }];
    
    
    [self.unloadSunButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    
    UIButton *xxpk_bindButton = [ShapeWinNet lightOcclusionLeaseCancelExclusiveColor:ShapeWinNet.daySeeWaistUse.reminderTextProgramAddSidebar];
    [xxpk_bindButton addTarget:self action:@selector(boldfaceDistinctAssemblyMarkupVitaminAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_bindButton];
    [xxpk_bindButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.newsstandTextField.mas_bottom).offset(ShapeWinNet.redoFinalTag.bagBlinkYard);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.tapsVoiceLaw);
    }];
}

- (void)boldfaceDistinctAssemblyMarkupVitaminAction:(id)sender {
    if (self.whoRedDigitTextField.whoRedDigitTextField.text.withinOptWet) {
        [SobAlertView maxRaiseDigitalSourcesInnerQuarter:ShapeWinNet.daySeeWaistUse.cursorDry message:ShapeWinNet.daySeeWaistUse.disablingMenBitRowsSnapDisallow completion:nil];
        return;
    }
    if (self.newsstandTextField.text.withinOptWet) {
        [SobAlertView maxRaiseDigitalSourcesInnerQuarter:ShapeWinNet.daySeeWaistUse.cursorDry message:ShapeWinNet.daySeeWaistUse.funnelNextHostFindAppended completion:nil];
        return;
    }
    NSString *utilitiesCode = self.whoRedDigitTextField.fileBigHandPub;
    NSString *tiedSockSex = self.whoRedDigitTextField.tempFooterMusicianTriggersExpand;
    if ([self.sumHellmanAir respondsToSelector:@selector(helpDeferringMobileInjectionPlaneDeliveryRenew:code:dropCode:completion:)]) {
        [BitOwnPathView tapsWatchTimeWindow];
        [self.sumHellmanAir helpDeferringMobileInjectionPlaneDeliveryRenew:tiedSockSex code:self.newsstandTextField.text dropCode:utilitiesCode completion:^(id object) {
            [BitOwnPathView preventedHigherClinicalWindowYearWindow];
            if ([object boolValue]) {
                [[HitUnableManager shared] exceedsPotentialAgeAtomicSlovakIntervalsViewController:self.navigationController];
                [OutToast dashFunOff:ShapeWinNet.daySeeWaistUse.funkResultsProtocolsSymbolicGroupedSparse];
                if ([self.retryManSpa[1] isKindOfClass:[WKWebView class]]) {
                    WKWebView *tapFarShare = (WKWebView *)self.retryManSpa[1];
                    [tapFarShare reload];
                }
            }
        }];
    }
}

- (void)dealloc {
    [self.unloadSunButton insertedIncludingDecipherFalloffCall];
}

@end
