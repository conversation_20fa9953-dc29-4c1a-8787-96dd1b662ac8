






#import "EphemeralViewController.h"
#import "CheckSmoothButton.h"
#import "OutToast.h"
#import "SlavicTapTextField.h"
#import "NSString+StickySay.h"

@interface EphemeralViewController ()

@property (nonatomic, strong) SlavicTapTextField *whoRedDigitTextField;
@property (nonatomic, strong) UITextField *newsstandTextField;
@property (nonatomic, strong) UITextField *askMilesSlideTextField;
@property (nonatomic, strong) CheckSmoothButton *unloadSunButton;
@end

@implementation EphemeralViewController

- (CheckSmoothButton *)unloadSunButton {
    if (!_unloadSunButton) {
        _unloadSunButton = [[CheckSmoothButton alloc] init];
    }
    return _unloadSunButton;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.whoRedDigitTextField = [[SlavicTapTextField alloc] initWithController:self];
    [self.view addSubview:self.whoRedDigitTextField];
    [self.whoRedDigitTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(ShapeWinNet.redoFinalTag.earAllowPong);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    
    
    self.newsstandTextField = [ShapeWinNet digestSerialInsideUndoMeasureIllegalCode];
    [self.view addSubview:self.newsstandTextField];
    [self.newsstandTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.whoRedDigitTextField.mas_bottom).offset(ShapeWinNet.redoFinalTag.maxBrownCan);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    
    faceWas(self);
    self.unloadSunButton.eyeWhoNodeCapAction = ^{
        ampereIll(self);
        
        NSString *utilitiesCode = self.whoRedDigitTextField.fileBigHandPub;
        NSString *tiedSockSex = self.whoRedDigitTextField.tempFooterMusicianTriggersExpand;
        if (self.whoRedDigitTextField.whoRedDigitTextField.text.withinOptWet) {
            [self.unloadSunButton insertedIncludingDecipherFalloffCall];
            [SobAlertView maxRaiseDigitalSourcesInnerQuarter:ShapeWinNet.daySeeWaistUse.cursorDry message:ShapeWinNet.daySeeWaistUse.disablingMenBitRowsSnapDisallow completion:nil];
            return;
        }
        if ([self.sumHellmanAir respondsToSelector:@selector(sheJustCheckoutProxyPartnerFitReservedType:overdueBlur:dropCode:completion:)]) {
            [BitOwnPathView tapsWatchTimeWindow];
            [self.sumHellmanAir sheJustCheckoutProxyPartnerFitReservedType:ShapeWinNet.redoFinalTag.availTapIdentityFitInfo overdueBlur:tiedSockSex dropCode:utilitiesCode completion:^(id object) {
                [BitOwnPathView preventedHigherClinicalWindowYearWindow];
                if ([object boolValue]) {
                    [OutToast dashFunOff:ShapeWinNet.daySeeWaistUse.exactnessHourlyOutBinCutDayCode];
                }else {
                    [self.unloadSunButton insertedIncludingDecipherFalloffCall];
                }
            }];
        }
    };
    [self.view addSubview:self.unloadSunButton];
    [self.unloadSunButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.newsstandTextField);
        make.height.equalTo(self.newsstandTextField);
        make.left.equalTo(self.newsstandTextField.mas_right).offset(ShapeWinNet.redoFinalTag.cervicalPath);
        make.right.equalTo(self.whoRedDigitTextField);
    }];
    
    
    [self.unloadSunButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    
    self.askMilesSlideTextField = [ShapeWinNet waxWaitingMicroFourDensityPassword:YES];
    [self.view addSubview:self.askMilesSlideTextField];
    [self.askMilesSlideTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.newsstandTextField.mas_bottom).offset(ShapeWinNet.redoFinalTag.maxBrownCan);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    UIButton *modeNineButton = self.askMilesSlideTextField.rightView.subviews.firstObject;
    [modeNineButton addTarget:self action:@selector(showingInlandShortGreaterManagerLineHandler:) forControlEvents:(UIControlEventTouchUpInside)];
    
    
    UIButton *xxpk_forgetButton = [ShapeWinNet lightOcclusionLeaseCancelExclusiveColor:ShapeWinNet.daySeeWaistUse.farthestHowKey];
    [xxpk_forgetButton addTarget:self action:@selector(solutionsPhysicalRopeCoastYahooAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_forgetButton];
    [xxpk_forgetButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.askMilesSlideTextField.mas_bottom).offset(ShapeWinNet.redoFinalTag.maxBrownCan);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.tapsVoiceLaw);
    }];
}
- (void)showingInlandShortGreaterManagerLineHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.askMilesSlideTextField.secureTextEntry = !self.askMilesSlideTextField.isSecureTextEntry;
}

- (void)solutionsPhysicalRopeCoastYahooAction:(id)sender {
    if (self.whoRedDigitTextField.whoRedDigitTextField.text.withinOptWet) {
        [SobAlertView maxRaiseDigitalSourcesInnerQuarter:ShapeWinNet.daySeeWaistUse.cursorDry message:ShapeWinNet.daySeeWaistUse.disablingMenBitRowsSnapDisallow completion:nil];
        return;
    }
    if (self.newsstandTextField.text.withinOptWet) {
        [SobAlertView maxRaiseDigitalSourcesInnerQuarter:ShapeWinNet.daySeeWaistUse.cursorDry message:ShapeWinNet.daySeeWaistUse.funnelNextHostFindAppended completion:nil];
        return;
    }
    if (self.askMilesSlideTextField.text.length < ShapeWinNet.redoFinalTag.missingStop) {
        [SobAlertView maxRaiseDigitalSourcesInnerQuarter:ShapeWinNet.daySeeWaistUse.cursorDry message:ShapeWinNet.daySeeWaistUse.iconSonWordRepublicTelephotoReject completion:nil];
        return;
    }
    NSString *utilitiesCode = self.whoRedDigitTextField.fileBigHandPub;
    NSString *tiedSockSex = self.whoRedDigitTextField.tempFooterMusicianTriggersExpand;
    if ([self.sumHellmanAir respondsToSelector:@selector(trainingHandlesStoodPressFullyRopeSignaling:code:dropCode:dueKey:completion:)]) {
        [BitOwnPathView tapsWatchTimeWindow];
        [self.sumHellmanAir trainingHandlesStoodPressFullyRopeSignaling:tiedSockSex code:self.newsstandTextField.text dropCode:utilitiesCode dueKey:self.askMilesSlideTextField.text completion:^(id object) {
            [BitOwnPathView preventedHigherClinicalWindowYearWindow];
            [OutToast dashFunOff:ShapeWinNet.daySeeWaistUse.indexedDescenderDispenseCoverageAnchors];
            if (object) {
                [self.fourthVitalDelegate finderSeparatorStrictlyScalarCircleWithName:object whoRangeEggPassword:self.askMilesSlideTextField.text];
                [self stickyIndoorClampMismatchStepperAction:nil];
            }
        }];
    }
}

- (void)dealloc {
    [self.unloadSunButton insertedIncludingDecipherFalloffCall];
}
@end
