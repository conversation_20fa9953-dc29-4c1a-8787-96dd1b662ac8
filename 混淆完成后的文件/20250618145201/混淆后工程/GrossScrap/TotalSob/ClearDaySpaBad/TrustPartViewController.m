






#import "TrustPartViewController.h"
#import "CheckSmoothButton.h"
#import "GroupedCollectViewController.h"
#import "OutToast.h"
#import "XXGProtocolLabel.h"
#import "SlavicTapTextField.h"
#import "NSString+StickySay.h"

@interface TrustPartViewController ()

@property (nonatomic, strong) SlavicTapTextField *whoRedDigitTextField;
@property (nonatomic, strong) UITextField *newsstandTextField;
@property (nonatomic, strong) CheckSmoothButton *unloadSunButton;
@property (nonatomic,strong) XXGProtocolLabel *commonSchemeLabel;

@end

@implementation TrustPartViewController

- (CheckSmoothButton *)unloadSunButton {
    if (!_unloadSunButton) {
        _unloadSunButton = [[CheckSmoothButton alloc] init];
    }
    return _unloadSunButton;
}

- (XXGProtocolLabel *)commonSchemeLabel {
    if (!_commonSchemeLabel) {
        _commonSchemeLabel = [XXGProtocolLabel farSeePartnerLabel:NO];
    }
    return _commonSchemeLabel;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.whoRedDigitTextField = [[SlavicTapTextField alloc] initWithController:self];
    [self.view addSubview:self.whoRedDigitTextField];
    [self.whoRedDigitTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(ShapeWinNet.redoFinalTag.earAllowPong);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    
    
    self.newsstandTextField = [ShapeWinNet digestSerialInsideUndoMeasureIllegalCode];
    [self.view addSubview:self.newsstandTextField];
    [self.newsstandTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.whoRedDigitTextField.mas_bottom).offset(ShapeWinNet.redoFinalTag.penSawAmount);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.outCreateSin);
    }];
    
    faceWas(self);
    self.unloadSunButton.eyeWhoNodeCapAction = ^{
        ampereIll(self);
        NSString *utilitiesCode = self.whoRedDigitTextField.fileBigHandPub;
        NSString *tiedSockSex = self.whoRedDigitTextField.tempFooterMusicianTriggersExpand;
        if (self.whoRedDigitTextField.whoRedDigitTextField.text.withinOptWet) {
            [self.unloadSunButton insertedIncludingDecipherFalloffCall];
            [SobAlertView maxRaiseDigitalSourcesInnerQuarter:ShapeWinNet.daySeeWaistUse.cursorDry message:ShapeWinNet.daySeeWaistUse.disablingMenBitRowsSnapDisallow completion:nil];
            return;
        }
        if ([self.sumHellmanAir respondsToSelector:@selector(sheJustCheckoutProxyPartnerFitReservedType:overdueBlur:dropCode:completion:)]) {
            [BitOwnPathView tapsWatchTimeWindow];
            [self.sumHellmanAir sheJustCheckoutProxyPartnerFitReservedType:ShapeWinNet.redoFinalTag.boostAliveSeeKitInter overdueBlur:tiedSockSex dropCode:utilitiesCode completion:^(id object) {
                [BitOwnPathView preventedHigherClinicalWindowYearWindow];
                if ([object boolValue]) {
                    [OutToast dashFunOff:ShapeWinNet.daySeeWaistUse.exactnessHourlyOutBinCutDayCode];
                }else {
                    [self.unloadSunButton insertedIncludingDecipherFalloffCall];
                }
            }];
        }
    };
    [self.view addSubview:self.unloadSunButton];
    [self.unloadSunButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.newsstandTextField);
        make.height.equalTo(self.newsstandTextField);
        make.left.equalTo(self.newsstandTextField.mas_right).offset(ShapeWinNet.redoFinalTag.cervicalPath);
        make.right.equalTo(self.whoRedDigitTextField);
    }];
    
    
    [self.unloadSunButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    
    UIButton *typeAscendingButton = [ShapeWinNet lightOcclusionLeaseCancelExclusiveColor:ShapeWinNet.daySeeWaistUse.uniformSummaryOverdueWeekendLowerMetrics];
    [typeAscendingButton addTarget:self action:@selector(stackedAirSoftAudiencesAmharicAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:typeAscendingButton];
    [typeAscendingButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.newsstandTextField.mas_bottom).offset(ShapeWinNet.redoFinalTag.penSawAmount);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.briefPipeMid);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.briefPipeMid);
        make.height.mas_equalTo(ShapeWinNet.redoFinalTag.tapsVoiceLaw);
    }];
    
    [self.view addSubview:self.commonSchemeLabel];
    [self.commonSchemeLabel setGlyphAverageLibrariesLegacyWrappedFace:^{
        ampereIll(self);
        [self lossyRangeMuteHandoverCutSafariAction];
    }];
    [self.commonSchemeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(-ShapeWinNet.redoFinalTag.findDryFlash);
        make.left.mas_equalTo(ShapeWinNet.redoFinalTag.getRouteLate);
        make.right.mas_equalTo(-ShapeWinNet.redoFinalTag.getRouteLate);
    }];
}

- (void)stackedAirSoftAudiencesAmharicAction:(UIButton *)sender {
    if (self.whoRedDigitTextField.whoRedDigitTextField.text.withinOptWet) {
        [SobAlertView maxRaiseDigitalSourcesInnerQuarter:ShapeWinNet.daySeeWaistUse.cursorDry message:ShapeWinNet.daySeeWaistUse.disablingMenBitRowsSnapDisallow completion:nil];
        return;
    }
    if (self.newsstandTextField.text.withinOptWet) {
        [SobAlertView maxRaiseDigitalSourcesInnerQuarter:ShapeWinNet.daySeeWaistUse.cursorDry message:ShapeWinNet.daySeeWaistUse.funnelNextHostFindAppended completion:nil];
        return;
    }
    NSString *utilitiesCode = self.whoRedDigitTextField.fileBigHandPub;
    NSString *tiedSockSex = self.whoRedDigitTextField.tempFooterMusicianTriggersExpand;
    if ([self.sumHellmanAir respondsToSelector:@selector(includesFixRangeUnifyMeanDispatchWasRotation:code:dropCode:completion:)]) {
        [BitOwnPathView tapsWatchTimeWindow];
        [self.sumHellmanAir includesFixRangeUnifyMeanDispatchWasRotation:tiedSockSex code:self.newsstandTextField.text dropCode:utilitiesCode completion:^(id object) {
            [BitOwnPathView preventedHigherClinicalWindowYearWindow];
        }];
    }
}


- (void)lossyRangeMuteHandoverCutSafariAction {
    GroupedCollectViewController *modeGrouping = [GroupedCollectViewController new];
    modeGrouping.retryManSpa = @(YES);
    modeGrouping.sumHellmanAir = self.sumHellmanAir;
    [modeGrouping setBinaryAppearsInstallQueryEntries:^(BOOL result) {
        self.commonSchemeLabel.bezelOffCutFor = result;
    }];
    [self.navigationController pushViewController:modeGrouping animated:NO];
}

- (void)dealloc {
    [self.unloadSunButton insertedIncludingDecipherFalloffCall];
}
@end
