







#import <Foundation/Foundation.h>

@protocol MQTTTransportDelegate;



@protocol MQTTTransport <NSObject>



 typedef NS_ENUM(NSInteger, MQTTTransportState) {
     
     

     MQTTTransportCreated = 0,
     
     

     MQTTTransportOpening,
     
     

     MQTTTransportOpen,
     
     

     MQTTTransportClosing,
     
     

     MQTTTransportClosed
 };



@property (strong, nonatomic, nonnull) dispatch_queue_t queue;



@property (strong, nonatomic, nonnull) NSString *streamSSLLevel;



@property (strong, nonatomic, nonnull) NSString *host;



@property (nonatomic) UInt32 port;



@property (weak, nonatomic) _Nullable id<MQTTTransportDelegate> delegate;



@property (nonatomic) MQTTTransportState state;



- (void)open;



- (BOOL)send:(nonnull NSData *)data;



- (void)close;

@end



@protocol MQTTTransportDelegate <NSObject>



 - (void)mqttTransport:(nonnull id<MQTTTransport>)mqttTransport didReceiveMessage:(nonnull NSData *)message;

@optional



- (void)mqttTransportDidOpen:(_Nonnull id<MQTTTransport>)mqttTransport;



- (void)mqttTransport:(_Nonnull id<MQTTTransport>)mqttTransport didFailWithError:(nullable NSError *)error;



- (void)mqttTransportDidClose:(_Nonnull id<MQTTTransport>)mqttTransport;

@end

@interface MQTTTransport : NSObject <MQTTTransport>
@end

