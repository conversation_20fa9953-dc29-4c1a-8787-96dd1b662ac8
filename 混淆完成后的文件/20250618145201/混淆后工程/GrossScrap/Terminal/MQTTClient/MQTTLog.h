







@import Foundation;

#ifdef LUMBERJACK

#define LOG_LEVEL_DEF ddLogLevel
#import <CocoaLumberjack/CocoaLumberjack.h>

#else 


typedef NS_OPTIONS(NSUInteger, DDLogFlag){
    

    DDLogFlagError      = (1 << 0),

    

    DDLogFlagWarning    = (1 << 1),

    

    DDLogFlagInfo       = (1 << 2),

    

    DDLogFlagDebug      = (1 << 3),

    

    DDLogFlagVerbose    = (1 << 4)
};


typedef NS_ENUM(NSUInteger, DDLogLevel){
DDLogLevelOff       = 0,



DDLogLevelError     = (DDLogFlagError),



DDLogLevelWarning   = (DDLogLevelError   | DDLogFlagWarning),



DDLogLevelInfo      = (DDLogLevelWarning | DDLogFlagInfo),



DDLogLevelDebug     = (DDLogLevelInfo    | DDLogFlagDebug),



DDLogLevelVerbose   = (DDLogLevelDebug   | DDLogFlagVerbose),



DDLogLevelAll       = NSUIntegerMax
};

#ifdef DEBUG

#define DDLogVerbose if (ddLogLevel & DDLogFlagVerbose) NSLog
#define DDLogDebug if (ddLogLevel & DDLogFlagDebug) NSLog
#define DDLogWarn if (ddLogLevel & DDLogFlagWarning) NSLog
#define DDLogInfo if (ddLogLevel & DDLogFlagInfo) NSLog
#define DDLogError if (ddLogLevel & DDLogFlagError) NSLog

#else

#define DDLogVerbose(...)
#define DDLogDebug(...)
#define DDLogWarn(...)
#define DDLogInfo(...)
#define DDLogError(...)

#endif 

#endif 


extern DDLogLevel ddLogLevel;



@interface MQTTLog: NSObject



+ (void)setLogLevel:(DDLogLevel)logLevel;

@end
