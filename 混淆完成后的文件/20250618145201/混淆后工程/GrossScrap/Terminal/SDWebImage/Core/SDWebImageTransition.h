


#import "SDWebImageCompat.h"

#if SD_UIKIT || SD_MAC
#import "SDImageCache.h"

#if SD_UIKIT
typedef UIViewAnimationOptions SDWebImageAnimationOptions;
#else
typedef NS_OPTIONS(NSUInteger, SDWebImageAnimationOptions) {
    SDWebImageAnimationOptionAllowsImplicitAnimation   = 1 << 0, 
    
    SDWebImageAnimationOptionCurveEaseInOut            = 0 << 16, 
    SDWebImageAnimationOptionCurveEaseIn               = 1 << 16,
    SDWebImageAnimationOptionCurveEaseOut              = 2 << 16,
    SDWebImageAnimationOptionCurveLinear               = 3 << 16,
    
    SDWebImageAnimationOptionTransitionNone            = 0 << 20, 
    SDWebImageAnimationOptionTransitionFlipFromLeft    = 1 << 20,
    SDWebImageAnimationOptionTransitionFlipFromRight   = 2 << 20,
    SDWebImageAnimationOptionTransitionCurlUp          = 3 << 20,
    SDWebImageAnimationOptionTransitionCurlDown        = 4 << 20,
    SDWebImageAnimationOptionTransitionCrossDissolve   = 5 << 20,
    SDWebImageAnimationOptionTransitionFlipFromTop     = 6 << 20,
    SDWebImageAnimationOptionTransitionFlipFromBottom  = 7 << 20,
};
#endif

typedef void (^SDWebImageTransitionPreparesBlock)(__kindof UIView * _Nonnull view, UIImage * _Nullable image, NSData * _Nullable imageData, SDImageCacheType cacheType, NSURL * _Nullable imageURL);
typedef void (^SDWebImageTransitionAnimationsBlock)(__kindof UIView * _Nonnull view, UIImage * _Nullable image);
typedef void (^SDWebImageTransitionCompletionBlock)(BOOL finished);



@interface SDWebImageTransition : NSObject



@property (nonatomic, assign) BOOL avoidAutoSetImage;


@property (nonatomic, assign) NSTimeInterval duration;


@property (nonatomic, strong, nullable) CAMediaTimingFunction *timingFunction API_UNAVAILABLE(ios, tvos, watchos) API_DEPRECATED("Use SDWebImageAnimationOptions instead, or grab NSAnimationContext.currentContext and modify the timingFunction", macos(10.10, 10.10));


@property (nonatomic, assign) SDWebImageAnimationOptions animationOptions;


@property (nonatomic, copy, nullable) SDWebImageTransitionPreparesBlock prepares;


@property (nonatomic, copy, nullable) SDWebImageTransitionAnimationsBlock animations;


@property (nonatomic, copy, nullable) SDWebImageTransitionCompletionBlock completion;

@end



@interface SDWebImageTransition (Conveniences)


@property (nonatomic, class, nonnull, readonly) SDWebImageTransition *fadeTransition;

@property (nonatomic, class, nonnull, readonly) SDWebImageTransition *flipFromLeftTransition;

@property (nonatomic, class, nonnull, readonly) SDWebImageTransition *flipFromRightTransition;

@property (nonatomic, class, nonnull, readonly) SDWebImageTransition *flipFromTopTransition;

@property (nonatomic, class, nonnull, readonly) SDWebImageTransition *flipFromBottomTransition;

@property (nonatomic, class, nonnull, readonly) SDWebImageTransition *curlUpTransition;

@property (nonatomic, class, nonnull, readonly) SDWebImageTransition *curlDownTransition;



+ (nonnull instancetype)fadeTransitionWithDuration:(NSTimeInterval)duration NS_SWIFT_NAME(fade(duration:));



+ (nonnull instancetype)flipFromLeftTransitionWithDuration:(NSTimeInterval)duration NS_SWIFT_NAME(flipFromLeft(duration:));



+ (nonnull instancetype)flipFromRightTransitionWithDuration:(NSTimeInterval)duration NS_SWIFT_NAME(flipFromRight(duration:));



+ (nonnull instancetype)flipFromTopTransitionWithDuration:(NSTimeInterval)duration NS_SWIFT_NAME(flipFromTop(duration:));



+ (nonnull instancetype)flipFromBottomTransitionWithDuration:(NSTimeInterval)duration NS_SWIFT_NAME(flipFromBottom(duration:));



+ (nonnull instancetype)curlUpTransitionWithDuration:(NSTimeInterval)duration NS_SWIFT_NAME(curlUp(duration:));



+ (nonnull instancetype)curlDownTransitionWithDuration:(NSTimeInterval)duration NS_SWIFT_NAME(curlDown(duration:));

@end

#endif
