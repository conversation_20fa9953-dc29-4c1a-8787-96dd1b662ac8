


#import "SDWebImageCompat.h"
#import "SDWebImageDefine.h"
#import "SDWebImageManager.h"
#import "SDWebImageTransition.h"
#import "SDWebImageIndicator.h"
#import "UIView+WebCacheOperation.h"
#import "UIView+WebCacheState.h"



FOUNDATION_EXPORT const int64_t SDWebImageProgressUnitCountUnknown; 


typedef void(^SDSetImageBlock)(UIImage * _Nullable image, NSData * _Nullable imageData, SDImageCacheType cacheType, NSURL * _Nullable imageURL);



@interface UIView (WebCache)



@property (nonatomic, strong, readonly, nullable) NSString *sd_latestOperationKey;





@property (nonatomic, strong, readonly, nullable) NSURL *sd_imageURL;



@property (nonatomic, strong, null_resettable) NSProgress *sd_imageProgress;



- (nullable id<SDWebImageOperation>)sd_internalSetImageWithURL:(nullable NSURL *)url
                                              placeholderImage:(nullable UIImage *)placeholder
                                                       options:(SDWebImageOptions)options
                                                       context:(nullable SDWebImageContext *)context
                                                 setImageBlock:(nullable SDSetImageBlock)setImageBlock
                                                      progress:(nullable SDImageLoaderProgressBlock)progressBlock
                                                     completed:(nullable SDInternalCompletionBlock)completedBlock;



- (void)sd_cancelLatestImageLoad;



- (void)sd_cancelCurrentImageLoad API_DEPRECATED_WITH_REPLACEMENT("sd_cancelLatestImageLoad", macos(10.10, 10.10), ios(8.0, 8.0), tvos(9.0, 9.0), watchos(2.0, 2.0));

#if SD_UIKIT || SD_MAC





@property (nonatomic, strong, nullable) SDWebImageTransition *sd_imageTransition;





@property (nonatomic, strong, nullable) id<SDWebImageIndicator> sd_imageIndicator;

#endif

@end
