



#import "SDWebImageCompat.h"


typedef NS_ENUM(NSUInteger, SDCallbackPolicy) {
    
    SDCallbackPolicySafeExecute = 0,
    
    SDCallbackPolicyDispatch = 1,
    
    SDCallbackPolicyInvoke = 2,
    
    SDCallbackPolicySafeAsyncMainThread = 3,
};



@interface SDCallbackQueue : NSObject



@property (nonnull, class, readonly) SDCallbackQueue *mainQueue;



@property (nonnull, class, readonly) SDCallbackQueue *currentQueue;



@property (nonnull, class, readonly) SDCallbackQueue *globalQueue;


@property (nonatomic, assign, readwrite) SDCallbackPolicy policy;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;


- (nonnull instancetype)initWithDispatchQueue:(nonnull dispatch_queue_t)queue NS_DESIGNATED_INITIALIZER;





- (void)sync:(nonnull dispatch_block_t)block API_DEPRECATED("No longer use, may cause deadlock when misused", macos(10.10, 10.10), ios(8.0, 8.0), tvos(9.0, 9.0), watchos(2.0, 2.0));;



- (void)async:(nonnull dispatch_block_t)block;

@end
