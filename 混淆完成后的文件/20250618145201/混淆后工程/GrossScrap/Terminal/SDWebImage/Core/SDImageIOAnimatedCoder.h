


#import <Foundation/Foundation.h>
#import "SDImageCoder.h"



@interface SDImageIOAnimatedCoder : NSObject <SDProgressiveImageCoder, SDAnimatedImageCoder>




@property (class, readonly) SDImageFormat imageFormat;


@property (class, readonly, nonnull) NSString *imageUTType;


@property (class, readonly, nonnull) NSString *animatedImageUTType;


@property (class, readonly, nonnull) NSString *dictionaryProperty;


@property (class, readonly, nonnull) NSString *unclampedDelayTimeProperty;


@property (class, readonly, nonnull) NSString *delayTimeProperty;


@property (class, readonly, nonnull) NSString *loopCountProperty;


@property (class, readonly) NSUInteger defaultLoopCount;

@end
