






#import "OriginsFunnelSignalingReadyCaps.h"
#import "HandledEggConfig.h"
#import "NSData+PinThat.h"

@interface OriginsFunnelSignalingReadyCaps() {
    NSURL *touchesPrivacy;
    BOOL genericDirectionReturnsLoopsFunkLexicon;
    NSInteger _capSalt;
    NSDateFormatter *shadowPackWide;
    BOOL _windowSawSharingWrapperPressed;
}

@end

@implementation OriginsFunnelSignalingReadyCaps

- (instancetype)init
{
    self = [super init];
    if (self) {
        genericDirectionReturnsLoopsFunkLexicon = NO;
        _capSalt = 7;
        _windowSawSharingWrapperPressed = NO;

        shadowPackWide = [[NSDateFormatter alloc] init];
        shadowPackWide.dateFormat = renderFrame.seeSubFrenchIgnoreSwitchSettingSmaller;

        if (!touchesPrivacy) {
            NSURL *baseURL = [[NSFileManager defaultManager] URLsForDirectory:NSCachesDirectory inDomains:NSUserDomainMask].firstObject;
            touchesPrivacy = [baseURL URLByAppendingPathComponent:NSStringFromClass(self.class) isDirectory:YES];
        }
    }
    return self;
}


- (instancetype)initFailCapAllPass:(NSURL *)zb_url
{
    self = [super init];
    if (self) {
        self.bondBadManTag = zb_url;
    }
    return self;
}


- (NSString *)expires:(ShakeLevel)zb_level sumTap:(NSString *)sumTap squashMin:(NSString *)squashMin threads:(NSString *)threads pashtoStair:(NSString *)pashtoStair funPass:(NSUInteger)funPass creditsDog:(id)creditsDog {

    NSString *time = [self fixingDate:renderFrame.yardChooseRevisionOptErrorObject timeZone:nil];

    NSString *color = [self teethFaxLevel:zb_level];

    NSString *line = [NSString stringWithFormat:@"%lu", (unsigned long)funPass];

    NSString *formattedString = [NSString stringWithFormat:renderFrame.invertResumeUnsafeRecentMuteUnlimited,color,time,pashtoStair,line,sumTap];

    if (![formattedString isEqualToString:@""]) {
        NSURL *beenGetFile = [self dogAskUnitTalkNotifies];
        [self tempRaiseFile:formattedString fileURL:beenGetFile];
    }

    return formattedString;
}



- (BOOL)tempRaiseFile:(NSString *)zb_str {
    return [self tempRaiseFile:zb_str fileURL:touchesPrivacy];
}

- (BOOL)tempRaiseFile:(NSString *)zb_str fileURL:(NSURL *)fileURL {
    if (!fileURL) {
        return NO;
    }

    NSString *line = zb_str;

    
    if (_windowSawSharingWrapperPressed) {
        NSData *fillBlobData = [line dataUsingEncoding:NSUTF8StringEncoding];
        if (!fillBlobData) {
            return NO;
        }

        NSData *reductionData = [fillBlobData calciumSoundAssemblyHisTooPlus];
        if (!reductionData) {
            
            return NO;
        }

        
        line = [reductionData base64EncodedStringWithOptions:0];
    }

    
    line = [line stringByAppendingString:@"\n"];
    NSData *data = [line dataUsingEncoding:NSUTF8StringEncoding];
    if (!data) {
        return NO;
    }

    return [self makerMen:data acute:fileURL];
}

- (BOOL)makerMen:(NSData *)zb_data acute:(NSURL *)zb_url {
    __block BOOL anglePager = NO;
    NSFileCoordinator *zb_coordinator = [[NSFileCoordinator alloc] initWithFilePresenter:nil];
    NSError *hexSound = nil;
    [zb_coordinator coordinateWritingItemAtURL:zb_url options:0 error:&hexSound byAccessor:^(NSURL * _Nonnull zb_newURL) {

        NSError *hexSound = nil;

        if (![[NSFileManager defaultManager] fileExistsAtPath:zb_url.path]) {

            NSURL *ascenderFailBuffersParsingDelete = zb_url.URLByDeletingLastPathComponent;
            if (![[NSFileManager defaultManager] fileExistsAtPath:ascenderFailBuffersParsingDelete.path]) {
                [[NSFileManager defaultManager] createDirectoryAtURL:ascenderFailBuffersParsingDelete withIntermediateDirectories:YES attributes:nil error:&hexSound];
            }

            [[NSFileManager defaultManager] createFileAtPath:zb_url.path contents:nil attributes:nil];
        }

        NSFileHandle *zb_fileHandle = [NSFileHandle fileHandleForWritingToURL:zb_url error:&hexSound];
        [zb_fileHandle seekToEndOfFile];
        [zb_fileHandle writeData:zb_data];
        if (genericDirectionReturnsLoopsFunkLexicon) {
            [zb_fileHandle synchronizeFile];
        }
        [zb_fileHandle closeFile];

        if (hexSound) {
            
        }else {
            anglePager = YES;
        }

    }];

    if (hexSound) {
        
    }

    return anglePager;
}

- (NSURL *)bondBadManTag {
    return touchesPrivacy;
}

- (void)setBondBadManTag:(NSURL *)bondBadManTag {
    touchesPrivacy = bondBadManTag;
}

- (BOOL)anyUnsavedCombineBeatScopeExtending {
    return genericDirectionReturnsLoopsFunkLexicon;
}

- (void)setAnyUnsavedCombineBeatScopeExtending:(BOOL)anyUnsavedCombineBeatScopeExtending {
    genericDirectionReturnsLoopsFunkLexicon = anyUnsavedCombineBeatScopeExtending;
}




- (NSInteger)capSalt {
    return _capSalt;
}

- (void)setCapSalt:(NSInteger)capSalt {
    _capSalt = capSalt;
}

- (BOOL)windowSawSharingWrapperPressed {
    return _windowSawSharingWrapperPressed;
}

- (void)setWindowSawSharingWrapperPressed:(BOOL)windowSawSharingWrapperPressed {
    _windowSawSharingWrapperPressed = windowSawSharingWrapperPressed;
}



- (NSURL *)dogAskUnitTalkNotifies {
    NSString *englishPop = [shadowPackWide stringFromDate:[NSDate date]];
    return [touchesPrivacy URLByAppendingPathComponent:englishPop];
}

- (NSArray<NSURL *> *)croppingSon {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;

    if (![fileManager fileExistsAtPath:touchesPrivacy.path]) {
        return @[];
    }

    NSArray *thumbDue = [fileManager contentsOfDirectoryAtURL:touchesPrivacy
                                includingPropertiesForKeys:@[NSURLCreationDateKey]
                                                   options:NSDirectoryEnumerationSkipsHiddenFiles
                                                     error:&error];
    if (error) {
        
        return @[];
    }

    return [thumbDue sortedArrayUsingComparator:^NSComparisonResult(NSURL *url1, NSURL *url2) {
        NSDate *date1, *date2;
        [url1 getResourceValue:&date1 forKey:NSURLCreationDateKey error:nil];
        [url2 getResourceValue:&date2 forKey:NSURLCreationDateKey error:nil];
        return [date2 compare:date1]; 
    }];
}

- (NSString *)catFlowFile:(NSURL *)fileURL {
    NSError *error;

    
    NSString *tabIronOver = [NSString stringWithContentsOfURL:fileURL encoding:NSUTF8StringEncoding error:&error];
    if (error || !tabIronOver) {
        
        return @"";
    }

    
    if (_windowSawSharingWrapperPressed) {
        NSMutableString *allContent = [NSMutableString string];

        
        NSArray *lines = [tabIronOver componentsSeparatedByString:@"\n"];

        for (NSString *line in lines) {
            
            if (line.length == 0) {
                continue;
            }

            
            NSData *reductionData = [[NSData alloc] initWithBase64EncodedString:line options:0];
            if (!reductionData) {
                
                continue;
            }

            
            NSData *wrapGivenData = [reductionData haveOuterSharingAskBookStartupLogo];
            if (!wrapGivenData) {
                
                continue;
            }

            
            NSString *edgeMalayalam = [[NSString alloc] initWithData:wrapGivenData encoding:NSUTF8StringEncoding];
            if (edgeMalayalam) {
                [allContent appendString:edgeMalayalam];
                [allContent appendString:@"\n"];
            } else {
                
            }
        }

        return allContent;
    } else {
        
        return tabIronOver;
    }
}

- (NSString *)putPulseCup {
    NSArray *thumbDue = [self croppingSon];
    NSMutableString *allContent = [NSMutableString string];

    for (NSURL *fileURL in thumbDue) {
        NSString *content = [self catFlowFile:fileURL];
        if (content.length > 0) {
            [allContent appendFormat:renderFrame.nineSpanishUndefinedEarUnwindChangeCricket, fileURL.lastPathComponent];
            [allContent appendString:content];
            [allContent appendString:@"\n"];
        }
    }

    return allContent;
}

- (NSString *)imageFunkClean {
    NSArray *thumbDue = [self croppingSon];
    NSMutableString *allContent = [NSMutableString string];

    for (NSURL *fileURL in thumbDue) {
        
        NSError *error;
        NSString *tabIronOver = [NSString stringWithContentsOfURL:fileURL encoding:NSUTF8StringEncoding error:&error];
        if (error || !tabIronOver) {
            
            continue;
        }

        if (tabIronOver.length > 0) {
            [allContent appendFormat:renderFrame.nineSpanishUndefinedEarUnwindChangeCricket, fileURL.lastPathComponent];
            [allContent appendString:tabIronOver];
            [allContent appendString:@"\n"];
        }
    }

    return allContent;
}

- (NSString *)rateInhalerDate:(NSDate *)date {
    if (!date) {
        return @"";
    }

    NSString *englishPop = [shadowPackWide stringFromDate:date];
    NSURL *fileURL = [touchesPrivacy URLByAppendingPathComponent:englishPop];

    return [self catFlowFile:fileURL];
}

- (NSArray<NSDate *> *)racePageAnd {
    NSMutableArray *dates = [NSMutableArray array];
    NSArray *thumbDue = [self croppingSon];

    for (NSURL *fileURL in thumbDue) {
        NSString *fileName = fileURL.lastPathComponent;
        
        NSDate *date = [shadowPackWide dateFromString:fileName];
        if (date) {
            [dates addObject:date];
        }
    }

    
    [dates sortUsingComparator:^NSComparisonResult(NSDate *date1, NSDate *date2) {
        return [date2 compare:date1];
    }];

    return dates;
}

- (void)rowBigSlopePen {
    if (_capSalt <= 0) return;

    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSArray *thumbDue = [self croppingSon];
    NSDate *cutoffDate = [NSDate dateWithTimeIntervalSinceNow:-_capSalt * 24 * 60 * 60];

    for (NSURL *fileURL in thumbDue) {
        NSDate *creationDate;
        [fileURL getResourceValue:&creationDate forKey:NSURLCreationDateKey error:nil];

        if (creationDate && [creationDate compare:cutoffDate] == NSOrderedAscending) {
            NSError *error;
            [fileManager removeItemAtURL:fileURL error:&error];
            if (error) {
                
            }
        }
    }
}

@end
