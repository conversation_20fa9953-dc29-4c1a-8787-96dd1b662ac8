

#import "CountViewController.h"
#import "Adobe.h"
#import "SettingEncodingsStringMiterRefreshedSubgroup.h"
#import "OriginsFunnelSignalingReadyCaps.h"
#import "HandledEggConfig.h"

@interface CountViewController ()
@property (nonatomic, strong) UITextView *textView;
@property (nonatomic, strong) SettingEncodingsStringMiterRefreshedSubgroup *postScannedSequencesCacheDownloads;
@property (nonatomic, strong) OriginsFunnelSignalingReadyCaps *sinEightHisWarningAmerican;
@property (nonatomic, strong) NSDate *spaArrayDate; 
@end

static OriginsFunnelSignalingReadyCaps *ageNapPenHoursSignalLog = nil;
static SettingEncodingsStringMiterRefreshedSubgroup *kilogramsFinalRhythmNineDocumentsDropped = nil;

@implementation CountViewController

+ (void)wetDenySnap {
    
    [Adobe adjustedInverseMoreExtrasLoadingStoryline];

    kilogramsFinalRhythmNineDocumentsDropped = [[SettingEncodingsStringMiterRefreshedSubgroup alloc] init];
    kilogramsFinalRhythmNineDocumentsDropped.detachLevel = PreparingGainTwoEarMagic;

    [Adobe gaussianCoachedMusicianSpeakersCanonical:kilogramsFinalRhythmNineDocumentsDropped];

    ageNapPenHoursSignalLog = [[OriginsFunnelSignalingReadyCaps alloc] init];
    ageNapPenHoursSignalLog.detachLevel = FillRealGuide;
    ageNapPenHoursSignalLog.capSalt = 7;
    ageNapPenHoursSignalLog.windowSawSharingWrapperPressed = YES;
    [Adobe gaussianCoachedMusicianSpeakersCanonical:ageNapPenHoursSignalLog];

    [ageNapPenHoursSignalLog rowBigSlopePen];
}

+ (OriginsFunnelSignalingReadyCaps *)availSmallVersionsAddHomeJump {
    return ageNapPenHoursSignalLog;
}
+ (SettingEncodingsStringMiterRefreshedSubgroup *)alignmentLinerUsageMidActivatedGet {
    return kilogramsFinalRhythmNineDocumentsDropped;
}

- (SettingEncodingsStringMiterRefreshedSubgroup *)postScannedSequencesCacheDownloads {
    return kilogramsFinalRhythmNineDocumentsDropped;
}

+ (void)showFromViewController:(UIViewController *)parentVC {
    CountViewController *coast = [[CountViewController alloc] init];
    UINavigationController *arm = [[UINavigationController alloc] initWithRootViewController:coast];
    arm.modalPresentationStyle = UIModalPresentationFullScreen;
    [parentVC presentViewController:arm animated:YES completion:nil];
}

- (void)viewDidLoad {
    [super viewDidLoad];

    self.title = renderFrame.leapEvaluateArmNanogramsSearchDecline;
    self.view.backgroundColor = [UIColor systemBackgroundColor];

    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc]
                                            initWithBarButtonSystemItem:UIBarButtonSystemItemCancel
                                            target:self
                                            action:@selector(digitAction)];

    self.navigationItem.rightBarButtonItems = @[
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemRefresh
                                                      target:self
                                                      action:@selector(fixLastAction)],
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemAction
                                                      target:self
                                                      action:@selector(clangAction)],
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemBookmarks
                                                      target:self
                                                      action:@selector(binFlemishAction)]
    ];
    self.navigationController.navigationBar.layoutMargins = UIEdgeInsetsMake(0, 0, 0, -10);

    _textView = [[UITextView alloc] init];
    _textView.font = [UIFont systemFontOfSize:11];
    _textView.editable = NO;
    _textView.backgroundColor = [UIColor systemBackgroundColor];
    _textView.textColor = [UIColor labelColor];
    _textView.translatesAutoresizingMaskIntoConstraints = NO;
    _textView.showsVerticalScrollIndicator = YES;
    _textView.showsHorizontalScrollIndicator = YES;
    _textView.alwaysBounceVertical = YES;
    
    _textView.scrollEnabled = YES;
    [self.view addSubview:_textView];

    [NSLayoutConstraint activateConstraints:@[
        [_textView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [_textView.leadingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.leadingAnchor constant:8],
        [_textView.trailingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.trailingAnchor constant:-8],
        [_textView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];

    self.sinEightHisWarningAmerican = [CountViewController availSmallVersionsAddHomeJump];

    [self slopeSpa];
}

- (void)slopeSpa {
    if (!self.sinEightHisWarningAmerican) {
        _textView.text = renderFrame.plusWaitFloorDebuggingSolveCan;
        return;
    }

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSString *logs;
        if (self.spaArrayDate) {
            logs = [self.sinEightHisWarningAmerican rateInhalerDate:self.spaArrayDate];
        } else {
            logs = [self.sinEightHisWarningAmerican putPulseCup];
        }

        dispatch_async(dispatch_get_main_queue(), ^{
            if (logs.length > 0) {
                self.textView.text = logs;
                
                [self.textView scrollRangeToVisible:NSMakeRange(logs.length - 1, 1)];
            } else {
                self.textView.text = renderFrame.handshakeNoticeBuiltTexturedPasswordsRoute;
            }

            [self orderAirHer];
        });
    });
}

- (void)digitAction {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)fixLastAction {
    [self slopeSpa];
}

- (void)orderAirHer {
    if (self.spaArrayDate) {
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.dateFormat = renderFrame.flatBundleGatherLetterAlarmCostAbout;
        NSString *englishPop = [formatter stringFromDate:self.spaArrayDate];

        NSCalendar *calendar = [NSCalendar currentCalendar];
        if ([calendar isDateInToday:self.spaArrayDate]) {
            self.title = renderFrame.prologPossibleExistentAssumePointersCurrently;
        } else if ([calendar isDateInYesterday:self.spaArrayDate]) {
            self.title = renderFrame.minRealmSumSliceFrontNothingTime;
        } else {
            self.title = englishPop;
        }
    } else {
        self.title = renderFrame.carPromotionBoundaryUsabilityManPashto;
    }
}

- (void)binFlemishAction {
    if (!self.sinEightHisWarningAmerican) {
        return;
    }

    NSArray<NSDate *> *availableDates = [self.sinEightHisWarningAmerican racePageAnd];
    if (availableDates.count == 0) {
        UIAlertController *daily = [UIAlertController alertControllerWithTitle:renderFrame.fillerInsideGenderTapGaspPan
                                                                       message:renderFrame.handshakeNoticeBuiltTexturedPasswordsRoute
                                                                preferredStyle:UIAlertControllerStyleAlert];
        [daily addAction:[UIAlertAction actionWithTitle:renderFrame.rollSessionTreeMetricsSafeMan style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:daily animated:YES completion:nil];
        return;
    }

    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:renderFrame.locationGopherRangeSeeSpanishGarbageSob
                                                                         message:nil
                                                                  preferredStyle:UIAlertControllerStyleActionSheet];

    [actionSheet addAction:[UIAlertAction actionWithTitle:renderFrame.carPromotionBoundaryUsabilityManPashto
                                                    style:UIAlertActionStyleDefault
                                                  handler:^(UIAlertAction *action) {
        self.spaArrayDate = nil;
        [self slopeSpa];
    }]];

    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = renderFrame.flatBundleGatherLetterAlarmCostAbout;

    NSCalendar *calendar = [NSCalendar currentCalendar];

    for (NSDate *date in availableDates) {
        NSString *title;
        if ([calendar isDateInToday:date]) {
            title = renderFrame.prologPossibleExistentAssumePointersCurrently;
        } else if ([calendar isDateInYesterday:date]) {
            title = renderFrame.minRealmSumSliceFrontNothingTime;
        } else {
            title = [formatter stringFromDate:date];
        }

        [actionSheet addAction:[UIAlertAction actionWithTitle:title
                                                        style:UIAlertActionStyleDefault
                                                      handler:^(UIAlertAction *action) {
            self.spaArrayDate = date;
            [self slopeSpa];
        }]];
    }

    [actionSheet addAction:[UIAlertAction actionWithTitle:renderFrame.selfKitDitherDomainPeerChat style:UIAlertActionStyleCancel handler:nil]];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        actionSheet.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:actionSheet animated:YES completion:nil];
}

- (void)clangAction {
    if (!self.sinEightHisWarningAmerican) {
        return;
    }

    NSArray *thumbDue = [self.sinEightHisWarningAmerican croppingSon];
    if (thumbDue.count == 0) {
        UIAlertController *daily = [UIAlertController alertControllerWithTitle:renderFrame.fillerInsideGenderTapGaspPan
                                                                       message:renderFrame.ratingsDeletingViolationCompositeSedentaryPrevented
                                                                preferredStyle:UIAlertControllerStyleAlert];
        [daily addAction:[UIAlertAction actionWithTitle:renderFrame.rollSessionTreeMetricsSafeMan style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:daily animated:YES completion:nil];
        return;
    }

    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:renderFrame.panSideSchoolPopEnteredFloaterTeaspoons
                                                                         message:nil
                                                                  preferredStyle:UIAlertControllerStyleActionSheet];

    [actionSheet addAction:[UIAlertAction actionWithTitle:renderFrame.unpluggedSetupShiftOptionQuarterBezelChapter
                                                    style:UIAlertActionStyleDefault
                                                  handler:^(UIAlertAction *action) {
        [self hostYouBlink];
    }]];

    for (NSURL *fileURL in thumbDue) {
        NSString *fileName = fileURL.lastPathComponent;
        [actionSheet addAction:[UIAlertAction actionWithTitle:[NSString stringWithFormat:renderFrame.expansionPanoramasBusyFactJumpCommandsAssertion, fileName]
                                                        style:UIAlertActionStyleDefault
                                                      handler:^(UIAlertAction *action) {
            [self dropHalfFile:fileURL];
        }]];
    }

    [actionSheet addAction:[UIAlertAction actionWithTitle:renderFrame.selfKitDitherDomainPeerChat style:UIAlertActionStyleCancel handler:nil]];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        actionSheet.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:actionSheet animated:YES completion:nil];
}

- (void)hostYouBlink {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        
        NSString *onlyOut = [self.sinEightHisWarningAmerican imageFunkClean];

        dispatch_async(dispatch_get_main_queue(), ^{
            if (onlyOut.length > 0) {
                UIActivityViewController *retRateMid = [[UIActivityViewController alloc]
                                                       initWithActivityItems:@[onlyOut]
                                                       applicationActivities:nil];

                if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
                    retRateMid.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
                }

                [self presentViewController:retRateMid animated:YES completion:nil];
            }
        });
    });
}

- (void)dropHalfFile:(NSURL *)fileURL {
    UIActivityViewController *retRateMid = [[UIActivityViewController alloc]
                                           initWithActivityItems:@[fileURL]
                                           applicationActivities:nil];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        retRateMid.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:retRateMid animated:YES completion:nil];
}

@end
