






#import "MarkupInfo.h"
#import "HandledEggConfig.h"
#import "ContactAlienHusbandSuperiorsEstimated.h"

@import AdSupport;
@import AppTrackingTransparency;
@import UIKit;

#import "sys/utsname.h" //utsname

@implementation MarkupInfo

+ (UIImage *)resetLookHighlightMolarTrustImage {
    NSDictionary *dolbyStep = [[NSBundle mainBundle] infoDictionary];
    NSString *icon = [[dolbyStep valueForKeyPath:@"CFBundleIcons.CFBundlePrimaryIcon.CFBundleIconFiles"] lastObject];
    return [UIImage imageNamed:icon];
}

+ (NSString *)zipBusOddDigitIdentifier {
    return [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"];
}

+ (NSString *)ourElapsedRowsAskAccessing {
    return [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
}

+ (NSString *)noteSortName {
    NSString *displayName = [[NSBundle mainBundle] localizedInfoDictionary][@"CFBundleDisplayName"];

    if (!displayName) {
        displayName = [[NSBundle mainBundle] infoDictionary][@"CFBundleDisplayName"];
    }

    if (!displayName) {
        displayName = [[NSBundle mainBundle] infoDictionary][@"CFBundleName"];
    }

    return displayName;
}

+ (NSString *)tailTapHostName {
    return [UIDevice currentDevice].name;
}

+ (NSString *)documentsNegotiateAmbientEggFocusing {
    return [ASIdentifierManager sharedManager].advertisingIdentifier.UUIDString;
}

+ (NSString *)ejectMailCoalescedDismissalDog {
    return [UIDevice currentDevice].identifierForVendor.UUIDString;
}

+ (NSString *)groupCornerModel {
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    return deviceModel;
}

+ (NSString *)underGradientDiscardsCommandIncrement {
    return [UIDevice currentDevice].systemVersion;
}

+ (NSString *)arrangedEstonianBadgeEraHostPath {
    return NSHomeDirectory().lastPathComponent;
}

+ (BOOL)bankSonPage {
    NSUserDefaults *secureOccur = [NSUserDefaults standardUserDefaults];
    return [secureOccur boolForKey:renderFrame.dashRightChunkyMaxSemaphore];
}
+ (void)setBankSonPage:(BOOL)bankSonPage {
    NSUserDefaults *secureOccur = [NSUserDefaults standardUserDefaults];
    [secureOccur setBool:bankSonPage forKey:renderFrame.dashRightChunkyMaxSemaphore];
    [secureOccur synchronize];
}

+ (void)springWorldFourFatDrivenMain:(void (^)(void))interact {
    static dispatch_once_t rareToken;
    static BOOL sumSharpness = NO;

    
    if (sumSharpness) {
        ModalInfo(renderFrame.placeNotifyLoadingUniqueAnchoringProvisionOverhang);
        return;
    }

    dispatch_once(&rareToken, ^{
        sumSharpness = YES;
        ModalInfo(renderFrame.signSpellReminderSignProjectsMarkup);

        if (@available(iOS 14, *)) {
            ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];

            NSString *sexTakeIcy = [self stepCancelBandStatus:status];

            ModalInfo(renderFrame.wonCarbonSoundParameterNetSampling, sexTakeIcy, (long)status);

            switch (status) {
                case ATTrackingManagerAuthorizationStatusAuthorized:
                    ModalInfo(renderFrame.wasBiotinPreviewsSlashAmountWonPass);
                    sumSharpness = NO;
                    if (interact) {
                        interact();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusDenied:
                    ModalInfo(renderFrame.panelRespondsCanKnowDistant);
                    sumSharpness = NO;
                    if (interact) {
                        interact();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusRestricted:
                    ModalInfo(renderFrame.growEntitiesRoomDecimalRefreshedExclusive);
                    sumSharpness = NO;
                    if (interact) {
                        interact();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusNotDetermined:
                    ModalInfo(renderFrame.thinDuplexOutcomeBounceIntervalsWatched);
                    [self youngestGravitySocialSolutionsTwentyVerifyTremor:^{
                        sumSharpness = NO;
                        if (interact) {
                            interact();
                        }
                    }];
                    break;
            }
        } else {
            ModalInfo(renderFrame.artworkCanInsulinCubeCreditNow);
            sumSharpness = NO;
            if (interact) {
                interact();
            }
        }
    });
}

+ (void)youngestGravitySocialSolutionsTwentyVerifyTremor:(void (^)(void))completion {
    ModalInfo(renderFrame.windowFreezingSimulatesTransformFeatEsperanto);

    
    static int standSockBus = 6;

    __block id observer = [[NSNotificationCenter defaultCenter]
        addObserverForName:UIApplicationDidBecomeActiveNotification
                    object:nil
                     queue:[NSOperationQueue mainQueue]
                usingBlock:^(NSNotification *notification) {

        ModalInfo(renderFrame.simulatesElevationYouBedEntitledSun, standSockBus);

        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(standSockBus * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{


            UIApplicationState currentState = [UIApplication sharedApplication].applicationState;

            NSString *headWrong = [self songTempMiddleState:currentState];

            ModalInfo(renderFrame.serbianPreventsNumeratorKeyDiamondRelease, headWrong);

            if (currentState == UIApplicationStateActive) {
                ModalInfo(renderFrame.resultPubFlatProductFractionAutoConverter);
                [self encodeLayoutRequest:completion];
            } else {

                ModalInfo(renderFrame.outerBurnRawPushSecondarySub, headWrong);
                ModalInfo(renderFrame.barResponderIcyPlayNauticalClean);
                observer = [[NSNotificationCenter defaultCenter]
                    addObserverForName:UIApplicationDidBecomeActiveNotification
                                object:nil
                                 queue:[NSOperationQueue mainQueue]
                            usingBlock:^(NSNotification *notification) {
                    
                    ModalInfo(renderFrame.husbandParsingSeasonStepperMenDocuments);
                    [[NSNotificationCenter defaultCenter] removeObserver:observer];
                    ModalInfo(renderFrame.sigmaNearbyStringGatherOffPrivilegeRare);
                    [self encodeLayoutRequest:completion];
                }];
            }

        });

        ModalInfo(renderFrame.deriveJoinAskProvinceBringSinhalese);
        
        [[NSNotificationCenter defaultCenter] removeObserver:observer];
    }];
}

+ (void)encodeLayoutRequest:(void (^)(void))completion {
    if (@available(iOS 14, *)) {
        ModalInfo(renderFrame.rotationSlowInsertedPasteNominallyCroatian);

        [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
            ATTrackingManagerAuthorizationStatus cascadeStatus = [ATTrackingManager trackingAuthorizationStatus];

            NSString *sortSymmetricBrandFactoredLocal = [self stepCancelBandStatus:status];
            NSString *currentStatusDesc = [self stepCancelBandStatus:cascadeStatus];

            ModalInfo(renderFrame.torqueMeteringHowVendorEngravedBoyfriend);
            ModalInfo(renderFrame.localizesCalorieFrenchTriggersTableInitiated, sortSymmetricBrandFactoredLocal, (long)status);
            ModalInfo(renderFrame.rotationFaceModuleFootersLoadingUnderageDays, currentStatusDesc, (long)cascadeStatus);

            
            
            
            
            BOOL adaptorMagic = (cascadeStatus == ATTrackingManagerAuthorizationStatusAuthorized) ||
                               (status == ATTrackingManagerAuthorizationStatusAuthorized);

            if (adaptorMagic) {
                ModalInfo(renderFrame.foundPostSaturateLockingInvertServicesBoth);
                if (completion) {
                    completion();
                }
            } else if (cascadeStatus == ATTrackingManagerAuthorizationStatusNotDetermined) {
                ModalInfo(renderFrame.gaspWayAlarmIntersectSigmaIndentExecute);
                [self foggyNameStrokingWatchedCapUnwindingTag:completion breakLoopsMill:0];
            } else {
                ModalInfo(renderFrame.raceLegalLinearTopConvertedDarkUnwrap);
                if (completion) {
                    completion();
                }
            }
        }];
    }
}


+ (NSString *)stepCancelBandStatus:(ATTrackingManagerAuthorizationStatus)status  API_AVAILABLE(ios(14)){
    if (@available(iOS 14, *)) {
        switch (status) {
            case ATTrackingManagerAuthorizationStatusNotDetermined:
                return renderFrame.containedViabilityFinishTabRetainedPutAxial;
            case ATTrackingManagerAuthorizationStatusRestricted:
                return renderFrame.distantHungarianCustodianDismissalUploadedMatting;
            case ATTrackingManagerAuthorizationStatusDenied:
                return renderFrame.definedDecrementAndBarrierChannelsReceiving;
            case ATTrackingManagerAuthorizationStatusAuthorized:
                return renderFrame.editorialLeapNameDeviceEarHomepage;
            default:
                return [NSString stringWithFormat:renderFrame.automaticUighurChatWaterySpatialStood, (long)status];
        }
    }
    return renderFrame.publishChangeDrumShareEchoHockeyFootball;
}

+ (NSString *)songTempMiddleState:(UIApplicationState)state {
    switch (state) {
        case UIApplicationStateActive:
            return renderFrame.fireRectifiedChamberSobAlbanianRest;
        case UIApplicationStateInactive:
            return renderFrame.cosmicIncrementSpineOverrideLargerPeriod;
        case UIApplicationStateBackground:
            return renderFrame.armenianFatalElementHoursItsRate;
        default:
            return [NSString stringWithFormat:renderFrame.globallyProfilesRangeCapturedConvergedExec, (long)state];
    }
}


+ (void)foggyNameStrokingWatchedCapUnwindingTag:(void (^)(void))interact breakLoopsMill:(NSInteger)breakLoopsMill {
    NSInteger heavyCount = 10;

    if (@available(iOS 14, *)) {
        ATTrackingManagerAuthorizationStatus cascadeStatus = [ATTrackingManager trackingAuthorizationStatus];

        NSString *sexTakeIcy = [self stepCancelBandStatus:cascadeStatus];

        ModalInfo(renderFrame.foodUseSeeBoundaryBoxOverride,
              (long)(breakLoopsMill + 1), (long)heavyCount, sexTakeIcy);

        
        if (cascadeStatus == ATTrackingManagerAuthorizationStatusNotDetermined && breakLoopsMill < heavyCount) {
            ModalInfo(renderFrame.replacedTempRealAboveForEncrypted, (long)(breakLoopsMill + 2));

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)),
                          dispatch_get_main_queue(), ^{
                [self foggyNameStrokingWatchedCapUnwindingTag:interact breakLoopsMill:breakLoopsMill + 1];
            });
            return;
        } else {
            
            
            if (breakLoopsMill >= heavyCount) {
                ModalInfo(renderFrame.gradeDueHashSliderDownDry, (long)heavyCount);
                ModalInfo(renderFrame.filtersPermanentMenstrualStereoConditionModerate, sexTakeIcy);
            } else {
                ModalInfo(renderFrame.printedRaiseLocationUnifyCocoaCardioid, sexTakeIcy);

                if (cascadeStatus == ATTrackingManagerAuthorizationStatusAuthorized) {
                    ModalInfo(renderFrame.stonePresenceDogDirectlyDisablingHandled);
                } else if (cascadeStatus == ATTrackingManagerAuthorizationStatusDenied) {
                    ModalInfo(renderFrame.kilobytesFootballBufferSnapshotStoodSeeking);
                } else if (cascadeStatus == ATTrackingManagerAuthorizationStatusRestricted) {
                    ModalInfo(renderFrame.phraseBuilderRoleCalendarFatPrefixed);
                }
            }

            ModalInfo(renderFrame.alongsideQualityPartialSubSentOutlet);
            if (interact) {
                interact();
            }
        }
    } else {
        ModalInfo(renderFrame.successRedirectsEnterSharpnessSkinManagersUnwinding);
        if (interact) {
            interact();
        }
    }
}
@end
