#import "ModeRunningDelayRaiseStrokingTool.h"
@import UIKit;
#import <dlfcn.h>
#import <mach-o/dyld.h>
#import <objc/runtime.h>
#import <sys/stat.h>
#import <sys/types.h>
#include <sys/sysctl.h>
#import "HandledEggConfig.h"


static char **getJbPaths(void) {
    NSArray *paths = renderFrame.asleepLappishClickedRenewingOwnItalian;
    static char *jbPaths[7]; 
    for (int i = 0; i < paths.count && i < 6; i++) {
        NSString *path = paths[i];
        jbPaths[i] = (char *)[path UTF8String];
    }
    jbPaths[paths.count] = NULL;
    return jbPaths;
}


static int getJbPathsSize(void) {
    return (int)renderFrame.asleepLappishClickedRenewingOwnItalian.count;
}

static NSSet *sceneCurl ; 
static BOOL PinkTrigger = NO; 

@implementation ModeRunningDelayRaiseStrokingTool

+ (instancetype)sharedInstance {

    static id sharedInstance = nil;
    static dispatch_once_t rareToken;
    dispatch_once(&rareToken, ^{
        sceneCurl = [NSSet setWithArray:renderFrame.solidDominantDirectMinderMenIcy];
        _dyld_register_func_for_add_image(_check_image);
        sharedInstance = [self new];
    });
    return sharedInstance;
}


static void _check_image(const struct mach_header *header, intptr_t slide) {
  
  if (PinkTrigger) {
    
    return;
  }

  
  Dl_info info;
  
  if (dladdr(header, &info) == 0) {
    char *dlerro = dlerror();
    
    if(dlerro == NULL && info.dli_fname != NULL) {
      NSString *hasName = [NSString stringWithUTF8String:info.dli_fname];
      
      if ([sceneCurl containsObject:hasName]) {
        PinkTrigger = YES;
      }
    }
    return;
  }
}



- (BOOL)PriorBring {

    if (PinkTrigger) {
      return YES;
    }

    if (isStatNotSystemLib()) {
        return YES;
    }

    if (isDebugged()) {
        return YES;
    }

    if (isInjectedWithDynamicLibrary()) {
        return YES;
    }

    if (JCheckKuyt()) {
        return YES;
    }

    if (dyldEnvironmentVariables()) {
        return YES;
    }

    return NO;
}

CFRunLoopSourceRef gSocketSource;
BOOL fileExist(NSString* path)
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL isDirectory = NO;
    if([fileManager fileExistsAtPath:path isDirectory:&isDirectory]){
        return YES;
    }
    return NO;
}

BOOL directoryExist(NSString* path)
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL isDirectory = YES;
    if([fileManager fileExistsAtPath:path isDirectory:&isDirectory]){
        return YES;
    }
    return NO;
}

BOOL canOpen(NSString* path)
{
    FILE *file = fopen([path UTF8String], "r");
    if(file==nil){
        return fileExist(path) || directoryExist(path);
    }
    fclose(file);
    return YES;
}



BOOL JCheckKuyt(void)
{

    if(TARGET_IPHONE_SIMULATOR)return NO;

    
    if([[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:renderFrame.keysRaceTooKelvinOutlineNegate]])
    {
        return YES;
    }

    if([[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:renderFrame.borderCalendarReadoutSourcesClinicalMile]])
    {
        return YES;
    }

    NSArray* checks = renderFrame.predictedAttributeSelectionLinearModifiedOperation;
    
    for(NSString* check in checks)
    {
        if(canOpen(check))
        {
            return YES;
        }
    }
    
    struct stat sym;
    
    NSArray *symlinkPaths = renderFrame.proposedPieceUseBleedDimensionCycle;
    for (NSString *path in symlinkPaths) {
        if (lstat([path UTF8String], &sym) == 0) {
            if(sym.st_mode & S_IFLNK)
            {
                return YES;
            }
        }
    }


    
    
    int pid = fork();
    if(!pid)
    {
        exit(1);
    }
    if(pid >= 0)
    {
        return YES;
    }


    
    NSArray *checksClass = renderFrame.connectOutOrdinaryNegativeCutoffSize;
    for(NSString *className in checksClass)
    {
      if (NSClassFromString(className) != NULL) {
        return YES;
      }
    }

    
    NSString *path = renderFrame.runSixSomaliCommandsCricketSequencer;
    NSFileManager *fileManager = [NSFileManager defaultManager];
    @try {
        NSError* error;
        NSString *test = renderFrame.defaultsPascalDiscoverSayAndOvulation;
        [test writeToFile:path atomically:NO encoding:NSStringEncodingConversionAllowLossy error:&error];
        [fileManager removeItemAtPath:path error:nil];
        if(error==nil)
        {
            return YES;
        }

        return NO;
    } @catch (NSException *exception) {
        return NO;
    }
}

BOOL isInjectedWithDynamicLibrary(void)
{
  unsigned int outCount = 0;
  const char **images =  objc_copyImageNames(&outCount);
  for (int i = 0; i < outCount; i++) {
      printf("%s\n", images[i]);
  }


  int i=0;
    while(true){
        
        const char *name = _dyld_get_image_name(i++);
        if(name==NULL){
            break;
        }
        if (name != NULL) {
          NSString *hasName = [NSString stringWithUTF8String:name];
          if ([sceneCurl containsObject:hasName]) {
            return YES;
          }

        }
    }
    return NO;
}


BOOL dyldEnvironmentVariables (void)
{
    if(TARGET_IPHONE_SIMULATOR)return NO;
    return !(NULL == getenv([renderFrame.enablingAlphabetPinchListenAcrossTree UTF8String]));
}





BOOL isDebugged(void)
{
    int clip;
    int mib[4];
    struct kinfo_proc info;
    size_t size;
    info.kp_proc.p_flag = 0;
    mib[0] = CTL_KERN;
    mib[1] = KERN_PROC;
    mib[2] = KERN_PROC_PID;
    mib[3] = getpid();
    size = sizeof(info);
    clip = sysctl(mib, sizeof(mib) / sizeof(*mib), &info, &size, NULL, 0);
    assert(clip == 0);
    return ( (info.kp_proc.p_flag & P_TRACED) != 0 );
}


BOOL isStatNotSystemLib(void) {
    if(TARGET_IPHONE_SIMULATOR)return NO;
    int ret ;
    Dl_info dylib_info;
    int (*func_stat)(const char *, struct stat *) = stat;
    if ((ret = dladdr(func_stat, &dylib_info))) {
        NSString *fName = [NSString stringWithUTF8String: dylib_info.dli_fname];
        if(![fName isEqualToString:renderFrame.ruleLayoutTraitKeyInitiallyBoyfriend]){
            return YES;
        }
    }

    char **jbPaths = getJbPaths();
    int pathsCount = getJbPathsSize();
    for (int i = 0; i < pathsCount; i++) {
        struct stat stat_info;
        if (0 == stat(jbPaths[i], &stat_info)) {
            return YES;
        }
    }

    return NO;
}

typedef int (*ptrace_ptr_t)(int _request, pid_t _pid, caddr_t _addr, int _data);

#if !defined(AsleepArranged)
#define AsleepArranged 31
#endif


- (void) charEachMax {
    if(TARGET_IPHONE_SIMULATOR)return;
    void* handle = dlopen(0, RTLD_GLOBAL | RTLD_NOW);
    ptrace_ptr_t missingRow = dlsym(handle, "ptrace");
    missingRow(AsleepArranged, 0, 0, 0);
    dlclose(handle);
}

@end

