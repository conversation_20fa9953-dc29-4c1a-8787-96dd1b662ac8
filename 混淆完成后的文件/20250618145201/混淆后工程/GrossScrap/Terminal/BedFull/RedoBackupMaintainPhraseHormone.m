





#import "RedoBackupMaintainPhraseHormone.h"
@import Network;

static NSString *claimAllTopSay = nil;
static nw_path_monitor_t cellFlatPlural = NULL;

@implementation RedoBackupMaintainPhraseHormone

+ (BOOL)plainWideBookmarkExpandedNet {
    return claimAllTopSay != nil;
}

+ (NSString *)drumFullyBitType {
    return claimAllTopSay ?: @"none";
}

+ (void)restingOutTopFolderNibblesDay:(void (^)(BOOL plainWideBookmarkExpandedNet))completion {
    
    if (cellFlatPlural != NULL) {
        nw_path_monitor_cancel(cellFlatPlural);
        cellFlatPlural = NULL;
    }
    
    
    cellFlatPlural = nw_path_monitor_create();
    nw_path_monitor_set_queue(cellFlatPlural, dispatch_get_main_queue());
    
    __block nw_path_monitor_t blockMonitor = cellFlatPlural;
    nw_path_monitor_set_update_handler(cellFlatPlural, ^(nw_path_t path) {
        nw_path_status_t status = nw_path_get_status(path);
        if (status == nw_path_status_satisfied) {
            if (nw_path_uses_interface_type(path, nw_interface_type_wifi)) {
                claimAllTopSay = @"wifi";
            } else if (nw_path_uses_interface_type(path, nw_interface_type_cellular)) {
                claimAllTopSay = @"cellular";
            } else {
                
                claimAllTopSay = @"other";
            }
            
            
            if (blockMonitor) {
                nw_path_monitor_cancel(blockMonitor);
                blockMonitor = NULL;
                cellFlatPlural = NULL;
            }
            
        } else {
            claimAllTopSay = nil;
        }
        
        
        if (completion) {
            completion([self plainWideBookmarkExpandedNet]);
        }
        
    });
    
    
    nw_path_monitor_start(cellFlatPlural);
}

@end
