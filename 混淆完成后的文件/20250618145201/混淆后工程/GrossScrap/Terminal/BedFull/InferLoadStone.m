






#import "InferLoadStone.h"

#define faceWas(obj) __weak typeof(obj) weak##obj = obj;
#define ampereIll(obj) __strong typeof(obj) obj = weak##obj;

@interface InferLoadStone()

@property (nonatomic,strong) NSURLSession *eggLocalized;

@end

@implementation InferLoadStone


+ (instancetype)shared {
    static InferLoadStone *shared = nil;
    static dispatch_once_t rareToken;
    dispatch_once(&rareToken, ^{
        shared = [[super allocWithZone:NULL] init];
        shared.eggLocalized = [NSURLSession sessionWithConfiguration:[NSURLSessionConfiguration defaultSessionConfiguration] delegate:shared delegateQueue:[[NSOperationQueue alloc] init]];
        shared.eggLocalized.delegateQueue.maxConcurrentOperationCount = 1;
    });
    return shared;
}

- (void)remoteDrizzleRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock
                     success:(void(^)(NSDictionary * cleanPieceCell))success
                     maxRole:(void(^)(NSError *error))maxRole
                  heavyCount:(NSInteger)heavyCount {

    [self sawExportRequest:request
                   process:processBlock
                   success:success
                   maxRole:maxRole
                heavyCount:heavyCount
            breakLoopsMill:0];
}


- (void)sawExportRequest:(NSMutableURLRequest *)request
                 process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock
                 success:(void(^)(NSDictionary * cleanPieceCell))success
                 maxRole:(void(^)(NSError *error))maxRole
              heavyCount:(NSInteger)heavyCount
          breakLoopsMill:(NSInteger)breakLoopsMill {

    faceWas(self);
    NSURLSessionDataTask *task = [self.eggLocalized dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        ampereIll(self);
        
        NSError *oneMileSix = [self handleError:error response:response data:data];
        if (oneMileSix) {
            

            
            if (breakLoopsMill < heavyCount) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self sawExportRequest:request process:processBlock success:success maxRole:maxRole heavyCount:heavyCount breakLoopsMill:breakLoopsMill + 1];
                });
                return;
            }

            
            if (maxRole) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    maxRole(oneMileSix);
                });
            }
            return;
        }

        
        NSData *processedData = processBlock ? processBlock(data) : data;
        if (!processedData) {
            NSError *processingError = [NSError errorWithDomain:@"NetworkCore"
                                                           code:-30002
                                                       userInfo:@{NSLocalizedDescriptionKey : @"Data processing failed"}];
            if (maxRole) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    maxRole(processingError);
                });
            }
            return;
        }

        NSError *jsonError;
        NSDictionary *jsonResponse = [NSJSONSerialization JSONObjectWithData:processedData options:0 error:&jsonError];

        if (!jsonError && [jsonResponse isKindOfClass:[NSDictionary class]]) {
            if (success) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    success(jsonResponse);
                });
            }
        } else {
            
            if (maxRole) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    maxRole(jsonError);
                });
            }
        }
    }];

    [task resume];
}


- (NSError *)handleError:(NSError *)error response:(NSURLResponse *)response data:(NSData *)data {
    if (error) {
        return error;
    }

    if (!data) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:-30001
                               userInfo:@{NSLocalizedDescriptionKey : @"The data is empty."}];
    }

    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
    if (![httpResponse isKindOfClass:[NSHTTPURLResponse class]] || httpResponse.statusCode != 200) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:httpResponse.statusCode
                               userInfo:@{NSLocalizedDescriptionKey : [NSString stringWithFormat:@"HTTPError，code: %ld", (long)httpResponse.statusCode]}];
    }

    return nil;
}

@end
