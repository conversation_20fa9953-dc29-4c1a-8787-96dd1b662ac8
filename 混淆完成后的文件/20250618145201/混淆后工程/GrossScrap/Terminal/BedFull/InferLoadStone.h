






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface InferLoadStone : NSObject
<
NSURLSessionDelegate,
NSURLSessionTaskDelegate
>

+ (instancetype)shared;

- (void)remoteDrizzleRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock 
                     success:(void(^_Nullable)(NSDictionary * cleanPieceCell))success
                     maxRole:(void(^_Nullable)(NSError *error))maxRole
                  heavyCount:(NSInteger)heavyCount;

@end

NS_ASSUME_NONNULL_END
