






#import "UIDevice+BarDevice.h"
#import "HitUnableManager.h"
@import UIKit;

@implementation UIDevice (BarDevice)

static NSInteger waxMax = -1;
+ (BOOL)waxMax {
    if (waxMax < 0) {
        waxMax = [UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad ? 1 : 0;
    }
    return waxMax > 0;
}

+ (BOOL)abnormal {
    if (@available(iOS 11.0, *)) {
        
        UIWindow *window = HitUnableManager.shared.laterExceedsWindow;
        
        UIEdgeInsets safeArea = window.safeAreaInsets;
        
        
        BOOL deletion = ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPhone);
        
        
        return deletion && (
            safeArea.top > 20.0 ||          
            safeArea.left > 0 ||            
            safeArea.right > 0              
        );
    }
    return NO; 
}

@end
