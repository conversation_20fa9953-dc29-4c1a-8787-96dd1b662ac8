






#import "UIImage+OwnImage.h"
#import "NSData+PinThat.h"
#import "NSString+StickySay.h"
#import "VerboseDigestSignUnorderedSynthesis.h"

@implementation UIImage (OwnImage)

+ (UIImage *)lateGenreCloudColor:(UIColor *)color {
    
    CGRect rect=CGRectMake(0.0f,0.0f, 1.0f,1.0f);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    UIImage *maxImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return maxImage;
}

+ (UIImage *)domainIndexObserverUploadingInitiatedName:(NSString *)imageName {
    
    if (!imageName) {
        return nil;
    }
    
    UIImage *image = nil;
    
    NSString *mayPath= [[VerboseDigestSignUnorderedSynthesis exporting] stringByAppendingPathComponent:imageName];
    
    if (mayPath.uplinkUseDetailedTeamVolatile) {
        
        image = [UIImage imageWithContentsOfFile:mayPath];
    }
    
    if (!image) {
        
        NSData *reductionData = [NSData dataWithContentsOfFile:mayPath];
       
       
        image = [reductionData laterHisCardioidLegibleCursorButPerson];
    }
    
    return image;
}

- (UIImage *)iconBuffersHairCreatorNameColor:(UIColor *)tintColor {
   
    if (!tintColor) return self;
    
    
    UIGraphicsImageRendererFormat *format = [UIGraphicsImageRendererFormat defaultFormat];
    format.scale = self.scale;
    format.opaque = NO;
    
    UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:self.size format:format];
    
    return [renderer imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull context) {
        
        [tintColor setFill];
        
        
        CGRect bounds = CGRectMake(0, 0, self.size.width, self.size.height);
        [self drawInRect:bounds];
        
        
        CGContextSetBlendMode(context.CGContext, kCGBlendModeSourceIn);
        CGContextFillRect(context.CGContext, bounds);
    }];
}
@end
