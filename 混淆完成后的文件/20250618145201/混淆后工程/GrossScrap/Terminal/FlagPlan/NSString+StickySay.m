






#import "NSString+StickySay.h"

@implementation NSObject (StickySay)

- (BOOL)withinOptWet {
    
    
    if (self == nil || (id)self == [NSNull null]) {
        return YES;
    }
    
    
    if ([self isKindOfClass:[NSString class]]) {
        NSString *str = (NSString *)self;
        if (str.length == 0) {
            return YES;
        }
        
        NSString *rawLift = [str stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
        return (rawLift.length == 0);
    }
    
    







    
    
    
    return YES;
}

- (BOOL)uplinkUseDetailedTeamVolatile {
    return ![self withinOptWet];
}

//- (BOOL)uplinkUseDetailedTeamVolatile {

//}

//- (BOOL)withinOptWet {




//}

@end
