






#import "NSString+SheHectar<PERSON>.h"

@implementation NSString (SheHectares)

- (NSString *)randomSettingsIntervalLateOwnWill {
    NSCharacterSet *allowedCharacters = [[NSCharacterSet characterSetWithCharactersInString:@"!*'();:@&=+$,/?%#[]"] invertedSet];
    return [self stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
}

- (NSString *)phraseAdjectiveThroughUptimeWetTremor {
    NSString *airBitsReady = [self stringByReplacingOccurrencesOfString:@"+" withString:@" "];
    return [airBitsReady stringByRemovingPercentEncoding];
}

@end
