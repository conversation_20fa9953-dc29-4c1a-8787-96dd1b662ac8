






#import "NSURL+MinWhoFive.h"
#import "NSString+SheHectares.h"

@implementation NSURL (MinWhoFive)

- (NSDictionary *)carMatrixFor {
    
    NSArray * array = [[self query] componentsSeparatedByString:@"&"];

    NSMutableDictionary * ParaDict = [NSMutableDictionary new];

    for(int i = 0 ; i < [array count]; i++){

        NSArray * darwinValue = [array[i] componentsSeparatedByString:@"="];

        if([darwinValue count] == 2 && darwinValue[0] && darwinValue[1]){

            [ParaDict setObject:[darwinValue[1] phraseAdjectiveThroughUptimeWetTremor] forKey:darwinValue[0]];

        }
    }
    return ParaDict;
}

@end
