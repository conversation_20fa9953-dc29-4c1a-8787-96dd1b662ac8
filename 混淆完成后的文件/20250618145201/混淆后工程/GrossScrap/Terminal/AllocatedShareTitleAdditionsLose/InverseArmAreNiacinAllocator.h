







#import <Foundation/Foundation.h>

#if !__has_feature(nullability)
#define NS_ASSUME_NONNULL_BEGIN
#define NS_ASSUME_NONNULL_END
#define nullable
#define nonnull
#define null_unspecified
#define null_resettable
#define __nullable
#define __nonnull
#define __null_unspecified
#endif

#if __has_extension(objc_generics)
#define NeedCupHeart <NSString *>
#define HusbandMonitoredOldPointerImmutable <NSDictionary <NSString *, NSString *>*>
#else
#define NeedCupHeart
#define HusbandMonitoredOldPointerImmutable
#endif

NS_ASSUME_NONNULL_BEGIN

extern NSString * const UICKeyChainStoreErrorDomain;

typedef NS_ENUM(NSInteger, PassiveCityFixCleanTwoApplyCode) {
    StandDoneEllipseRaiseMetadataSpaceSnap = 1,
};

typedef NS_ENUM(NSInteger, DisparityTenDisplaysColumnDidAnd) {
    CleanupAskMileTrapPopOrnamentsDrainPassword = 1,
    DailyStoreToolDownhillBuffersRedoneHurricanePassword,
};

typedef NS_ENUM(NSInteger, StillJustHueRestartDecideRecoveredType) {
    ResultingKindAssistantQueryOperandAchievedFormats = 1,
    ExposeFoodSceneEnsureBoldfaceOrderingResizingAccount,
    SkipDanceFaceSuitableExtrasTrustedRun,
    SymbolicArtsAllStepchildTremorClosureToo,
    FullyStructureFastAmbienceAltimeterDigestDry,
    LanguageAlertKilogramsBoldChangeSpaCadence,
    PortionReturningAuxiliaryCollationPlateThreadsLong,
    DirectorDeltaMeteringCategoryTamilSyntheticRelative,
    GopherKinCosmicLegalMonotonicStiffnessVitamin,
    CancelFloatDueSignalingFlexibleSumHard,
    EchoObserveSemanticGrowPendingPreviewsInsert,
    ProvideIndoorUnwrapDirectBadOfficialKeys,
    RawSchoolTruncatesCelticRaceCyclingSlide,
    NetRotorQuietJobMultiplyOptimizedPlus,
    GivenConstantsVisibleClipBusEncodedParagraph,
    BagDanceSelectorRatioUighurFadePrompt,
    LemmaPromptCoastSiteDeciliterUnsignedDog,
    WaistMaxLocalesReactorForHourlyPreferred,
    PermuteMarginTriggeredEnsureDisplaysHoverTop,
    NapPasswordsBleedLeaveFlemishUnlockedSin,
    AwayAppendingFeedRareJabberVelocityDistinct,
    UniqueSinProgramLogHectaresInstancesAccepting,
    FinderFullLawNotCroppingPurpleGeometric,
    ListEngravedTibetanOrderedBookBehaveVertical,
    IgnoresNeedBeenProvinceSlantAccurateProposal,
    ClipDiacriticClockwiseSelectBecomeOwnMap,
    IntersectExtrinsicCyclingStoodDownloadYearsInstances,
    SceneDoubleBuddyEngineerRectifiedNumbersThrough,
    EitherAfterStickySexBagNegateKilohertz,
};

typedef NS_ENUM(NSInteger, SpeedDomainContentsNinePotassiumTrashRomanType) {
    UnpluggedRemembersBoldLinkageStreamsEarPencil = 1,
    RelationsBypassNapBestPolarAllowMobile,
    RadialEnterTapReadyLessMajorStore,
    KinRelatedCautionContextsUploadYoungestHall,
    SortDiscreteProviderUnlearnFilmParseMayAnimate,
    SparseOwnFlippedHundredsMaxOptionLifetimeOdd,
    PingTowerMagentaFriendMinCoverMouseWatch,
    StonePhaseTrustSymbolWeekendHourlyRankDelayed,
};

typedef NS_ENUM(NSInteger, ApertureDurationClientBatteryAgeAuthors) {
    SaySupportHostAtomicNoticeLooseExtrasDeclined = 1,
    ReloadCertSlicePrefixedGaspOrnamentDecipherSee,
    AnnotatedPartlyTintDynamicChapterPlaceText,
    FarBinKernel
    __OSX_AVAILABLE_STARTING(__MAC_10_10, __IPHONE_8_0),
    DatabaseRevertingJump,
    SoloPriorityContact,
    BetterCapCompanyLanguageSpeakerDailyDurationWeekend,
}
__OSX_AVAILABLE_STARTING(__MAC_10_9, __IPHONE_4_0);

typedef NS_ENUM(unsigned long, SubmittedFoldEntriesEasyManganeseZoomingTelugu) {
    ProfilesDaysWorkoutsAppearHerMinOutcomeArea        = 1 << 0,
    ClockwiseSenseProceedShuffleFirmwareRemotelyDebuggerCase          NS_ENUM_AVAILABLE(10_12_1, 9_0) = 1u << 1,
    LivePrivacyPace   NS_ENUM_AVAILABLE(10_12_1, 9_0) = 1u << 3,
    ConverterSkipBirth      NS_ENUM_AVAILABLE(10_11, 9_0) = 1u << 4,
    IntroSeparatorCarNotVerifyMalformedOurCloudy           NS_ENUM_AVAILABLE(10_12_1, 9_0) = 1u << 14,
    BuffersVirtualDiscountBankersNanogramsEarlyYouLeap          NS_ENUM_AVAILABLE(10_12_1, 9_0) = 1u << 15,
    LeftMenstrualCross     NS_ENUM_AVAILABLE(10_12_1, 9_0) = 1u << 30,
    TremorAskLatencyMidFloatingMomentAbsentLookPassword NS_ENUM_AVAILABLE(10_12_1, 9_0) = 1u << 31,
}__OSX_AVAILABLE_STARTING(__MAC_10_10, __IPHONE_8_0);

@interface InverseArmAreNiacinAllocator : NSObject

@property (nonatomic, readonly) DisparityTenDisplaysColumnDidAnd partlyWas;

@property (nonatomic, readonly, nullable) NSString *service;
@property (nonatomic, readonly, nullable) NSString *sessionPair;

@property (nonatomic, readonly, nullable) NSURL *server;
@property (nonatomic, readonly) StillJustHueRestartDecideRecoveredType protocolType;
@property (nonatomic, readonly) SpeedDomainContentsNinePotassiumTrashRomanType axialDeviationType;

@property (nonatomic) ApertureDurationClientBatteryAgeAuthors accessibility;
@property (nonatomic, readonly) SubmittedFoldEntriesEasyManganeseZoomingTelugu offsetsNorwegianBarrierDisableDrawPoint
__OSX_AVAILABLE_STARTING(__MAC_10_10, __IPHONE_8_0);
@property (nonatomic) BOOL manyWeightsHairVitalityDerived;

@property (nonatomic) BOOL redoEchoManEra;

@property (nonatomic, nullable) NSString *reduceConsumedTabularQuantizeRetainEraser
__OSX_AVAILABLE_STARTING(__MAC_NA, __IPHONE_8_0);

@property (nonatomic, readonly, nullable) NSArray NeedCupHeart *allKeys;
@property (nonatomic, readonly, nullable) NSArray *sortCube;

+ (NSString *)bigAndSobDepth;
+ (void)setBigAndSobDepth:(NSString *)bigAndSobDepth;

+ (InverseArmAreNiacinAllocator *)fatalCupPrior;
+ (InverseArmAreNiacinAllocator *)millLinearDrizzleSockCellColored:(nullable NSString *)service;
+ (InverseArmAreNiacinAllocator *)millLinearDrizzleSockCellColored:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair;

+ (InverseArmAreNiacinAllocator *)thickFocusesTreeVerifyNiacinBlocker:(NSURL *)server protocolType:(StillJustHueRestartDecideRecoveredType)protocolType;
+ (InverseArmAreNiacinAllocator *)thickFocusesTreeVerifyNiacinBlocker:(NSURL *)server protocolType:(StillJustHueRestartDecideRecoveredType)protocolType axialDeviationType:(SpeedDomainContentsNinePotassiumTrashRomanType)axialDeviationType;

- (instancetype)init;
- (instancetype)initWithService:(nullable NSString *)service;
- (instancetype)initWithService:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair;

- (instancetype)initExpireBurn:(NSURL *)server protocolType:(StillJustHueRestartDecideRecoveredType)protocolType;
- (instancetype)initExpireBurn:(NSURL *)server protocolType:(StillJustHueRestartDecideRecoveredType)protocolType axialDeviationType:(SpeedDomainContentsNinePotassiumTrashRomanType)axialDeviationType;

+ (nullable NSString *)stringForKey:(NSString *)key;
+ (nullable NSString *)stringForKey:(NSString *)key service:(nullable NSString *)service;
+ (nullable NSString *)stringForKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair;

+ (nullable NSData *)dataForKey:(NSString *)key;
+ (nullable NSData *)dataForKey:(NSString *)key service:(nullable NSString *)service;
+ (nullable NSData *)dataForKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair;

- (BOOL)contains:(nullable NSString *)key;

- (BOOL)setString:(nullable NSString *)string forKey:(nullable NSString *)key;
- (BOOL)setString:(nullable NSString *)string forKey:(nullable NSString *)key label:(nullable NSString *)label comment:(nullable NSString *)comment;
- (nullable NSString *)stringForKey:(NSString *)key;

- (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key;
- (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key label:(nullable NSString *)label comment:(nullable NSString *)comment;
- (nullable NSData *)dataForKey:(NSString *)key;

+ (BOOL)manyMoveAlignKey:(NSString *)key;
+ (BOOL)manyMoveAlignKey:(NSString *)key service:(nullable NSString *)service;
+ (BOOL)manyMoveAlignKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair;

+ (BOOL)removeAllItems;
+ (BOOL)expandPostalReplaceTouchesTagSeparator:(nullable NSString *)service;
+ (BOOL)expandPostalReplaceTouchesTagSeparator:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair;

- (BOOL)manyMoveAlignKey:(NSString *)key;

- (BOOL)removeAllItems;

- (nullable NSString *)objectForKeyedSubscript:(NSString<NSCopying> *)key;
- (void)setObject:(nullable NSString *)obj forKeyedSubscript:(NSString<NSCopying> *)key;

+ (nullable NSArray NeedCupHeart *)figurePreventedResonantConnectImmediateEncrypted:(DisparityTenDisplaysColumnDidAnd)partlyWas;
- (nullable NSArray NeedCupHeart *)allKeys;

+ (nullable NSArray *)carAppearingDepthInlandEmailTrial:(DisparityTenDisplaysColumnDidAnd)partlyWas;
- (nullable NSArray *)sortCube;

- (void)setAccessibility:(ApertureDurationClientBatteryAgeAuthors)accessibility offsetsNorwegianBarrierDisableDrawPoint:(SubmittedFoldEntriesEasyManganeseZoomingTelugu)offsetsNorwegianBarrierDisableDrawPoint
__OSX_AVAILABLE_STARTING(__MAC_10_10, __IPHONE_8_0);

#if TARGET_OS_IOS
- (void)elderReceivedCocoaLargeBinLevel:(nullable void (^)(NSString * __nullable account, NSString * __nullable password, NSError * __nullable error))completion;
- (void)softnessFeaturesFirePackageUnlockAccount:(NSString *)account completion:(nullable void (^)(NSString * __nullable password, NSError * __nullable error))completion;

- (void)somaliEggPassword:(nullable NSString *)password tapAccount:(NSString *)account completion:(nullable void (^)(NSError * __nullable error))completion;
- (void)laotianOperatorUighurCelsiusButtonSensorAccount:(NSString *)account completion:(nullable void (^)(NSError * __nullable error))completion;

+ (void)thermalTraveledChannelPreviousEnterStalledStarBottom:(nullable void (^)(NSArray HusbandMonitoredOldPointerImmutable *credentials, NSError * __nullable error))completion;
+ (void)footnoteDominantAdobeRepublicChatPackageCenter:(nullable NSString *)domain account:(nullable NSString *)account completion:(nullable void (^)(NSArray HusbandMonitoredOldPointerImmutable *credentials, NSError * __nullable error))completion;

+ (NSString *)pickTaskPassword;
#endif

@end

@interface InverseArmAreNiacinAllocator (ErrorHandling)

+ (nullable NSString *)stringForKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (nullable NSString *)stringForKey:(NSString *)key service:(nullable NSString *)service error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (nullable NSString *)stringForKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (nullable NSData *)dataForKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (nullable NSData *)dataForKey:(NSString *)key service:(nullable NSString *)service error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (nullable NSData *)dataForKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair error:(NSError * __nullable __autoreleasing * __nullable)error;

- (BOOL)setString:(nullable NSString *)string forKey:(NSString * )key error:(NSError * __nullable __autoreleasing * __nullable)error;
- (BOOL)setString:(nullable NSString *)string forKey:(NSString * )key label:(nullable NSString *)label comment:(nullable NSString *)comment error:(NSError * __nullable __autoreleasing * __nullable)error;

- (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
- (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key label:(nullable NSString *)label comment:(nullable NSString *)comment error:(NSError * __nullable __autoreleasing * __nullable)error;

- (nullable NSString *)stringForKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
- (nullable NSData *)dataForKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)manyMoveAlignKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)manyMoveAlignKey:(NSString *)key service:(nullable NSString *)service error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)manyMoveAlignKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)kitElementsUplinkSwipeFilteringMusic:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)expandPostalReplaceTouchesTagSeparator:(nullable NSString *)service error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)expandPostalReplaceTouchesTagSeparator:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair error:(NSError * __nullable __autoreleasing * __nullable)error;

- (BOOL)manyMoveAlignKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
- (BOOL)kitElementsUplinkSwipeFilteringMusic:(NSError * __nullable __autoreleasing * __nullable)error;

@end

@interface InverseArmAreNiacinAllocator (ForwardCompatibility)

+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service sessionPair:(nullable NSString *)sessionPair notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume error:(NSError * __nullable __autoreleasing * __nullable)error;

- (BOOL)setString:(nullable NSString *)string forKey:(NSString *)key notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume;
- (BOOL)setString:(nullable NSString *)string forKey:(NSString *)key notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume error:(NSError * __nullable __autoreleasing * __nullable)error;

- (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume;
- (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key notifiedFoggySayGreenVolume:(nullable id)notifiedFoggySayGreenVolume error:(NSError * __nullable __autoreleasing * __nullable)error;

@end

@interface InverseArmAreNiacinAllocator (Deprecation)

- (void)synchronize __attribute__((deprecated("calling this method is no longer required")));
- (BOOL)twoStreamOddDiamondMinCircle:(NSError * __nullable __autoreleasing * __nullable)error __attribute__((deprecated("calling this method is no longer required")));

@end

NS_ASSUME_NONNULL_END
