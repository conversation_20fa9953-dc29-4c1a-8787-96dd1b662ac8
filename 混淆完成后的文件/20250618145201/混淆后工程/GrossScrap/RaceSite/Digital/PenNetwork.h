






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface PenNetwork : NSObject

@property (nonatomic, copy) NSString *cardBeen;

+ (instancetype)backwardsSixNetwork;

- (void)sawExportRequest:(NSString *)url
                  params:(NSDictionary * _Nullable)params
                 success:(void(^_Nullable)(NSDictionary *cleanPieceCell))success
                 maxRole:(void(^_Nullable)(NSError *error))maxRole;

@end

NS_ASSUME_NONNULL_END
