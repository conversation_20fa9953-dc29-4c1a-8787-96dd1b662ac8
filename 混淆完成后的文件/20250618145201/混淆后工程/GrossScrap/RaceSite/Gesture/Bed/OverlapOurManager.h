






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface OverlapOurManager : NSObject

+ (void)basalLemmaRandomDecigramsWideLinearlyDone:(NSString *)appId complete:(void (^_Nullable)(BOOL isCanLogin))complete;

+ (void)notifyNeedFaceBandZoomController:(UIViewController *_Nonnull)controller array:(NSArray *)array success:(void (^_Nullable)(NSDictionary * _Nonnull synthesis))success periodJoin:(void (^_Nullable)(NSString * _Nonnull error))error fontJobAction:(void(^)(NSInteger))action;

@end

NS_ASSUME_NONNULL_END
