






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SplatManager : NSObject

+ (void)nameArraySubViewController:(UIViewController *)vc handler:(void(^)(<PERSON>OOL isCancell,NSString *userID, NSString*token, NSString*error))handler;

+ (BOOL)darkAdobeDecrementLegacyBook:(UIApplication *)application
                waxBrand:(NSURL *)url
                filmHall:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options;

+ (void)insetInsertedBitLinkFloaterCommand:(NSString *)clientId encodeNetWay:(NSString *)encodeNetWay;

@end

NS_ASSUME_NONNULL_END
