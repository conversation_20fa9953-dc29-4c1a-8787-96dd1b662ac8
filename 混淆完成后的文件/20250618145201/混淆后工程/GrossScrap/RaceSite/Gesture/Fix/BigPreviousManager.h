






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface BigPreviousManager : NSObject

+ (void)rangeAccessoryMetabolicTagInsertingCapsKey:(NSString *)xxpk_maxkey volumeCleanupIntegralArtistChain:(NSString *)volumeCleanupIntegralArtistChain combinedDirectorFollowerTerahertzBracket:(NSArray *)combinedDirectorFollowerTerahertzBracket;

+ (void)enterExtentChinaLimitDueMembersData:(nullable NSString *)customData interact:(void(^)(BOOL result))interact;

@end

NS_ASSUME_NONNULL_END
