// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		009566CC56E14E2A6A4121D1 /* SDImageCacheDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 12AD43BCA9BB937E9495379F /* SDImageCacheDefine.h */; };
		00E5071DC05F7FCCB271C04E /* NSBezierPath+SDRoundedCorners.m in Sources */ = {isa = PBXBuildFile; fileRef = 7E0B305297F5073C3490BDBE /* NSBezierPath+SDRoundedCorners.m */; };
		00E98AE41A51C02DD9BB7AE8 /* PoloFigure.h in Headers */ = {isa = PBXBuildFile; fileRef = 2FEBDB4B13FF801913DD149D /* PoloFigure.h */; };
		01A3F660EDA632ABA11AFA1D /* LongRaceView.m in Sources */ = {isa = PBXBuildFile; fileRef = F13CC0EA8C3A370B941C6771 /* LongRaceView.m */; };
		024B6817A8A72117F24F35AA /* SDImageLoader.m in Sources */ = {isa = PBXBuildFile; fileRef = 1F6240B50BA9D3C49F0795BA /* SDImageLoader.m */; };
		0264B69396DBC9A707F92E3B /* CiphersEarModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 08C90987D3B6128A4C00EAA3 /* CiphersEarModel.h */; };
		02956EE136ECBE00675233D0 /* DogTempPin.h in Headers */ = {isa = PBXBuildFile; fileRef = 522FBE91931224EFC4B8EFDF /* DogTempPin.h */; };
		038E0500EA2C899120F2BE19 /* SDImageFramePool.h in Headers */ = {isa = PBXBuildFile; fileRef = 8AFF7D6AEF62C3CCA694403E /* SDImageFramePool.h */; };
		06AE19DA1634D35C7404C080 /* SDWebImageError.m in Sources */ = {isa = PBXBuildFile; fileRef = B9D628FF5F86D0C511C5D3C1 /* SDWebImageError.m */; };
		06D680EFD8B88E1AEDEF3682 /* EarSigningManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FA7D6EEE5407F35673F0C6DB /* EarSigningManager.m */; };
		0966B737F3BA9C35D4B578FE /* SDWebImageDownloaderDecryptor.h in Headers */ = {isa = PBXBuildFile; fileRef = 59B72C2A47FF3AA133E5695A /* SDWebImageDownloaderDecryptor.h */; };
		0A8A816493C334267E473AB6 /* BendRope.m in Sources */ = {isa = PBXBuildFile; fileRef = A7611D0E441C837EBB1C8E45 /* BendRope.m */; };
		0B42760FF94DB2092398CE27 /* CanBusEnableModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 3A4D7A9CE02AB7CA653DF027 /* CanBusEnableModel.m */; };
		0BBF6981A267FC543F1BCC6D /* SDImageGIFCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 61D0B4FF84ACEDE3C1C8E103 /* SDImageGIFCoder.m */; };
		0CFB840D93E1ABCE9624EC88 /* OriginsFunnelSignalingReadyCaps.h in Headers */ = {isa = PBXBuildFile; fileRef = 019802501F00732EF0141510 /* OriginsFunnelSignalingReadyCaps.h */; };
		0D4EA786420C4E116D805B06 /* NSObject+MenPunjabiInfoSongLost.m in Sources */ = {isa = PBXBuildFile; fileRef = 59ACC83996411FD32575C17C /* NSObject+MenPunjabiInfoSongLost.m */; };
		0F169F960396B9FD31E435AC /* SDWeakProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 5E558EB81B54F15693EF092E /* SDWeakProxy.h */; };
		103C5230F1DCD635F529DE65 /* NSImage+Compatibility.m in Sources */ = {isa = PBXBuildFile; fileRef = 3D6092DFE0F964A3E6237155 /* NSImage+Compatibility.m */; };
		10FB5E88853E3667EDF440C0 /* FigureGetSonInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = EE0A2D9FCF02C9B41F383659 /* FigureGetSonInfo.m */; };
		121553B285718252D15D7B31 /* SemicolonViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = CBEBB6D570A10D533CF56400 /* SemicolonViewController.m */; };
		128083A9D6993F17B519ADE7 /* SDWebImageDownloaderOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 765021F0844B36A086D2F559 /* SDWebImageDownloaderOperation.h */; };
		1285C7805570F79CCDE60BE4 /* ForegroundReconnection.h in Headers */ = {isa = PBXBuildFile; fileRef = 4CA75D9A537F0C2CBBFF2117 /* ForegroundReconnection.h */; };
		134C3EFDF9536EC29311E1B1 /* SDWebImageOptionsProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = 62BA177E66745DB971E6F2F1 /* SDWebImageOptionsProcessor.h */; };
		134FDE3B808856A27A590F7C /* SDWebImageCacheSerializer.m in Sources */ = {isa = PBXBuildFile; fileRef = 145C3EE3B7D9A537F15C9590 /* SDWebImageCacheSerializer.m */; };
		13A9742734BA1792877627A8 /* DetailedPutManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 72665F68328011A7C3B606C1 /* DetailedPutManager.h */; };
		14EB7E0EE1694A2ACF39BE19 /* RawUpsideOdd.m in Sources */ = {isa = PBXBuildFile; fileRef = 6D26887745EF2EBED0C11C07 /* RawUpsideOdd.m */; };
		150D77B1C37666A147E85BC6 /* MQTTSessionLegacy.m in Sources */ = {isa = PBXBuildFile; fileRef = D4953E7AC4DAF7EB614C7BBE /* MQTTSessionLegacy.m */; };
		1528CB5D151A16EE74B76B20 /* NSURL+MinWhoFive.m in Sources */ = {isa = PBXBuildFile; fileRef = 46FF86A625C1DEB63C82C39C /* NSURL+MinWhoFive.m */; };
		176D964CBBB67E9620061C48 /* SDWebImageCacheKeyFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = C1350EC177815442723EAE11 /* SDWebImageCacheKeyFilter.h */; };
		17FF73839FCCCC28F653A46D /* MQTTSSLSecurityPolicyTransport.m in Sources */ = {isa = PBXBuildFile; fileRef = 98F20B819A90A4CD76469ABF /* MQTTSSLSecurityPolicyTransport.m */; };
		186DBC1161CB04F53E183941 /* ChromaFarManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A4A89951A76082008E6BDE9 /* ChromaFarManager.h */; };
		18BE1337647133A4764D9E9C /* FrequencyInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 6E64521DC7FB648B69CDEF12 /* FrequencyInfo.h */; };
		190B709556CE952D372E39CB /* SDMemoryCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 5B4B926BB72EAEA93712A2E1 /* SDMemoryCache.h */; };
		19752B3A11B90F00395EB97E /* ModeRunningDelayRaiseStrokingTool.h in Headers */ = {isa = PBXBuildFile; fileRef = B39FBC08DBA11ED48FDCA170 /* ModeRunningDelayRaiseStrokingTool.h */; };
		19C3DB7303D1C01E0571878A /* SDImageCachesManagerOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 5A2FD435C2DF590B507C35CA /* SDImageCachesManagerOperation.h */; };
		19FAF1CE6C5FDA324B96D771 /* CaseBurstViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5D4B02B18D4D9AAA58CE8CF2 /* CaseBurstViewController.m */; };
		1BF5F5C6611468D3ECCE9E61 /* MQTTCFSocketDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = AAF87AD30A40EDEA45D6772C /* MQTTCFSocketDecoder.h */; };
		1C099A9DEDC84DDF6C71B755 /* LogLowerHexManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 29CECAFAAD51234B54AE2852 /* LogLowerHexManager.h */; };
		1C73CD9A036DD9846F72310D /* MQTTProperties.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C95468E6D2D68C3AD996FA2 /* MQTTProperties.m */; };
		1CCA31C6C725D9ED46621498 /* EarSigningManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 599F7FE7A88B9997B9B65C28 /* EarSigningManager.h */; };
		1D1051BC755DA79C16F61CE7 /* SDAnimatedImageRep.m in Sources */ = {isa = PBXBuildFile; fileRef = 2684143DA4ED106F85A2D860 /* SDAnimatedImageRep.m */; };
		1D2D84A29CE21459F4FC8DBC /* SDAnimatedImageView.h in Headers */ = {isa = PBXBuildFile; fileRef = B8F1E5B4FD030FDF052136B1 /* SDAnimatedImageView.h */; };
		1DF25A27156461544DAA8DC9 /* SDImageLoadersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = EEA58B5534F6981BE89ED80D /* SDImageLoadersManager.m */; };
		1FDF19177C0D1E6E5565E2B8 /* MQTTSessionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 703B509329B646238ED0491E /* MQTTSessionManager.h */; };
		215FD02EDB3C7C264AE03B7B /* SobAlertView.h in Headers */ = {isa = PBXBuildFile; fileRef = 8FD49831B42BCB4EB8F826EB /* SobAlertView.h */; };
		21FB2A917F857031A399C9D5 /* UIViewController+BoxViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 8325D31701C5A1AF97B49EC7 /* UIViewController+BoxViewController.h */; };
		23A68692F0065E7AC5935DFB /* NSString+StickySay.h in Headers */ = {isa = PBXBuildFile; fileRef = 3323343FA46D8E3EE37A7F16 /* NSString+StickySay.h */; };
		23B5226FAEDA3A21685330FC /* NSURL+MinWhoFive.h in Headers */ = {isa = PBXBuildFile; fileRef = 4749A2EDBC9F6DDB425B5687 /* NSURL+MinWhoFive.h */; };
		244C4700B8B1B18139FA8316 /* ReportManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 65416AB0B657D9896DB22BCC /* ReportManager.m */; };
		2539169F896DD4D6403AB446 /* WindowsSobInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 54B82C9C2C7803067141F0A2 /* WindowsSobInfo.m */; };
		2633F37C5280577CFF451E38 /* SDImageHEICCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = A9B149D11C468671532D9859 /* SDImageHEICCoder.m */; };
		263B62E74CAE10AA9F51CC2B /* TowerHisManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 99BE632F1D381DA69F954F51 /* TowerHisManager.h */; };
		264878515920E47EA7115085 /* NSData+ImageContentType.h in Headers */ = {isa = PBXBuildFile; fileRef = D48688A96AFCF354716D4106 /* NSData+ImageContentType.h */; };
		2733E4FF3631A20F585178CB /* BigPrologHasBankersWon.m in Sources */ = {isa = PBXBuildFile; fileRef = ABFC2C1CF2846CDF3C758E56 /* BigPrologHasBankersWon.m */; };
		2837C7C9C6E28933B72F7C4A /* LeapHueHeapSunViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = E4A16B7F7CC40EBFD91FA9E4 /* LeapHueHeapSunViewController.h */; };
		287FB1366FA671FBE396B543 /* Adobe.h in Headers */ = {isa = PBXBuildFile; fileRef = 7FD99A55FB6BF2CFF90809E5 /* Adobe.h */; };
		28E677C0550C73FA7CA2244F /* LongRaceView.h in Headers */ = {isa = PBXBuildFile; fileRef = 95E416A3926FF9B3E2930DAF /* LongRaceView.h */; };
		29E6880B33FF0F9B0D0FF5AC /* MASConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 4C33F9BCB6334778CB1CA6A0 /* MASConstraint.m */; };
		2A4A34ECA7AC0DF3B67C601C /* CheckSmoothButton.m in Sources */ = {isa = PBXBuildFile; fileRef = D8E0A9108BAD255FD0B9B73E /* CheckSmoothButton.m */; };
		2A7380AD26E16DA8CDD9E8C3 /* TagSidebarView.m in Sources */ = {isa = PBXBuildFile; fileRef = E644D62B85EC55C8D65EF74B /* TagSidebarView.m */; };
		2AE761889D9149F058F8C3D3 /* HandballProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 242947CD7310C773C8E5870B /* HandballProtocol.h */; };
		2B20DC9AB889BF3323ACE803 /* YoungerViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = E61D933DFE8AD798918F8E6F /* YoungerViewController.h */; };
		2B4836BDC5E21B397EA3F71F /* PopPickMayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F7160B4CB79B64ECE5299318 /* PopPickMayViewController.m */; };
		2B57B29033EBBC15F6DE4427 /* SDCallbackQueue.h in Headers */ = {isa = PBXBuildFile; fileRef = F0BBA238C2C5CAAEC3C5B0B8 /* SDCallbackQueue.h */; };
		2B90037DD5615E4742A13443 /* FloaterBendTerabytesCopperTaggingReasonViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F13BC6A6309DB96973E773D /* FloaterBendTerabytesCopperTaggingReasonViewController.m */; };
		2C10F79E08CAFA5E6260ADB0 /* NSButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DACB72F4DAF3E8507DF1F26 /* NSButton+WebCache.h */; };
		2CB77D4A8792B27102372185 /* SDWeakProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 36AA8C439B87BA05D260F635 /* SDWeakProxy.m */; };
		2CD96A24E5432CBF427A97D6 /* MASCompositeConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 8B9985503C2558EBED8A522B /* MASCompositeConstraint.m */; };
		2CE2809793AC003FA1A99ED7 /* SDDisplayLink.m in Sources */ = {isa = PBXBuildFile; fileRef = 36EF06B1B8D01EBC1869D02D /* SDDisplayLink.m */; };
		2DB50D09151A536E585203D2 /* UIImage+Metadata.m in Sources */ = {isa = PBXBuildFile; fileRef = 7B00D48E7E0C45FF6FEA8A27 /* UIImage+Metadata.m */; };
		2DB82E4C5B171D9457B4D61B /* MASConstraint+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 0C95057C0EF58E3683F01C1A /* MASConstraint+Private.h */; };
		2E52B65E92D39CF6B77EA497 /* HeaderManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 2B4C304BF34F7EC6AD4FF4B3 /* HeaderManager.m */; };
		2E6D602A5519F513ACEF7C03 /* FillerBus.h in Headers */ = {isa = PBXBuildFile; fileRef = A79FF5F6358D7DC093C923D0 /* FillerBus.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2E749424EE092A5E28FA9AFD /* InferLoadStone.h in Headers */ = {isa = PBXBuildFile; fileRef = A0C3F888FCE6646A9D8E570D /* InferLoadStone.h */; };
		2EAEB91FDE551EA7726E24B7 /* SDWebImageTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = 59128F9A580DC9546B10909C /* SDWebImageTransition.m */; };
		2ED030925264D5DB22030FFF /* FloaterBendTerabytesCopperTaggingReasonViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 4CE7D497208591F65E3B51B5 /* FloaterBendTerabytesCopperTaggingReasonViewController.h */; };
		2F46882F5C20CB72D1F6D148 /* SobAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 119476203B3EE7F1F6B1F86B /* SobAlertView.m */; };
		2F877BF14C512F76A2575C20 /* PenNetwork.h in Headers */ = {isa = PBXBuildFile; fileRef = 011282B95EB00976868F5474 /* PenNetwork.h */; };
		2F97E63EC37D5DE9A41031CC /* SDWebImageDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = C879A314FF2F9FADA35B7CB1 /* SDWebImageDownloader.m */; };
		2FC0E975B987732224AAD866 /* YouWireCupCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B780ED46527409568520807B /* YouWireCupCell.m */; };
		302836A707A305A4EBDEAA75 /* LiveStyleInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = D75D6974ED3BBEB39F8B882B /* LiveStyleInfo.m */; };
		302F8E7F25FDC783D65BD5BD /* SendEraBlobRet.m in Sources */ = {isa = PBXBuildFile; fileRef = 826A397D003A8BBB583475B7 /* SendEraBlobRet.m */; };
		302FA6187AB4BC64E86BC631 /* SDGraphicsImageRenderer.m in Sources */ = {isa = PBXBuildFile; fileRef = 03265601EED058DA6453672E /* SDGraphicsImageRenderer.m */; };
		323DC6522A3402A546204165 /* SDWebImageCompat.m in Sources */ = {isa = PBXBuildFile; fileRef = AA6C9D551DD261FBE522E2ED /* SDWebImageCompat.m */; };
		33186E196E5104FA25A4CAC7 /* SDImageAWebPCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = D033E2451C9BB3548B44FAA9 /* SDImageAWebPCoder.h */; };
		334B85E3683D3E13EA010DA4 /* RawUpsideOdd.h in Headers */ = {isa = PBXBuildFile; fileRef = 621613F59D0A1240676C9C33 /* RawUpsideOdd.h */; };
		334B92915240EC8337B7D098 /* UIImage+OwnImage.m in Sources */ = {isa = PBXBuildFile; fileRef = 655FF22C8697C0ECFE0DE284 /* UIImage+OwnImage.m */; };
		3351C5EAAD5C49E0DCF6989B /* MQTTStrict.m in Sources */ = {isa = PBXBuildFile; fileRef = 7E1C4A1E26051D2535645C05 /* MQTTStrict.m */; };
		33660A7B9E719FAB2FDE4582 /* SayUseTurn.h in Headers */ = {isa = PBXBuildFile; fileRef = 8EA4F67EF7A7425A5363BED3 /* SayUseTurn.h */; settings = {ATTRIBUTES = (Public, ); }; };
		344A19548029F69430B126FC /* HeaderManager.h in Headers */ = {isa = PBXBuildFile; fileRef = EF6EC7506679990D58D356A6 /* HeaderManager.h */; };
		3494F215E10E58402357B878 /* MQTTCFSocketTransport.h in Headers */ = {isa = PBXBuildFile; fileRef = 929825736A2C48EE78582E27 /* MQTTCFSocketTransport.h */; };
		35D8C6601E9786A64CE61034 /* MarkupInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = A6A4F39B3E082AA46A7EAE3E /* MarkupInfo.h */; };
		3607E874047948E6FB6A47CC /* MQTTDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = CAEACE0711E5978589266D6A /* MQTTDecoder.h */; };
		36F84E894256DF4A5E5122D8 /* SpeakPaperFair.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A8C676CD9A57895ED3B026D /* SpeakPaperFair.h */; };
		37B909CEC6B9321344D7FCE3 /* TowerHisManager.m in Sources */ = {isa = PBXBuildFile; fileRef = E5F0F8109E51B91DC6607DC6 /* TowerHisManager.m */; };
		37EBDEE2A88AD621E10F2E67 /* MQTTMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = D0FC3A44A8F54271DCB2F555 /* MQTTMessage.m */; };
		385323F3C28080D36A7F5645 /* FoodHoursHave.m in Sources */ = {isa = PBXBuildFile; fileRef = 6340D0B9C30B559043DD35ED /* FoodHoursHave.m */; };
		3928F8EB51D1C956931571E7 /* NSLayoutConstraint+MASDebugAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 7B660278A8474DFC044B942B /* NSLayoutConstraint+MASDebugAdditions.h */; };
		3A715DCB13A0771FF51798B2 /* SDImageTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 4AB7ABD372122B19DE58E776 /* SDImageTransformer.m */; };
		3B4E9FF0EB419B36DE452E6F /* SDAsyncBlockOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 1E9E13758ADED3A4739A87A0 /* SDAsyncBlockOperation.h */; };
		3BD54DC38C682D229C62F42E /* MakerCarbon.h in Headers */ = {isa = PBXBuildFile; fileRef = 4CD08BB09A2A00613342E3F0 /* MakerCarbon.h */; };
		3CC752E6290222458FBEBEF6 /* NSObject+WayModel.h in Headers */ = {isa = PBXBuildFile; fileRef = E2CB455F5978E6B3CF42D5C3 /* NSObject+WayModel.h */; };
		3E89E2540481BE8FE374C9AA /* PivotForManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 0E57C97795C3156ECA8E396B /* PivotForManager.h */; };
		3ED5F6962692C6E1D4BD4E02 /* HomePhraseRankCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 084C63BA5351BECA0417E9D9 /* HomePhraseRankCell.m */; };
		3F12E2563E1683F736DE072F /* MQTTLog.h in Headers */ = {isa = PBXBuildFile; fileRef = E6774C23291339746F26C8B3 /* MQTTLog.h */; };
		3F6EF2B9BBDB49FEEDBDCD65 /* UIButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 1B45373C923CBF4D8EC610E7 /* UIButton+WebCache.m */; };
		406D836763F1B5BA25477AAF /* RareRaceColor.h in Headers */ = {isa = PBXBuildFile; fileRef = 07AB7BF57284036AC3E7843C /* RareRaceColor.h */; };
		40A9C58A1ECAE6E3631256F2 /* FrequencyInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 8FDCD3EF8AB3AC2164CB8618 /* FrequencyInfo.m */; };
		40FB843DAF398E697FDC9013 /* MQTTClient.h in Headers */ = {isa = PBXBuildFile; fileRef = 9D1B2BF8242D832E71C2229A /* MQTTClient.h */; };
		420A5532243A872F944226D7 /* HitUnableManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DDE760ED9A7F247D97D9A773 /* HitUnableManager.m */; };
		425AC8D0A195C565E6BDCDA5 /* SDImageIOAnimatedCoderInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = 241130EE051DD80C02AE1B3A /* SDImageIOAnimatedCoderInternal.h */; };
		42BB719508C1C0E1728AFCD3 /* UIView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F6F83D66EB1ADC89D69AFE66 /* UIView+WebCache.m */; };
		42DFF3BF3C24FE6914510C91 /* MQTTSSLSecurityPolicyTransport.h in Headers */ = {isa = PBXBuildFile; fileRef = 10EDC15ED349579399FA44AF /* MQTTSSLSecurityPolicyTransport.h */; };
		4435EABA834BCECEDD1CCCFC /* MQTTSessionSynchron.m in Sources */ = {isa = PBXBuildFile; fileRef = 34C8B3F7899686F0381905C0 /* MQTTSessionSynchron.m */; };
		44A2039FB004080FD9CEACE0 /* ReconnectTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = A45BCE8A0AAFD9EF4320949C /* ReconnectTimer.m */; };
		4504E12D256F1671CBA2E119 /* SDWebImageDownloaderRequestModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = FE20C76967514DAFC4526ACD /* SDWebImageDownloaderRequestModifier.h */; };
		452D02290CC513C40723D786 /* MASViewConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 48ACCB832BE1756214BAAF51 /* MASViewConstraint.m */; };
		456E9636D355811F2DF5D8A0 /* View+MASShorthandAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 47A2E9BCA649D320CFDCA64D /* View+MASShorthandAdditions.h */; };
		45E2D915818F557F1623EB37 /* InverseArmAreNiacinAllocator.h in Headers */ = {isa = PBXBuildFile; fileRef = B9035013C150E1B068E40367 /* InverseArmAreNiacinAllocator.h */; };
		46EC93916F8ED63E8D9ECEBC /* UIImage+Metadata.h in Headers */ = {isa = PBXBuildFile; fileRef = 57DB2E4A7DD8B57D95569C8F /* UIImage+Metadata.h */; };
		472477DEB549EA877793E4F6 /* AtomHasDogViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A8E2731B992E7D07432E8E22 /* AtomHasDogViewController.m */; };
		47AE926D3D31536176A3931E /* TrustPartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1194455A0B1FDE7910690830 /* TrustPartViewController.m */; };
		48424EDAD434677DCEE66F4B /* ShapeWinNet.h in Headers */ = {isa = PBXBuildFile; fileRef = DCE4CACD3FAAD6725A7C1C08 /* ShapeWinNet.h */; };
		48645C2F4A9C5F910C8BBF68 /* NSError+FootnoteOdd.m in Sources */ = {isa = PBXBuildFile; fileRef = 5081ADE5478E2895C42CCD71 /* NSError+FootnoteOdd.m */; };
		48A96E32AB61B7D8781EE9A2 /* ContactAlienHusbandSuperiorsEstimated.h in Headers */ = {isa = PBXBuildFile; fileRef = 9FA029A8CE0393069445EE34 /* ContactAlienHusbandSuperiorsEstimated.h */; };
		48B2B814FCE579BF684F902E /* NSData+PinThat.m in Sources */ = {isa = PBXBuildFile; fileRef = 351DADE01FACBE096758CCB1 /* NSData+PinThat.m */; };
		490F39E034734E2D084C6738 /* BigPreviousManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 0B0A101A5DD125FBD2C93468 /* BigPreviousManager.h */; };
		495BB12F4497146CDD1A7E19 /* LiveStyleInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 063DFD81C5A95D79BB63410D /* LiveStyleInfo.h */; };
		4A2C3FB79EB44E750FC1555F /* SDWebImageDownloaderRequestModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = 79139984A151B08E3973FB12 /* SDWebImageDownloaderRequestModifier.m */; };
		4A57090C44FE1743F6853A31 /* IrishAlbanian.h in Headers */ = {isa = PBXBuildFile; fileRef = EE91E2BC708C254977FCFA0E /* IrishAlbanian.h */; };
		4AADD3F8BF8A9C961C7AE435 /* SDImageIOCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E6F6EB14C3ECF3A931AB678 /* SDImageIOCoder.m */; };
		4C0009868AF9B78E6D9F8AA5 /* MusicalManager.h in Headers */ = {isa = PBXBuildFile; fileRef = E8C659EF4AB3C294AD8876DB /* MusicalManager.h */; };
		4C6DA9EB064CB0528F5C09FE /* NSObject+WayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B3F4339078396977D1071E9 /* NSObject+WayModel.m */; };
		4C81DAAAFF90EF45DC6B6E69 /* SDWebImageDownloaderConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 54AFEA69507B76536B3A7CA3 /* SDWebImageDownloaderConfig.h */; };
		4CB95F57157BFE5F5D2601FB /* MASUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = EFEA9B14BAAE8070ECC9E14F /* MASUtilities.h */; };
		4CE0C76753BA53506C63A66C /* ModeRunningDelayRaiseStrokingTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 12735A0C46854BFF4033F457 /* ModeRunningDelayRaiseStrokingTool.m */; };
		4D104BA88C532961FB83F47C /* MQTTProperties.h in Headers */ = {isa = PBXBuildFile; fileRef = 0C8A72E2AA673E981782D570 /* MQTTProperties.h */; };
		4D13B8F10ABDFE0AE602FF6E /* SDImageCodersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = EAC32D15A3C3520F677E420A /* SDImageCodersManager.h */; };
		4D91964573554B9609DDAA72 /* LeapHueHeapSunViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 62BE62A55246194110072272 /* LeapHueHeapSunViewController.m */; };
		4E01C857726A5D2CC0F18167 /* SDImageCodersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 62260EF186CE02377AB68E43 /* SDImageCodersManager.m */; };
		4F4A74A2A814411F247746EA /* RedoBackupMaintainPhraseHormone.h in Headers */ = {isa = PBXBuildFile; fileRef = B3EF13F323C89D82167485F4 /* RedoBackupMaintainPhraseHormone.h */; };
		4F7B54620D2B78E6A2AFD42F /* DueSumZoom.h in Headers */ = {isa = PBXBuildFile; fileRef = 22FB6D64825E583D595501A0 /* DueSumZoom.h */; };
		4FBCF62671ABB440D9DC427F /* UIImage+GIF.m in Sources */ = {isa = PBXBuildFile; fileRef = 5EF58CA7DAB4F981B425F783 /* UIImage+GIF.m */; };
		50079C96565227CDA6F05D4F /* ProcessCircularTemporalCountryFlag.m in Sources */ = {isa = PBXBuildFile; fileRef = 18036C457522B87FD2F487AE /* ProcessCircularTemporalCountryFlag.m */; };
		508D302B5685D776C3C30041 /* GroupedCollectViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 7F786B34C25438FE40A1BC80 /* GroupedCollectViewController.h */; };
		50FEFE2CD44A4DD95DA83EE9 /* SDImageCachesManagerOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 49C280B0F3924999B94EBB23 /* SDImageCachesManagerOperation.m */; };
		51DF28D31DCD7EA5137EA1C5 /* ConfirmProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = C1F3DBAA72D1C0491BE25FC8 /* ConfirmProtocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		520948E8D08B8A8B1E0F1D65 /* SplatManager.m in Sources */ = {isa = PBXBuildFile; fileRef = E88C0ED9F44BCDED912961B6 /* SplatManager.m */; };
		52C862886BE567091B4621B8 /* Adobe.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E3DF17F33E037D4F4C64ABD /* Adobe.m */; };
		541A006C365214E367144649 /* DueLockAirTooViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 3B413606488EFABAB1B9DDF2 /* DueLockAirTooViewController.h */; };
		54DA993D5430D87E122157B7 /* GroupedCollectViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 60B213F8F4BDE0AB141A3966 /* GroupedCollectViewController.m */; };
		54DB16D6BDC669A5A36C68FA /* SDAnimatedImage.h in Headers */ = {isa = PBXBuildFile; fileRef = B05E6EF5C880AB75F6314663 /* SDAnimatedImage.h */; };
		55102C1A8DBD52D3AB33AF2E /* SDImageAPNGCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 774B65842412B1B4A37BCFB0 /* SDImageAPNGCoder.m */; };
		5650634D8E32970488798BEA /* SDWebImageError.h in Headers */ = {isa = PBXBuildFile; fileRef = 32856A0DC097D147BCE05959 /* SDWebImageError.h */; };
		566383CBEA4066F9599975C2 /* MQTTCFSocketEncoder.h in Headers */ = {isa = PBXBuildFile; fileRef = B4F88381B59032F0E6AEB62F /* MQTTCFSocketEncoder.h */; };
		56930FA4A002A6B88B0754C6 /* SDFileAttributeHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = F75AC053055E4FEFBC9455D2 /* SDFileAttributeHelper.h */; };
		56C9C4E8B8737B85D964D22E /* SDmetamacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 35489DC6C272CFB63EF56B36 /* SDmetamacros.h */; };
		570891A6718DCB62A3FE654A /* NSString+SheHectares.m in Sources */ = {isa = PBXBuildFile; fileRef = E679753715D0341E66919A22 /* NSString+SheHectares.m */; };
		5761F200354AB26079EAE89A /* SDImageCacheConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = C3BC3DAF0C82C2A0ACD5A3D0 /* SDImageCacheConfig.h */; };
		584A87E295C3186CB5022B49 /* YoungerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 3F89F36B7B42FCE0CCB8D46E /* YoungerViewController.m */; };
		584EDDE4FA0C3A0C2823A8C8 /* SDAnimatedImagePlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 5CC542103E1AF74C78DDC910 /* SDAnimatedImagePlayer.h */; };
		585D4E8C521BE02039E7AA51 /* FixtureDrive.m in Sources */ = {isa = PBXBuildFile; fileRef = 30CC03B955A70C1E299D9E97 /* FixtureDrive.m */; };
		58BC352825A76DA382D167C7 /* MQTTLog.m in Sources */ = {isa = PBXBuildFile; fileRef = 7CB6BF8CA0FB51CB187EF20E /* MQTTLog.m */; };
		59228357FE34D19AEA3E1EE8 /* CountViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = BBE6651B03CDB86C221E2664 /* CountViewController.m */; };
		597E014119C5D530945C8760 /* ForegroundReconnection.m in Sources */ = {isa = PBXBuildFile; fileRef = 6628491DC935B15A7CA883E9 /* ForegroundReconnection.m */; };
		59B7F391CE6FCE9F3058B2AB /* UIView+WebCacheState.m in Sources */ = {isa = PBXBuildFile; fileRef = 75A4BDB2F625D52CED26FCAA /* UIView+WebCacheState.m */; };
		5AEFE2CF58752EED97428226 /* SemicolonViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 2160314877FA80BAD837DA84 /* SemicolonViewController.h */; };
		5B8DDB55D281020B4A8EABAF /* SplatManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 952F3717A40F0A4F70C0BD03 /* SplatManager.h */; };
		5C80C59CD7E2D3507E724ADD /* BitOwnPathView.m in Sources */ = {isa = PBXBuildFile; fileRef = 41F160ED4AEA638266D25C54 /* BitOwnPathView.m */; };
		5C93409BC9BA62D610CCB4A5 /* SDAnimatedImagePlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = F93326F810C2C007AA7187A6 /* SDAnimatedImagePlayer.m */; };
		5CCE0F6B4B765EA5FDDA4F8A /* UIImage+ExtendedCacheData.h in Headers */ = {isa = PBXBuildFile; fileRef = 1F5EF08D30C2ADD28A6FEFB2 /* UIImage+ExtendedCacheData.h */; };
		5D53F0578D2CE57EF0566D39 /* ScriptsFourthViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 9CCD6207DE38B438F42ACAE9 /* ScriptsFourthViewController.h */; };
		5DF5A2DD24398E016BBE445C /* MQTTSSLSecurityPolicy.m in Sources */ = {isa = PBXBuildFile; fileRef = 9F1921F51A5F4C8C27A677DF /* MQTTSSLSecurityPolicy.m */; };
		5E1F9656E968187B9DF89642 /* BevelNarrative.m in Sources */ = {isa = PBXBuildFile; fileRef = 075A98A85E24ADCAB95E62DA /* BevelNarrative.m */; };
		5E98EF46358885356FD71567 /* SlavicTapTextField.h in Headers */ = {isa = PBXBuildFile; fileRef = 610ACB12959C73B721D25E07 /* SlavicTapTextField.h */; };
		5E9D7354F18E031613EBC63F /* YouWireCupCell.h in Headers */ = {isa = PBXBuildFile; fileRef = C8D7F79A01761A9B77B0D88F /* YouWireCupCell.h */; };
		5F5326221FB2CAC64300FD72 /* SDImageLoader.h in Headers */ = {isa = PBXBuildFile; fileRef = 8296299DD79244266387E372 /* SDImageLoader.h */; };
		601B27AC9089CC30ED9984F9 /* SlowDaySeekThe+HitOut.m in Sources */ = {isa = PBXBuildFile; fileRef = 003D730C055AAC937DCEFA41 /* SlowDaySeekThe+HitOut.m */; };
		608CA36E0CC14A776A79205A /* SDImageGIFCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 0C013B661469AC985B667BB8 /* SDImageGIFCoder.h */; };
		60A6DCD9D9F719FB07787F6B /* SlowDaySeekThe+EraDecide.m in Sources */ = {isa = PBXBuildFile; fileRef = 6B08B877A13FDDA534B430FC /* SlowDaySeekThe+EraDecide.m */; };
		619AE0C25EE50FB78450F1D1 /* TagSidebarView.h in Headers */ = {isa = PBXBuildFile; fileRef = 28FF93557AC2F072EC9D9AA0 /* TagSidebarView.h */; };
		624B960837589CDB83D3FF88 /* ParameterStillCalculateAdverbBoldface.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D5D6452E883910714CC90D8 /* ParameterStillCalculateAdverbBoldface.m */; };
		624C32486459233383636037 /* ConstantAdditionsValueRadioMouseYou.m in Sources */ = {isa = PBXBuildFile; fileRef = 910F5B4B70CBFD2210325BDB /* ConstantAdditionsValueRadioMouseYou.m */; };
		63467D5E10CF9CC0C93F39A8 /* SDImageGraphics.h in Headers */ = {isa = PBXBuildFile; fileRef = AA3F4130C5D1930F0F65CC3D /* SDImageGraphics.h */; };
		63E12F4D7C8E7AA1BCF25625 /* SDImageCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 8A9D158F1F0B06F8FF80A7AF /* SDImageCoder.m */; };
		658F5334E776029D8D9B6EA2 /* SDAsyncBlockOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 440ADF7DB1BE50D635323F1D /* SDAsyncBlockOperation.m */; };
		65B29C0CF8077A4845901A83 /* SlowDaySeekThe+EraDecide.h in Headers */ = {isa = PBXBuildFile; fileRef = 6521DF77CCE57BB721205D01 /* SlowDaySeekThe+EraDecide.h */; };
		65C9CBBF90BC00F1F4771A98 /* SDImageCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 734D8BEFB29ABD07DE305303 /* SDImageCache.h */; };
		6630D64C920443EE0C9DFC6F /* IrishAlbanian.m in Sources */ = {isa = PBXBuildFile; fileRef = 57BE0F078288DFE28782B334 /* IrishAlbanian.m */; };
		66603DBC4BAC856C5554D8BB /* InteractItalicBouncingRegionsDimensionModel.h in Headers */ = {isa = PBXBuildFile; fileRef = E7AAD7321C9E92E3C77BC3A0 /* InteractItalicBouncingRegionsDimensionModel.h */; };
		667672100F9E59A956C1810C /* NSLayoutConstraint+MASDebugAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A73451FB7580B8846B64D48 /* NSLayoutConstraint+MASDebugAdditions.m */; };
		66D0C4DB8FDD4ED0C19B7FCF /* SDImageGraphics.m in Sources */ = {isa = PBXBuildFile; fileRef = F5CBC881AD560B0186CF9E39 /* SDImageGraphics.m */; };
		67274F2BE0196FFE66B1AF4F /* MQTTSSLSecurityPolicy.h in Headers */ = {isa = PBXBuildFile; fileRef = 108037828C063003BF48489F /* MQTTSSLSecurityPolicy.h */; };
		68199695E767D3FA63D47FBC /* SpeakPaperFair.m in Sources */ = {isa = PBXBuildFile; fileRef = CF5B15F2791506236A7E8AF4 /* SpeakPaperFair.m */; };
		683E00929CA8593987F0BA4D /* SumBuffersVisitAlbumBuildViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 1B18831364CB0E981BA89C03 /* SumBuffersVisitAlbumBuildViewController.h */; };
		691F585AB7016BB3D595E9F7 /* MQTTMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F0844873B4C513741A2DC0F /* MQTTMessage.h */; };
		6A066E1BAE76B8A038E00240 /* ConstantAdditionsValueRadioMouseYou.h in Headers */ = {isa = PBXBuildFile; fileRef = 76F559DDE845E315EDDCE279 /* ConstantAdditionsValueRadioMouseYou.h */; };
		6ACE6A3B2F113D33BCBDA9FE /* MQTTSessionLegacy.h in Headers */ = {isa = PBXBuildFile; fileRef = 02DD979D5FE1048F555DD3C7 /* MQTTSessionLegacy.h */; };
		6AE30085A0B43AFD6B857F0A /* DownloadBondLongSucceededTen.h in Headers */ = {isa = PBXBuildFile; fileRef = 83F42FB69264565B5A4F7AC8 /* DownloadBondLongSucceededTen.h */; };
		6B48BC16B29C6473A7AECB4F /* SDWebImageDownloaderResponseModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = AD008523C8762CE3FDE9E126 /* SDWebImageDownloaderResponseModifier.m */; };
		6BA9E8250E834A58D5FA3F94 /* NSBezierPath+SDRoundedCorners.h in Headers */ = {isa = PBXBuildFile; fileRef = 6A64E0D7795BE83EFB33E790 /* NSBezierPath+SDRoundedCorners.h */; };
		6BBD59DF02260BC247977ED1 /* SDFileAttributeHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 80F029319B8502440B280082 /* SDFileAttributeHelper.m */; };
		6CAD80BBAECD38DAC5FDC0D3 /* SDWebImagePrefetcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 73E486C243EA0F69A3DB98DA /* SDWebImagePrefetcher.m */; };
		6D2EB60631A6B26D9F6D78B2 /* MQTTSSLSecurityPolicyEncoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 532F458511582393B7DD2EC9 /* MQTTSSLSecurityPolicyEncoder.m */; };
		6D913532D1F71249EDBF1345 /* DirectTouchAction.m in Sources */ = {isa = PBXBuildFile; fileRef = CBF6933420F4D34E87C3BD95 /* DirectTouchAction.m */; };
		6E04DFF567165AEBE158A037 /* Masonry.h in Headers */ = {isa = PBXBuildFile; fileRef = 6845270D58C1DB53E2340179 /* Masonry.h */; };
		6E599A3D94653B0B40671593 /* SDDiskCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 09590734533E49DE06264FE9 /* SDDiskCache.h */; };
		6E683A2FB0B2D4C54E6688EF /* CellTapCan.m in Sources */ = {isa = PBXBuildFile; fileRef = 80890ED257B46A7E4D6D2B37 /* CellTapCan.m */; };
		6F1ECEA74CB21105B22D4713 /* SDImageTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 918825460FD0F4E77785BD33 /* SDImageTransformer.h */; };
		6FD3D68C659E932DB4C54897 /* MakerCarbon.m in Sources */ = {isa = PBXBuildFile; fileRef = 8AD8264D8CD66F6C9A6C2CB5 /* MakerCarbon.m */; };
		706B2BD4**************** /* TildeScanPlaneWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = 2A3753D7CB6B20650CF003C2 /* TildeScanPlaneWindow.m */; };
		70A3BDE247E0D22477796317 /* DryBarInterKey.m in Sources */ = {isa = PBXBuildFile; fileRef = 8B2BDD97E75E6A0AAE4F153F /* DryBarInterKey.m */; };
		7165935B079FBD9B1B9DAE42 /* SDWebImageDownloaderDecryptor.m in Sources */ = {isa = PBXBuildFile; fileRef = D22838B3AA8DCCA73C62A211 /* SDWebImageDownloaderDecryptor.m */; };
		71DF3FD26176B738D02B3B3B /* BigPrologHasBankersWon.h in Headers */ = {isa = PBXBuildFile; fileRef = 99A0329A38A350A2284E86CE /* BigPrologHasBankersWon.h */; };
		7262D13E3C8CA8A02BA4156F /* NSError+FootnoteOdd.h in Headers */ = {isa = PBXBuildFile; fileRef = 9E8D772A2CC7288306A9F533 /* NSError+FootnoteOdd.h */; };
		72F17D77DC3A3887739CBE97 /* MQTTPersistence.h in Headers */ = {isa = PBXBuildFile; fileRef = 8F443F99646BFBF13B997E50 /* MQTTPersistence.h */; };
		744DA262D8D3D27C18D5A069 /* MQTTCFSocketEncoder.m in Sources */ = {isa = PBXBuildFile; fileRef = B3B861B24B94AB2B0F4FB89E /* MQTTCFSocketEncoder.m */; };
		746E70EE580C05A2E190022A /* TurnWordTipViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = C222DC51AC392A3A2A7E03CB /* TurnWordTipViewController.m */; };
		749B91E4126C57432D662120 /* SDDiskCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 031988A0A1CB41E50B93A56C /* SDDiskCache.m */; };
		74C39B1802DC5C8C0E4DB7D1 /* MASViewAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = 5AAFAE63C658BA6E9975FE1A /* MASViewAttribute.m */; };
		74E6F5AC29C8CF04B6DB0946 /* BevelNarrative.h in Headers */ = {isa = PBXBuildFile; fileRef = 3187FED2E7DD23E9195A0903 /* BevelNarrative.h */; };
		7656AE37A911231E1D9D2E02 /* HandledEggConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 2C19280CEAF6296A042CD9FF /* HandledEggConfig.h */; };
		76AF0E8427D667BFC877DC8E /* BendRope.h in Headers */ = {isa = PBXBuildFile; fileRef = 9382D750D033E0535E7A4FCB /* BendRope.h */; };
		77456B72EAFFF0B13EB9209D /* SDInternalMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = DD839CEE8E1CF135C858713F /* SDInternalMacros.h */; };
		77C49ECD346605E8F254A7A6 /* RedoBackupMaintainPhraseHormone.m in Sources */ = {isa = PBXBuildFile; fileRef = E3EEA740232D3DBCB1A85F08 /* RedoBackupMaintainPhraseHormone.m */; };
		782EA787BC4FB6954A0030DC /* SumBuffersVisitAlbumBuildViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 78AC831585293B779B63AA7D /* SumBuffersVisitAlbumBuildViewController.m */; };
		78301F295B7231934993E3A6 /* SDImageAssetManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 786F1B2CFE1C4C22C76572FA /* SDImageAssetManager.m */; };
		78DA4A05EFF62481CB4C615A /* SDImageHEICCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = DC9B41D076BEFF57534297AC /* SDImageHEICCoder.h */; };
		79915D47E74AF70442DC8A8B /* UIView+WebCacheOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 01E9CF12555DAB3F6F8023B5 /* UIView+WebCacheOperation.m */; };
		7A6AE46ED26946DACC42A79E /* SDWebImageDownloaderConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C8E03C183B7F0BFCCF93D3C /* SDWebImageDownloaderConfig.m */; };
		7A97D13DE781C958695E776C /* PenNetwork.m in Sources */ = {isa = PBXBuildFile; fileRef = 499F09CF234117D187F8CE3C /* PenNetwork.m */; };
		7BEC7597E7145E3D6FFEFC7A /* ScriptsFourthViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = BF8A9E4E49E4371F6891B8AF /* ScriptsFourthViewController.m */; };
		7BECFEA388539C55F3B52206 /* SDImageCoderHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 4139A0A3A460E14F0E2E6829 /* SDImageCoderHelper.m */; };
		7C23BC77CE436289939D563F /* HitUnableManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F1DC8B25B25146A5EA05DFD /* HitUnableManager.h */; };
		7C54A94AD6692D8892D87103 /* UIImage+MultiFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = 7B4855EC1E2CF1CFB78CB727 /* UIImage+MultiFormat.h */; };
		7D83723A33DD25FA3DF0435B /* NSData+PinThat.h in Headers */ = {isa = PBXBuildFile; fileRef = CE4786DE6961098D8E6CCFAC /* NSData+PinThat.h */; };
		7DA01C3EB033D2F83E7542E9 /* SDWebImageCompat.h in Headers */ = {isa = PBXBuildFile; fileRef = D18AD52E7501C087DFF4064A /* SDWebImageCompat.h */; };
		7DB646D7BB190EFF836411CC /* TwoVideoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 2D0EC61AADCC9C4F22F80906 /* TwoVideoViewController.m */; };
		7DFB7208FC48740ACD939510 /* SDImageAssetManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 046CF37B220240DB19ECA78A /* SDImageAssetManager.h */; };
		7E64DDD7B238E4706715299C /* Dominant.h in Headers */ = {isa = PBXBuildFile; fileRef = ABA4989C34A6160D10B93275 /* Dominant.h */; };
		7E78F694731E3E705A8B59E0 /* UIImageView+HighlightedWebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A12E16479067FC78C36FBA7 /* UIImageView+HighlightedWebCache.m */; };
		7F1D5D587601B53F6799A671 /* RetMenuLastPanInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 752E3F6D071D3BCFDEE6736B /* RetMenuLastPanInfo.m */; };
		801D09F1797D41AB84BD9454 /* DogTempPin.m in Sources */ = {isa = PBXBuildFile; fileRef = CEE505EED78D727EB9D40F04 /* DogTempPin.m */; };
		803974A8827A180961C6FF77 /* TildeScanPlaneWindow.h in Headers */ = {isa = PBXBuildFile; fileRef = 80D849B37F13C2B97CE1FA9E /* TildeScanPlaneWindow.h */; };
		80BC63356A2D60493D8AD7D0 /* UIColor+GetColor.m in Sources */ = {isa = PBXBuildFile; fileRef = D54D31DBAE03F84E6F8F3925 /* UIColor+GetColor.m */; };
		816A98DA92BABDB84FD9E9EC /* SDDisplayLink.h in Headers */ = {isa = PBXBuildFile; fileRef = 26DB1986EBEFCCF6ACBC952B /* SDDisplayLink.h */; };
		832BE7E5AA70D452384207CB /* SinPushCapFlowButton.m in Sources */ = {isa = PBXBuildFile; fileRef = B4F228E6D28A14DB5E75C3E1 /* SinPushCapFlowButton.m */; };
		841BC7997858969724CA8B21 /* UIImage+MemoryCacheCost.h in Headers */ = {isa = PBXBuildFile; fileRef = EE25814C42D01892C4082552 /* UIImage+MemoryCacheCost.h */; };
		84963E001B51CCA1997FEC88 /* XXGProtocolLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C10FA3D701D9C37BCD6AB1F /* XXGProtocolLabel.m */; };
		84DF91033597C6A0B678B4C9 /* SDWebImageDownloaderOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 577A8CB5C71872A866960B40 /* SDWebImageDownloaderOperation.m */; };
		8557C8DC70061D0682F50828 /* SDWebImageDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 4088889F0E7F68BB932C4EB6 /* SDWebImageDefine.m */; };
		85807CC17CA2819AC1B1E2F8 /* UIButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 6D5DC52AFB94DD9DF43F08D8 /* UIButton+WebCache.h */; };
		8601F93910A84D1FB8BB1D36 /* PopPickMayViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 3881C1C23C5DE581C44DAA68 /* PopPickMayViewController.h */; };
		8610F33DB40265B9C3C8D721 /* UIDevice+BarDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 2F21AE936CD54329FF18C248 /* UIDevice+BarDevice.m */; };
		863DB0B18B5D08523F371413 /* VerboseCombine.m in Sources */ = {isa = PBXBuildFile; fileRef = 704A35DF36C9A6988B35B4DE /* VerboseCombine.m */; };
		8641DB125DC3A2EF2110C5A2 /* SeeVitalModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 73A54D350AB1050302E45F7A /* SeeVitalModel.m */; };
		8650260BA635FFAE5E55E6C7 /* PronounLegibleDefinesFlightsAttitudeBit.h in Headers */ = {isa = PBXBuildFile; fileRef = 60501A564493ACE768E36507 /* PronounLegibleDefinesFlightsAttitudeBit.h */; };
		86C16D060DEF9A76D18ADEE6 /* MASViewConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = BF7002E9182182E66DDAE441 /* MASViewConstraint.h */; };
		86D710F96299B3234ACD46E9 /* SDAnimatedImage.m in Sources */ = {isa = PBXBuildFile; fileRef = 761387AED213CDF98CA96E54 /* SDAnimatedImage.m */; };
		8769D85D5AF4CCD6C1B4A075 /* GCDTimer.h in Headers */ = {isa = PBXBuildFile; fileRef = 0E69F8FD195C6E96E3CBF5C2 /* GCDTimer.h */; };
		8903D65A5D2812C515C722B6 /* MarkupInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = B63BC79C78EF011BE5341BDB /* MarkupInfo.m */; };
		8924DDC9A4C0215B77E6A1E9 /* SDWebImageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = C7CED6E36657A83A1125619A /* SDWebImageManager.m */; };
		8A5822C1826A9910D62F6DE9 /* ParameterStillCalculateAdverbBoldface.h in Headers */ = {isa = PBXBuildFile; fileRef = 04A8D6BF6F61F10754AA2047 /* ParameterStillCalculateAdverbBoldface.h */; };
		8A5A0F323AB9BE61B6F20EBE /* ArtworkModel.h in Headers */ = {isa = PBXBuildFile; fileRef = C21EA3B199C7ECAC4F04631F /* ArtworkModel.h */; };
		8AA9D52F9824CF0823683584 /* ReconnectTimer.h in Headers */ = {isa = PBXBuildFile; fileRef = 960445813D0E8366023112B0 /* ReconnectTimer.h */; };
		8AE8297F4457559D67E9DDD9 /* MQTTSSLSecurityPolicyDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = E7D264E4EAB565F1282B4BEC /* MQTTSSLSecurityPolicyDecoder.h */; };
		8AF8E8CC877C11F21F8B5BF3 /* MQTTSSLSecurityPolicyEncoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 400ED8AD84D4C888FA8796A0 /* MQTTSSLSecurityPolicyEncoder.h */; };
		8B0D21CD34FF724A2F7E3B79 /* UIImageView+HighlightedWebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = F5290FB23CF5BEFFAB1B4EDE /* UIImageView+HighlightedWebCache.h */; };
		8B1614566F3709F0D519F39D /* DetailedPutManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 520D2EE66A5E9B9AF37E228E /* DetailedPutManager.m */; };
		8B2AE36CF84E1DDC2A509E3D /* UIImage+ForceDecode.h in Headers */ = {isa = PBXBuildFile; fileRef = DC6D736C7D7A708AFF1ED28A /* UIImage+ForceDecode.h */; };
		8B4A2D61D11A1241F21ED1E0 /* SDWebImageIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = 46F2451F446710FB100F9B38 /* SDWebImageIndicator.h */; };
		8B67C8C8DFCBBF07A0F4B4BA /* GarbageAndViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 8555A76523BD1EE55FAC79C3 /* GarbageAndViewController.h */; };
		8C96047F4FDFE328C8E2A3BB /* SDImageCachesManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 806E61F6010E3BF2D703150E /* SDImageCachesManager.h */; };
		8CF13C6D60E31E503EB38038 /* ReportManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2EE48B16358C30D873C7E47A /* ReportManager.h */; };
		8E2859D9A51FB8227C1C03CC /* HomePhraseRankCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 16D1254D2725C3BFC6325B8E /* HomePhraseRankCell.h */; };
		8FBD66B5320A232782D40A14 /* ViewController+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 98EC7A95842807C3A4ABC105 /* ViewController+MASAdditions.m */; };
		9064CECB0672A71736F18D17 /* NSString+BedLow.m in Sources */ = {isa = PBXBuildFile; fileRef = 712E15DCDE60A47774B1A56F /* NSString+BedLow.m */; };
		90A26749916DDFB87D87C3CB /* SonAssetWinNetCell.h in Headers */ = {isa = PBXBuildFile; fileRef = DE5F03E4D6E4D90258589F1F /* SonAssetWinNetCell.h */; };
		912E4DA5D4884DC6AA38483D /* SDImageLoadersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2940FFAF377927CD8B8147A7 /* SDImageLoadersManager.h */; };
		925A9DC81C65D6501DF0F76E /* ViewController+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = D1162E48F16A5587B5834A55 /* ViewController+MASAdditions.h */; };
		926181795457D7F4132A811D /* SlowDaySeekThe+Speak.m in Sources */ = {isa = PBXBuildFile; fileRef = 435EB89050EB338B73E23EA9 /* SlowDaySeekThe+Speak.m */; };
		93003918EC4C4EF6693E9C44 /* LoopsEyeRomanController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0975D156CFF4E760E0DD35C8 /* LoopsEyeRomanController.m */; };
		93385DD1CA745891F3ACCC94 /* SDImageCachesManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 788DAD5EBC5D957687661E7D /* SDImageCachesManager.m */; };
		936BB04535E08F9D3F86632D /* BitOwnPathView.h in Headers */ = {isa = PBXBuildFile; fileRef = 87936D27E5C11C7234DE055D /* BitOwnPathView.h */; };
		94FBB65C4195A2FE2C774936 /* InteractItalicBouncingRegionsDimensionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 506FE79C8374AE774CCB2D39 /* InteractItalicBouncingRegionsDimensionModel.m */; };
		952EB2B464AAA6CCDDD73AD1 /* SayUseTurn.m in Sources */ = {isa = PBXBuildFile; fileRef = F2348336B7C9E975C460AB55 /* SayUseTurn.m */; };
		954806F7D48511D023032475 /* SDWebImageIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = BC20AAC905D0DBA1CED83CBE /* SDWebImageIndicator.m */; };
		959003B7785CF8F3483933CE /* BoxArtsCategoryClickedWrap.m in Sources */ = {isa = PBXBuildFile; fileRef = 0287249B8092EA18ABB7D378 /* BoxArtsCategoryClickedWrap.m */; };
		9696199CDC7858FCD5EFB97A /* DownloadBondLongSucceededTen.m in Sources */ = {isa = PBXBuildFile; fileRef = 45349E2ECA273FB15CC01379 /* DownloadBondLongSucceededTen.m */; };
		96A8AB736243B752B5E612BF /* GCDTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A857E86BDB0F72D9EC1FD6D /* GCDTimer.m */; };
		96DFE139423BDBC0EDA1E4C0 /* SDImageFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = 9F019B5F911FFE0CEA26BD57 /* SDImageFrame.m */; };
		97B33422FF482FDBF763C299 /* RareRaceColor.m in Sources */ = {isa = PBXBuildFile; fileRef = C72851D6305D2CC455F459D4 /* RareRaceColor.m */; };
		9983A7B57E4A8782DC1D2041 /* SlowDaySeekThe.h in Headers */ = {isa = PBXBuildFile; fileRef = 58AAAFDD9D74B5023CDA8EA0 /* SlowDaySeekThe.h */; };
		99894B2CDE347BF19C3F624E /* SDWebImageOptionsProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = 34E1280B1162F5A722224D72 /* SDWebImageOptionsProcessor.m */; };
		9A48451DE194E948248E0622 /* NSData+ImageContentType.m in Sources */ = {isa = PBXBuildFile; fileRef = 2A8641B8D2CB4BC84A3F9C6E /* NSData+ImageContentType.m */; };
		9A50E51A186073778718C83F /* FoodHoursHave.h in Headers */ = {isa = PBXBuildFile; fileRef = AA1FE06D8E08EDF887DBBDC3 /* FoodHoursHave.h */; };
		9A820BBC8AECD3B3F213C599 /* MASLayoutConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 1C9F7EBF29F2DCE54A316389 /* MASLayoutConstraint.h */; };
		9AE573962E046D084BA83579 /* SDAnimatedImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = EFCC696346F2F466955E2F9E /* SDAnimatedImageView+WebCache.h */; };
		9B544172F6A6CDBEB97EE921 /* NSButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 844D053014924000C84552B2 /* NSButton+WebCache.m */; };
		9D369D6CAF470C07021242E4 /* GrossScrap.h in Headers */ = {isa = PBXBuildFile; fileRef = C2EFBAD99545AEDB27629D0C /* GrossScrap.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9D6A3F11D0C086A18114C44C /* OutToast.m in Sources */ = {isa = PBXBuildFile; fileRef = D80AD2662A0EFE9686BC96A6 /* OutToast.m */; };
		9D6A49E305D680F68F334CCE /* CanBusEnableModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 8FF9D0CF510B86703F742ED6 /* CanBusEnableModel.h */; };
		9E7F0E88DD0A126569E75048 /* SDAnimatedImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 93359E4BDBFCA0096391994F /* SDAnimatedImageView.m */; };
		9F70EF7A78884FE98F35773A /* MQTTInMemoryPersistence.h in Headers */ = {isa = PBXBuildFile; fileRef = EAD34BAE17FA0F85DB72D2EE /* MQTTInMemoryPersistence.h */; };
		9F8DDC75855FD6B9477241BA /* View+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = B3913A4615556C08D8D7697C /* View+MASAdditions.h */; };
		A00363A2624181007390B84A /* MASConstraintMaker.h in Headers */ = {isa = PBXBuildFile; fileRef = E9ACAA719E5AAE897A74B2DA /* MASConstraintMaker.h */; };
		A0349A6CA69F66E016C6CDF3 /* SDAssociatedObject.h in Headers */ = {isa = PBXBuildFile; fileRef = B15A1174DD0FEAB4D55CD6A0 /* SDAssociatedObject.h */; };
		A0804206A8CAEA96EEC678A7 /* HandledEggConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 11BA57B66A0184C04682439B /* HandledEggConfig.m */; };
		A0AF6669505413A19D1F2C37 /* MQTTDecoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 219E78127921453B2F6C1056 /* MQTTDecoder.m */; };
		A241BFCFD02E9C05E38217F0 /* SDWebImageTransition.h in Headers */ = {isa = PBXBuildFile; fileRef = C4E2F31339A7F676EBE2EEC9 /* SDWebImageTransition.h */; };
		A3CA3D662EC126937340A717 /* UIImage+Transform.h in Headers */ = {isa = PBXBuildFile; fileRef = 6E901509B484F481CCB25196 /* UIImage+Transform.h */; };
		A3F7D71BE822F44BAE22FB9C /* TrustPartViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 139B6C6E6A1BEC2891985EB1 /* TrustPartViewController.h */; };
		A4F44D203D84C703CE82BA83 /* FixtureDrive.h in Headers */ = {isa = PBXBuildFile; fileRef = 7E49D2DBC439824A8F6D7A88 /* FixtureDrive.h */; };
		A5BF006E6F25A857B1F5FE52 /* VerboseDigestSignUnorderedSynthesis.m in Sources */ = {isa = PBXBuildFile; fileRef = 9C631BB8E26620EE1A705852 /* VerboseDigestSignUnorderedSynthesis.m */; };
		A752A3F45FC689B14B43D3A0 /* SDImageCacheDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = C8C381B0E95E844A9E2D9477 /* SDImageCacheDefine.m */; };
		A7AEAD9B9A1E6790D24786E0 /* SendEraBlobRet.h in Headers */ = {isa = PBXBuildFile; fileRef = 4FB03109F8F8D76BA7F7BBA5 /* SendEraBlobRet.h */; };
		A7EDA41CD23C8AB3BD58B3B3 /* SettingEncodingsStringMiterRefreshedSubgroup.h in Headers */ = {isa = PBXBuildFile; fileRef = E58A16FAEBAD1EC98EB8904E /* SettingEncodingsStringMiterRefreshedSubgroup.h */; };
		A89E21257F5D8921CDB11D89 /* SDWebImageTransitionInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = D94C964EBF95600633E0ACDF /* SDWebImageTransitionInternal.h */; };
		A924922297F9084415E39799 /* ArtworkModel.m in Sources */ = {isa = PBXBuildFile; fileRef = F1380F186F9E6144C2DE26CA /* ArtworkModel.m */; };
		A92798E4CACCA15C3B913A95 /* UIImage+OwnImage.h in Headers */ = {isa = PBXBuildFile; fileRef = 24207DCB35CED81A4095CC1D /* UIImage+OwnImage.h */; };
		AAACA02A15F40AA6B1343DE7 /* SDWebImageDownloaderResponseModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = 7B3F2CB27F8A51548C415D15 /* SDWebImageDownloaderResponseModifier.h */; };
		AADADCB77C65A265556E0FF4 /* SlowDaySeekThe+Speak.h in Headers */ = {isa = PBXBuildFile; fileRef = 5FD49BBB07C032ACE14B7AAD /* SlowDaySeekThe+Speak.h */; };
		AB59ED89ECD52CE9AA815B44 /* SDWebImageCacheKeyFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = 5934DA760D5BE597EBEFFCF9 /* SDWebImageCacheKeyFilter.m */; };
		AB934412D9A125104AB20F4E /* SlowDaySeekThe.m in Sources */ = {isa = PBXBuildFile; fileRef = 122832A3C2AF465A59BE46DD /* SlowDaySeekThe.m */; };
		ACA19921D4606A5CF4602B92 /* GarbageAndViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 61534C68825F5A527CD9DBB1 /* GarbageAndViewController.m */; };
		ACACFDB109F83A83BFA9E74C /* SlavicTapTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 4AEF1AE47130634EA83AB271 /* SlavicTapTextField.m */; };
		ADEB646202B815F3D90553F8 /* UIImage+ForceDecode.m in Sources */ = {isa = PBXBuildFile; fileRef = B3537F21C8DDFA381419F5D3 /* UIImage+ForceDecode.m */; };
		AE203F81239EC91BE756FB6A /* PivotForManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BFA4C898A79394DD0A112AE5 /* PivotForManager.m */; };
		AEFB1B4A13F798CAE6A001D8 /* MQTTCoreDataPersistence.m in Sources */ = {isa = PBXBuildFile; fileRef = D889D3FFA19134627FB7490A /* MQTTCoreDataPersistence.m */; };
		AF1EE17057B02D9D6ED2FB7C /* DeepApplyProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 47CF62633E6CAB07C137AECB /* DeepApplyProtocol.h */; };
		AFB687809B23FB2BA0EF2D56 /* SDWebImageOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 5AC4BFAF1E35EF8B7798504F /* SDWebImageOperation.h */; };
		AFC65B29743176298B2544FB /* SDAnimatedImageRep.h in Headers */ = {isa = PBXBuildFile; fileRef = 5E6AA5D3FD552DAC226BF709 /* SDAnimatedImageRep.h */; };
		B1459573B43EB2E6EBF75261 /* UIViewController+BoxViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A1F5E0CDE4EF1FAF3501CAE3 /* UIViewController+BoxViewController.m */; };
		B15FD15F31BCEDB65C4B4E20 /* MQTTCFSocketTransport.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C3F4EB15860B12A7D2ABBC8 /* MQTTCFSocketTransport.m */; };
		B1D189E0E3606D3C73309562 /* MQTTSession.h in Headers */ = {isa = PBXBuildFile; fileRef = F4A69E0D5D2F7EB87ABE1B4C /* MQTTSession.h */; };
		B21CD8D2CC5C1BD42D65BF93 /* NSString+BedLow.h in Headers */ = {isa = PBXBuildFile; fileRef = A33D467241A6B9AD5C265B19 /* NSString+BedLow.h */; };
		B2393D8DEB63967E3D51F5D5 /* UIImage+ExtendedCacheData.m in Sources */ = {isa = PBXBuildFile; fileRef = 21C9778A0E03E1EB0E673A76 /* UIImage+ExtendedCacheData.m */; };
		B2AD382F9AC6D361CA378D0E /* SDImageIOCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 7C813B8908AE41FBDA038B69 /* SDImageIOCoder.h */; };
		B2B5C4CD84101E3285565336 /* MQTTTransportProtocol.m in Sources */ = {isa = PBXBuildFile; fileRef = 185F3719C9E7989DDFB85A3A /* MQTTTransportProtocol.m */; };
		B4E0EADD2D86A0C8A111A5A4 /* LikeAsleepViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 2A93CED35997E1E49A88D706 /* LikeAsleepViewController.h */; };
		B4F19DCAE86B1CE0EF1C55CA /* UIColor+SDHexString.m in Sources */ = {isa = PBXBuildFile; fileRef = 5F5A4DBAF2FCC097CC68CF3C /* UIColor+SDHexString.m */; };
		B502B3FDA713AF0AC83C924E /* OutToast.h in Headers */ = {isa = PBXBuildFile; fileRef = 7B6C2BC2A56A2DBF38D9C5CB /* OutToast.h */; };
		B5084CB44C6C8B7395F69576 /* UIView+WebCacheOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 40D94C43715824246785DD98 /* UIView+WebCacheOperation.h */; };
		B6250E31B9D42CA330870749 /* AtomHasDogViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = ADC89DC15EF784D12132229E /* AtomHasDogViewController.h */; };
		B658043AD0E69D37EAE25687 /* PolarEachViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 961EE029580B54701D59C539 /* PolarEachViewController.m */; };
		B6B5AD89CF628A5DEDE67E2D /* NSArray+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = F732C4825DAD97C8CAAA7BE2 /* NSArray+MASAdditions.m */; };
		B732A64E35DB3BC5B3772CCC /* DirectTouchAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 8B9201D6F397E422509FFEC9 /* DirectTouchAction.h */; };
		B79E7844B0222FDC9A2B02E1 /* InferSonOut.h in Headers */ = {isa = PBXBuildFile; fileRef = 20E82B425482A5DB5D393C95 /* InferSonOut.h */; };
		B88597C4D0B634FE7EFBF427 /* MQTTInMemoryPersistence.m in Sources */ = {isa = PBXBuildFile; fileRef = 8482282A8FA54219A986DEFF /* MQTTInMemoryPersistence.m */; };
		B8D2E0F1E4EC8B86CC633B99 /* CiphersEarModel.m in Sources */ = {isa = PBXBuildFile; fileRef = AF7722249232BF8AA6956F33 /* CiphersEarModel.m */; };
		B962DD6EECDFBCB572AEDDEA /* XXGProtocolLabel.h in Headers */ = {isa = PBXBuildFile; fileRef = 7B369E3BEEBA79BA4EBBB0C7 /* XXGProtocolLabel.h */; };
		B9948D41D310F38BDFE91E80 /* UIImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = F3C8853DC9B4097B37BE0CB5 /* UIImageView+WebCache.h */; };
		B9DA1D412BBA4538429318E4 /* SlowDaySeekThe+HitOut.h in Headers */ = {isa = PBXBuildFile; fileRef = 1BBC7AB4603D69FB20F3E49F /* SlowDaySeekThe+HitOut.h */; };
		BA3514344B45F46DA4EF1460 /* DryBarInterKey.h in Headers */ = {isa = PBXBuildFile; fileRef = 96C5F770FDD202C53F513DE6 /* DryBarInterKey.h */; };
		BA9B86FC3A3923C3BFFD5680 /* UIImage+GIF.h in Headers */ = {isa = PBXBuildFile; fileRef = 72B2F1E53E9B749C5EB8DD0B /* UIImage+GIF.h */; };
		BB06BB1AE21C0BFD59572D37 /* CheckSmoothButton.h in Headers */ = {isa = PBXBuildFile; fileRef = BBFFDBBF311A296ABA71FDB6 /* CheckSmoothButton.h */; };
		BBDCDC86108D6F94102584F5 /* MQTTSessionSynchron.h in Headers */ = {isa = PBXBuildFile; fileRef = 7BAEAD1037AC48FEB03D1828 /* MQTTSessionSynchron.h */; };
		BC082F8B96BC73AC073F17CC /* UIView+WebCacheState.h in Headers */ = {isa = PBXBuildFile; fileRef = 8A3B40C339176206225E729D /* UIView+WebCacheState.h */; };
		BC32722BEDBB7335966D6998 /* GrossScrap.m in Sources */ = {isa = PBXBuildFile; fileRef = 97367A094F7D94128C5A2DD1 /* GrossScrap.m */; };
		BC3CADBBE58235B7594FEDBB /* BigPreviousManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 11D101468D70C37A2D11091D /* BigPreviousManager.m */; };
		BC861D80BA93636511DCD141 /* EphemeralViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 38A367446A04EC8EF6C98737 /* EphemeralViewController.h */; };
		BCB20D683C4AF793ACDC1031 /* SDAssociatedObject.m in Sources */ = {isa = PBXBuildFile; fileRef = F6927318C51AE6047559AB0B /* SDAssociatedObject.m */; };
		BE331E7B0D1398BCB13C4FF7 /* PolarEachViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 2011FDC51AE0616BC13FDDA3 /* PolarEachViewController.h */; };
		BE5B78DB8521262FA0F5A8A5 /* MASViewAttribute.h in Headers */ = {isa = PBXBuildFile; fileRef = DEBB7F0D921167B046720EE2 /* MASViewAttribute.h */; };
		BF39CB5FE6A4300537024128 /* DueLockAirTooViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B4973CA7D716AA569EFA2515 /* DueLockAirTooViewController.m */; };
		BF44FF72C44BFC07FE5D0ECB /* SeeVitalModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 3435A5A7984863470FD16450 /* SeeVitalModel.h */; };
		BFE3CDF89E0352A58504DE81 /* FigureGetSonInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 9426CA6FFCAC3071BBE411C5 /* FigureGetSonInfo.h */; };
		C01BD291107741D9DDFCC3E9 /* UIColor+SDHexString.h in Headers */ = {isa = PBXBuildFile; fileRef = D3A2B233264052BBF8AE259D /* UIColor+SDHexString.h */; };
		C2454E919647416AFBA6A058 /* SDMemoryCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 571C0F0FE0C21511E0EE6B7C /* SDMemoryCache.m */; };
		C2744A91158CC9B485C2D03E /* VisionCousinManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 3D5FD16300567A1F7F0FEC64 /* VisionCousinManager.m */; };
		C2DA4F7B944BC36BBAB757EE /* OpenNetTreeViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = EC6095C0F081AE9B2A447E4D /* OpenNetTreeViewController.h */; };
		C3AD52B1742878E418B896B6 /* MusicalManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DDC4885CB05DCF91C7E7D03F /* MusicalManager.m */; };
		C3B3610C6F6562C3121DF347 /* InferLoadStone.m in Sources */ = {isa = PBXBuildFile; fileRef = 9FBFEBDD8B76544AD505EA97 /* InferLoadStone.m */; };
		C422C7DACBAAB30BB6C90D85 /* SDImageCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 933EA8BEFF1633D2E630A542 /* SDImageCache.m */; };
		C5784CD78EA408158F48A461 /* CountViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 111D9780FDF4742EF59487EA /* CountViewController.h */; };
		C58C8FC25E7350F139FCDB4B /* SDDeviceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CE7404A5B857491331F0EE9 /* SDDeviceHelper.m */; };
		C6ACF2A07C4502D98BC527A5 /* WindowsSobInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = BDAA633582DA82F314FE0308 /* WindowsSobInfo.h */; };
		C9727060F121038B0DAF3F20 /* MASConstraintMaker.m in Sources */ = {isa = PBXBuildFile; fileRef = ABF09445C28227568CAA5D19 /* MASConstraintMaker.m */; };
		C99D91AC2ED0479CC75BFD6A /* MASCompositeConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 3232561B9138041D744ED834 /* MASCompositeConstraint.h */; };
		C9A8B499B5F0D162366ADCC2 /* SDImageAPNGCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 238A4A118ADA817A7F6F17DD /* SDImageAPNGCoder.h */; };
		CAC5397FC70660EEDC207EB3 /* UIImage+MultiFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = D5753DEF9F08E09790649A1F /* UIImage+MultiFormat.m */; };
		CBBC6CC2B4FFA62111317C2A /* NSString+SheHectares.h in Headers */ = {isa = PBXBuildFile; fileRef = 3F4135AE6EB31F1D2C41416D /* NSString+SheHectares.h */; };
		CC6DAC50D6A2C1AB5A0B7DFE /* SDImageIOAnimatedCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 39F2C1CAB58415CAE9A39160 /* SDImageIOAnimatedCoder.m */; };
		CCE2BD19CB56C8ECBB5E0C78 /* SDGraphicsImageRenderer.h in Headers */ = {isa = PBXBuildFile; fileRef = 57C596B81CAB498DC30A1C86 /* SDGraphicsImageRenderer.h */; };
		CCE5443107ADB68C895432F7 /* SDWebImageOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 23752001EA6CA86B8A204848 /* SDWebImageOperation.m */; };
		CD3243E6207983AAFD60330C /* SDImageIOAnimatedCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 116EA32D69BCA88DE21162AA /* SDImageIOAnimatedCoder.h */; };
		CE8626D5C7A73CF0D673DCAE /* LogLowerHexManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 8EC5EF980D4750DF4118E059 /* LogLowerHexManager.m */; };
		CEC69FB298B4CCE2B9CDC756 /* PoloFigure.m in Sources */ = {isa = PBXBuildFile; fileRef = D038C382E4D9E79E7C54AA90 /* PoloFigure.m */; };
		CECEF2AD7E83537D3423B66D /* View+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 27A8552B178C3603F092FF44 /* View+MASAdditions.m */; };
		CF9A9DB79070052C0EE036A0 /* NSArray+MASShorthandAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 6ABE635325CE02C4D4668053 /* NSArray+MASShorthandAdditions.h */; };
		CFE58ECF5A13C78827807334 /* UIView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 6109994009405BBEA9FD7173 /* UIView+WebCache.h */; };
		D0950042B576985C220B5CDC /* SDWebImageManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 9BE06F3F6DED35454604CCE9 /* SDWebImageManager.h */; };
		D161B81813D532B3F923FB02 /* MQTTCoreDataPersistence.h in Headers */ = {isa = PBXBuildFile; fileRef = FECC5E01A667C3EF9AFF1B3E /* MQTTCoreDataPersistence.h */; };
		D1C240375B9D5CDDB3A9C145 /* FillerBus.m in Sources */ = {isa = PBXBuildFile; fileRef = A16C21678357C3769FFAAD4C /* FillerBus.m */; };
		D364E3B866415A2845616E73 /* ChromaFarManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 3DDABA37309CA9C35520E37A /* ChromaFarManager.m */; };
		D3F365F42EFFB8AFB3913BF0 /* SeeBusStepList.h in Headers */ = {isa = PBXBuildFile; fileRef = AC31E4F862D1FA26436419BF /* SeeBusStepList.h */; };
		D407035008F74BEBF81379F9 /* SDInternalMacros.m in Sources */ = {isa = PBXBuildFile; fileRef = 6494CFA5F6196754AD0DC0FA /* SDInternalMacros.m */; };
		D4E21026EEA6532FB5BDD57E /* LikeAsleepViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 816BDB33EC2F6DD5D3A85C27 /* LikeAsleepViewController.m */; };
		D55620305DDC5AAC957BE4AB /* MQTTSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 65819365EE0250CBD661F628 /* MQTTSessionManager.m */; };
		D55A6160E16F55FC7418CFC3 /* NSObject+MenPunjabiInfoSongLost.h in Headers */ = {isa = PBXBuildFile; fileRef = EEF22BE46A3C7BE43553DAD2 /* NSObject+MenPunjabiInfoSongLost.h */; };
		D59FC28ED0A98BE1EA612133 /* BoxArtsCategoryClickedWrap.h in Headers */ = {isa = PBXBuildFile; fileRef = CF0E7E1B3AFF658D5FB5E800 /* BoxArtsCategoryClickedWrap.h */; };
		D6390B1D3187C6CA08597FBA /* SDImageCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 5B7F37D3D921AF5276D55762 /* SDImageCoder.h */; };
		D86CEEF0C2B56E5E4811F870 /* LoopsEyeRomanController.h in Headers */ = {isa = PBXBuildFile; fileRef = 23B9AF9494261220F72DEBC1 /* LoopsEyeRomanController.h */; };
		D8F4EE430D004FD0378D70C0 /* ProcessCircularTemporalCountryFlag.h in Headers */ = {isa = PBXBuildFile; fileRef = F274A76DDAFD6B314EAB213C /* ProcessCircularTemporalCountryFlag.h */; };
		DAA8857818BCBC3E8D8D7F1D /* MusicalRadians.h in Headers */ = {isa = PBXBuildFile; fileRef = F6EC2CB6E609496E4AACBCFA /* MusicalRadians.h */; };
		DB32F3F897F7833A3CA2616F /* MQTTStrict.h in Headers */ = {isa = PBXBuildFile; fileRef = 86952889D487204ECCFE8524 /* MQTTStrict.h */; };
		DC1ED349675E4484BE0B50C2 /* SDWebImageDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = A4C71E0853BBFA77EB1A4CD7 /* SDWebImageDownloader.h */; };
		DC4CC33A9AFBFE19D658841C /* CaseBurstViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 8C46394F770D59B80AD06D0E /* CaseBurstViewController.h */; };
		DD7EA0156BFDF0286ECE1466 /* UIColor+GetColor.h in Headers */ = {isa = PBXBuildFile; fileRef = 9D025ABF2D1413C216DAEDAF /* UIColor+GetColor.h */; };
		DEEDF8F3EF901E527BD790DC /* RetMenuLastPanInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F4939D7CA2AB7805AC2889A /* RetMenuLastPanInfo.h */; };
		DF781F47E0AB1515E5F6D426 /* TwoVideoViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 786820D06D995DB0FCE29C91 /* TwoVideoViewController.h */; };
		DFACBBC650A8053827DA9E9F /* SongArmBleedManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 97E75C2DAC9C5D9E51025C54 /* SongArmBleedManager.m */; };
		E031F54A1F6B2EE070F6A42E /* SDWebImagePrefetcher.h in Headers */ = {isa = PBXBuildFile; fileRef = 008C15FF420117A04C1D7916 /* SDWebImagePrefetcher.h */; };
		E0A267C2C8A7DF47696310D5 /* SpaKinWhite.m in Sources */ = {isa = PBXBuildFile; fileRef = 642150167C12E0D160781B6E /* SpaKinWhite.m */; };
		E1399EA671AA7055856530C8 /* NotNotConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 895251A9C85D040D0551EDD6 /* NotNotConfig.m */; };
		E23752624B5A13FF18D1AFEB /* VerboseCombine.h in Headers */ = {isa = PBXBuildFile; fileRef = EA7E4B20AA86BF07DD5FF27E /* VerboseCombine.h */; };
		E35FED8F5E4D14CDDC2A97CA /* CellTapCan.h in Headers */ = {isa = PBXBuildFile; fileRef = EDB144A9AC8943F749E8FBDF /* CellTapCan.h */; };
		E374EC9B93F3EE8E5D0F0291 /* SongArmBleedManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 6D2B7128B8196278B107CD29 /* SongArmBleedManager.h */; };
		E42B2E94A7F93BE970027C2B /* UIImage+MemoryCacheCost.m in Sources */ = {isa = PBXBuildFile; fileRef = B4C82662C4880AA1C18A3E1C /* UIImage+MemoryCacheCost.m */; };
		E4A1EBA60C6119DDA363B2B3 /* Dominant.m in Sources */ = {isa = PBXBuildFile; fileRef = A8534AEAF52C53A9AAF39924 /* Dominant.m */; };
		E5E904DBAE812C9BAB8F2B55 /* EncodingsDarkWasPasswordFilteringCell.m in Sources */ = {isa = PBXBuildFile; fileRef = C11BDE88063FEC2D0412B542 /* EncodingsDarkWasPasswordFilteringCell.m */; };
		E63793CCA193B325AFB1B7F0 /* PronounLegibleDefinesFlightsAttitudeBit.m in Sources */ = {isa = PBXBuildFile; fileRef = 39948EDBDDFFFF378096A2E0 /* PronounLegibleDefinesFlightsAttitudeBit.m */; };
		E638617A2B739C7CA3C2A190 /* SDImageCoderHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 300C5FCF1212E013FB04B6B1 /* SDImageCoderHelper.h */; };
		E7EF748D99BECF73B797869D /* EncodingsDarkWasPasswordFilteringCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 64B6B6667792D1932DF167E6 /* EncodingsDarkWasPasswordFilteringCell.h */; };
		E80382D1E3769EC954861C43 /* MusicalRadians.m in Sources */ = {isa = PBXBuildFile; fileRef = 39093C9AB15345682D7BDF40 /* MusicalRadians.m */; };
		E8366E8DD2A24F80781B598B /* SettingEncodingsStringMiterRefreshedSubgroup.m in Sources */ = {isa = PBXBuildFile; fileRef = FBE62222A98340344572CF44 /* SettingEncodingsStringMiterRefreshedSubgroup.m */; };
		E92720FD2081C6AD54AC5968 /* UIImage+Transform.m in Sources */ = {isa = PBXBuildFile; fileRef = BF335B9943B67D0267FF3C3C /* UIImage+Transform.m */; };
		EAAE32A7577C0AAB1CD8DEFE /* SonAssetWinNetCell.m in Sources */ = {isa = PBXBuildFile; fileRef = F88E05BFAE9117371EC429C6 /* SonAssetWinNetCell.m */; };
		ED2FE6DE324397EEA6A6A73A /* SDImageCacheConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 88F73A3CDD3491E1A37B2C7C /* SDImageCacheConfig.m */; };
		ED699CB1A72E6C68E5DDB477 /* SDImageAWebPCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 498C71B6574E5EB6309FB718 /* SDImageAWebPCoder.m */; };
		ED6C62BBE6F03583C04F2CBB /* EphemeralViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = C58B6424C4532D20AE232524 /* EphemeralViewController.m */; };
		ED7A29EF57BCD9E1B365A5E0 /* SDImageFrame.h in Headers */ = {isa = PBXBuildFile; fileRef = A2DD5F3E823868BD057D53E7 /* SDImageFrame.h */; };
		EE5E2662223106F7F5DC3D47 /* SDWebImageDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = C8CFB9F29CD950FDECB97979 /* SDWebImageDefine.h */; };
		EE7892B1C1846141136F48D0 /* SinPushCapFlowButton.h in Headers */ = {isa = PBXBuildFile; fileRef = 36FF7503A1801D2D91E90485 /* SinPushCapFlowButton.h */; };
		EEC806AC1178DEBCDB2E4824 /* UIDevice+BarDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 7F36F83C66BF2ABC423F2AAE /* UIDevice+BarDevice.h */; };
		EF0413B8A2AC49A059B17DBD /* MASConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 615E13833664019613FE173D /* MASConstraint.h */; };
		EF9CCF11F2133BAF82901EA1 /* VerboseDigestSignUnorderedSynthesis.h in Headers */ = {isa = PBXBuildFile; fileRef = E2EFB371955803F2045FDF3F /* VerboseDigestSignUnorderedSynthesis.h */; };
		EFD4146F36BC5710E4F96B21 /* OriginsFunnelSignalingReadyCaps.m in Sources */ = {isa = PBXBuildFile; fileRef = 76BFF839E893E30C8BD2561F /* OriginsFunnelSignalingReadyCaps.m */; };
		EFFE5B934729A41E22EF2618 /* SemicolonThe.m in Sources */ = {isa = PBXBuildFile; fileRef = E1D193CB430460776F20FD9E /* SemicolonThe.m */; };
		F0FE9BDD93F13BB1914CA57B /* NSImage+Compatibility.h in Headers */ = {isa = PBXBuildFile; fileRef = 884817DE23B8A78CB52F99CB /* NSImage+Compatibility.h */; };
		F238731F31F345C68F0182C5 /* MQTTSSLSecurityPolicyDecoder.m in Sources */ = {isa = PBXBuildFile; fileRef = FF3AE64B18C101B5CCF3EFCB /* MQTTSSLSecurityPolicyDecoder.m */; };
		F2D6A2E5786F29BF20F39482 /* MQTTSession.m in Sources */ = {isa = PBXBuildFile; fileRef = 9818C45335B785FE986EA794 /* MQTTSession.m */; };
		F321013C0199B06C70A4F688 /* NSArray+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = D7AB1A05A52B80B0C9F61B5D /* NSArray+MASAdditions.h */; };
		F379327E08C5818D1D3A46BD /* TurnWordTipViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = FA6C84C77A89406F708EBCCB /* TurnWordTipViewController.h */; };
		F5F9D119CA45CF2CD2F05241 /* InverseArmAreNiacinAllocator.m in Sources */ = {isa = PBXBuildFile; fileRef = 159360FDE0E3761862872AE5 /* InverseArmAreNiacinAllocator.m */; };
		F62C98E950E16A276381C05E /* UIImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 9AB6B4825C7BBFFAF75AAC39 /* UIImageView+WebCache.m */; };
		F7AF2CCE5427B8C41C624226 /* MASLayoutConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 76568D5D140940001FF28B59 /* MASLayoutConstraint.m */; };
		F7D4413B9455734BC083AC30 /* SDWebImageCacheSerializer.h in Headers */ = {isa = PBXBuildFile; fileRef = B421363D1A90300A738CD851 /* SDWebImageCacheSerializer.h */; };
		F89E90F41E185B3E1C496B48 /* ShapeWinNet.m in Sources */ = {isa = PBXBuildFile; fileRef = 795536FCA694F27781DB8D17 /* ShapeWinNet.m */; };
		F8EABA6D87108B6A8BA6BF0C /* SpaKinWhite.h in Headers */ = {isa = PBXBuildFile; fileRef = 065A8DC6DCA25026A23A93EC /* SpaKinWhite.h */; };
		F9165AAD2F0D70013320F60E /* VisionCousinManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 61B8877129E4ACB8CC88F8D2 /* VisionCousinManager.h */; };
		F9E827F98CD2870EC7B8A99B /* SemicolonThe.h in Headers */ = {isa = PBXBuildFile; fileRef = 67A3C139357BCF84AE8773B1 /* SemicolonThe.h */; };
		FA012A49A7D2C2ECDD73BCA8 /* KoreanSeeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DBA2BA9B6C920BF73364F218 /* KoreanSeeViewController.m */; };
		FA225D287F1FF25CC8B5CF1B /* SDImageFramePool.m in Sources */ = {isa = PBXBuildFile; fileRef = 119CD837E13880DF5801A97C /* SDImageFramePool.m */; };
		FA6EF1B3AE42C19EBFD8BA6E /* NSString+StickySay.m in Sources */ = {isa = PBXBuildFile; fileRef = 4775D0F244E9F1A9EC07465B /* NSString+StickySay.m */; };
		FB01D2E9F1601CF17B2CAF94 /* MQTTTransportProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 23E129F53E3FDABD630678A8 /* MQTTTransportProtocol.h */; };
		FBF4B04FF031D8C0A0C81BF3 /* MQTTCFSocketDecoder.m in Sources */ = {isa = PBXBuildFile; fileRef = B674C429CFB3A07337B7EDFE /* MQTTCFSocketDecoder.m */; };
		FCD8FE3EB68B339E64DE58AF /* NotNotConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 3695AD8A8301F36F262BEE31 /* NotNotConfig.h */; };
		FDBE948B98038674DF09D9C0 /* SDDeviceHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 6A13D2593CBB3A72AEC4B148 /* SDDeviceHelper.h */; };
		FE6E4F0C7685A35123A835BF /* SDAnimatedImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 7AD9233AFD2366371FBB913C /* SDAnimatedImageView+WebCache.m */; };
		FE924B9DF2B2AB2B145C5D65 /* KoreanSeeViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = E38C8E3C273FDFD7844F2E76 /* KoreanSeeViewController.h */; };
		FEBDD10203AB9F7007050A86 /* SeeBusStepList.m in Sources */ = {isa = PBXBuildFile; fileRef = ED911157B82E8EAFFA2D9D94 /* SeeBusStepList.m */; };
		FEBEF11561D650B0BE87B887 /* SDCallbackQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = 90663872276239701FF450A7 /* SDCallbackQueue.m */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		003D730C055AAC937DCEFA41 /* SlowDaySeekThe+HitOut.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "SlowDaySeekThe+HitOut.m"; sourceTree = "<group>"; };
		008C15FF420117A04C1D7916 /* SDWebImagePrefetcher.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImagePrefetcher.h; sourceTree = "<group>"; };
		011282B95EB00976868F5474 /* PenNetwork.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PenNetwork.h; sourceTree = "<group>"; };
		019802501F00732EF0141510 /* OriginsFunnelSignalingReadyCaps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OriginsFunnelSignalingReadyCaps.h; sourceTree = "<group>"; };
		01E9CF12555DAB3F6F8023B5 /* UIView+WebCacheOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCacheOperation.m"; sourceTree = "<group>"; };
		0287249B8092EA18ABB7D378 /* BoxArtsCategoryClickedWrap.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BoxArtsCategoryClickedWrap.m; sourceTree = "<group>"; };
		02DD979D5FE1048F555DD3C7 /* MQTTSessionLegacy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSessionLegacy.h; sourceTree = "<group>"; };
		031988A0A1CB41E50B93A56C /* SDDiskCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDDiskCache.m; sourceTree = "<group>"; };
		03265601EED058DA6453672E /* SDGraphicsImageRenderer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDGraphicsImageRenderer.m; sourceTree = "<group>"; };
		046CF37B220240DB19ECA78A /* SDImageAssetManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageAssetManager.h; sourceTree = "<group>"; };
		04A8D6BF6F61F10754AA2047 /* ParameterStillCalculateAdverbBoldface.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ParameterStillCalculateAdverbBoldface.h; sourceTree = "<group>"; };
		063DFD81C5A95D79BB63410D /* LiveStyleInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LiveStyleInfo.h; sourceTree = "<group>"; };
		065A8DC6DCA25026A23A93EC /* SpaKinWhite.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SpaKinWhite.h; sourceTree = "<group>"; };
		070E6F5B032C4D293F1FAC2F /* RadixDeny.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RadixDeny.m; sourceTree = "<group>"; };
		075A98A85E24ADCAB95E62DA /* BevelNarrative.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BevelNarrative.m; sourceTree = "<group>"; };
		07AB7BF57284036AC3E7843C /* RareRaceColor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RareRaceColor.h; sourceTree = "<group>"; };
		084C63BA5351BECA0417E9D9 /* HomePhraseRankCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HomePhraseRankCell.m; sourceTree = "<group>"; };
		08C90987D3B6128A4C00EAA3 /* CiphersEarModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CiphersEarModel.h; sourceTree = "<group>"; };
		09590734533E49DE06264FE9 /* SDDiskCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDDiskCache.h; sourceTree = "<group>"; };
		0975D156CFF4E760E0DD35C8 /* LoopsEyeRomanController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LoopsEyeRomanController.m; sourceTree = "<group>"; };
		0B0A101A5DD125FBD2C93468 /* BigPreviousManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BigPreviousManager.h; sourceTree = "<group>"; };
		0C013B661469AC985B667BB8 /* SDImageGIFCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageGIFCoder.h; sourceTree = "<group>"; };
		0C8A72E2AA673E981782D570 /* MQTTProperties.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTProperties.h; sourceTree = "<group>"; };
		0C95057C0EF58E3683F01C1A /* MASConstraint+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "MASConstraint+Private.h"; sourceTree = "<group>"; };
		0E57C97795C3156ECA8E396B /* PivotForManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PivotForManager.h; sourceTree = "<group>"; };
		0E69F8FD195C6E96E3CBF5C2 /* GCDTimer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GCDTimer.h; sourceTree = "<group>"; };
		0F13BC6A6309DB96973E773D /* FloaterBendTerabytesCopperTaggingReasonViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FloaterBendTerabytesCopperTaggingReasonViewController.m; sourceTree = "<group>"; };
		108037828C063003BF48489F /* MQTTSSLSecurityPolicy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicy.h; sourceTree = "<group>"; };
		10EDC15ED349579399FA44AF /* MQTTSSLSecurityPolicyTransport.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicyTransport.h; sourceTree = "<group>"; };
		111D9780FDF4742EF59487EA /* CountViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CountViewController.h; sourceTree = "<group>"; };
		116EA32D69BCA88DE21162AA /* SDImageIOAnimatedCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageIOAnimatedCoder.h; sourceTree = "<group>"; };
		1194455A0B1FDE7910690830 /* TrustPartViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TrustPartViewController.m; sourceTree = "<group>"; };
		119476203B3EE7F1F6B1F86B /* SobAlertView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SobAlertView.m; sourceTree = "<group>"; };
		119CD837E13880DF5801A97C /* SDImageFramePool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageFramePool.m; sourceTree = "<group>"; };
		11BA57B66A0184C04682439B /* HandledEggConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HandledEggConfig.m; sourceTree = "<group>"; };
		11D101468D70C37A2D11091D /* BigPreviousManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BigPreviousManager.m; sourceTree = "<group>"; };
		122832A3C2AF465A59BE46DD /* SlowDaySeekThe.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SlowDaySeekThe.m; sourceTree = "<group>"; };
		12735A0C46854BFF4033F457 /* ModeRunningDelayRaiseStrokingTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ModeRunningDelayRaiseStrokingTool.m; sourceTree = "<group>"; };
		12AD43BCA9BB937E9495379F /* SDImageCacheDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCacheDefine.h; sourceTree = "<group>"; };
		139B6C6E6A1BEC2891985EB1 /* TrustPartViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TrustPartViewController.h; sourceTree = "<group>"; };
		145C3EE3B7D9A537F15C9590 /* SDWebImageCacheSerializer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCacheSerializer.m; sourceTree = "<group>"; };
		159360FDE0E3761862872AE5 /* InverseArmAreNiacinAllocator.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InverseArmAreNiacinAllocator.m; sourceTree = "<group>"; };
		16D1254D2725C3BFC6325B8E /* HomePhraseRankCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HomePhraseRankCell.h; sourceTree = "<group>"; };
		18036C457522B87FD2F487AE /* ProcessCircularTemporalCountryFlag.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ProcessCircularTemporalCountryFlag.m; sourceTree = "<group>"; };
		185F3719C9E7989DDFB85A3A /* MQTTTransportProtocol.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTTransportProtocol.m; sourceTree = "<group>"; };
		1B18831364CB0E981BA89C03 /* SumBuffersVisitAlbumBuildViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SumBuffersVisitAlbumBuildViewController.h; sourceTree = "<group>"; };
		1B45373C923CBF4D8EC610E7 /* UIButton+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIButton+WebCache.m"; sourceTree = "<group>"; };
		1BBC7AB4603D69FB20F3E49F /* SlowDaySeekThe+HitOut.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SlowDaySeekThe+HitOut.h"; sourceTree = "<group>"; };
		1C9F7EBF29F2DCE54A316389 /* MASLayoutConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASLayoutConstraint.h; sourceTree = "<group>"; };
		1E9E13758ADED3A4739A87A0 /* SDAsyncBlockOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAsyncBlockOperation.h; sourceTree = "<group>"; };
		1F5EF08D30C2ADD28A6FEFB2 /* UIImage+ExtendedCacheData.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+ExtendedCacheData.h"; sourceTree = "<group>"; };
		1F6240B50BA9D3C49F0795BA /* SDImageLoader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageLoader.m; sourceTree = "<group>"; };
		2011FDC51AE0616BC13FDDA3 /* PolarEachViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PolarEachViewController.h; sourceTree = "<group>"; };
		20E82B425482A5DB5D393C95 /* InferSonOut.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InferSonOut.h; sourceTree = "<group>"; };
		2160314877FA80BAD837DA84 /* SemicolonViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SemicolonViewController.h; sourceTree = "<group>"; };
		219E78127921453B2F6C1056 /* MQTTDecoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTDecoder.m; sourceTree = "<group>"; };
		21C9778A0E03E1EB0E673A76 /* UIImage+ExtendedCacheData.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+ExtendedCacheData.m"; sourceTree = "<group>"; };
		22FB6D64825E583D595501A0 /* DueSumZoom.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DueSumZoom.h; sourceTree = "<group>"; };
		23752001EA6CA86B8A204848 /* SDWebImageOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageOperation.m; sourceTree = "<group>"; };
		238A4A118ADA817A7F6F17DD /* SDImageAPNGCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageAPNGCoder.h; sourceTree = "<group>"; };
		23B9AF9494261220F72DEBC1 /* LoopsEyeRomanController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LoopsEyeRomanController.h; sourceTree = "<group>"; };
		23E129F53E3FDABD630678A8 /* MQTTTransportProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTTransportProtocol.h; sourceTree = "<group>"; };
		241130EE051DD80C02AE1B3A /* SDImageIOAnimatedCoderInternal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageIOAnimatedCoderInternal.h; sourceTree = "<group>"; };
		24207DCB35CED81A4095CC1D /* UIImage+OwnImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+OwnImage.h"; sourceTree = "<group>"; };
		242947CD7310C773C8E5870B /* HandballProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HandballProtocol.h; sourceTree = "<group>"; };
		2684143DA4ED106F85A2D860 /* SDAnimatedImageRep.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImageRep.m; sourceTree = "<group>"; };
		26DB1986EBEFCCF6ACBC952B /* SDDisplayLink.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDDisplayLink.h; sourceTree = "<group>"; };
		27A8552B178C3603F092FF44 /* View+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "View+MASAdditions.m"; sourceTree = "<group>"; };
		28FF93557AC2F072EC9D9AA0 /* TagSidebarView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TagSidebarView.h; sourceTree = "<group>"; };
		2940FFAF377927CD8B8147A7 /* SDImageLoadersManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageLoadersManager.h; sourceTree = "<group>"; };
		29CECAFAAD51234B54AE2852 /* LogLowerHexManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LogLowerHexManager.h; sourceTree = "<group>"; };
		2A3753D7CB6B20650CF003C2 /* TildeScanPlaneWindow.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TildeScanPlaneWindow.m; sourceTree = "<group>"; };
		2A8641B8D2CB4BC84A3F9C6E /* NSData+ImageContentType.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSData+ImageContentType.m"; sourceTree = "<group>"; };
		2A93CED35997E1E49A88D706 /* LikeAsleepViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LikeAsleepViewController.h; sourceTree = "<group>"; };
		2B4C304BF34F7EC6AD4FF4B3 /* HeaderManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HeaderManager.m; sourceTree = "<group>"; };
		2C19280CEAF6296A042CD9FF /* HandledEggConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HandledEggConfig.h; sourceTree = "<group>"; };
		2C95468E6D2D68C3AD996FA2 /* MQTTProperties.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTProperties.m; sourceTree = "<group>"; };
		2D0EC61AADCC9C4F22F80906 /* TwoVideoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TwoVideoViewController.m; sourceTree = "<group>"; };
		2E3DF17F33E037D4F4C64ABD /* Adobe.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Adobe.m; sourceTree = "<group>"; };
		2E6F6EB14C3ECF3A931AB678 /* SDImageIOCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageIOCoder.m; sourceTree = "<group>"; };
		2EE48B16358C30D873C7E47A /* ReportManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReportManager.h; sourceTree = "<group>"; };
		2F0844873B4C513741A2DC0F /* MQTTMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTMessage.h; sourceTree = "<group>"; };
		2F1DC8B25B25146A5EA05DFD /* HitUnableManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HitUnableManager.h; sourceTree = "<group>"; };
		2F21AE936CD54329FF18C248 /* UIDevice+BarDevice.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIDevice+BarDevice.m"; sourceTree = "<group>"; };
		2F4939D7CA2AB7805AC2889A /* RetMenuLastPanInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RetMenuLastPanInfo.h; sourceTree = "<group>"; };
		2FEBDB4B13FF801913DD149D /* PoloFigure.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PoloFigure.h; sourceTree = "<group>"; };
		300C5FCF1212E013FB04B6B1 /* SDImageCoderHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCoderHelper.h; sourceTree = "<group>"; };
		30CC03B955A70C1E299D9E97 /* FixtureDrive.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FixtureDrive.m; sourceTree = "<group>"; };
		3187FED2E7DD23E9195A0903 /* BevelNarrative.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BevelNarrative.h; sourceTree = "<group>"; };
		3232561B9138041D744ED834 /* MASCompositeConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASCompositeConstraint.h; sourceTree = "<group>"; };
		32856A0DC097D147BCE05959 /* SDWebImageError.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageError.h; sourceTree = "<group>"; };
		3323343FA46D8E3EE37A7F16 /* NSString+StickySay.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+StickySay.h"; sourceTree = "<group>"; };
		3435A5A7984863470FD16450 /* SeeVitalModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SeeVitalModel.h; sourceTree = "<group>"; };
		34C8B3F7899686F0381905C0 /* MQTTSessionSynchron.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSessionSynchron.m; sourceTree = "<group>"; };
		34E1280B1162F5A722224D72 /* SDWebImageOptionsProcessor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageOptionsProcessor.m; sourceTree = "<group>"; };
		351DADE01FACBE096758CCB1 /* NSData+PinThat.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSData+PinThat.m"; sourceTree = "<group>"; };
		35489DC6C272CFB63EF56B36 /* SDmetamacros.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDmetamacros.h; sourceTree = "<group>"; };
		3695AD8A8301F36F262BEE31 /* NotNotConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NotNotConfig.h; sourceTree = "<group>"; };
		36AA8C439B87BA05D260F635 /* SDWeakProxy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWeakProxy.m; sourceTree = "<group>"; };
		36EF06B1B8D01EBC1869D02D /* SDDisplayLink.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDDisplayLink.m; sourceTree = "<group>"; };
		36FF7503A1801D2D91E90485 /* SinPushCapFlowButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SinPushCapFlowButton.h; sourceTree = "<group>"; };
		377537E085BA8BC2621BBA99 /* WithinAskTagManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WithinAskTagManager.h; sourceTree = "<group>"; };
		3881C1C23C5DE581C44DAA68 /* PopPickMayViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PopPickMayViewController.h; sourceTree = "<group>"; };
		38A367446A04EC8EF6C98737 /* EphemeralViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EphemeralViewController.h; sourceTree = "<group>"; };
		39093C9AB15345682D7BDF40 /* MusicalRadians.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MusicalRadians.m; sourceTree = "<group>"; };
		39948EDBDDFFFF378096A2E0 /* PronounLegibleDefinesFlightsAttitudeBit.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PronounLegibleDefinesFlightsAttitudeBit.m; sourceTree = "<group>"; };
		39F2C1CAB58415CAE9A39160 /* SDImageIOAnimatedCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageIOAnimatedCoder.m; sourceTree = "<group>"; };
		3A4D7A9CE02AB7CA653DF027 /* CanBusEnableModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CanBusEnableModel.m; sourceTree = "<group>"; };
		3B413606488EFABAB1B9DDF2 /* DueLockAirTooViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DueLockAirTooViewController.h; sourceTree = "<group>"; };
		3D5FD16300567A1F7F0FEC64 /* VisionCousinManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VisionCousinManager.m; sourceTree = "<group>"; };
		3D6092DFE0F964A3E6237155 /* NSImage+Compatibility.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSImage+Compatibility.m"; sourceTree = "<group>"; };
		3DDABA37309CA9C35520E37A /* ChromaFarManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChromaFarManager.m; sourceTree = "<group>"; };
		3F4135AE6EB31F1D2C41416D /* NSString+SheHectares.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+SheHectares.h"; sourceTree = "<group>"; };
		3F89F36B7B42FCE0CCB8D46E /* YoungerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = YoungerViewController.m; sourceTree = "<group>"; };
		400ED8AD84D4C888FA8796A0 /* MQTTSSLSecurityPolicyEncoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicyEncoder.h; sourceTree = "<group>"; };
		4088889F0E7F68BB932C4EB6 /* SDWebImageDefine.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDefine.m; sourceTree = "<group>"; };
		40D94C43715824246785DD98 /* UIView+WebCacheOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCacheOperation.h"; sourceTree = "<group>"; };
		4139A0A3A460E14F0E2E6829 /* SDImageCoderHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCoderHelper.m; sourceTree = "<group>"; };
		41F160ED4AEA638266D25C54 /* BitOwnPathView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BitOwnPathView.m; sourceTree = "<group>"; };
		433F68F0C09BE3A301681307 /* OpenNetTreeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OpenNetTreeViewController.m; sourceTree = "<group>"; };
		435EB89050EB338B73E23EA9 /* SlowDaySeekThe+Speak.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "SlowDaySeekThe+Speak.m"; sourceTree = "<group>"; };
		440ADF7DB1BE50D635323F1D /* SDAsyncBlockOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAsyncBlockOperation.m; sourceTree = "<group>"; };
		45349E2ECA273FB15CC01379 /* DownloadBondLongSucceededTen.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DownloadBondLongSucceededTen.m; sourceTree = "<group>"; };
		46F2451F446710FB100F9B38 /* SDWebImageIndicator.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageIndicator.h; sourceTree = "<group>"; };
		46FF86A625C1DEB63C82C39C /* NSURL+MinWhoFive.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSURL+MinWhoFive.m"; sourceTree = "<group>"; };
		4749A2EDBC9F6DDB425B5687 /* NSURL+MinWhoFive.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSURL+MinWhoFive.h"; sourceTree = "<group>"; };
		4775D0F244E9F1A9EC07465B /* NSString+StickySay.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+StickySay.m"; sourceTree = "<group>"; };
		47A2E9BCA649D320CFDCA64D /* View+MASShorthandAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "View+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		47CF62633E6CAB07C137AECB /* DeepApplyProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DeepApplyProtocol.h; sourceTree = "<group>"; };
		48ACCB832BE1756214BAAF51 /* MASViewConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASViewConstraint.m; sourceTree = "<group>"; };
		498C71B6574E5EB6309FB718 /* SDImageAWebPCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageAWebPCoder.m; sourceTree = "<group>"; };
		499F09CF234117D187F8CE3C /* PenNetwork.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PenNetwork.m; sourceTree = "<group>"; };
		49C280B0F3924999B94EBB23 /* SDImageCachesManagerOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCachesManagerOperation.m; sourceTree = "<group>"; };
		4AB7ABD372122B19DE58E776 /* SDImageTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageTransformer.m; sourceTree = "<group>"; };
		4AEF1AE47130634EA83AB271 /* SlavicTapTextField.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SlavicTapTextField.m; sourceTree = "<group>"; };
		4C33F9BCB6334778CB1CA6A0 /* MASConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASConstraint.m; sourceTree = "<group>"; };
		4CA75D9A537F0C2CBBFF2117 /* ForegroundReconnection.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ForegroundReconnection.h; sourceTree = "<group>"; };
		4CD08BB09A2A00613342E3F0 /* MakerCarbon.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MakerCarbon.h; sourceTree = "<group>"; };
		4CE7404A5B857491331F0EE9 /* SDDeviceHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDDeviceHelper.m; sourceTree = "<group>"; };
		4CE7D497208591F65E3B51B5 /* FloaterBendTerabytesCopperTaggingReasonViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FloaterBendTerabytesCopperTaggingReasonViewController.h; sourceTree = "<group>"; };
		4FB03109F8F8D76BA7F7BBA5 /* SendEraBlobRet.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SendEraBlobRet.h; sourceTree = "<group>"; };
		506FE79C8374AE774CCB2D39 /* InteractItalicBouncingRegionsDimensionModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InteractItalicBouncingRegionsDimensionModel.m; sourceTree = "<group>"; };
		5081ADE5478E2895C42CCD71 /* NSError+FootnoteOdd.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSError+FootnoteOdd.m"; sourceTree = "<group>"; };
		520D2EE66A5E9B9AF37E228E /* DetailedPutManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DetailedPutManager.m; sourceTree = "<group>"; };
		522FBE91931224EFC4B8EFDF /* DogTempPin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DogTempPin.h; sourceTree = "<group>"; };
		532F458511582393B7DD2EC9 /* MQTTSSLSecurityPolicyEncoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicyEncoder.m; sourceTree = "<group>"; };
		54AFEA69507B76536B3A7CA3 /* SDWebImageDownloaderConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderConfig.h; sourceTree = "<group>"; };
		54B82C9C2C7803067141F0A2 /* WindowsSobInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WindowsSobInfo.m; sourceTree = "<group>"; };
		571C0F0FE0C21511E0EE6B7C /* SDMemoryCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDMemoryCache.m; sourceTree = "<group>"; };
		577A8CB5C71872A866960B40 /* SDWebImageDownloaderOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderOperation.m; sourceTree = "<group>"; };
		57BE0F078288DFE28782B334 /* IrishAlbanian.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IrishAlbanian.m; sourceTree = "<group>"; };
		57C596B81CAB498DC30A1C86 /* SDGraphicsImageRenderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDGraphicsImageRenderer.h; sourceTree = "<group>"; };
		57DB2E4A7DD8B57D95569C8F /* UIImage+Metadata.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+Metadata.h"; sourceTree = "<group>"; };
		58AAAFDD9D74B5023CDA8EA0 /* SlowDaySeekThe.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SlowDaySeekThe.h; sourceTree = "<group>"; };
		59128F9A580DC9546B10909C /* SDWebImageTransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageTransition.m; sourceTree = "<group>"; };
		5934DA760D5BE597EBEFFCF9 /* SDWebImageCacheKeyFilter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCacheKeyFilter.m; sourceTree = "<group>"; };
		599F7FE7A88B9997B9B65C28 /* EarSigningManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EarSigningManager.h; sourceTree = "<group>"; };
		59ACC83996411FD32575C17C /* NSObject+MenPunjabiInfoSongLost.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSObject+MenPunjabiInfoSongLost.m"; sourceTree = "<group>"; };
		59B72C2A47FF3AA133E5695A /* SDWebImageDownloaderDecryptor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderDecryptor.h; sourceTree = "<group>"; };
		5A2FD435C2DF590B507C35CA /* SDImageCachesManagerOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCachesManagerOperation.h; sourceTree = "<group>"; };
		5A6468627A67324F63E20B7E /* WithinAskTagManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WithinAskTagManager.m; sourceTree = "<group>"; };
		5AAFAE63C658BA6E9975FE1A /* MASViewAttribute.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASViewAttribute.m; sourceTree = "<group>"; };
		5AC4BFAF1E35EF8B7798504F /* SDWebImageOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageOperation.h; sourceTree = "<group>"; };
		5B4B926BB72EAEA93712A2E1 /* SDMemoryCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDMemoryCache.h; sourceTree = "<group>"; };
		5B7F37D3D921AF5276D55762 /* SDImageCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCoder.h; sourceTree = "<group>"; };
		5CC542103E1AF74C78DDC910 /* SDAnimatedImagePlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImagePlayer.h; sourceTree = "<group>"; };
		5D4B02B18D4D9AAA58CE8CF2 /* CaseBurstViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CaseBurstViewController.m; sourceTree = "<group>"; };
		5E558EB81B54F15693EF092E /* SDWeakProxy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWeakProxy.h; sourceTree = "<group>"; };
		5E6AA5D3FD552DAC226BF709 /* SDAnimatedImageRep.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImageRep.h; sourceTree = "<group>"; };
		5EF58CA7DAB4F981B425F783 /* UIImage+GIF.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+GIF.m"; sourceTree = "<group>"; };
		5F5A4DBAF2FCC097CC68CF3C /* UIColor+SDHexString.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIColor+SDHexString.m"; sourceTree = "<group>"; };
		5FD49BBB07C032ACE14B7AAD /* SlowDaySeekThe+Speak.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SlowDaySeekThe+Speak.h"; sourceTree = "<group>"; };
		60501A564493ACE768E36507 /* PronounLegibleDefinesFlightsAttitudeBit.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PronounLegibleDefinesFlightsAttitudeBit.h; sourceTree = "<group>"; };
		60B213F8F4BDE0AB141A3966 /* GroupedCollectViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GroupedCollectViewController.m; sourceTree = "<group>"; };
		6109994009405BBEA9FD7173 /* UIView+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCache.h"; sourceTree = "<group>"; };
		610ACB12959C73B721D25E07 /* SlavicTapTextField.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SlavicTapTextField.h; sourceTree = "<group>"; };
		61534C68825F5A527CD9DBB1 /* GarbageAndViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GarbageAndViewController.m; sourceTree = "<group>"; };
		615E13833664019613FE173D /* MASConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASConstraint.h; sourceTree = "<group>"; };
		61B8877129E4ACB8CC88F8D2 /* VisionCousinManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VisionCousinManager.h; sourceTree = "<group>"; };
		61D0B4FF84ACEDE3C1C8E103 /* SDImageGIFCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageGIFCoder.m; sourceTree = "<group>"; };
		621613F59D0A1240676C9C33 /* RawUpsideOdd.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RawUpsideOdd.h; sourceTree = "<group>"; };
		62260EF186CE02377AB68E43 /* SDImageCodersManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCodersManager.m; sourceTree = "<group>"; };
		62BA177E66745DB971E6F2F1 /* SDWebImageOptionsProcessor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageOptionsProcessor.h; sourceTree = "<group>"; };
		62BE62A55246194110072272 /* LeapHueHeapSunViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LeapHueHeapSunViewController.m; sourceTree = "<group>"; };
		62F0B47937E63BA9F1C7DF90 /* MaximumFirst.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MaximumFirst.m; sourceTree = "<group>"; };
		6340D0B9C30B559043DD35ED /* FoodHoursHave.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FoodHoursHave.m; sourceTree = "<group>"; };
		642150167C12E0D160781B6E /* SpaKinWhite.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SpaKinWhite.m; sourceTree = "<group>"; };
		6494CFA5F6196754AD0DC0FA /* SDInternalMacros.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDInternalMacros.m; sourceTree = "<group>"; };
		64B6B6667792D1932DF167E6 /* EncodingsDarkWasPasswordFilteringCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EncodingsDarkWasPasswordFilteringCell.h; sourceTree = "<group>"; };
		6521DF77CCE57BB721205D01 /* SlowDaySeekThe+EraDecide.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SlowDaySeekThe+EraDecide.h"; sourceTree = "<group>"; };
		65416AB0B657D9896DB22BCC /* ReportManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReportManager.m; sourceTree = "<group>"; };
		655FF22C8697C0ECFE0DE284 /* UIImage+OwnImage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+OwnImage.m"; sourceTree = "<group>"; };
		65819365EE0250CBD661F628 /* MQTTSessionManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSessionManager.m; sourceTree = "<group>"; };
		6628491DC935B15A7CA883E9 /* ForegroundReconnection.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ForegroundReconnection.m; sourceTree = "<group>"; };
		67A3C139357BCF84AE8773B1 /* SemicolonThe.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SemicolonThe.h; sourceTree = "<group>"; };
		67BA7B3C076C143E76470851 /* RadixDeny.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RadixDeny.h; sourceTree = "<group>"; };
		6845270D58C1DB53E2340179 /* Masonry.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Masonry.h; sourceTree = "<group>"; };
		6A13D2593CBB3A72AEC4B148 /* SDDeviceHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDDeviceHelper.h; sourceTree = "<group>"; };
		6A64E0D7795BE83EFB33E790 /* NSBezierPath+SDRoundedCorners.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSBezierPath+SDRoundedCorners.h"; sourceTree = "<group>"; };
		6ABE635325CE02C4D4668053 /* NSArray+MASShorthandAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		6B08B877A13FDDA534B430FC /* SlowDaySeekThe+EraDecide.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "SlowDaySeekThe+EraDecide.m"; sourceTree = "<group>"; };
		6D26887745EF2EBED0C11C07 /* RawUpsideOdd.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RawUpsideOdd.m; sourceTree = "<group>"; };
		6D2B7128B8196278B107CD29 /* SongArmBleedManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SongArmBleedManager.h; sourceTree = "<group>"; };
		6D5DC52AFB94DD9DF43F08D8 /* UIButton+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIButton+WebCache.h"; sourceTree = "<group>"; };
		6E64521DC7FB648B69CDEF12 /* FrequencyInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FrequencyInfo.h; sourceTree = "<group>"; };
		6E901509B484F481CCB25196 /* UIImage+Transform.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+Transform.h"; sourceTree = "<group>"; };
		703B509329B646238ED0491E /* MQTTSessionManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSessionManager.h; sourceTree = "<group>"; };
		704A35DF36C9A6988B35B4DE /* VerboseCombine.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VerboseCombine.m; sourceTree = "<group>"; };
		712E15DCDE60A47774B1A56F /* NSString+BedLow.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+BedLow.m"; sourceTree = "<group>"; };
		72665F68328011A7C3B606C1 /* DetailedPutManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DetailedPutManager.h; sourceTree = "<group>"; };
		72B2F1E53E9B749C5EB8DD0B /* UIImage+GIF.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+GIF.h"; sourceTree = "<group>"; };
		734D8BEFB29ABD07DE305303 /* SDImageCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCache.h; sourceTree = "<group>"; };
		73A54D350AB1050302E45F7A /* SeeVitalModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SeeVitalModel.m; sourceTree = "<group>"; };
		73E486C243EA0F69A3DB98DA /* SDWebImagePrefetcher.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImagePrefetcher.m; sourceTree = "<group>"; };
		752E3F6D071D3BCFDEE6736B /* RetMenuLastPanInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RetMenuLastPanInfo.m; sourceTree = "<group>"; };
		75A4BDB2F625D52CED26FCAA /* UIView+WebCacheState.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCacheState.m"; sourceTree = "<group>"; };
		761387AED213CDF98CA96E54 /* SDAnimatedImage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImage.m; sourceTree = "<group>"; };
		765021F0844B36A086D2F559 /* SDWebImageDownloaderOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderOperation.h; sourceTree = "<group>"; };
		76568D5D140940001FF28B59 /* MASLayoutConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASLayoutConstraint.m; sourceTree = "<group>"; };
		76BFF839E893E30C8BD2561F /* OriginsFunnelSignalingReadyCaps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OriginsFunnelSignalingReadyCaps.m; sourceTree = "<group>"; };
		76F559DDE845E315EDDCE279 /* ConstantAdditionsValueRadioMouseYou.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ConstantAdditionsValueRadioMouseYou.h; sourceTree = "<group>"; };
		774B65842412B1B4A37BCFB0 /* SDImageAPNGCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageAPNGCoder.m; sourceTree = "<group>"; };
		786820D06D995DB0FCE29C91 /* TwoVideoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TwoVideoViewController.h; sourceTree = "<group>"; };
		786F1B2CFE1C4C22C76572FA /* SDImageAssetManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageAssetManager.m; sourceTree = "<group>"; };
		788DAD5EBC5D957687661E7D /* SDImageCachesManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCachesManager.m; sourceTree = "<group>"; };
		78AC831585293B779B63AA7D /* SumBuffersVisitAlbumBuildViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SumBuffersVisitAlbumBuildViewController.m; sourceTree = "<group>"; };
		79139984A151B08E3973FB12 /* SDWebImageDownloaderRequestModifier.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderRequestModifier.m; sourceTree = "<group>"; };
		795536FCA694F27781DB8D17 /* ShapeWinNet.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShapeWinNet.m; sourceTree = "<group>"; };
		7A12E16479067FC78C36FBA7 /* UIImageView+HighlightedWebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+HighlightedWebCache.m"; sourceTree = "<group>"; };
		7A4A89951A76082008E6BDE9 /* ChromaFarManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChromaFarManager.h; sourceTree = "<group>"; };
		7A552C34B2E63050DE2EB259 /* MaximumFirst.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MaximumFirst.h; sourceTree = "<group>"; };
		7A8C676CD9A57895ED3B026D /* SpeakPaperFair.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SpeakPaperFair.h; sourceTree = "<group>"; };
		7AD9233AFD2366371FBB913C /* SDAnimatedImageView+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "SDAnimatedImageView+WebCache.m"; sourceTree = "<group>"; };
		7B00D48E7E0C45FF6FEA8A27 /* UIImage+Metadata.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Metadata.m"; sourceTree = "<group>"; };
		7B369E3BEEBA79BA4EBBB0C7 /* XXGProtocolLabel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGProtocolLabel.h; sourceTree = "<group>"; };
		7B3F2CB27F8A51548C415D15 /* SDWebImageDownloaderResponseModifier.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderResponseModifier.h; sourceTree = "<group>"; };
		7B4855EC1E2CF1CFB78CB727 /* UIImage+MultiFormat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+MultiFormat.h"; sourceTree = "<group>"; };
		7B660278A8474DFC044B942B /* NSLayoutConstraint+MASDebugAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSLayoutConstraint+MASDebugAdditions.h"; sourceTree = "<group>"; };
		7B6C2BC2A56A2DBF38D9C5CB /* OutToast.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OutToast.h; sourceTree = "<group>"; };
		7BAEAD1037AC48FEB03D1828 /* MQTTSessionSynchron.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSessionSynchron.h; sourceTree = "<group>"; };
		7C10FA3D701D9C37BCD6AB1F /* XXGProtocolLabel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGProtocolLabel.m; sourceTree = "<group>"; };
		7C3F4EB15860B12A7D2ABBC8 /* MQTTCFSocketTransport.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCFSocketTransport.m; sourceTree = "<group>"; };
		7C813B8908AE41FBDA038B69 /* SDImageIOCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageIOCoder.h; sourceTree = "<group>"; };
		7C8E03C183B7F0BFCCF93D3C /* SDWebImageDownloaderConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderConfig.m; sourceTree = "<group>"; };
		7CB6BF8CA0FB51CB187EF20E /* MQTTLog.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTLog.m; sourceTree = "<group>"; };
		7D5D6452E883910714CC90D8 /* ParameterStillCalculateAdverbBoldface.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ParameterStillCalculateAdverbBoldface.m; sourceTree = "<group>"; };
		7DACB72F4DAF3E8507DF1F26 /* NSButton+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSButton+WebCache.h"; sourceTree = "<group>"; };
		7E0B305297F5073C3490BDBE /* NSBezierPath+SDRoundedCorners.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSBezierPath+SDRoundedCorners.m"; sourceTree = "<group>"; };
		7E1C4A1E26051D2535645C05 /* MQTTStrict.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTStrict.m; sourceTree = "<group>"; };
		7E49D2DBC439824A8F6D7A88 /* FixtureDrive.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FixtureDrive.h; sourceTree = "<group>"; };
		7F36F83C66BF2ABC423F2AAE /* UIDevice+BarDevice.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIDevice+BarDevice.h"; sourceTree = "<group>"; };
		7F786B34C25438FE40A1BC80 /* GroupedCollectViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GroupedCollectViewController.h; sourceTree = "<group>"; };
		7FD99A55FB6BF2CFF90809E5 /* Adobe.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Adobe.h; sourceTree = "<group>"; };
		806E61F6010E3BF2D703150E /* SDImageCachesManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCachesManager.h; sourceTree = "<group>"; };
		80890ED257B46A7E4D6D2B37 /* CellTapCan.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CellTapCan.m; sourceTree = "<group>"; };
		80D849B37F13C2B97CE1FA9E /* TildeScanPlaneWindow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TildeScanPlaneWindow.h; sourceTree = "<group>"; };
		80F029319B8502440B280082 /* SDFileAttributeHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDFileAttributeHelper.m; sourceTree = "<group>"; };
		816BDB33EC2F6DD5D3A85C27 /* LikeAsleepViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LikeAsleepViewController.m; sourceTree = "<group>"; };
		826A397D003A8BBB583475B7 /* SendEraBlobRet.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SendEraBlobRet.m; sourceTree = "<group>"; };
		8296299DD79244266387E372 /* SDImageLoader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageLoader.h; sourceTree = "<group>"; };
		8325D31701C5A1AF97B49EC7 /* UIViewController+BoxViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIViewController+BoxViewController.h"; sourceTree = "<group>"; };
		83F42FB69264565B5A4F7AC8 /* DownloadBondLongSucceededTen.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DownloadBondLongSucceededTen.h; sourceTree = "<group>"; };
		844D053014924000C84552B2 /* NSButton+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSButton+WebCache.m"; sourceTree = "<group>"; };
		8482282A8FA54219A986DEFF /* MQTTInMemoryPersistence.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTInMemoryPersistence.m; sourceTree = "<group>"; };
		8555A76523BD1EE55FAC79C3 /* GarbageAndViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GarbageAndViewController.h; sourceTree = "<group>"; };
		86952889D487204ECCFE8524 /* MQTTStrict.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTStrict.h; sourceTree = "<group>"; };
		87936D27E5C11C7234DE055D /* BitOwnPathView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BitOwnPathView.h; sourceTree = "<group>"; };
		884817DE23B8A78CB52F99CB /* NSImage+Compatibility.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSImage+Compatibility.h"; sourceTree = "<group>"; };
		88F73A3CDD3491E1A37B2C7C /* SDImageCacheConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCacheConfig.m; sourceTree = "<group>"; };
		895251A9C85D040D0551EDD6 /* NotNotConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NotNotConfig.m; sourceTree = "<group>"; };
		8A3B40C339176206225E729D /* UIView+WebCacheState.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCacheState.h"; sourceTree = "<group>"; };
		8A9D158F1F0B06F8FF80A7AF /* SDImageCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCoder.m; sourceTree = "<group>"; };
		8AD8264D8CD66F6C9A6C2CB5 /* MakerCarbon.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MakerCarbon.m; sourceTree = "<group>"; };
		8AFF7D6AEF62C3CCA694403E /* SDImageFramePool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageFramePool.h; sourceTree = "<group>"; };
		8B2BDD97E75E6A0AAE4F153F /* DryBarInterKey.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DryBarInterKey.m; sourceTree = "<group>"; };
		8B9201D6F397E422509FFEC9 /* DirectTouchAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DirectTouchAction.h; sourceTree = "<group>"; };
		8B9985503C2558EBED8A522B /* MASCompositeConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASCompositeConstraint.m; sourceTree = "<group>"; };
		8C46394F770D59B80AD06D0E /* CaseBurstViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CaseBurstViewController.h; sourceTree = "<group>"; };
		8EA4F67EF7A7425A5363BED3 /* SayUseTurn.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SayUseTurn.h; sourceTree = "<group>"; };
		8EC5EF980D4750DF4118E059 /* LogLowerHexManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LogLowerHexManager.m; sourceTree = "<group>"; };
		8F443F99646BFBF13B997E50 /* MQTTPersistence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTPersistence.h; sourceTree = "<group>"; };
		8F63D756D740D1D28AD21035 /* OverlapOurManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OverlapOurManager.h; sourceTree = "<group>"; };
		8FD49831B42BCB4EB8F826EB /* SobAlertView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SobAlertView.h; sourceTree = "<group>"; };
		8FDCD3EF8AB3AC2164CB8618 /* FrequencyInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FrequencyInfo.m; sourceTree = "<group>"; };
		8FF9D0CF510B86703F742ED6 /* CanBusEnableModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CanBusEnableModel.h; sourceTree = "<group>"; };
		90663872276239701FF450A7 /* SDCallbackQueue.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDCallbackQueue.m; sourceTree = "<group>"; };
		910F5B4B70CBFD2210325BDB /* ConstantAdditionsValueRadioMouseYou.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ConstantAdditionsValueRadioMouseYou.m; sourceTree = "<group>"; };
		911C8E95F97D6F2B815B80FF /* GrossScrap.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = GrossScrap.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		918825460FD0F4E77785BD33 /* SDImageTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageTransformer.h; sourceTree = "<group>"; };
		929825736A2C48EE78582E27 /* MQTTCFSocketTransport.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCFSocketTransport.h; sourceTree = "<group>"; };
		93359E4BDBFCA0096391994F /* SDAnimatedImageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImageView.m; sourceTree = "<group>"; };
		933EA8BEFF1633D2E630A542 /* SDImageCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCache.m; sourceTree = "<group>"; };
		9382D750D033E0535E7A4FCB /* BendRope.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BendRope.h; sourceTree = "<group>"; };
		9426CA6FFCAC3071BBE411C5 /* FigureGetSonInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FigureGetSonInfo.h; sourceTree = "<group>"; };
		952F3717A40F0A4F70C0BD03 /* SplatManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SplatManager.h; sourceTree = "<group>"; };
		95E416A3926FF9B3E2930DAF /* LongRaceView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LongRaceView.h; sourceTree = "<group>"; };
		960445813D0E8366023112B0 /* ReconnectTimer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReconnectTimer.h; sourceTree = "<group>"; };
		961EE029580B54701D59C539 /* PolarEachViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PolarEachViewController.m; sourceTree = "<group>"; };
		96C5F770FDD202C53F513DE6 /* DryBarInterKey.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DryBarInterKey.h; sourceTree = "<group>"; };
		97367A094F7D94128C5A2DD1 /* GrossScrap.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GrossScrap.m; sourceTree = "<group>"; };
		97E75C2DAC9C5D9E51025C54 /* SongArmBleedManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SongArmBleedManager.m; sourceTree = "<group>"; };
		9818C45335B785FE986EA794 /* MQTTSession.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSession.m; sourceTree = "<group>"; };
		98EC7A95842807C3A4ABC105 /* ViewController+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "ViewController+MASAdditions.m"; sourceTree = "<group>"; };
		98F20B819A90A4CD76469ABF /* MQTTSSLSecurityPolicyTransport.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicyTransport.m; sourceTree = "<group>"; };
		99A0329A38A350A2284E86CE /* BigPrologHasBankersWon.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BigPrologHasBankersWon.h; sourceTree = "<group>"; };
		99BE632F1D381DA69F954F51 /* TowerHisManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TowerHisManager.h; sourceTree = "<group>"; };
		9A73451FB7580B8846B64D48 /* NSLayoutConstraint+MASDebugAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSLayoutConstraint+MASDebugAdditions.m"; sourceTree = "<group>"; };
		9A857E86BDB0F72D9EC1FD6D /* GCDTimer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GCDTimer.m; sourceTree = "<group>"; };
		9AB6B4825C7BBFFAF75AAC39 /* UIImageView+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+WebCache.m"; sourceTree = "<group>"; };
		9B3F4339078396977D1071E9 /* NSObject+WayModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSObject+WayModel.m"; sourceTree = "<group>"; };
		9BE06F3F6DED35454604CCE9 /* SDWebImageManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageManager.h; sourceTree = "<group>"; };
		9C631BB8E26620EE1A705852 /* VerboseDigestSignUnorderedSynthesis.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VerboseDigestSignUnorderedSynthesis.m; sourceTree = "<group>"; };
		9CCD6207DE38B438F42ACAE9 /* ScriptsFourthViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ScriptsFourthViewController.h; sourceTree = "<group>"; };
		9D025ABF2D1413C216DAEDAF /* UIColor+GetColor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIColor+GetColor.h"; sourceTree = "<group>"; };
		9D1B2BF8242D832E71C2229A /* MQTTClient.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTClient.h; sourceTree = "<group>"; };
		9E8D772A2CC7288306A9F533 /* NSError+FootnoteOdd.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSError+FootnoteOdd.h"; sourceTree = "<group>"; };
		9F019B5F911FFE0CEA26BD57 /* SDImageFrame.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageFrame.m; sourceTree = "<group>"; };
		9F1921F51A5F4C8C27A677DF /* MQTTSSLSecurityPolicy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicy.m; sourceTree = "<group>"; };
		9FA029A8CE0393069445EE34 /* ContactAlienHusbandSuperiorsEstimated.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ContactAlienHusbandSuperiorsEstimated.h; sourceTree = "<group>"; };
		9FBFEBDD8B76544AD505EA97 /* InferLoadStone.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InferLoadStone.m; sourceTree = "<group>"; };
		A0C3F888FCE6646A9D8E570D /* InferLoadStone.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InferLoadStone.h; sourceTree = "<group>"; };
		A16C21678357C3769FFAAD4C /* FillerBus.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FillerBus.m; sourceTree = "<group>"; };
		A1F5E0CDE4EF1FAF3501CAE3 /* UIViewController+BoxViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+BoxViewController.m"; sourceTree = "<group>"; };
		A2DD5F3E823868BD057D53E7 /* SDImageFrame.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageFrame.h; sourceTree = "<group>"; };
		A33D467241A6B9AD5C265B19 /* NSString+BedLow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+BedLow.h"; sourceTree = "<group>"; };
		A45BCE8A0AAFD9EF4320949C /* ReconnectTimer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReconnectTimer.m; sourceTree = "<group>"; };
		A4C71E0853BBFA77EB1A4CD7 /* SDWebImageDownloader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloader.h; sourceTree = "<group>"; };
		A6A4F39B3E082AA46A7EAE3E /* MarkupInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MarkupInfo.h; sourceTree = "<group>"; };
		A7611D0E441C837EBB1C8E45 /* BendRope.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BendRope.m; sourceTree = "<group>"; };
		A79FF5F6358D7DC093C923D0 /* FillerBus.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FillerBus.h; sourceTree = "<group>"; };
		A8534AEAF52C53A9AAF39924 /* Dominant.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Dominant.m; sourceTree = "<group>"; };
		A8E2731B992E7D07432E8E22 /* AtomHasDogViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AtomHasDogViewController.m; sourceTree = "<group>"; };
		A9B149D11C468671532D9859 /* SDImageHEICCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageHEICCoder.m; sourceTree = "<group>"; };
		AA1FE06D8E08EDF887DBBDC3 /* FoodHoursHave.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FoodHoursHave.h; sourceTree = "<group>"; };
		AA3F4130C5D1930F0F65CC3D /* SDImageGraphics.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageGraphics.h; sourceTree = "<group>"; };
		AA6C9D551DD261FBE522E2ED /* SDWebImageCompat.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCompat.m; sourceTree = "<group>"; };
		AAF87AD30A40EDEA45D6772C /* MQTTCFSocketDecoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCFSocketDecoder.h; sourceTree = "<group>"; };
		ABA4989C34A6160D10B93275 /* Dominant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Dominant.h; sourceTree = "<group>"; };
		ABF09445C28227568CAA5D19 /* MASConstraintMaker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASConstraintMaker.m; sourceTree = "<group>"; };
		ABFC2C1CF2846CDF3C758E56 /* BigPrologHasBankersWon.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BigPrologHasBankersWon.m; sourceTree = "<group>"; };
		AC31E4F862D1FA26436419BF /* SeeBusStepList.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SeeBusStepList.h; sourceTree = "<group>"; };
		AD008523C8762CE3FDE9E126 /* SDWebImageDownloaderResponseModifier.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderResponseModifier.m; sourceTree = "<group>"; };
		ADC89DC15EF784D12132229E /* AtomHasDogViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AtomHasDogViewController.h; sourceTree = "<group>"; };
		AF7722249232BF8AA6956F33 /* CiphersEarModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CiphersEarModel.m; sourceTree = "<group>"; };
		B05E6EF5C880AB75F6314663 /* SDAnimatedImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImage.h; sourceTree = "<group>"; };
		B15A1174DD0FEAB4D55CD6A0 /* SDAssociatedObject.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAssociatedObject.h; sourceTree = "<group>"; };
		B3537F21C8DDFA381419F5D3 /* UIImage+ForceDecode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+ForceDecode.m"; sourceTree = "<group>"; };
		B3913A4615556C08D8D7697C /* View+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "View+MASAdditions.h"; sourceTree = "<group>"; };
		B39FBC08DBA11ED48FDCA170 /* ModeRunningDelayRaiseStrokingTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ModeRunningDelayRaiseStrokingTool.h; sourceTree = "<group>"; };
		B3B861B24B94AB2B0F4FB89E /* MQTTCFSocketEncoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCFSocketEncoder.m; sourceTree = "<group>"; };
		B3EF13F323C89D82167485F4 /* RedoBackupMaintainPhraseHormone.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RedoBackupMaintainPhraseHormone.h; sourceTree = "<group>"; };
		B421363D1A90300A738CD851 /* SDWebImageCacheSerializer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageCacheSerializer.h; sourceTree = "<group>"; };
		B4973CA7D716AA569EFA2515 /* DueLockAirTooViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DueLockAirTooViewController.m; sourceTree = "<group>"; };
		B4C82662C4880AA1C18A3E1C /* UIImage+MemoryCacheCost.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+MemoryCacheCost.m"; sourceTree = "<group>"; };
		B4F228E6D28A14DB5E75C3E1 /* SinPushCapFlowButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SinPushCapFlowButton.m; sourceTree = "<group>"; };
		B4F88381B59032F0E6AEB62F /* MQTTCFSocketEncoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCFSocketEncoder.h; sourceTree = "<group>"; };
		B63BC79C78EF011BE5341BDB /* MarkupInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MarkupInfo.m; sourceTree = "<group>"; };
		B674C429CFB3A07337B7EDFE /* MQTTCFSocketDecoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCFSocketDecoder.m; sourceTree = "<group>"; };
		B780ED46527409568520807B /* YouWireCupCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = YouWireCupCell.m; sourceTree = "<group>"; };
		B8F1E5B4FD030FDF052136B1 /* SDAnimatedImageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImageView.h; sourceTree = "<group>"; };
		B9035013C150E1B068E40367 /* InverseArmAreNiacinAllocator.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InverseArmAreNiacinAllocator.h; sourceTree = "<group>"; };
		B9D628FF5F86D0C511C5D3C1 /* SDWebImageError.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageError.m; sourceTree = "<group>"; };
		BBE6651B03CDB86C221E2664 /* CountViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CountViewController.m; sourceTree = "<group>"; };
		BBFFDBBF311A296ABA71FDB6 /* CheckSmoothButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CheckSmoothButton.h; sourceTree = "<group>"; };
		BC20AAC905D0DBA1CED83CBE /* SDWebImageIndicator.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageIndicator.m; sourceTree = "<group>"; };
		BDAA633582DA82F314FE0308 /* WindowsSobInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WindowsSobInfo.h; sourceTree = "<group>"; };
		BF335B9943B67D0267FF3C3C /* UIImage+Transform.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Transform.m"; sourceTree = "<group>"; };
		BF7002E9182182E66DDAE441 /* MASViewConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASViewConstraint.h; sourceTree = "<group>"; };
		BF8A9E4E49E4371F6891B8AF /* ScriptsFourthViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ScriptsFourthViewController.m; sourceTree = "<group>"; };
		BFA4C898A79394DD0A112AE5 /* PivotForManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PivotForManager.m; sourceTree = "<group>"; };
		C11BDE88063FEC2D0412B542 /* EncodingsDarkWasPasswordFilteringCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EncodingsDarkWasPasswordFilteringCell.m; sourceTree = "<group>"; };
		C1350EC177815442723EAE11 /* SDWebImageCacheKeyFilter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageCacheKeyFilter.h; sourceTree = "<group>"; };
		C1F3DBAA72D1C0491BE25FC8 /* ConfirmProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ConfirmProtocol.h; sourceTree = "<group>"; };
		C21EA3B199C7ECAC4F04631F /* ArtworkModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ArtworkModel.h; sourceTree = "<group>"; };
		C222DC51AC392A3A2A7E03CB /* TurnWordTipViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TurnWordTipViewController.m; sourceTree = "<group>"; };
		C2EFBAD99545AEDB27629D0C /* GrossScrap.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GrossScrap.h; sourceTree = "<group>"; };
		C3BC3DAF0C82C2A0ACD5A3D0 /* SDImageCacheConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCacheConfig.h; sourceTree = "<group>"; };
		C4E2F31339A7F676EBE2EEC9 /* SDWebImageTransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageTransition.h; sourceTree = "<group>"; };
		C58B6424C4532D20AE232524 /* EphemeralViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EphemeralViewController.m; sourceTree = "<group>"; };
		C72851D6305D2CC455F459D4 /* RareRaceColor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RareRaceColor.m; sourceTree = "<group>"; };
		C7CED6E36657A83A1125619A /* SDWebImageManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageManager.m; sourceTree = "<group>"; };
		C879A314FF2F9FADA35B7CB1 /* SDWebImageDownloader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloader.m; sourceTree = "<group>"; };
		C8C381B0E95E844A9E2D9477 /* SDImageCacheDefine.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCacheDefine.m; sourceTree = "<group>"; };
		C8CFB9F29CD950FDECB97979 /* SDWebImageDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDefine.h; sourceTree = "<group>"; };
		C8D7F79A01761A9B77B0D88F /* YouWireCupCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = YouWireCupCell.h; sourceTree = "<group>"; };
		CAEACE0711E5978589266D6A /* MQTTDecoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTDecoder.h; sourceTree = "<group>"; };
		CBEBB6D570A10D533CF56400 /* SemicolonViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SemicolonViewController.m; sourceTree = "<group>"; };
		CBF6933420F4D34E87C3BD95 /* DirectTouchAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DirectTouchAction.m; sourceTree = "<group>"; };
		CE4786DE6961098D8E6CCFAC /* NSData+PinThat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSData+PinThat.h"; sourceTree = "<group>"; };
		CEE505EED78D727EB9D40F04 /* DogTempPin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DogTempPin.m; sourceTree = "<group>"; };
		CF0E7E1B3AFF658D5FB5E800 /* BoxArtsCategoryClickedWrap.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BoxArtsCategoryClickedWrap.h; sourceTree = "<group>"; };
		CF5B15F2791506236A7E8AF4 /* SpeakPaperFair.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SpeakPaperFair.m; sourceTree = "<group>"; };
		D033E2451C9BB3548B44FAA9 /* SDImageAWebPCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageAWebPCoder.h; sourceTree = "<group>"; };
		D038C382E4D9E79E7C54AA90 /* PoloFigure.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PoloFigure.m; sourceTree = "<group>"; };
		D0FC3A44A8F54271DCB2F555 /* MQTTMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTMessage.m; sourceTree = "<group>"; };
		D1162E48F16A5587B5834A55 /* ViewController+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "ViewController+MASAdditions.h"; sourceTree = "<group>"; };
		D18AD52E7501C087DFF4064A /* SDWebImageCompat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageCompat.h; sourceTree = "<group>"; };
		D22838B3AA8DCCA73C62A211 /* SDWebImageDownloaderDecryptor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderDecryptor.m; sourceTree = "<group>"; };
		D3A2B233264052BBF8AE259D /* UIColor+SDHexString.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIColor+SDHexString.h"; sourceTree = "<group>"; };
		D48688A96AFCF354716D4106 /* NSData+ImageContentType.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSData+ImageContentType.h"; sourceTree = "<group>"; };
		D4953E7AC4DAF7EB614C7BBE /* MQTTSessionLegacy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSessionLegacy.m; sourceTree = "<group>"; };
		D54D31DBAE03F84E6F8F3925 /* UIColor+GetColor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIColor+GetColor.m"; sourceTree = "<group>"; };
		D5753DEF9F08E09790649A1F /* UIImage+MultiFormat.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+MultiFormat.m"; sourceTree = "<group>"; };
		D75D6974ED3BBEB39F8B882B /* LiveStyleInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LiveStyleInfo.m; sourceTree = "<group>"; };
		D7AB1A05A52B80B0C9F61B5D /* NSArray+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASAdditions.h"; sourceTree = "<group>"; };
		D80AD2662A0EFE9686BC96A6 /* OutToast.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OutToast.m; sourceTree = "<group>"; };
		D889D3FFA19134627FB7490A /* MQTTCoreDataPersistence.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCoreDataPersistence.m; sourceTree = "<group>"; };
		D8E0A9108BAD255FD0B9B73E /* CheckSmoothButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CheckSmoothButton.m; sourceTree = "<group>"; };
		D94C964EBF95600633E0ACDF /* SDWebImageTransitionInternal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageTransitionInternal.h; sourceTree = "<group>"; };
		DBA2BA9B6C920BF73364F218 /* KoreanSeeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = KoreanSeeViewController.m; sourceTree = "<group>"; };
		DC6D736C7D7A708AFF1ED28A /* UIImage+ForceDecode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+ForceDecode.h"; sourceTree = "<group>"; };
		DC9B41D076BEFF57534297AC /* SDImageHEICCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageHEICCoder.h; sourceTree = "<group>"; };
		DCE4CACD3FAAD6725A7C1C08 /* ShapeWinNet.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShapeWinNet.h; sourceTree = "<group>"; };
		DD839CEE8E1CF135C858713F /* SDInternalMacros.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDInternalMacros.h; sourceTree = "<group>"; };
		DDC4885CB05DCF91C7E7D03F /* MusicalManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MusicalManager.m; sourceTree = "<group>"; };
		DDE760ED9A7F247D97D9A773 /* HitUnableManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HitUnableManager.m; sourceTree = "<group>"; };
		DE5F03E4D6E4D90258589F1F /* SonAssetWinNetCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SonAssetWinNetCell.h; sourceTree = "<group>"; };
		DEBB7F0D921167B046720EE2 /* MASViewAttribute.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASViewAttribute.h; sourceTree = "<group>"; };
		E1D193CB430460776F20FD9E /* SemicolonThe.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SemicolonThe.m; sourceTree = "<group>"; };
		E2CB455F5978E6B3CF42D5C3 /* NSObject+WayModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSObject+WayModel.h"; sourceTree = "<group>"; };
		E2EFB371955803F2045FDF3F /* VerboseDigestSignUnorderedSynthesis.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VerboseDigestSignUnorderedSynthesis.h; sourceTree = "<group>"; };
		E38C8E3C273FDFD7844F2E76 /* KoreanSeeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = KoreanSeeViewController.h; sourceTree = "<group>"; };
		E3EEA740232D3DBCB1A85F08 /* RedoBackupMaintainPhraseHormone.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RedoBackupMaintainPhraseHormone.m; sourceTree = "<group>"; };
		E4A16B7F7CC40EBFD91FA9E4 /* LeapHueHeapSunViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LeapHueHeapSunViewController.h; sourceTree = "<group>"; };
		E58A16FAEBAD1EC98EB8904E /* SettingEncodingsStringMiterRefreshedSubgroup.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SettingEncodingsStringMiterRefreshedSubgroup.h; sourceTree = "<group>"; };
		E5F0F8109E51B91DC6607DC6 /* TowerHisManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TowerHisManager.m; sourceTree = "<group>"; };
		E61D933DFE8AD798918F8E6F /* YoungerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = YoungerViewController.h; sourceTree = "<group>"; };
		E644D62B85EC55C8D65EF74B /* TagSidebarView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TagSidebarView.m; sourceTree = "<group>"; };
		E6774C23291339746F26C8B3 /* MQTTLog.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTLog.h; sourceTree = "<group>"; };
		E679753715D0341E66919A22 /* NSString+SheHectares.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+SheHectares.m"; sourceTree = "<group>"; };
		E7AAD7321C9E92E3C77BC3A0 /* InteractItalicBouncingRegionsDimensionModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InteractItalicBouncingRegionsDimensionModel.h; sourceTree = "<group>"; };
		E7D264E4EAB565F1282B4BEC /* MQTTSSLSecurityPolicyDecoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicyDecoder.h; sourceTree = "<group>"; };
		E88C0ED9F44BCDED912961B6 /* SplatManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SplatManager.m; sourceTree = "<group>"; };
		E8C659EF4AB3C294AD8876DB /* MusicalManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MusicalManager.h; sourceTree = "<group>"; };
		E9ACAA719E5AAE897A74B2DA /* MASConstraintMaker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASConstraintMaker.h; sourceTree = "<group>"; };
		EA7E4B20AA86BF07DD5FF27E /* VerboseCombine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VerboseCombine.h; sourceTree = "<group>"; };
		EAC32D15A3C3520F677E420A /* SDImageCodersManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCodersManager.h; sourceTree = "<group>"; };
		EAD34BAE17FA0F85DB72D2EE /* MQTTInMemoryPersistence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTInMemoryPersistence.h; sourceTree = "<group>"; };
		EC6095C0F081AE9B2A447E4D /* OpenNetTreeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OpenNetTreeViewController.h; sourceTree = "<group>"; };
		ED911157B82E8EAFFA2D9D94 /* SeeBusStepList.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SeeBusStepList.m; sourceTree = "<group>"; };
		EDB144A9AC8943F749E8FBDF /* CellTapCan.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CellTapCan.h; sourceTree = "<group>"; };
		EE0A2D9FCF02C9B41F383659 /* FigureGetSonInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FigureGetSonInfo.m; sourceTree = "<group>"; };
		EE25814C42D01892C4082552 /* UIImage+MemoryCacheCost.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+MemoryCacheCost.h"; sourceTree = "<group>"; };
		EE91E2BC708C254977FCFA0E /* IrishAlbanian.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IrishAlbanian.h; sourceTree = "<group>"; };
		EEA58B5534F6981BE89ED80D /* SDImageLoadersManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageLoadersManager.m; sourceTree = "<group>"; };
		EEF22BE46A3C7BE43553DAD2 /* NSObject+MenPunjabiInfoSongLost.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSObject+MenPunjabiInfoSongLost.h"; sourceTree = "<group>"; };
		EF6EC7506679990D58D356A6 /* HeaderManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HeaderManager.h; sourceTree = "<group>"; };
		EFCC696346F2F466955E2F9E /* SDAnimatedImageView+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SDAnimatedImageView+WebCache.h"; sourceTree = "<group>"; };
		EFEA9B14BAAE8070ECC9E14F /* MASUtilities.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASUtilities.h; sourceTree = "<group>"; };
		F0BBA238C2C5CAAEC3C5B0B8 /* SDCallbackQueue.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDCallbackQueue.h; sourceTree = "<group>"; };
		F1380F186F9E6144C2DE26CA /* ArtworkModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ArtworkModel.m; sourceTree = "<group>"; };
		F13CC0EA8C3A370B941C6771 /* LongRaceView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LongRaceView.m; sourceTree = "<group>"; };
		F2348336B7C9E975C460AB55 /* SayUseTurn.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SayUseTurn.m; sourceTree = "<group>"; };
		F274A76DDAFD6B314EAB213C /* ProcessCircularTemporalCountryFlag.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ProcessCircularTemporalCountryFlag.h; sourceTree = "<group>"; };
		F3C8853DC9B4097B37BE0CB5 /* UIImageView+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImageView+WebCache.h"; sourceTree = "<group>"; };
		F4A69E0D5D2F7EB87ABE1B4C /* MQTTSession.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSession.h; sourceTree = "<group>"; };
		F5290FB23CF5BEFFAB1B4EDE /* UIImageView+HighlightedWebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImageView+HighlightedWebCache.h"; sourceTree = "<group>"; };
		F56A5E730808513DEE0AB1F6 /* OverlapOurManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OverlapOurManager.m; sourceTree = "<group>"; };
		F5CBC881AD560B0186CF9E39 /* SDImageGraphics.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageGraphics.m; sourceTree = "<group>"; };
		F6927318C51AE6047559AB0B /* SDAssociatedObject.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAssociatedObject.m; sourceTree = "<group>"; };
		F6EC2CB6E609496E4AACBCFA /* MusicalRadians.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MusicalRadians.h; sourceTree = "<group>"; };
		F6F83D66EB1ADC89D69AFE66 /* UIView+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCache.m"; sourceTree = "<group>"; };
		F7160B4CB79B64ECE5299318 /* PopPickMayViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PopPickMayViewController.m; sourceTree = "<group>"; };
		F732C4825DAD97C8CAAA7BE2 /* NSArray+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSArray+MASAdditions.m"; sourceTree = "<group>"; };
		F75AC053055E4FEFBC9455D2 /* SDFileAttributeHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDFileAttributeHelper.h; sourceTree = "<group>"; };
		F88E05BFAE9117371EC429C6 /* SonAssetWinNetCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SonAssetWinNetCell.m; sourceTree = "<group>"; };
		F93326F810C2C007AA7187A6 /* SDAnimatedImagePlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImagePlayer.m; sourceTree = "<group>"; };
		FA6C84C77A89406F708EBCCB /* TurnWordTipViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TurnWordTipViewController.h; sourceTree = "<group>"; };
		FA7D6EEE5407F35673F0C6DB /* EarSigningManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EarSigningManager.m; sourceTree = "<group>"; };
		FBE62222A98340344572CF44 /* SettingEncodingsStringMiterRefreshedSubgroup.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SettingEncodingsStringMiterRefreshedSubgroup.m; sourceTree = "<group>"; };
		FE20C76967514DAFC4526ACD /* SDWebImageDownloaderRequestModifier.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderRequestModifier.h; sourceTree = "<group>"; };
		FECC5E01A667C3EF9AFF1B3E /* MQTTCoreDataPersistence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCoreDataPersistence.h; sourceTree = "<group>"; };
		FF3AE64B18C101B5CCF3EFCB /* MQTTSSLSecurityPolicyDecoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicyDecoder.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		E3F63D22B6938B515AFBE756 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0530984A752E191E813E12CA /* PrepMen */ = {
			isa = PBXGroup;
			children = (
				C411D06063D3CDCBD7B9B7AD /* Cover */,
				73B4BDD69A588F8AEFE99F9D /* DarkWay */,
				0D1A74DC2F07BE6D877FCEA7 /* Great */,
				3695AD8A8301F36F262BEE31 /* NotNotConfig.h */,
				895251A9C85D040D0551EDD6 /* NotNotConfig.m */,
				22FB6D64825E583D595501A0 /* DueSumZoom.h */,
				47CF62633E6CAB07C137AECB /* DeepApplyProtocol.h */,
			);
			path = PrepMen;
			sourceTree = "<group>";
		};
		06BD20D8A6BCE58B8F4E8D99 /* DiskAskAnySong */ = {
			isa = PBXGroup;
			children = (
				4CE7D497208591F65E3B51B5 /* FloaterBendTerabytesCopperTaggingReasonViewController.h */,
				0F13BC6A6309DB96973E773D /* FloaterBendTerabytesCopperTaggingReasonViewController.m */,
				EDB144A9AC8943F749E8FBDF /* CellTapCan.h */,
				80890ED257B46A7E4D6D2B37 /* CellTapCan.m */,
				36FF7503A1801D2D91E90485 /* SinPushCapFlowButton.h */,
				B4F228E6D28A14DB5E75C3E1 /* SinPushCapFlowButton.m */,
			);
			path = DiskAskAnySong;
			sourceTree = "<group>";
		};
		086700A5E835274E45C7DF92 /* Day */ = {
			isa = PBXGroup;
			children = (
				EE91E2BC708C254977FCFA0E /* IrishAlbanian.h */,
				57BE0F078288DFE28782B334 /* IrishAlbanian.m */,
				AA1FE06D8E08EDF887DBBDC3 /* FoodHoursHave.h */,
				6340D0B9C30B559043DD35ED /* FoodHoursHave.m */,
				C21EA3B199C7ECAC4F04631F /* ArtworkModel.h */,
				F1380F186F9E6144C2DE26CA /* ArtworkModel.m */,
				07AB7BF57284036AC3E7843C /* RareRaceColor.h */,
				C72851D6305D2CC455F459D4 /* RareRaceColor.m */,
				67A3C139357BCF84AE8773B1 /* SemicolonThe.h */,
				E1D193CB430460776F20FD9E /* SemicolonThe.m */,
				BDAA633582DA82F314FE0308 /* WindowsSobInfo.h */,
				54B82C9C2C7803067141F0A2 /* WindowsSobInfo.m */,
				83F42FB69264565B5A4F7AC8 /* DownloadBondLongSucceededTen.h */,
				45349E2ECA273FB15CC01379 /* DownloadBondLongSucceededTen.m */,
				EA7E4B20AA86BF07DD5FF27E /* VerboseCombine.h */,
				704A35DF36C9A6988B35B4DE /* VerboseCombine.m */,
				60501A564493ACE768E36507 /* PronounLegibleDefinesFlightsAttitudeBit.h */,
				39948EDBDDFFFF378096A2E0 /* PronounLegibleDefinesFlightsAttitudeBit.m */,
				F274A76DDAFD6B314EAB213C /* ProcessCircularTemporalCountryFlag.h */,
				18036C457522B87FD2F487AE /* ProcessCircularTemporalCountryFlag.m */,
				2F4939D7CA2AB7805AC2889A /* RetMenuLastPanInfo.h */,
				752E3F6D071D3BCFDEE6736B /* RetMenuLastPanInfo.m */,
				9426CA6FFCAC3071BBE411C5 /* FigureGetSonInfo.h */,
				EE0A2D9FCF02C9B41F383659 /* FigureGetSonInfo.m */,
				3187FED2E7DD23E9195A0903 /* BevelNarrative.h */,
				075A98A85E24ADCAB95E62DA /* BevelNarrative.m */,
				6E64521DC7FB648B69CDEF12 /* FrequencyInfo.h */,
				8FDCD3EF8AB3AC2164CB8618 /* FrequencyInfo.m */,
			);
			path = Day;
			sourceTree = "<group>";
		};
		0B4099BF47C10DFA7F32FFAB = {
			isa = PBXGroup;
			children = (
				FFCE253E02760F058E80AB1E /* GrossScrap */,
				509CCF2F29497DC71FE64377 /* Products */,
			);
			sourceTree = "<group>";
		};
		0D1A74DC2F07BE6D877FCEA7 /* Great */ = {
			isa = PBXGroup;
			children = (
				E7AAD7321C9E92E3C77BC3A0 /* InteractItalicBouncingRegionsDimensionModel.h */,
				506FE79C8374AE774CCB2D39 /* InteractItalicBouncingRegionsDimensionModel.m */,
			);
			path = Great;
			sourceTree = "<group>";
		};
		1C687CAFFB9B7078B3C2E44A /* Terminal */ = {
			isa = PBXGroup;
			children = (
				ABA4989C34A6160D10B93275 /* Dominant.h */,
				A8534AEAF52C53A9AAF39924 /* Dominant.m */,
				AC80B3787E218507B535F1D2 /* SedentaryNumericHungarianDevicesFurlongs */,
				3D28015DB72ADE20B9823492 /* BankStroked */,
				CA66DFBA57230D3DC7C086B0 /* MQTTClient */,
				0530984A752E191E813E12CA /* PrepMen */,
				F5942DE82FB69AA460077D61 /* LooperLength */,
				BA20C2B9AA005988C838D82C /* SDWebImage */,
				DF96301D6E5D71A035B1962A /* Masonry */,
				5049D2E9FCCBCDC65DEE0A19 /* FlagPlan */,
				7778A21487780F7879C8FC83 /* BedFull */,
				9975741CC9F269B780932107 /* AllocatedShareTitleAdditionsLose */,
				A6A4F39B3E082AA46A7EAE3E /* MarkupInfo.h */,
				B63BC79C78EF011BE5341BDB /* MarkupInfo.m */,
				B39FBC08DBA11ED48FDCA170 /* ModeRunningDelayRaiseStrokingTool.h */,
				12735A0C46854BFF4033F457 /* ModeRunningDelayRaiseStrokingTool.m */,
			);
			path = Terminal;
			sourceTree = "<group>";
		};
		1DEE274415AFD55A978573A1 /* Hex */ = {
			isa = PBXGroup;
			children = (
				04A8D6BF6F61F10754AA2047 /* ParameterStillCalculateAdverbBoldface.h */,
				7D5D6452E883910714CC90D8 /* ParameterStillCalculateAdverbBoldface.m */,
				7E49D2DBC439824A8F6D7A88 /* FixtureDrive.h */,
				30CC03B955A70C1E299D9E97 /* FixtureDrive.m */,
				621613F59D0A1240676C9C33 /* RawUpsideOdd.h */,
				6D26887745EF2EBED0C11C07 /* RawUpsideOdd.m */,
				063DFD81C5A95D79BB63410D /* LiveStyleInfo.h */,
				D75D6974ED3BBEB39F8B882B /* LiveStyleInfo.m */,
				7A8C676CD9A57895ED3B026D /* SpeakPaperFair.h */,
				CF5B15F2791506236A7E8AF4 /* SpeakPaperFair.m */,
				76F559DDE845E315EDDCE279 /* ConstantAdditionsValueRadioMouseYou.h */,
				910F5B4B70CBFD2210325BDB /* ConstantAdditionsValueRadioMouseYou.m */,
				4CD08BB09A2A00613342E3F0 /* MakerCarbon.h */,
				8AD8264D8CD66F6C9A6C2CB5 /* MakerCarbon.m */,
			);
			path = Hex;
			sourceTree = "<group>";
		};
		219FB9F5398E6B8EB7313FD0 /* Late */ = {
			isa = PBXGroup;
			children = (
				E4A16B7F7CC40EBFD91FA9E4 /* LeapHueHeapSunViewController.h */,
				62BE62A55246194110072272 /* LeapHueHeapSunViewController.m */,
				E61D933DFE8AD798918F8E6F /* YoungerViewController.h */,
				3F89F36B7B42FCE0CCB8D46E /* YoungerViewController.m */,
				23B9AF9494261220F72DEBC1 /* LoopsEyeRomanController.h */,
				0975D156CFF4E760E0DD35C8 /* LoopsEyeRomanController.m */,
				2F1DC8B25B25146A5EA05DFD /* HitUnableManager.h */,
				DDE760ED9A7F247D97D9A773 /* HitUnableManager.m */,
				DCE4CACD3FAAD6725A7C1C08 /* ShapeWinNet.h */,
				795536FCA694F27781DB8D17 /* ShapeWinNet.m */,
				242947CD7310C773C8E5870B /* HandballProtocol.h */,
				4FB03109F8F8D76BA7F7BBA5 /* SendEraBlobRet.h */,
				826A397D003A8BBB583475B7 /* SendEraBlobRet.m */,
				522FBE91931224EFC4B8EFDF /* DogTempPin.h */,
				CEE505EED78D727EB9D40F04 /* DogTempPin.m */,
			);
			path = Late;
			sourceTree = "<group>";
		};
		2E7809454EAAB0A759E3724C /* FeaturedRomanUnlearnRegionsLarger */ = {
			isa = PBXGroup;
			children = (
				8C46394F770D59B80AD06D0E /* CaseBurstViewController.h */,
				5D4B02B18D4D9AAA58CE8CF2 /* CaseBurstViewController.m */,
				786820D06D995DB0FCE29C91 /* TwoVideoViewController.h */,
				2D0EC61AADCC9C4F22F80906 /* TwoVideoViewController.m */,
				3881C1C23C5DE581C44DAA68 /* PopPickMayViewController.h */,
				F7160B4CB79B64ECE5299318 /* PopPickMayViewController.m */,
			);
			path = FeaturedRomanUnlearnRegionsLarger;
			sourceTree = "<group>";
		};
		3D28015DB72ADE20B9823492 /* BankStroked */ = {
			isa = PBXGroup;
			children = (
				065A8DC6DCA25026A23A93EC /* SpaKinWhite.h */,
				642150167C12E0D160781B6E /* SpaKinWhite.m */,
			);
			path = BankStroked;
			sourceTree = "<group>";
		};
		48F5B2549F9E982E8B5E5448 /* Fix */ = {
			isa = PBXGroup;
			children = (
				72665F68328011A7C3B606C1 /* DetailedPutManager.h */,
				520D2EE66A5E9B9AF37E228E /* DetailedPutManager.m */,
				61B8877129E4ACB8CC88F8D2 /* VisionCousinManager.h */,
				3D5FD16300567A1F7F0FEC64 /* VisionCousinManager.m */,
				29CECAFAAD51234B54AE2852 /* LogLowerHexManager.h */,
				8EC5EF980D4750DF4118E059 /* LogLowerHexManager.m */,
				952F3717A40F0A4F70C0BD03 /* SplatManager.h */,
				E88C0ED9F44BCDED912961B6 /* SplatManager.m */,
				7A4A89951A76082008E6BDE9 /* ChromaFarManager.h */,
				3DDABA37309CA9C35520E37A /* ChromaFarManager.m */,
				0E57C97795C3156ECA8E396B /* PivotForManager.h */,
				BFA4C898A79394DD0A112AE5 /* PivotForManager.m */,
				0B0A101A5DD125FBD2C93468 /* BigPreviousManager.h */,
				11D101468D70C37A2D11091D /* BigPreviousManager.m */,
			);
			path = Fix;
			sourceTree = "<group>";
		};
		5049D2E9FCCBCDC65DEE0A19 /* FlagPlan */ = {
			isa = PBXGroup;
			children = (
				E2CB455F5978E6B3CF42D5C3 /* NSObject+WayModel.h */,
				9B3F4339078396977D1071E9 /* NSObject+WayModel.m */,
				3323343FA46D8E3EE37A7F16 /* NSString+StickySay.h */,
				4775D0F244E9F1A9EC07465B /* NSString+StickySay.m */,
				9D025ABF2D1413C216DAEDAF /* UIColor+GetColor.h */,
				D54D31DBAE03F84E6F8F3925 /* UIColor+GetColor.m */,
				8325D31701C5A1AF97B49EC7 /* UIViewController+BoxViewController.h */,
				A1F5E0CDE4EF1FAF3501CAE3 /* UIViewController+BoxViewController.m */,
				24207DCB35CED81A4095CC1D /* UIImage+OwnImage.h */,
				655FF22C8697C0ECFE0DE284 /* UIImage+OwnImage.m */,
				3F4135AE6EB31F1D2C41416D /* NSString+SheHectares.h */,
				E679753715D0341E66919A22 /* NSString+SheHectares.m */,
				EEF22BE46A3C7BE43553DAD2 /* NSObject+MenPunjabiInfoSongLost.h */,
				59ACC83996411FD32575C17C /* NSObject+MenPunjabiInfoSongLost.m */,
				4749A2EDBC9F6DDB425B5687 /* NSURL+MinWhoFive.h */,
				46FF86A625C1DEB63C82C39C /* NSURL+MinWhoFive.m */,
				A33D467241A6B9AD5C265B19 /* NSString+BedLow.h */,
				712E15DCDE60A47774B1A56F /* NSString+BedLow.m */,
				CE4786DE6961098D8E6CCFAC /* NSData+PinThat.h */,
				351DADE01FACBE096758CCB1 /* NSData+PinThat.m */,
				7F36F83C66BF2ABC423F2AAE /* UIDevice+BarDevice.h */,
				2F21AE936CD54329FF18C248 /* UIDevice+BarDevice.m */,
			);
			path = FlagPlan;
			sourceTree = "<group>";
		};
		509CCF2F29497DC71FE64377 /* Products */ = {
			isa = PBXGroup;
			children = (
				911C8E95F97D6F2B815B80FF /* GrossScrap.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		531593B778313F45855BA435 /* Font */ = {
			isa = PBXGroup;
			children = (
				06BD20D8A6BCE58B8F4E8D99 /* DiskAskAnySong */,
				F33103458B55B5EB4A1E92E5 /* AlignFlatten */,
				B45D91E61EF7840FBE9DB664 /* BagMolarFix */,
				8FD49831B42BCB4EB8F826EB /* SobAlertView.h */,
				119476203B3EE7F1F6B1F86B /* SobAlertView.m */,
				7B369E3BEEBA79BA4EBBB0C7 /* XXGProtocolLabel.h */,
				7C10FA3D701D9C37BCD6AB1F /* XXGProtocolLabel.m */,
				87936D27E5C11C7234DE055D /* BitOwnPathView.h */,
				41F160ED4AEA638266D25C54 /* BitOwnPathView.m */,
				64B6B6667792D1932DF167E6 /* EncodingsDarkWasPasswordFilteringCell.h */,
				C11BDE88063FEC2D0412B542 /* EncodingsDarkWasPasswordFilteringCell.m */,
				BBFFDBBF311A296ABA71FDB6 /* CheckSmoothButton.h */,
				D8E0A9108BAD255FD0B9B73E /* CheckSmoothButton.m */,
				7B6C2BC2A56A2DBF38D9C5CB /* OutToast.h */,
				D80AD2662A0EFE9686BC96A6 /* OutToast.m */,
				C8D7F79A01761A9B77B0D88F /* YouWireCupCell.h */,
				B780ED46527409568520807B /* YouWireCupCell.m */,
				610ACB12959C73B721D25E07 /* SlavicTapTextField.h */,
				4AEF1AE47130634EA83AB271 /* SlavicTapTextField.m */,
			);
			path = Font;
			sourceTree = "<group>";
		};
		60DE89861E700DA24A454A8E /* Core */ = {
			isa = PBXGroup;
			children = (
				7DACB72F4DAF3E8507DF1F26 /* NSButton+WebCache.h */,
				844D053014924000C84552B2 /* NSButton+WebCache.m */,
				D48688A96AFCF354716D4106 /* NSData+ImageContentType.h */,
				2A8641B8D2CB4BC84A3F9C6E /* NSData+ImageContentType.m */,
				884817DE23B8A78CB52F99CB /* NSImage+Compatibility.h */,
				3D6092DFE0F964A3E6237155 /* NSImage+Compatibility.m */,
				B05E6EF5C880AB75F6314663 /* SDAnimatedImage.h */,
				761387AED213CDF98CA96E54 /* SDAnimatedImage.m */,
				5CC542103E1AF74C78DDC910 /* SDAnimatedImagePlayer.h */,
				F93326F810C2C007AA7187A6 /* SDAnimatedImagePlayer.m */,
				5E6AA5D3FD552DAC226BF709 /* SDAnimatedImageRep.h */,
				2684143DA4ED106F85A2D860 /* SDAnimatedImageRep.m */,
				B8F1E5B4FD030FDF052136B1 /* SDAnimatedImageView.h */,
				93359E4BDBFCA0096391994F /* SDAnimatedImageView.m */,
				EFCC696346F2F466955E2F9E /* SDAnimatedImageView+WebCache.h */,
				7AD9233AFD2366371FBB913C /* SDAnimatedImageView+WebCache.m */,
				F0BBA238C2C5CAAEC3C5B0B8 /* SDCallbackQueue.h */,
				90663872276239701FF450A7 /* SDCallbackQueue.m */,
				09590734533E49DE06264FE9 /* SDDiskCache.h */,
				031988A0A1CB41E50B93A56C /* SDDiskCache.m */,
				57C596B81CAB498DC30A1C86 /* SDGraphicsImageRenderer.h */,
				03265601EED058DA6453672E /* SDGraphicsImageRenderer.m */,
				238A4A118ADA817A7F6F17DD /* SDImageAPNGCoder.h */,
				774B65842412B1B4A37BCFB0 /* SDImageAPNGCoder.m */,
				D033E2451C9BB3548B44FAA9 /* SDImageAWebPCoder.h */,
				498C71B6574E5EB6309FB718 /* SDImageAWebPCoder.m */,
				734D8BEFB29ABD07DE305303 /* SDImageCache.h */,
				933EA8BEFF1633D2E630A542 /* SDImageCache.m */,
				C3BC3DAF0C82C2A0ACD5A3D0 /* SDImageCacheConfig.h */,
				88F73A3CDD3491E1A37B2C7C /* SDImageCacheConfig.m */,
				12AD43BCA9BB937E9495379F /* SDImageCacheDefine.h */,
				C8C381B0E95E844A9E2D9477 /* SDImageCacheDefine.m */,
				806E61F6010E3BF2D703150E /* SDImageCachesManager.h */,
				788DAD5EBC5D957687661E7D /* SDImageCachesManager.m */,
				5B7F37D3D921AF5276D55762 /* SDImageCoder.h */,
				8A9D158F1F0B06F8FF80A7AF /* SDImageCoder.m */,
				300C5FCF1212E013FB04B6B1 /* SDImageCoderHelper.h */,
				4139A0A3A460E14F0E2E6829 /* SDImageCoderHelper.m */,
				EAC32D15A3C3520F677E420A /* SDImageCodersManager.h */,
				62260EF186CE02377AB68E43 /* SDImageCodersManager.m */,
				A2DD5F3E823868BD057D53E7 /* SDImageFrame.h */,
				9F019B5F911FFE0CEA26BD57 /* SDImageFrame.m */,
				0C013B661469AC985B667BB8 /* SDImageGIFCoder.h */,
				61D0B4FF84ACEDE3C1C8E103 /* SDImageGIFCoder.m */,
				AA3F4130C5D1930F0F65CC3D /* SDImageGraphics.h */,
				F5CBC881AD560B0186CF9E39 /* SDImageGraphics.m */,
				DC9B41D076BEFF57534297AC /* SDImageHEICCoder.h */,
				A9B149D11C468671532D9859 /* SDImageHEICCoder.m */,
				116EA32D69BCA88DE21162AA /* SDImageIOAnimatedCoder.h */,
				39F2C1CAB58415CAE9A39160 /* SDImageIOAnimatedCoder.m */,
				7C813B8908AE41FBDA038B69 /* SDImageIOCoder.h */,
				2E6F6EB14C3ECF3A931AB678 /* SDImageIOCoder.m */,
				8296299DD79244266387E372 /* SDImageLoader.h */,
				1F6240B50BA9D3C49F0795BA /* SDImageLoader.m */,
				2940FFAF377927CD8B8147A7 /* SDImageLoadersManager.h */,
				EEA58B5534F6981BE89ED80D /* SDImageLoadersManager.m */,
				918825460FD0F4E77785BD33 /* SDImageTransformer.h */,
				4AB7ABD372122B19DE58E776 /* SDImageTransformer.m */,
				5B4B926BB72EAEA93712A2E1 /* SDMemoryCache.h */,
				571C0F0FE0C21511E0EE6B7C /* SDMemoryCache.m */,
				C1350EC177815442723EAE11 /* SDWebImageCacheKeyFilter.h */,
				5934DA760D5BE597EBEFFCF9 /* SDWebImageCacheKeyFilter.m */,
				B421363D1A90300A738CD851 /* SDWebImageCacheSerializer.h */,
				145C3EE3B7D9A537F15C9590 /* SDWebImageCacheSerializer.m */,
				D18AD52E7501C087DFF4064A /* SDWebImageCompat.h */,
				AA6C9D551DD261FBE522E2ED /* SDWebImageCompat.m */,
				C8CFB9F29CD950FDECB97979 /* SDWebImageDefine.h */,
				4088889F0E7F68BB932C4EB6 /* SDWebImageDefine.m */,
				A4C71E0853BBFA77EB1A4CD7 /* SDWebImageDownloader.h */,
				C879A314FF2F9FADA35B7CB1 /* SDWebImageDownloader.m */,
				54AFEA69507B76536B3A7CA3 /* SDWebImageDownloaderConfig.h */,
				7C8E03C183B7F0BFCCF93D3C /* SDWebImageDownloaderConfig.m */,
				59B72C2A47FF3AA133E5695A /* SDWebImageDownloaderDecryptor.h */,
				D22838B3AA8DCCA73C62A211 /* SDWebImageDownloaderDecryptor.m */,
				765021F0844B36A086D2F559 /* SDWebImageDownloaderOperation.h */,
				577A8CB5C71872A866960B40 /* SDWebImageDownloaderOperation.m */,
				FE20C76967514DAFC4526ACD /* SDWebImageDownloaderRequestModifier.h */,
				79139984A151B08E3973FB12 /* SDWebImageDownloaderRequestModifier.m */,
				7B3F2CB27F8A51548C415D15 /* SDWebImageDownloaderResponseModifier.h */,
				AD008523C8762CE3FDE9E126 /* SDWebImageDownloaderResponseModifier.m */,
				32856A0DC097D147BCE05959 /* SDWebImageError.h */,
				B9D628FF5F86D0C511C5D3C1 /* SDWebImageError.m */,
				46F2451F446710FB100F9B38 /* SDWebImageIndicator.h */,
				BC20AAC905D0DBA1CED83CBE /* SDWebImageIndicator.m */,
				9BE06F3F6DED35454604CCE9 /* SDWebImageManager.h */,
				C7CED6E36657A83A1125619A /* SDWebImageManager.m */,
				5AC4BFAF1E35EF8B7798504F /* SDWebImageOperation.h */,
				23752001EA6CA86B8A204848 /* SDWebImageOperation.m */,
				62BA177E66745DB971E6F2F1 /* SDWebImageOptionsProcessor.h */,
				34E1280B1162F5A722224D72 /* SDWebImageOptionsProcessor.m */,
				008C15FF420117A04C1D7916 /* SDWebImagePrefetcher.h */,
				73E486C243EA0F69A3DB98DA /* SDWebImagePrefetcher.m */,
				C4E2F31339A7F676EBE2EEC9 /* SDWebImageTransition.h */,
				59128F9A580DC9546B10909C /* SDWebImageTransition.m */,
				6D5DC52AFB94DD9DF43F08D8 /* UIButton+WebCache.h */,
				1B45373C923CBF4D8EC610E7 /* UIButton+WebCache.m */,
				1F5EF08D30C2ADD28A6FEFB2 /* UIImage+ExtendedCacheData.h */,
				21C9778A0E03E1EB0E673A76 /* UIImage+ExtendedCacheData.m */,
				DC6D736C7D7A708AFF1ED28A /* UIImage+ForceDecode.h */,
				B3537F21C8DDFA381419F5D3 /* UIImage+ForceDecode.m */,
				72B2F1E53E9B749C5EB8DD0B /* UIImage+GIF.h */,
				5EF58CA7DAB4F981B425F783 /* UIImage+GIF.m */,
				EE25814C42D01892C4082552 /* UIImage+MemoryCacheCost.h */,
				B4C82662C4880AA1C18A3E1C /* UIImage+MemoryCacheCost.m */,
				57DB2E4A7DD8B57D95569C8F /* UIImage+Metadata.h */,
				7B00D48E7E0C45FF6FEA8A27 /* UIImage+Metadata.m */,
				7B4855EC1E2CF1CFB78CB727 /* UIImage+MultiFormat.h */,
				D5753DEF9F08E09790649A1F /* UIImage+MultiFormat.m */,
				6E901509B484F481CCB25196 /* UIImage+Transform.h */,
				BF335B9943B67D0267FF3C3C /* UIImage+Transform.m */,
				F5290FB23CF5BEFFAB1B4EDE /* UIImageView+HighlightedWebCache.h */,
				7A12E16479067FC78C36FBA7 /* UIImageView+HighlightedWebCache.m */,
				F3C8853DC9B4097B37BE0CB5 /* UIImageView+WebCache.h */,
				9AB6B4825C7BBFFAF75AAC39 /* UIImageView+WebCache.m */,
				6109994009405BBEA9FD7173 /* UIView+WebCache.h */,
				F6F83D66EB1ADC89D69AFE66 /* UIView+WebCache.m */,
				40D94C43715824246785DD98 /* UIView+WebCacheOperation.h */,
				01E9CF12555DAB3F6F8023B5 /* UIView+WebCacheOperation.m */,
				8A3B40C339176206225E729D /* UIView+WebCacheState.h */,
				75A4BDB2F625D52CED26FCAA /* UIView+WebCacheState.m */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		6CD3572AA77050397AD44199 /* ClearDaySpaBad */ = {
			isa = PBXGroup;
			children = (
				2160314877FA80BAD837DA84 /* SemicolonViewController.h */,
				CBEBB6D570A10D533CF56400 /* SemicolonViewController.m */,
				8555A76523BD1EE55FAC79C3 /* GarbageAndViewController.h */,
				61534C68825F5A527CD9DBB1 /* GarbageAndViewController.m */,
				139B6C6E6A1BEC2891985EB1 /* TrustPartViewController.h */,
				1194455A0B1FDE7910690830 /* TrustPartViewController.m */,
				E38C8E3C273FDFD7844F2E76 /* KoreanSeeViewController.h */,
				DBA2BA9B6C920BF73364F218 /* KoreanSeeViewController.m */,
				1B18831364CB0E981BA89C03 /* SumBuffersVisitAlbumBuildViewController.h */,
				78AC831585293B779B63AA7D /* SumBuffersVisitAlbumBuildViewController.m */,
				38A367446A04EC8EF6C98737 /* EphemeralViewController.h */,
				C58B6424C4532D20AE232524 /* EphemeralViewController.m */,
				2011FDC51AE0616BC13FDDA3 /* PolarEachViewController.h */,
				961EE029580B54701D59C539 /* PolarEachViewController.m */,
				9CCD6207DE38B438F42ACAE9 /* ScriptsFourthViewController.h */,
				BF8A9E4E49E4371F6891B8AF /* ScriptsFourthViewController.m */,
				FA6C84C77A89406F708EBCCB /* TurnWordTipViewController.h */,
				C222DC51AC392A3A2A7E03CB /* TurnWordTipViewController.m */,
				EC6095C0F081AE9B2A447E4D /* OpenNetTreeViewController.h */,
				433F68F0C09BE3A301681307 /* OpenNetTreeViewController.m */,
				ADC89DC15EF784D12132229E /* AtomHasDogViewController.h */,
				A8E2731B992E7D07432E8E22 /* AtomHasDogViewController.m */,
				7F786B34C25438FE40A1BC80 /* GroupedCollectViewController.h */,
				60B213F8F4BDE0AB141A3966 /* GroupedCollectViewController.m */,
				3B413606488EFABAB1B9DDF2 /* DueLockAirTooViewController.h */,
				B4973CA7D716AA569EFA2515 /* DueLockAirTooViewController.m */,
				2A93CED35997E1E49A88D706 /* LikeAsleepViewController.h */,
				816BDB33EC2F6DD5D3A85C27 /* LikeAsleepViewController.m */,
			);
			path = ClearDaySpaBad;
			sourceTree = "<group>";
		};
		7141255F15D3D3632D67733D /* Private */ = {
			isa = PBXGroup;
			children = (
				6A64E0D7795BE83EFB33E790 /* NSBezierPath+SDRoundedCorners.h */,
				7E0B305297F5073C3490BDBE /* NSBezierPath+SDRoundedCorners.m */,
				B15A1174DD0FEAB4D55CD6A0 /* SDAssociatedObject.h */,
				F6927318C51AE6047559AB0B /* SDAssociatedObject.m */,
				1E9E13758ADED3A4739A87A0 /* SDAsyncBlockOperation.h */,
				440ADF7DB1BE50D635323F1D /* SDAsyncBlockOperation.m */,
				6A13D2593CBB3A72AEC4B148 /* SDDeviceHelper.h */,
				4CE7404A5B857491331F0EE9 /* SDDeviceHelper.m */,
				26DB1986EBEFCCF6ACBC952B /* SDDisplayLink.h */,
				36EF06B1B8D01EBC1869D02D /* SDDisplayLink.m */,
				F75AC053055E4FEFBC9455D2 /* SDFileAttributeHelper.h */,
				80F029319B8502440B280082 /* SDFileAttributeHelper.m */,
				046CF37B220240DB19ECA78A /* SDImageAssetManager.h */,
				786F1B2CFE1C4C22C76572FA /* SDImageAssetManager.m */,
				5A2FD435C2DF590B507C35CA /* SDImageCachesManagerOperation.h */,
				49C280B0F3924999B94EBB23 /* SDImageCachesManagerOperation.m */,
				8AFF7D6AEF62C3CCA694403E /* SDImageFramePool.h */,
				119CD837E13880DF5801A97C /* SDImageFramePool.m */,
				241130EE051DD80C02AE1B3A /* SDImageIOAnimatedCoderInternal.h */,
				DD839CEE8E1CF135C858713F /* SDInternalMacros.h */,
				6494CFA5F6196754AD0DC0FA /* SDInternalMacros.m */,
				35489DC6C272CFB63EF56B36 /* SDmetamacros.h */,
				5E558EB81B54F15693EF092E /* SDWeakProxy.h */,
				36AA8C439B87BA05D260F635 /* SDWeakProxy.m */,
				D94C964EBF95600633E0ACDF /* SDWebImageTransitionInternal.h */,
				D3A2B233264052BBF8AE259D /* UIColor+SDHexString.h */,
				5F5A4DBAF2FCC097CC68CF3C /* UIColor+SDHexString.m */,
			);
			path = Private;
			sourceTree = "<group>";
		};
		72FC2E9FE8E282BF6AC3079F /* Slight */ = {
			isa = PBXGroup;
			children = (
				A7E700B30C77C17214BBF842 /* OffPost */,
				F94D3A1ADE931066A8125AA8 /* Produce */,
				8EA4F67EF7A7425A5363BED3 /* SayUseTurn.h */,
				F2348336B7C9E975C460AB55 /* SayUseTurn.m */,
				C1F3DBAA72D1C0491BE25FC8 /* ConfirmProtocol.h */,
			);
			path = Slight;
			sourceTree = "<group>";
		};
		73B4BDD69A588F8AEFE99F9D /* DarkWay */ = {
			isa = PBXGroup;
			children = (
				599F7FE7A88B9997B9B65C28 /* EarSigningManager.h */,
				FA7D6EEE5407F35673F0C6DB /* EarSigningManager.m */,
				6D2B7128B8196278B107CD29 /* SongArmBleedManager.h */,
				97E75C2DAC9C5D9E51025C54 /* SongArmBleedManager.m */,
			);
			path = DarkWay;
			sourceTree = "<group>";
		};
		7778A21487780F7879C8FC83 /* BedFull */ = {
			isa = PBXGroup;
			children = (
				A0C3F888FCE6646A9D8E570D /* InferLoadStone.h */,
				9FBFEBDD8B76544AD505EA97 /* InferLoadStone.m */,
				B3EF13F323C89D82167485F4 /* RedoBackupMaintainPhraseHormone.h */,
				E3EEA740232D3DBCB1A85F08 /* RedoBackupMaintainPhraseHormone.m */,
			);
			path = BedFull;
			sourceTree = "<group>";
		};
		8E1911252896F0EC8B287307 /* Bed */ = {
			isa = PBXGroup;
			children = (
				8F63D756D740D1D28AD21035 /* OverlapOurManager.h */,
				F56A5E730808513DEE0AB1F6 /* OverlapOurManager.m */,
				377537E085BA8BC2621BBA99 /* WithinAskTagManager.h */,
				5A6468627A67324F63E20B7E /* WithinAskTagManager.m */,
			);
			path = Bed;
			sourceTree = "<group>";
		};
		90D04CBE6C810DB3E1F0605F /* WinMix */ = {
			isa = PBXGroup;
			children = (
				1DEE274415AFD55A978573A1 /* Hex */,
				086700A5E835274E45C7DF92 /* Day */,
			);
			path = WinMix;
			sourceTree = "<group>";
		};
		9975741CC9F269B780932107 /* AllocatedShareTitleAdditionsLose */ = {
			isa = PBXGroup;
			children = (
				B9035013C150E1B068E40367 /* InverseArmAreNiacinAllocator.h */,
				159360FDE0E3761862872AE5 /* InverseArmAreNiacinAllocator.m */,
			);
			path = AllocatedShareTitleAdditionsLose;
			sourceTree = "<group>";
		};
		A7E700B30C77C17214BBF842 /* OffPost */ = {
			isa = PBXGroup;
			children = (
				7A552C34B2E63050DE2EB259 /* MaximumFirst.h */,
				62F0B47937E63BA9F1C7DF90 /* MaximumFirst.m */,
				67BA7B3C076C143E76470851 /* RadixDeny.h */,
				070E6F5B032C4D293F1FAC2F /* RadixDeny.m */,
			);
			path = OffPost;
			sourceTree = "<group>";
		};
		AC80B3787E218507B535F1D2 /* SedentaryNumericHungarianDevicesFurlongs */ = {
			isa = PBXGroup;
			children = (
				99A0329A38A350A2284E86CE /* BigPrologHasBankersWon.h */,
				ABFC2C1CF2846CDF3C758E56 /* BigPrologHasBankersWon.m */,
				E58A16FAEBAD1EC98EB8904E /* SettingEncodingsStringMiterRefreshedSubgroup.h */,
				FBE62222A98340344572CF44 /* SettingEncodingsStringMiterRefreshedSubgroup.m */,
				019802501F00732EF0141510 /* OriginsFunnelSignalingReadyCaps.h */,
				76BFF839E893E30C8BD2561F /* OriginsFunnelSignalingReadyCaps.m */,
				7FD99A55FB6BF2CFF90809E5 /* Adobe.h */,
				2E3DF17F33E037D4F4C64ABD /* Adobe.m */,
				F6EC2CB6E609496E4AACBCFA /* MusicalRadians.h */,
				39093C9AB15345682D7BDF40 /* MusicalRadians.m */,
				20E82B425482A5DB5D393C95 /* InferSonOut.h */,
				111D9780FDF4742EF59487EA /* CountViewController.h */,
				BBE6651B03CDB86C221E2664 /* CountViewController.m */,
				9FA029A8CE0393069445EE34 /* ContactAlienHusbandSuperiorsEstimated.h */,
			);
			path = SedentaryNumericHungarianDevicesFurlongs;
			sourceTree = "<group>";
		};
		B45D91E61EF7840FBE9DB664 /* BagMolarFix */ = {
			isa = PBXGroup;
			children = (
				28FF93557AC2F072EC9D9AA0 /* TagSidebarView.h */,
				E644D62B85EC55C8D65EF74B /* TagSidebarView.m */,
				16D1254D2725C3BFC6325B8E /* HomePhraseRankCell.h */,
				084C63BA5351BECA0417E9D9 /* HomePhraseRankCell.m */,
				96C5F770FDD202C53F513DE6 /* DryBarInterKey.h */,
				8B2BDD97E75E6A0AAE4F153F /* DryBarInterKey.m */,
				DE5F03E4D6E4D90258589F1F /* SonAssetWinNetCell.h */,
				F88E05BFAE9117371EC429C6 /* SonAssetWinNetCell.m */,
			);
			path = BagMolarFix;
			sourceTree = "<group>";
		};
		B7FAD21AF5ADAEE98D4A38F6 /* Gesture */ = {
			isa = PBXGroup;
			children = (
				8E1911252896F0EC8B287307 /* Bed */,
				48F5B2549F9E982E8B5E5448 /* Fix */,
				99BE632F1D381DA69F954F51 /* TowerHisManager.h */,
				E5F0F8109E51B91DC6607DC6 /* TowerHisManager.m */,
				2EE48B16358C30D873C7E47A /* ReportManager.h */,
				65416AB0B657D9896DB22BCC /* ReportManager.m */,
				EF6EC7506679990D58D356A6 /* HeaderManager.h */,
				2B4C304BF34F7EC6AD4FF4B3 /* HeaderManager.m */,
				E8C659EF4AB3C294AD8876DB /* MusicalManager.h */,
				DDC4885CB05DCF91C7E7D03F /* MusicalManager.m */,
			);
			path = Gesture;
			sourceTree = "<group>";
		};
		BA20C2B9AA005988C838D82C /* SDWebImage */ = {
			isa = PBXGroup;
			children = (
				60DE89861E700DA24A454A8E /* Core */,
				7141255F15D3D3632D67733D /* Private */,
			);
			path = SDWebImage;
			sourceTree = "<group>";
		};
		BDC20BBA26FB325CC335A945 /* RaceSite */ = {
			isa = PBXGroup;
			children = (
				72FC2E9FE8E282BF6AC3079F /* Slight */,
				F6A2060A76C92DF57A71DC4E /* Maximum */,
				90D04CBE6C810DB3E1F0605F /* WinMix */,
				C92C31990349FF859565F9E4 /* Digital */,
				B7FAD21AF5ADAEE98D4A38F6 /* Gesture */,
			);
			path = RaceSite;
			sourceTree = "<group>";
		};
		C411D06063D3CDCBD7B9B7AD /* Cover */ = {
			isa = PBXGroup;
			children = (
				9E8D772A2CC7288306A9F533 /* NSError+FootnoteOdd.h */,
				5081ADE5478E2895C42CCD71 /* NSError+FootnoteOdd.m */,
			);
			path = Cover;
			sourceTree = "<group>";
		};
		C92C31990349FF859565F9E4 /* Digital */ = {
			isa = PBXGroup;
			children = (
				2FEBDB4B13FF801913DD149D /* PoloFigure.h */,
				D038C382E4D9E79E7C54AA90 /* PoloFigure.m */,
				08C90987D3B6128A4C00EAA3 /* CiphersEarModel.h */,
				AF7722249232BF8AA6956F33 /* CiphersEarModel.m */,
				011282B95EB00976868F5474 /* PenNetwork.h */,
				499F09CF234117D187F8CE3C /* PenNetwork.m */,
				AC31E4F862D1FA26436419BF /* SeeBusStepList.h */,
				ED911157B82E8EAFFA2D9D94 /* SeeBusStepList.m */,
			);
			path = Digital;
			sourceTree = "<group>";
		};
		CA66DFBA57230D3DC7C086B0 /* MQTTClient */ = {
			isa = PBXGroup;
			children = (
				4CA75D9A537F0C2CBBFF2117 /* ForegroundReconnection.h */,
				6628491DC935B15A7CA883E9 /* ForegroundReconnection.m */,
				0E69F8FD195C6E96E3CBF5C2 /* GCDTimer.h */,
				9A857E86BDB0F72D9EC1FD6D /* GCDTimer.m */,
				AAF87AD30A40EDEA45D6772C /* MQTTCFSocketDecoder.h */,
				B674C429CFB3A07337B7EDFE /* MQTTCFSocketDecoder.m */,
				B4F88381B59032F0E6AEB62F /* MQTTCFSocketEncoder.h */,
				B3B861B24B94AB2B0F4FB89E /* MQTTCFSocketEncoder.m */,
				929825736A2C48EE78582E27 /* MQTTCFSocketTransport.h */,
				7C3F4EB15860B12A7D2ABBC8 /* MQTTCFSocketTransport.m */,
				9D1B2BF8242D832E71C2229A /* MQTTClient.h */,
				FECC5E01A667C3EF9AFF1B3E /* MQTTCoreDataPersistence.h */,
				D889D3FFA19134627FB7490A /* MQTTCoreDataPersistence.m */,
				CAEACE0711E5978589266D6A /* MQTTDecoder.h */,
				219E78127921453B2F6C1056 /* MQTTDecoder.m */,
				EAD34BAE17FA0F85DB72D2EE /* MQTTInMemoryPersistence.h */,
				8482282A8FA54219A986DEFF /* MQTTInMemoryPersistence.m */,
				E6774C23291339746F26C8B3 /* MQTTLog.h */,
				7CB6BF8CA0FB51CB187EF20E /* MQTTLog.m */,
				2F0844873B4C513741A2DC0F /* MQTTMessage.h */,
				D0FC3A44A8F54271DCB2F555 /* MQTTMessage.m */,
				8F443F99646BFBF13B997E50 /* MQTTPersistence.h */,
				0C8A72E2AA673E981782D570 /* MQTTProperties.h */,
				2C95468E6D2D68C3AD996FA2 /* MQTTProperties.m */,
				F4A69E0D5D2F7EB87ABE1B4C /* MQTTSession.h */,
				9818C45335B785FE986EA794 /* MQTTSession.m */,
				02DD979D5FE1048F555DD3C7 /* MQTTSessionLegacy.h */,
				D4953E7AC4DAF7EB614C7BBE /* MQTTSessionLegacy.m */,
				703B509329B646238ED0491E /* MQTTSessionManager.h */,
				65819365EE0250CBD661F628 /* MQTTSessionManager.m */,
				7BAEAD1037AC48FEB03D1828 /* MQTTSessionSynchron.h */,
				34C8B3F7899686F0381905C0 /* MQTTSessionSynchron.m */,
				108037828C063003BF48489F /* MQTTSSLSecurityPolicy.h */,
				9F1921F51A5F4C8C27A677DF /* MQTTSSLSecurityPolicy.m */,
				E7D264E4EAB565F1282B4BEC /* MQTTSSLSecurityPolicyDecoder.h */,
				FF3AE64B18C101B5CCF3EFCB /* MQTTSSLSecurityPolicyDecoder.m */,
				400ED8AD84D4C888FA8796A0 /* MQTTSSLSecurityPolicyEncoder.h */,
				532F458511582393B7DD2EC9 /* MQTTSSLSecurityPolicyEncoder.m */,
				10EDC15ED349579399FA44AF /* MQTTSSLSecurityPolicyTransport.h */,
				98F20B819A90A4CD76469ABF /* MQTTSSLSecurityPolicyTransport.m */,
				86952889D487204ECCFE8524 /* MQTTStrict.h */,
				7E1C4A1E26051D2535645C05 /* MQTTStrict.m */,
				23E129F53E3FDABD630678A8 /* MQTTTransportProtocol.h */,
				185F3719C9E7989DDFB85A3A /* MQTTTransportProtocol.m */,
				960445813D0E8366023112B0 /* ReconnectTimer.h */,
				A45BCE8A0AAFD9EF4320949C /* ReconnectTimer.m */,
			);
			path = MQTTClient;
			sourceTree = "<group>";
		};
		D80C0E12D2BF2460586B9C4D /* TotalSob */ = {
			isa = PBXGroup;
			children = (
				9382D750D033E0535E7A4FCB /* BendRope.h */,
				A7611D0E441C837EBB1C8E45 /* BendRope.m */,
				219FB9F5398E6B8EB7313FD0 /* Late */,
				531593B778313F45855BA435 /* Font */,
				6CD3572AA77050397AD44199 /* ClearDaySpaBad */,
				2E7809454EAAB0A759E3724C /* FeaturedRomanUnlearnRegionsLarger */,
			);
			path = TotalSob;
			sourceTree = "<group>";
		};
		DF96301D6E5D71A035B1962A /* Masonry */ = {
			isa = PBXGroup;
			children = (
				3232561B9138041D744ED834 /* MASCompositeConstraint.h */,
				8B9985503C2558EBED8A522B /* MASCompositeConstraint.m */,
				615E13833664019613FE173D /* MASConstraint.h */,
				4C33F9BCB6334778CB1CA6A0 /* MASConstraint.m */,
				0C95057C0EF58E3683F01C1A /* MASConstraint+Private.h */,
				E9ACAA719E5AAE897A74B2DA /* MASConstraintMaker.h */,
				ABF09445C28227568CAA5D19 /* MASConstraintMaker.m */,
				1C9F7EBF29F2DCE54A316389 /* MASLayoutConstraint.h */,
				76568D5D140940001FF28B59 /* MASLayoutConstraint.m */,
				6845270D58C1DB53E2340179 /* Masonry.h */,
				EFEA9B14BAAE8070ECC9E14F /* MASUtilities.h */,
				DEBB7F0D921167B046720EE2 /* MASViewAttribute.h */,
				5AAFAE63C658BA6E9975FE1A /* MASViewAttribute.m */,
				BF7002E9182182E66DDAE441 /* MASViewConstraint.h */,
				48ACCB832BE1756214BAAF51 /* MASViewConstraint.m */,
				D7AB1A05A52B80B0C9F61B5D /* NSArray+MASAdditions.h */,
				F732C4825DAD97C8CAAA7BE2 /* NSArray+MASAdditions.m */,
				6ABE635325CE02C4D4668053 /* NSArray+MASShorthandAdditions.h */,
				7B660278A8474DFC044B942B /* NSLayoutConstraint+MASDebugAdditions.h */,
				9A73451FB7580B8846B64D48 /* NSLayoutConstraint+MASDebugAdditions.m */,
				B3913A4615556C08D8D7697C /* View+MASAdditions.h */,
				27A8552B178C3603F092FF44 /* View+MASAdditions.m */,
				47A2E9BCA649D320CFDCA64D /* View+MASShorthandAdditions.h */,
				D1162E48F16A5587B5834A55 /* ViewController+MASAdditions.h */,
				98EC7A95842807C3A4ABC105 /* ViewController+MASAdditions.m */,
			);
			path = Masonry;
			sourceTree = "<group>";
		};
		F33103458B55B5EB4A1E92E5 /* AlignFlatten */ = {
			isa = PBXGroup;
			children = (
				95E416A3926FF9B3E2930DAF /* LongRaceView.h */,
				F13CC0EA8C3A370B941C6771 /* LongRaceView.m */,
				80D849B37F13C2B97CE1FA9E /* TildeScanPlaneWindow.h */,
				2A3753D7CB6B20650CF003C2 /* TildeScanPlaneWindow.m */,
			);
			path = AlignFlatten;
			sourceTree = "<group>";
		};
		F5942DE82FB69AA460077D61 /* LooperLength */ = {
			isa = PBXGroup;
			children = (
				E2EFB371955803F2045FDF3F /* VerboseDigestSignUnorderedSynthesis.h */,
				9C631BB8E26620EE1A705852 /* VerboseDigestSignUnorderedSynthesis.m */,
				8FF9D0CF510B86703F742ED6 /* CanBusEnableModel.h */,
				3A4D7A9CE02AB7CA653DF027 /* CanBusEnableModel.m */,
				3435A5A7984863470FD16450 /* SeeVitalModel.h */,
				73A54D350AB1050302E45F7A /* SeeVitalModel.m */,
			);
			path = LooperLength;
			sourceTree = "<group>";
		};
		F6A2060A76C92DF57A71DC4E /* Maximum */ = {
			isa = PBXGroup;
			children = (
				2C19280CEAF6296A042CD9FF /* HandledEggConfig.h */,
				11BA57B66A0184C04682439B /* HandledEggConfig.m */,
				58AAAFDD9D74B5023CDA8EA0 /* SlowDaySeekThe.h */,
				122832A3C2AF465A59BE46DD /* SlowDaySeekThe.m */,
				5FD49BBB07C032ACE14B7AAD /* SlowDaySeekThe+Speak.h */,
				435EB89050EB338B73E23EA9 /* SlowDaySeekThe+Speak.m */,
				6521DF77CCE57BB721205D01 /* SlowDaySeekThe+EraDecide.h */,
				6B08B877A13FDDA534B430FC /* SlowDaySeekThe+EraDecide.m */,
				1BBC7AB4603D69FB20F3E49F /* SlowDaySeekThe+HitOut.h */,
				003D730C055AAC937DCEFA41 /* SlowDaySeekThe+HitOut.m */,
				CF0E7E1B3AFF658D5FB5E800 /* BoxArtsCategoryClickedWrap.h */,
				0287249B8092EA18ABB7D378 /* BoxArtsCategoryClickedWrap.m */,
				8B9201D6F397E422509FFEC9 /* DirectTouchAction.h */,
				CBF6933420F4D34E87C3BD95 /* DirectTouchAction.m */,
			);
			path = Maximum;
			sourceTree = "<group>";
		};
		F94D3A1ADE931066A8125AA8 /* Produce */ = {
			isa = PBXGroup;
			children = (
				C2EFBAD99545AEDB27629D0C /* GrossScrap.h */,
				97367A094F7D94128C5A2DD1 /* GrossScrap.m */,
				A79FF5F6358D7DC093C923D0 /* FillerBus.h */,
				A16C21678357C3769FFAAD4C /* FillerBus.m */,
			);
			path = Produce;
			sourceTree = "<group>";
		};
		FFCE253E02760F058E80AB1E /* GrossScrap */ = {
			isa = PBXGroup;
			children = (
				BDC20BBA26FB325CC335A945 /* RaceSite */,
				1C687CAFFB9B7078B3C2E44A /* Terminal */,
				D80C0E12D2BF2460586B9C4D /* TotalSob */,
			);
			path = GrossScrap;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		75087E5830DAE9B413958556 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0264B69396DBC9A707F92E3B /* CiphersEarModel.h in Headers */,
				E23752624B5A13FF18D1AFEB /* VerboseCombine.h in Headers */,
				3E89E2540481BE8FE374C9AA /* PivotForManager.h in Headers */,
				2ED030925264D5DB22030FFF /* FloaterBendTerabytesCopperTaggingReasonViewController.h in Headers */,
				A7AEAD9B9A1E6790D24786E0 /* SendEraBlobRet.h in Headers */,
				683E00929CA8593987F0BA4D /* SumBuffersVisitAlbumBuildViewController.h in Headers */,
				508D302B5685D776C3C30041 /* GroupedCollectViewController.h in Headers */,
				B21CD8D2CC5C1BD42D65BF93 /* NSString+BedLow.h in Headers */,
				E7EF748D99BECF73B797869D /* EncodingsDarkWasPasswordFilteringCell.h in Headers */,
				334B85E3683D3E13EA010DA4 /* RawUpsideOdd.h in Headers */,
				F9E827F98CD2870EC7B8A99B /* SemicolonThe.h in Headers */,
				C6ACF2A07C4502D98BC527A5 /* WindowsSobInfo.h in Headers */,
				76AF0E8427D667BFC877DC8E /* BendRope.h in Headers */,
				5E9D7354F18E031613EBC63F /* YouWireCupCell.h in Headers */,
				215FD02EDB3C7C264AE03B7B /* SobAlertView.h in Headers */,
				4F4A74A2A814411F247746EA /* RedoBackupMaintainPhraseHormone.h in Headers */,
				21FB2A917F857031A399C9D5 /* UIViewController+BoxViewController.h in Headers */,
				8CF13C6D60E31E503EB38038 /* ReportManager.h in Headers */,
				F379327E08C5818D1D3A46BD /* TurnWordTipViewController.h in Headers */,
				B502B3FDA713AF0AC83C924E /* OutToast.h in Headers */,
				23A68692F0065E7AC5935DFB /* NSString+StickySay.h in Headers */,
				3CC752E6290222458FBEBEF6 /* NSObject+WayModel.h in Headers */,
				8A5A0F323AB9BE61B6F20EBE /* ArtworkModel.h in Headers */,
				495BB12F4497146CDD1A7E19 /* LiveStyleInfo.h in Headers */,
				D3F365F42EFFB8AFB3913BF0 /* SeeBusStepList.h in Headers */,
				18BE1337647133A4764D9E9C /* FrequencyInfo.h in Headers */,
				45E2D915818F557F1623EB37 /* InverseArmAreNiacinAllocator.h in Headers */,
				D55A6160E16F55FC7418CFC3 /* NSObject+MenPunjabiInfoSongLost.h in Headers */,
				9D369D6CAF470C07021242E4 /* GrossScrap.h in Headers */,
				406D836763F1B5BA25477AAF /* RareRaceColor.h in Headers */,
				D86CEEF0C2B56E5E4811F870 /* LoopsEyeRomanController.h in Headers */,
				35D8C6601E9786A64CE61034 /* MarkupInfo.h in Headers */,
				344A19548029F69430B126FC /* HeaderManager.h in Headers */,
				263B62E74CAE10AA9F51CC2B /* TowerHisManager.h in Headers */,
				19752B3A11B90F00395EB97E /* ModeRunningDelayRaiseStrokingTool.h in Headers */,
				2E749424EE092A5E28FA9AFD /* InferLoadStone.h in Headers */,
				02956EE136ECBE00675233D0 /* DogTempPin.h in Headers */,
				C2DA4F7B944BC36BBAB757EE /* OpenNetTreeViewController.h in Headers */,
				6AE30085A0B43AFD6B857F0A /* DownloadBondLongSucceededTen.h in Headers */,
				33660A7B9E719FAB2FDE4582 /* SayUseTurn.h in Headers */,
				FCD8FE3EB68B339E64DE58AF /* NotNotConfig.h in Headers */,
				5B8DDB55D281020B4A8EABAF /* SplatManager.h in Headers */,
				1CCA31C6C725D9ED46621498 /* EarSigningManager.h in Headers */,
				4F7B54620D2B78E6A2AFD42F /* DueSumZoom.h in Headers */,
				E374EC9B93F3EE8E5D0F0291 /* SongArmBleedManager.h in Headers */,
				66603DBC4BAC856C5554D8BB /* InteractItalicBouncingRegionsDimensionModel.h in Headers */,
				AF1EE17057B02D9D6ED2FB7C /* DeepApplyProtocol.h in Headers */,
				7262D13E3C8CA8A02BA4156F /* NSError+FootnoteOdd.h in Headers */,
				9983A7B57E4A8782DC1D2041 /* SlowDaySeekThe.h in Headers */,
				5AEFE2CF58752EED97428226 /* SemicolonViewController.h in Headers */,
				7C23BC77CE436289939D563F /* HitUnableManager.h in Headers */,
				DD7EA0156BFDF0286ECE1466 /* UIColor+GetColor.h in Headers */,
				D59FC28ED0A98BE1EA612133 /* BoxArtsCategoryClickedWrap.h in Headers */,
				BE331E7B0D1398BCB13C4FF7 /* PolarEachViewController.h in Headers */,
				65B29C0CF8077A4845901A83 /* SlowDaySeekThe+EraDecide.h in Headers */,
				9A50E51A186073778718C83F /* FoodHoursHave.h in Headers */,
				4A57090C44FE1743F6853A31 /* IrishAlbanian.h in Headers */,
				B732A64E35DB3BC5B3772CCC /* DirectTouchAction.h in Headers */,
				7656AE37A911231E1D9D2E02 /* HandledEggConfig.h in Headers */,
				936BB04535E08F9D3F86632D /* BitOwnPathView.h in Headers */,
				C01BD291107741D9DDFCC3E9 /* UIColor+SDHexString.h in Headers */,
				7E64DDD7B238E4706715299C /* Dominant.h in Headers */,
				B2AD382F9AC6D361CA378D0E /* SDImageIOCoder.h in Headers */,
				8A5822C1826A9910D62F6DE9 /* ParameterStillCalculateAdverbBoldface.h in Headers */,
				56930FA4A002A6B88B0754C6 /* SDFileAttributeHelper.h in Headers */,
				E031F54A1F6B2EE070F6A42E /* SDWebImagePrefetcher.h in Headers */,
				1D2D84A29CE21459F4FC8DBC /* SDAnimatedImageView.h in Headers */,
				2C10F79E08CAFA5E6260ADB0 /* NSButton+WebCache.h in Headers */,
				DC4CC33A9AFBFE19D658841C /* CaseBurstViewController.h in Headers */,
				78DA4A05EFF62481CB4C615A /* SDImageHEICCoder.h in Headers */,
				BB06BB1AE21C0BFD59572D37 /* CheckSmoothButton.h in Headers */,
				DF781F47E0AB1515E5F6D426 /* TwoVideoViewController.h in Headers */,
				CD3243E6207983AAFD60330C /* SDImageIOAnimatedCoder.h in Headers */,
				E638617A2B739C7CA3C2A190 /* SDImageCoderHelper.h in Headers */,
				584EDDE4FA0C3A0C2823A8C8 /* SDAnimatedImagePlayer.h in Headers */,
				BFE3CDF89E0352A58504DE81 /* FigureGetSonInfo.h in Headers */,
				CFE58ECF5A13C78827807334 /* UIView+WebCache.h in Headers */,
				2AE761889D9149F058F8C3D3 /* HandballProtocol.h in Headers */,
				B962DD6EECDFBCB572AEDDEA /* XXGProtocolLabel.h in Headers */,
				F8EABA6D87108B6A8BA6BF0C /* SpaKinWhite.h in Headers */,
				134C3EFDF9536EC29311E1B1 /* SDWebImageOptionsProcessor.h in Headers */,
				A89E21257F5D8921CDB11D89 /* SDWebImageTransitionInternal.h in Headers */,
				0F169F960396B9FD31E435AC /* SDWeakProxy.h in Headers */,
				4C0009868AF9B78E6D9F8AA5 /* MusicalManager.h in Headers */,
				7DA01C3EB033D2F83E7542E9 /* SDWebImageCompat.h in Headers */,
				2B57B29033EBBC15F6DE4427 /* SDCallbackQueue.h in Headers */,
				56C9C4E8B8737B85D964D22E /* SDmetamacros.h in Headers */,
				A3CA3D662EC126937340A717 /* UIImage+Transform.h in Headers */,
				7C54A94AD6692D8892D87103 /* UIImage+MultiFormat.h in Headers */,
				7DFB7208FC48740ACD939510 /* SDImageAssetManager.h in Headers */,
				19C3DB7303D1C01E0571878A /* SDImageCachesManagerOperation.h in Headers */,
				DEEDF8F3EF901E527BD790DC /* RetMenuLastPanInfo.h in Headers */,
				8B0D21CD34FF724A2F7E3B79 /* UIImageView+HighlightedWebCache.h in Headers */,
				128083A9D6993F17B519ADE7 /* SDWebImageDownloaderOperation.h in Headers */,
				0966B737F3BA9C35D4B578FE /* SDWebImageDownloaderDecryptor.h in Headers */,
				A3F7D71BE822F44BAE22FB9C /* TrustPartViewController.h in Headers */,
				33186E196E5104FA25A4CAC7 /* SDImageAWebPCoder.h in Headers */,
				77456B72EAFFF0B13EB9209D /* SDInternalMacros.h in Headers */,
				9AE573962E046D084BA83579 /* SDAnimatedImageView+WebCache.h in Headers */,
				D6390B1D3187C6CA08597FBA /* SDImageCoder.h in Headers */,
				F7D4413B9455734BC083AC30 /* SDWebImageCacheSerializer.h in Headers */,
				912E4DA5D4884DC6AA38483D /* SDImageLoadersManager.h in Headers */,
				8601F93910A84D1FB8BB1D36 /* PopPickMayViewController.h in Headers */,
				186DBC1161CB04F53E183941 /* ChromaFarManager.h in Headers */,
				90A26749916DDFB87D87C3CB /* SonAssetWinNetCell.h in Headers */,
				816A98DA92BABDB84FD9E9EC /* SDDisplayLink.h in Headers */,
				490F39E034734E2D084C6738 /* BigPreviousManager.h in Headers */,
				B4E0EADD2D86A0C8A111A5A4 /* LikeAsleepViewController.h in Headers */,
				8B4A2D61D11A1241F21ED1E0 /* SDWebImageIndicator.h in Headers */,
				DC1ED349675E4484BE0B50C2 /* SDWebImageDownloader.h in Headers */,
				B6250E31B9D42CA330870749 /* AtomHasDogViewController.h in Headers */,
				AFC65B29743176298B2544FB /* SDAnimatedImageRep.h in Headers */,
				5761F200354AB26079EAE89A /* SDImageCacheConfig.h in Headers */,
				038E0500EA2C899120F2BE19 /* SDImageFramePool.h in Headers */,
				A7EDA41CD23C8AB3BD58B3B3 /* SettingEncodingsStringMiterRefreshedSubgroup.h in Headers */,
				DAA8857818BCBC3E8D8D7F1D /* MusicalRadians.h in Headers */,
				C5784CD78EA408158F48A461 /* CountViewController.h in Headers */,
				B79E7844B0222FDC9A2B02E1 /* InferSonOut.h in Headers */,
				71DF3FD26176B738D02B3B3B /* BigPrologHasBankersWon.h in Headers */,
				287FB1366FA671FBE396B543 /* Adobe.h in Headers */,
				0CFB840D93E1ABCE9624EC88 /* OriginsFunnelSignalingReadyCaps.h in Headers */,
				48A96E32AB61B7D8781EE9A2 /* ContactAlienHusbandSuperiorsEstimated.h in Headers */,
				63467D5E10CF9CC0C93F39A8 /* SDImageGraphics.h in Headers */,
				7D83723A33DD25FA3DF0435B /* NSData+PinThat.h in Headers */,
				EF9CCF11F2133BAF82901EA1 /* VerboseDigestSignUnorderedSynthesis.h in Headers */,
				BA9B86FC3A3923C3BFFD5680 /* UIImage+GIF.h in Headers */,
				A4F44D203D84C703CE82BA83 /* FixtureDrive.h in Headers */,
				3B4E9FF0EB419B36DE452E6F /* SDAsyncBlockOperation.h in Headers */,
				009566CC56E14E2A6A4121D1 /* SDImageCacheDefine.h in Headers */,
				AAACA02A15F40AA6B1343DE7 /* SDWebImageDownloaderResponseModifier.h in Headers */,
				B5084CB44C6C8B7395F69576 /* UIView+WebCacheOperation.h in Headers */,
				AFB687809B23FB2BA0EF2D56 /* SDWebImageOperation.h in Headers */,
				BF44FF72C44BFC07FE5D0ECB /* SeeVitalModel.h in Headers */,
				8B67C8C8DFCBBF07A0F4B4BA /* GarbageAndViewController.h in Headers */,
				CCE2BD19CB56C8ECBB5E0C78 /* SDGraphicsImageRenderer.h in Headers */,
				AADADCB77C65A265556E0FF4 /* SlowDaySeekThe+Speak.h in Headers */,
				D8F4EE430D004FD0378D70C0 /* ProcessCircularTemporalCountryFlag.h in Headers */,
				1C099A9DEDC84DDF6C71B755 /* LogLowerHexManager.h in Headers */,
				EE7892B1C1846141136F48D0 /* SinPushCapFlowButton.h in Headers */,
				5F5326221FB2CAC64300FD72 /* SDImageLoader.h in Headers */,
				46EC93916F8ED63E8D9ECEBC /* UIImage+Metadata.h in Headers */,
				541A006C365214E367144649 /* DueLockAirTooViewController.h in Headers */,
				9D6A49E305D680F68F334CCE /* CanBusEnableModel.h in Headers */,
				608CA36E0CC14A776A79205A /* SDImageGIFCoder.h in Headers */,
				8B2AE36CF84E1DDC2A509E3D /* UIImage+ForceDecode.h in Headers */,
				8E2859D9A51FB8227C1C03CC /* HomePhraseRankCell.h in Headers */,
				BA3514344B45F46DA4EF1460 /* DryBarInterKey.h in Headers */,
				ED7A29EF57BCD9E1B365A5E0 /* SDImageFrame.h in Headers */,
				A92798E4CACCA15C3B913A95 /* UIImage+OwnImage.h in Headers */,
				D161B81813D532B3F923FB02 /* MQTTCoreDataPersistence.h in Headers */,
				BBDCDC86108D6F94102584F5 /* MQTTSessionSynchron.h in Headers */,
				67274F2BE0196FFE66B1AF4F /* MQTTSSLSecurityPolicy.h in Headers */,
				72F17D77DC3A3887739CBE97 /* MQTTPersistence.h in Headers */,
				8AA9D52F9824CF0823683584 /* ReconnectTimer.h in Headers */,
				8AF8E8CC877C11F21F8B5BF3 /* MQTTSSLSecurityPolicyEncoder.h in Headers */,
				42DFF3BF3C24FE6914510C91 /* MQTTSSLSecurityPolicyTransport.h in Headers */,
				4D104BA88C532961FB83F47C /* MQTTProperties.h in Headers */,
				23B5226FAEDA3A21685330FC /* NSURL+MinWhoFive.h in Headers */,
				DB32F3F897F7833A3CA2616F /* MQTTStrict.h in Headers */,
				3F12E2563E1683F736DE072F /* MQTTLog.h in Headers */,
				1285C7805570F79CCDE60BE4 /* ForegroundReconnection.h in Headers */,
				691F585AB7016BB3D595E9F7 /* MQTTMessage.h in Headers */,
				9F70EF7A78884FE98F35773A /* MQTTInMemoryPersistence.h in Headers */,
				3607E874047948E6FB6A47CC /* MQTTDecoder.h in Headers */,
				F9165AAD2F0D70013320F60E /* VisionCousinManager.h in Headers */,
				803974A8827A180961C6FF77 /* TildeScanPlaneWindow.h in Headers */,
				8769D85D5AF4CCD6C1B4A075 /* GCDTimer.h in Headers */,
				566383CBEA4066F9599975C2 /* MQTTCFSocketEncoder.h in Headers */,
				3494F215E10E58402357B878 /* MQTTCFSocketTransport.h in Headers */,
				EEC806AC1178DEBCDB2E4824 /* UIDevice+BarDevice.h in Headers */,
				FB01D2E9F1601CF17B2CAF94 /* MQTTTransportProtocol.h in Headers */,
				40FB843DAF398E697FDC9013 /* MQTTClient.h in Headers */,
				6ACE6A3B2F113D33BCBDA9FE /* MQTTSessionLegacy.h in Headers */,
				B1D189E0E3606D3C73309562 /* MQTTSession.h in Headers */,
				8AE8297F4457559D67E9DDD9 /* MQTTSSLSecurityPolicyDecoder.h in Headers */,
				1BF5F5C6611468D3ECCE9E61 /* MQTTCFSocketDecoder.h in Headers */,
				1FDF19177C0D1E6E5565E2B8 /* MQTTSessionManager.h in Headers */,
				48424EDAD434677DCEE66F4B /* ShapeWinNet.h in Headers */,
				F0FE9BDD93F13BB1914CA57B /* NSImage+Compatibility.h in Headers */,
				54DB16D6BDC669A5A36C68FA /* SDAnimatedImage.h in Headers */,
				D0950042B576985C220B5CDC /* SDWebImageManager.h in Headers */,
				5E98EF46358885356FD71567 /* SlavicTapTextField.h in Headers */,
				B9DA1D412BBA4538429318E4 /* SlowDaySeekThe+HitOut.h in Headers */,
				264878515920E47EA7115085 /* NSData+ImageContentType.h in Headers */,
				EE5E2662223106F7F5DC3D47 /* SDWebImageDefine.h in Headers */,
				BC082F8B96BC73AC073F17CC /* UIView+WebCacheState.h in Headers */,
				6F1ECEA74CB21105B22D4713 /* SDImageTransformer.h in Headers */,
				6E599A3D94653B0B40671593 /* SDDiskCache.h in Headers */,
				BC861D80BA93636511DCD141 /* EphemeralViewController.h in Headers */,
				A0349A6CA69F66E016C6CDF3 /* SDAssociatedObject.h in Headers */,
				425AC8D0A195C565E6BDCDA5 /* SDImageIOAnimatedCoderInternal.h in Headers */,
				51DF28D31DCD7EA5137EA1C5 /* ConfirmProtocol.h in Headers */,
				CBBC6CC2B4FFA62111317C2A /* NSString+SheHectares.h in Headers */,
				FDBE948B98038674DF09D9C0 /* SDDeviceHelper.h in Headers */,
				4D13B8F10ABDFE0AE602FF6E /* SDImageCodersManager.h in Headers */,
				4C81DAAAFF90EF45DC6B6E69 /* SDWebImageDownloaderConfig.h in Headers */,
				13A9742734BA1792877627A8 /* DetailedPutManager.h in Headers */,
				3BD54DC38C682D229C62F42E /* MakerCarbon.h in Headers */,
				5CCE0F6B4B765EA5FDDA4F8A /* UIImage+ExtendedCacheData.h in Headers */,
				B9948D41D310F38BDFE91E80 /* UIImageView+WebCache.h in Headers */,
				5650634D8E32970488798BEA /* SDWebImageError.h in Headers */,
				C9A8B499B5F0D162366ADCC2 /* SDImageAPNGCoder.h in Headers */,
				176D964CBBB67E9620061C48 /* SDWebImageCacheKeyFilter.h in Headers */,
				FE924B9DF2B2AB2B145C5D65 /* KoreanSeeViewController.h in Headers */,
				619AE0C25EE50FB78450F1D1 /* TagSidebarView.h in Headers */,
				841BC7997858969724CA8B21 /* UIImage+MemoryCacheCost.h in Headers */,
				4504E12D256F1671CBA2E119 /* SDWebImageDownloaderRequestModifier.h in Headers */,
				65C9CBBF90BC00F1F4771A98 /* SDImageCache.h in Headers */,
				190B709556CE952D372E39CB /* SDMemoryCache.h in Headers */,
				6BA9E8250E834A58D5FA3F94 /* NSBezierPath+SDRoundedCorners.h in Headers */,
				85807CC17CA2819AC1B1E2F8 /* UIButton+WebCache.h in Headers */,
				8650260BA635FFAE5E55E6C7 /* PronounLegibleDefinesFlightsAttitudeBit.h in Headers */,
				A241BFCFD02E9C05E38217F0 /* SDWebImageTransition.h in Headers */,
				8C96047F4FDFE328C8E2A3BB /* SDImageCachesManager.h in Headers */,
				00E98AE41A51C02DD9BB7AE8 /* PoloFigure.h in Headers */,
				2F877BF14C512F76A2575C20 /* PenNetwork.h in Headers */,
				2837C7C9C6E28933B72F7C4A /* LeapHueHeapSunViewController.h in Headers */,
				925A9DC81C65D6501DF0F76E /* ViewController+MASAdditions.h in Headers */,
				C99D91AC2ED0479CC75BFD6A /* MASCompositeConstraint.h in Headers */,
				456E9636D355811F2DF5D8A0 /* View+MASShorthandAdditions.h in Headers */,
				CF9A9DB79070052C0EE036A0 /* NSArray+MASShorthandAdditions.h in Headers */,
				F321013C0199B06C70A4F688 /* NSArray+MASAdditions.h in Headers */,
				9F8DDC75855FD6B9477241BA /* View+MASAdditions.h in Headers */,
				EF0413B8A2AC49A059B17DBD /* MASConstraint.h in Headers */,
				86C16D060DEF9A76D18ADEE6 /* MASViewConstraint.h in Headers */,
				A00363A2624181007390B84A /* MASConstraintMaker.h in Headers */,
				E35FED8F5E4D14CDDC2A97CA /* CellTapCan.h in Headers */,
				4CB95F57157BFE5F5D2601FB /* MASUtilities.h in Headers */,
				9A820BBC8AECD3B3F213C599 /* MASLayoutConstraint.h in Headers */,
				BE5B78DB8521262FA0F5A8A5 /* MASViewAttribute.h in Headers */,
				3928F8EB51D1C956931571E7 /* NSLayoutConstraint+MASDebugAdditions.h in Headers */,
				2DB82E4C5B171D9457B4D61B /* MASConstraint+Private.h in Headers */,
				6A066E1BAE76B8A038E00240 /* ConstantAdditionsValueRadioMouseYou.h in Headers */,
				28E677C0550C73FA7CA2244F /* LongRaceView.h in Headers */,
				6E04DFF567165AEBE158A037 /* Masonry.h in Headers */,
				2E6D602A5519F513ACEF7C03 /* FillerBus.h in Headers */,
				5D53F0578D2CE57EF0566D39 /* ScriptsFourthViewController.h in Headers */,
				74E6F5AC29C8CF04B6DB0946 /* BevelNarrative.h in Headers */,
				2B20DC9AB889BF3323ACE803 /* YoungerViewController.h in Headers */,
				36F84E894256DF4A5E5122D8 /* SpeakPaperFair.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		3FE20895CC34656FF39DF922 /* GrossScrap */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AEC23BE33D81B0E8600EC5A5 /* Build configuration list for PBXNativeTarget "GrossScrap" */;
			buildPhases = (
				75087E5830DAE9B413958556 /* Headers */,
				954976544EDD936216BA7256 /* Sources */,
				E3F63D22B6938B515AFBE756 /* Frameworks */,
				8673678FED6CD29D4BE64548 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = GrossScrap;
			packageProductDependencies = (
			);
			productName = GrossScrap;
			productReference = 911C8E95F97D6F2B815B80FF /* GrossScrap.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0FCCACE8925327AC74E86904 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					F17366C4F0A8597E4265C7C9 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 2432601553D91F48D0F9D0FF /* Build configuration list for PBXProject "GrossScrap" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 0B4099BF47C10DFA7F32FFAB;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 509CCF2F29497DC71FE64377 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				3FE20895CC34656FF39DF922 /* GrossScrap */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8673678FED6CD29D4BE64548 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		954976544EDD936216BA7256 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				385323F3C28080D36A7F5645 /* FoodHoursHave.m in Sources */,
				952EB2B464AAA6CCDDD73AD1 /* SayUseTurn.m in Sources */,
				47AE926D3D31536176A3931E /* TrustPartViewController.m in Sources */,
				8FBD66B5320A232782D40A14 /* ViewController+MASAdditions.m in Sources */,
				68199695E767D3FA63D47FBC /* SpeakPaperFair.m in Sources */,
				452D02290CC513C40723D786 /* MASViewConstraint.m in Sources */,
				926181795457D7F4132A811D /* SlowDaySeekThe+Speak.m in Sources */,
				667672100F9E59A956C1810C /* NSLayoutConstraint+MASDebugAdditions.m in Sources */,
				2FC0E975B987732224AAD866 /* YouWireCupCell.m in Sources */,
				29E6880B33FF0F9B0D0FF5AC /* MASConstraint.m in Sources */,
				01A3F660EDA632ABA11AFA1D /* LongRaceView.m in Sources */,
				F7AF2CCE5427B8C41C624226 /* MASLayoutConstraint.m in Sources */,
				C2454E919647416AFBA6A058 /* SDMemoryCache.m in Sources */,
				9E7F0E88DD0A126569E75048 /* SDAnimatedImageView.m in Sources */,
				585D4E8C521BE02039E7AA51 /* FixtureDrive.m in Sources */,
				624C32486459233383636037 /* ConstantAdditionsValueRadioMouseYou.m in Sources */,
				D407035008F74BEBF81379F9 /* SDInternalMacros.m in Sources */,
				37B909CEC6B9321344D7FCE3 /* TowerHisManager.m in Sources */,
				86D710F96299B3234ACD46E9 /* SDAnimatedImage.m in Sources */,
				9A48451DE194E948248E0622 /* NSData+ImageContentType.m in Sources */,
				9B544172F6A6CDBEB97EE921 /* NSButton+WebCache.m in Sources */,
				3A715DCB13A0771FF51798B2 /* SDImageTransformer.m in Sources */,
				BF39CB5FE6A4300537024128 /* DueLockAirTooViewController.m in Sources */,
				ED699CB1A72E6C68E5DDB477 /* SDImageAWebPCoder.m in Sources */,
				658F5334E776029D8D9B6EA2 /* SDAsyncBlockOperation.m in Sources */,
				3F6EF2B9BBDB49FEEDBDCD65 /* UIButton+WebCache.m in Sources */,
				FE6E4F0C7685A35123A835BF /* SDAnimatedImageView+WebCache.m in Sources */,
				7DB646D7BB190EFF836411CC /* TwoVideoViewController.m in Sources */,
				B2393D8DEB63967E3D51F5D5 /* UIImage+ExtendedCacheData.m in Sources */,
				024B6817A8A72117F24F35AA /* SDImageLoader.m in Sources */,
				7165935B079FBD9B1B9DAE42 /* SDWebImageDownloaderDecryptor.m in Sources */,
				334B92915240EC8337B7D098 /* UIImage+OwnImage.m in Sources */,
				2B4836BDC5E21B397EA3F71F /* PopPickMayViewController.m in Sources */,
				954806F7D48511D023032475 /* SDWebImageIndicator.m in Sources */,
				96DFE139423BDBC0EDA1E4C0 /* SDImageFrame.m in Sources */,
				6BBD59DF02260BC247977ED1 /* SDFileAttributeHelper.m in Sources */,
				06AE19DA1634D35C7404C080 /* SDWebImageError.m in Sources */,
				7A6AE46ED26946DACC42A79E /* SDWebImageDownloaderConfig.m in Sources */,
				1DF25A27156461544DAA8DC9 /* SDImageLoadersManager.m in Sources */,
				E42B2E94A7F93BE970027C2B /* UIImage+MemoryCacheCost.m in Sources */,
				7E78F694731E3E705A8B59E0 /* UIImageView+HighlightedWebCache.m in Sources */,
				66D0C4DB8FDD4ED0C19B7FCF /* SDImageGraphics.m in Sources */,
				50FEFE2CD44A4DD95DA83EE9 /* SDImageCachesManagerOperation.m in Sources */,
				8924DDC9A4C0215B77E6A1E9 /* SDWebImageManager.m in Sources */,
				63E12F4D7C8E7AA1BCF25625 /* SDImageCoder.m in Sources */,
				2A4A34ECA7AC0DF3B67C601C /* CheckSmoothButton.m in Sources */,
				4FBCF62671ABB440D9DC427F /* UIImage+GIF.m in Sources */,
				746E70EE580C05A2E190022A /* TurnWordTipViewController.m in Sources */,
				7BECFEA388539C55F3B52206 /* SDImageCoderHelper.m in Sources */,
				8610F33DB40265B9C3C8D721 /* UIDevice+BarDevice.m in Sources */,
				4D91964573554B9609DDAA72 /* LeapHueHeapSunViewController.m in Sources */,
				302F8E7F25FDC783D65BD5BD /* SendEraBlobRet.m in Sources */,
				4A2C3FB79EB44E750FC1555F /* SDWebImageDownloaderRequestModifier.m in Sources */,
				19FAF1CE6C5FDA324B96D771 /* CaseBurstViewController.m in Sources */,
				CE8626D5C7A73CF0D673DCAE /* LogLowerHexManager.m in Sources */,
				AB59ED89ECD52CE9AA815B44 /* SDWebImageCacheKeyFilter.m in Sources */,
				472477DEB549EA877793E4F6 /* AtomHasDogViewController.m in Sources */,
				0B42760FF94DB2092398CE27 /* CanBusEnableModel.m in Sources */,
				ED6C62BBE6F03583C04F2CBB /* EphemeralViewController.m in Sources */,
				FA012A49A7D2C2ECDD73BCA8 /* KoreanSeeViewController.m in Sources */,
				ED2FE6DE324397EEA6A6A73A /* SDImageCacheConfig.m in Sources */,
				4E01C857726A5D2CC0F18167 /* SDImageCodersManager.m in Sources */,
				5C93409BC9BA62D610CCB4A5 /* SDAnimatedImagePlayer.m in Sources */,
				48B2B814FCE579BF684F902E /* NSData+PinThat.m in Sources */,
				2E52B65E92D39CF6B77EA497 /* HeaderManager.m in Sources */,
				4AADD3F8BF8A9C961C7AE435 /* SDImageIOCoder.m in Sources */,
				6E683A2FB0B2D4C54E6688EF /* CellTapCan.m in Sources */,
				832BE7E5AA70D452384207CB /* SinPushCapFlowButton.m in Sources */,
				103C5230F1DCD635F529DE65 /* NSImage+Compatibility.m in Sources */,
				8557C8DC70061D0682F50828 /* SDWebImageDefine.m in Sources */,
				F62C98E950E16A276381C05E /* UIImageView+WebCache.m in Sources */,
				B658043AD0E69D37EAE25687 /* PolarEachViewController.m in Sources */,
				50079C96565227CDA6F05D4F /* ProcessCircularTemporalCountryFlag.m in Sources */,
				801D09F1797D41AB84BD9454 /* DogTempPin.m in Sources */,
				99894B2CDE347BF19C3F624E /* SDWebImageOptionsProcessor.m in Sources */,
				C2744A91158CC9B485C2D03E /* VisionCousinManager.m in Sources */,
				FEBEF11561D650B0BE87B887 /* SDCallbackQueue.m in Sources */,
				ADEB646202B815F3D90553F8 /* UIImage+ForceDecode.m in Sources */,
				1528CB5D151A16EE74B76B20 /* NSURL+MinWhoFive.m in Sources */,
				C58C8FC25E7350F139FCDB4B /* SDDeviceHelper.m in Sources */,
				0BBF6981A267FC543F1BCC6D /* SDImageGIFCoder.m in Sources */,
				59B7F391CE6FCE9F3058B2AB /* UIView+WebCacheState.m in Sources */,
				2DB50D09151A536E585203D2 /* UIImage+Metadata.m in Sources */,
				79915D47E74AF70442DC8A8B /* UIView+WebCacheOperation.m in Sources */,
				EAAE32A7577C0AAB1CD8DEFE /* SonAssetWinNetCell.m in Sources */,
				749B91E4126C57432D662120 /* SDDiskCache.m in Sources */,
				A752A3F45FC689B14B43D3A0 /* SDImageCacheDefine.m in Sources */,
				54DA993D5430D87E122157B7 /* GroupedCollectViewController.m in Sources */,
				0D4EA786420C4E116D805B06 /* NSObject+MenPunjabiInfoSongLost.m in Sources */,
				6D913532D1F71249EDBF1345 /* DirectTouchAction.m in Sources */,
				2CB77D4A8792B27102372185 /* SDWeakProxy.m in Sources */,
				520948E8D08B8A8B1E0F1D65 /* SplatManager.m in Sources */,
				84DF91033597C6A0B678B4C9 /* SDWebImageDownloaderOperation.m in Sources */,
				B4F19DCAE86B1CE0EF1C55CA /* UIColor+SDHexString.m in Sources */,
				ACACFDB109F83A83BFA9E74C /* SlavicTapTextField.m in Sources */,
				6B48BC16B29C6473A7AECB4F /* SDWebImageDownloaderResponseModifier.m in Sources */,
				CCE5443107ADB68C895432F7 /* SDWebImageOperation.m in Sources */,
				570891A6718DCB62A3FE654A /* NSString+SheHectares.m in Sources */,
				00E5071DC05F7FCCB271C04E /* NSBezierPath+SDRoundedCorners.m in Sources */,
				2B90037DD5615E4742A13443 /* FloaterBendTerabytesCopperTaggingReasonViewController.m in Sources */,
				55102C1A8DBD52D3AB33AF2E /* SDImageAPNGCoder.m in Sources */,
				D364E3B866415A2845616E73 /* ChromaFarManager.m in Sources */,
				E92720FD2081C6AD54AC5968 /* UIImage+Transform.m in Sources */,
				10FB5E88853E3667EDF440C0 /* FigureGetSonInfo.m in Sources */,
				78301F295B7231934993E3A6 /* SDImageAssetManager.m in Sources */,
				2EAEB91FDE551EA7726E24B7 /* SDWebImageTransition.m in Sources */,
				E0A267C2C8A7DF47696310D5 /* SpaKinWhite.m in Sources */,
				C422C7DACBAAB30BB6C90D85 /* SDImageCache.m in Sources */,
				2633F37C5280577CFF451E38 /* SDImageHEICCoder.m in Sources */,
				93385DD1CA745891F3ACCC94 /* SDImageCachesManager.m in Sources */,
				ACA19921D4606A5CF4602B92 /* GarbageAndViewController.m in Sources */,
				4435EABA834BCECEDD1CCCFC /* MQTTSessionSynchron.m in Sources */,
				9064CECB0672A71736F18D17 /* NSString+BedLow.m in Sources */,
				A0AF6669505413A19D1F2C37 /* MQTTDecoder.m in Sources */,
				150D77B1C37666A147E85BC6 /* MQTTSessionLegacy.m in Sources */,
				5DF5A2DD24398E016BBE445C /* MQTTSSLSecurityPolicy.m in Sources */,
				37EBDEE2A88AD621E10F2E67 /* MQTTMessage.m in Sources */,
				D55620305DDC5AAC957BE4AB /* MQTTSessionManager.m in Sources */,
				44A2039FB004080FD9CEACE0 /* ReconnectTimer.m in Sources */,
				17FF73839FCCCC28F653A46D /* MQTTSSLSecurityPolicyTransport.m in Sources */,
				F2D6A2E5786F29BF20F39482 /* MQTTSession.m in Sources */,
				AEFB1B4A13F798CAE6A001D8 /* MQTTCoreDataPersistence.m in Sources */,
				B15FD15F31BCEDB65C4B4E20 /* MQTTCFSocketTransport.m in Sources */,
				58BC352825A76DA382D167C7 /* MQTTLog.m in Sources */,
				F238731F31F345C68F0182C5 /* MQTTSSLSecurityPolicyDecoder.m in Sources */,
				6D2EB60631A6B26D9F6D78B2 /* MQTTSSLSecurityPolicyEncoder.m in Sources */,
				B2B5C4CD84101E3285565336 /* MQTTTransportProtocol.m in Sources */,
				B88597C4D0B634FE7EFBF427 /* MQTTInMemoryPersistence.m in Sources */,
				3351C5EAAD5C49E0DCF6989B /* MQTTStrict.m in Sources */,
				FBF4B04FF031D8C0A0C81BF3 /* MQTTCFSocketDecoder.m in Sources */,
				601B27AC9089CC30ED9984F9 /* SlowDaySeekThe+HitOut.m in Sources */,
				96A8AB736243B752B5E612BF /* GCDTimer.m in Sources */,
				744DA262D8D3D27C18D5A069 /* MQTTCFSocketEncoder.m in Sources */,
				1C73CD9A036DD9846F72310D /* MQTTProperties.m in Sources */,
				597E014119C5D530945C8760 /* ForegroundReconnection.m in Sources */,
				302FA6187AB4BC64E86BC631 /* SDGraphicsImageRenderer.m in Sources */,
				42BB719508C1C0E1728AFCD3 /* UIView+WebCache.m in Sources */,
				6CAD80BBAECD38DAC5FDC0D3 /* SDWebImagePrefetcher.m in Sources */,
				F89E90F41E185B3E1C496B48 /* ShapeWinNet.m in Sources */,
				134FDE3B808856A27A590F7C /* SDWebImageCacheSerializer.m in Sources */,
				2CE2809793AC003FA1A99ED7 /* SDDisplayLink.m in Sources */,
				2F97E63EC37D5DE9A41031CC /* SDWebImageDownloader.m in Sources */,
				FA225D287F1FF25CC8B5CF1B /* SDImageFramePool.m in Sources */,
				CC6DAC50D6A2C1AB5A0B7DFE /* SDImageIOAnimatedCoder.m in Sources */,
				CAC5397FC70660EEDC207EB3 /* UIImage+MultiFormat.m in Sources */,
				BCB20D683C4AF793ACDC1031 /* SDAssociatedObject.m in Sources */,
				1D1051BC755DA79C16F61CE7 /* SDAnimatedImageRep.m in Sources */,
				BC3CADBBE58235B7594FEDBB /* BigPreviousManager.m in Sources */,
				323DC6522A3402A546204165 /* SDWebImageCompat.m in Sources */,
				E8366E8DD2A24F80781B598B /* SettingEncodingsStringMiterRefreshedSubgroup.m in Sources */,
				E80382D1E3769EC954861C43 /* MusicalRadians.m in Sources */,
				EFD4146F36BC5710E4F96B21 /* OriginsFunnelSignalingReadyCaps.m in Sources */,
				52C862886BE567091B4621B8 /* Adobe.m in Sources */,
				59228357FE34D19AEA3E1EE8 /* CountViewController.m in Sources */,
				2733E4FF3631A20F585178CB /* BigPrologHasBankersWon.m in Sources */,
				B6B5AD89CF628A5DEDE67E2D /* NSArray+MASAdditions.m in Sources */,
				C9727060F121038B0DAF3F20 /* MASConstraintMaker.m in Sources */,
				40A9C58A1ECAE6E3631256F2 /* FrequencyInfo.m in Sources */,
				74C39B1802DC5C8C0E4DB7D1 /* MASViewAttribute.m in Sources */,
				2CD96A24E5432CBF427A97D6 /* MASCompositeConstraint.m in Sources */,
				CECEF2AD7E83537D3423B66D /* View+MASAdditions.m in Sources */,
				121553B285718252D15D7B31 /* SemicolonViewController.m in Sources */,
				7BEC7597E7145E3D6FFEFC7A /* ScriptsFourthViewController.m in Sources */,
				84963E001B51CCA1997FEC88 /* XXGProtocolLabel.m in Sources */,
				584A87E295C3186CB5022B49 /* YoungerViewController.m in Sources */,
				94FBB65C4195A2FE2C774936 /* InteractItalicBouncingRegionsDimensionModel.m in Sources */,
				48645C2F4A9C5F910C8BBF68 /* NSError+FootnoteOdd.m in Sources */,
				E1399EA671AA7055856530C8 /* NotNotConfig.m in Sources */,
				2A7380AD26E16DA8CDD9E8C3 /* TagSidebarView.m in Sources */,
				C3AD52B1742878E418B896B6 /* MusicalManager.m in Sources */,
				DFACBBC650A8053827DA9E9F /* SongArmBleedManager.m in Sources */,
				7F1D5D587601B53F6799A671 /* RetMenuLastPanInfo.m in Sources */,
				BC32722BEDBB7335966D6998 /* GrossScrap.m in Sources */,
				06D680EFD8B88E1AEDEF3682 /* EarSigningManager.m in Sources */,
				E5E904DBAE812C9BAB8F2B55 /* EncodingsDarkWasPasswordFilteringCell.m in Sources */,
				AB934412D9A125104AB20F4E /* SlowDaySeekThe.m in Sources */,
				244C4700B8B1B18139FA8316 /* ReportManager.m in Sources */,
				6630D64C920443EE0C9DFC6F /* IrishAlbanian.m in Sources */,
				5C80C59CD7E2D3507E724ADD /* BitOwnPathView.m in Sources */,
				F5F9D119CA45CF2CD2F05241 /* InverseArmAreNiacinAllocator.m in Sources */,
				0A8A816493C334267E473AB6 /* BendRope.m in Sources */,
				E4A1EBA60C6119DDA363B2B3 /* Dominant.m in Sources */,
				14EB7E0EE1694A2ACF39BE19 /* RawUpsideOdd.m in Sources */,
				8B1614566F3709F0D519F39D /* DetailedPutManager.m in Sources */,
				A0804206A8CAEA96EEC678A7 /* HandledEggConfig.m in Sources */,
				D4E21026EEA6532FB5BDD57E /* LikeAsleepViewController.m in Sources */,
				863DB0B18B5D08523F371413 /* VerboseCombine.m in Sources */,
				4C6DA9EB064CB0528F5C09FE /* NSObject+WayModel.m in Sources */,
				782EA787BC4FB6954A0030DC /* SumBuffersVisitAlbumBuildViewController.m in Sources */,
				93003918EC4C4EF6693E9C44 /* LoopsEyeRomanController.m in Sources */,
				B1459573B43EB2E6EBF75261 /* UIViewController+BoxViewController.m in Sources */,
				3ED5F6962692C6E1D4BD4E02 /* HomePhraseRankCell.m in Sources */,
				5E1F9656E968187B9DF89642 /* BevelNarrative.m in Sources */,
				70A3BDE247E0D22477796317 /* DryBarInterKey.m in Sources */,
				9696199CDC7858FCD5EFB97A /* DownloadBondLongSucceededTen.m in Sources */,
				77C49ECD346605E8F254A7A6 /* RedoBackupMaintainPhraseHormone.m in Sources */,
				CEC69FB298B4CCE2B9CDC756 /* PoloFigure.m in Sources */,
				624B960837589CDB83D3FF88 /* ParameterStillCalculateAdverbBoldface.m in Sources */,
				80BC63356A2D60493D8AD7D0 /* UIColor+GetColor.m in Sources */,
				2F46882F5C20CB72D1F6D148 /* SobAlertView.m in Sources */,
				B8D2E0F1E4EC8B86CC633B99 /* CiphersEarModel.m in Sources */,
				E63793CCA193B325AFB1B7F0 /* PronounLegibleDefinesFlightsAttitudeBit.m in Sources */,
				9D6A3F11D0C086A18114C44C /* OutToast.m in Sources */,
				C3B3610C6F6562C3121DF347 /* InferLoadStone.m in Sources */,
				97B33422FF482FDBF763C299 /* RareRaceColor.m in Sources */,
				AE203F81239EC91BE756FB6A /* PivotForManager.m in Sources */,
				A924922297F9084415E39799 /* ArtworkModel.m in Sources */,
				4CE0C76753BA53506C63A66C /* ModeRunningDelayRaiseStrokingTool.m in Sources */,
				FEBDD10203AB9F7007050A86 /* SeeBusStepList.m in Sources */,
				60A6DCD9D9F719FB07787F6B /* SlowDaySeekThe+EraDecide.m in Sources */,
				8903D65A5D2812C515C722B6 /* MarkupInfo.m in Sources */,
				7A97D13DE781C958695E776C /* PenNetwork.m in Sources */,
				FA6EF1B3AE42C19EBFD8BA6E /* NSString+StickySay.m in Sources */,
				420A5532243A872F944226D7 /* HitUnableManager.m in Sources */,
				A5BF006E6F25A857B1F5FE52 /* VerboseDigestSignUnorderedSynthesis.m in Sources */,
				959003B7785CF8F3483933CE /* BoxArtsCategoryClickedWrap.m in Sources */,
				8641DB125DC3A2EF2110C5A2 /* SeeVitalModel.m in Sources */,
				706B2BD4**************** /* TildeScanPlaneWindow.m in Sources */,
				EFFE5B934729A41E22EF2618 /* SemicolonThe.m in Sources */,
				D1C240375B9D5CDDB3A9C145 /* FillerBus.m in Sources */,
				302836A707A305A4EBDEAA75 /* LiveStyleInfo.m in Sources */,
				2539169F896DD4D6403AB446 /* WindowsSobInfo.m in Sources */,
				6FD3D68C659E932DB4C54897 /* MakerCarbon.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		259D7FB0D52FC86B134BC2DF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		75636C6FBC633C2D61B02503 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		80C587D8B0B05FE5C9C9B00C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = NO;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.GrossScrap;
				PRODUCT_NAME = GrossScrap;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		9EC99FAD42DAE3B7ACB1D264 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = NO;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.GrossScrap;
				PRODUCT_NAME = GrossScrap;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2432601553D91F48D0F9D0FF /* Build configuration list for PBXProject "GrossScrap" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				75636C6FBC633C2D61B02503 /* Debug */,
				259D7FB0D52FC86B134BC2DF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AEC23BE33D81B0E8600EC5A5 /* Build configuration list for PBXNativeTarget "GrossScrap" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9EC99FAD42DAE3B7ACB1D264 /* Debug */,
				80C587D8B0B05FE5C9C9B00C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0FCCACE8925327AC74E86904 /* Project object */;
}
