#!/bin/bash

# 完善的构建脚本模板
# 此脚本会被 handle_script 方法自动更新参数

# 设置工作区路径和基本参数
WORKSPACE_PATH="XXGPlayKit.xcworkspace"
SDK_NAME="GrossScrap"
SDK_VERSION="v3.2.2"
CN_OR_OS="国际版"
SCHEME_NAME="XXGPlayKitOSDemo"
BUILD_DIR="build"  # 存放构建文件的目录
SDK_DIR="SDK"      # 目标SDK目录
EXCLUDE_PATTERN="*Test*"  # 排除测试相关的文件

# 构建选项
SILENT_BUILD=true  # 是否静默构建（忽略详细输出）
SHOW_PROGRESS=true # 是否显示进度
DEEP_CLEAN=false   # 是否进行深度清理（清理 Xcode 缓存）

# 颜色输出函数
print_info() {
    echo -e "\033[32m[INFO]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $1"
}

# 检查必要的工具和文件
check_prerequisites() {
    print_info "检查构建环境..."
    
    # 检查 xcodebuild 是否可用
    if ! command -v xcodebuild &> /dev/null; then
        print_error "xcodebuild 未找到，请确保已安装 Xcode"
        exit 1
    fi
    
    # 检查工作区文件是否存在
    if [ ! -d "$WORKSPACE_PATH" ]; then
        print_error "工作区目录不存在: $WORKSPACE_PATH"
        exit 1
    fi
    
    print_info "环境检查通过"
}

# 清理旧的构建文件
clean_build() {
    print_info "清理旧的构建文件..."
    
    # 清理构建目录
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
        print_info "已删除旧的构建目录: $BUILD_DIR"
    fi
        
    # 清理SDK目录
    if [ -d "$SDK_DIR" ]; then
        rm -rf "$SDK_DIR"
        print_info "已删除旧的SDK目录: $SDK_DIR"
    fi
    
    # 创建新的构建目录
    mkdir -p "$BUILD_DIR"
    mkdir -p "$SDK_DIR"
    

    # 可选的深度清理（清理 Xcode 缓存）
    if [ "$DEEP_CLEAN" = true ]; then
        print_info "进行深度清理 - 清理 Xcode 项目缓存..."
        xcodebuild clean -workspace "$WORKSPACE_PATH" -scheme "$SCHEME_NAME" -configuration Release
        print_info "深度清理完成"
    else
        print_info "跳过 Xcode 缓存清理（xcodebuild clean build 会自动处理）"
    fi
    
    print_info "清理完成"
}

# 构建项目
build_project() {
    print_info "开始构建 $SCHEME_NAME (Release 配置)..."

    # 创建日志文件
    LOG_FILE="$BUILD_DIR/build.log"
    mkdir -p "$BUILD_DIR"

    if [ "$SILENT_BUILD" = true ]; then
        # 静默构建模式
        build_project_silent
    else
        # 详细输出模式
        build_project_verbose
    fi
}

# 静默构建（推荐）
build_project_silent() {
    # 显示进度的函数
    show_progress() {
        local step=$1
        local total=$2
        local message=$3
        local percent=$((step * 100 / total))
        if [ "$SHOW_PROGRESS" = true ]; then
            printf "\r\033[32m[进度]\033[0m [%3d%%] %s" "$percent" "$message"
        fi
    }

    # 开始构建进度显示
    show_progress 1 4 "正在清理项目..."

    # 构建项目，将详细输出重定向到日志文件
    xcodebuild -workspace "$WORKSPACE_PATH" \
               -scheme "$SCHEME_NAME" \
               -configuration Release \
               -sdk iphoneos \
               -destination "generic/platform=iOS" \
               clean build \
               -derivedDataPath "$BUILD_DIR" \
               ONLY_ACTIVE_ARCH=NO \
               BUILD_LIBRARY_FOR_DISTRIBUTION=YES \
               > "$LOG_FILE" 2>&1 &

    # 获取构建进程ID
    BUILD_PID=$!

    # 监控构建进度
    while kill -0 $BUILD_PID 2>/dev/null; do
        if [ "$SHOW_PROGRESS" = true ]; then
            # 根据日志内容显示进度
            if grep -q "Clean.Remove" "$LOG_FILE" 2>/dev/null; then
                show_progress 1 4 "正在清理项目..."
            elif grep -q "CompileC\|CompileSwift" "$LOG_FILE" 2>/dev/null; then
                show_progress 2 4 "正在编译源文件..."
            elif grep -q "Ld\|Link" "$LOG_FILE" 2>/dev/null; then
                show_progress 3 4 "正在链接..."
            elif grep -q "CodeSign\|ProcessInfoPlistFile" "$LOG_FILE" 2>/dev/null; then
                show_progress 4 4 "正在处理资源和签名..."
            fi
        fi
        sleep 1
    done

    # 等待构建完成并获取退出状态
    wait $BUILD_PID
    BUILD_RESULT=$?

    if [ "$SHOW_PROGRESS" = true ]; then
        echo  # 换行
    fi

    # 检查构建结果
    if [ $BUILD_RESULT -ne 0 ] || grep -q "BUILD FAILED" "$LOG_FILE"; then
        print_error "构建失败！详细信息请查看: $LOG_FILE"
        echo "最后几行错误信息:"
        tail -10 "$LOG_FILE"
        exit 1
    elif grep -q "BUILD SUCCEEDED" "$LOG_FILE"; then
        print_info "构建成功！详细日志保存在: $LOG_FILE"
    else
        print_warning "构建状态未知，请检查日志: $LOG_FILE"
    fi
}

# 详细输出模式（显示所有构建信息）
build_project_verbose() {
    print_info "使用详细输出模式..."

    xcodebuild -workspace "$WORKSPACE_PATH" \
               -scheme "$SCHEME_NAME" \
               -configuration Release \
               -sdk iphoneos \
               clean build \
               -derivedDataPath "$BUILD_DIR" \
               ONLY_ACTIVE_ARCH=NO \
               BUILD_LIBRARY_FOR_DISTRIBUTION=YES | tee "$LOG_FILE"

    if [ ${PIPESTATUS[0]} -ne 0 ]; then
        print_error "构建失败！"
        exit 1
    fi

    print_info "构建完成！详细日志保存在: $LOG_FILE"
}

# 复制框架文件
copy_frameworks() {
    print_info "复制 frameworks 和 .a 文件到 SDK 目录..."
    
    FRAMEWORK_DIR="$BUILD_DIR/Build/Products/Release-iphoneos"
    
    if [ ! -d "$FRAMEWORK_DIR" ]; then
        print_error "构建产物目录不存在: $FRAMEWORK_DIR"
        exit 1
    fi
    
    # 查找并复制 .framework 和 .a 文件
    print_info "查找并复制 .framework 和 .a 文件..."
    find "$FRAMEWORK_DIR" \( -name "*.framework" -o -name "*.a" \) ! -name "$EXCLUDE_PATTERN" -exec rsync -av {} "$SDK_DIR/" \;
    
    if [ $? -ne 0 ]; then
        print_error "复制 .framework 和 .a 文件失败"
        exit 1
    fi
    
    print_info "框架文件复制完成"
}

# 复制资源文件
copy_resources() {
    print_info "复制资源文件..."

    # 1. 复制 ${SDK_NAME}.bundle 整个文件夹到 $SDK_DIR
    if [ -d "${SDK_NAME}.bundle" ]; then
        # 确保复制整个文件夹，而不是文件夹内容
        cp -R "${SDK_NAME}.bundle" "$SDK_DIR/"
        print_info "已复制整个 ${SDK_NAME}.bundle 文件夹到 $SDK_DIR"

        # 验证复制结果
        if [ -d "$SDK_DIR/${SDK_NAME}.bundle" ]; then
            print_info "✅ 验证成功: $SDK_DIR/${SDK_NAME}.bundle 存在"
        else
            print_error "❌ 复制失败: $SDK_DIR/${SDK_NAME}.bundle 不存在"
        fi
    else
        print_warning "${SDK_NAME}.bundle 不存在，跳过"
    fi

    # 2. 根据版本类型复制对应的 Frameworks 和 Resources
    if [ "$CN_OR_OS" = "国内版" ]; then
        # 国内版：复制 CNFrameworks 和 CNResources
        if [ -d "CNFrameworks" ]; then
            cp -R "CNFrameworks" "$SDK_DIR/"
            print_info "已复制 CNFrameworks 到 $SDK_DIR"
        else
            print_warning "CNFrameworks 不存在，跳过"
        fi

        if [ -d "CNResources" ]; then
            cp -R "CNResources" "$SDK_DIR/"
            print_info "已复制 CNResources 到 $SDK_DIR"
        else
            print_warning "CNResources 不存在，跳过"
        fi
    else
        # 国际版：复制 OSFrameworks 和 OSResources
        if [ -d "OSFrameworks" ]; then
            cp -R "OSFrameworks" "$SDK_DIR/"
            print_info "已复制 OSFrameworks 到 $SDK_DIR"
        else
            print_warning "OSFrameworks 不存在，跳过"
        fi

        if [ -d "OSResources" ]; then
            cp -R "OSResources" "$SDK_DIR/"
            print_info "已复制 OSResources 到 $SDK_DIR"
        else
            print_warning "OSResources 不存在，跳过"
        fi
    fi

    print_info "资源文件复制完成"
    print_info "SDK目录内容:"
    ls -la "$SDK_DIR"
}

# 创建最终的 SDK 包：复制 Demo 项目并重命名为 FINAL_NAME，然后在其目录下添加文档和 SDK 文件
create_sdk_package() {
    print_info "创建最终的 SDK 包..."
    
    # 最终 SDK 包名称
    FINAL_NAME="闲闲SDK-iOS-${CN_OR_OS}-${SDK_NAME}-${SDK_VERSION}"
    DEST_PATH="../${FINAL_NAME}"
    
    # 如果已存在同名目录，先删除
    if [ -d "$DEST_PATH" ]; then
        rm -rf "$DEST_PATH"
        print_info "已删除旧的 SDK 包目录: $DEST_PATH"
    fi
    
    # 1. 复制 Demo 项目并重命名
    if [ -d "$SCHEME_NAME" ]; then
        cp -R "$SCHEME_NAME" "$DEST_PATH"
        print_info "已复制 Demo 项目并重命名为: $FINAL_NAME"
    else
        print_warning "Demo 项目目录不存在: $SCHEME_NAME"
        return 1
    fi

    # 2. 复制 SDK 对接文档到新目录
    if [ -d "SDK对接文档" ]; then
        cp -R "SDK对接文档" "${DEST_PATH}/"
        print_info "已复制 SDK对接文档 到: ${DEST_PATH}/SDK对接文档"
    else
        print_warning "SDK对接文档目录不存在，跳过"
    fi

    # 3. 复制构建的 SDK 文件夹到新目录
    if [ -d "$SDK_DIR" ]; then
        cp -R "$SDK_DIR" "${DEST_PATH}/"
        print_info "已复制构建输出 SDK 目录 到: ${DEST_PATH}/$(basename "$SDK_DIR")"
    else
        print_error "构建输出 SDK 目录不存在: $SDK_DIR"
        return 1
    fi
    
    print_info "SDK 包创建完成: $DEST_PATH"
}


# 显示构建信息
show_build_info() {
    print_info "构建信息:"
    echo "  工作区: $WORKSPACE_PATH"
    echo "  SDK名称: $SDK_NAME"
    echo "  SDK版本: $SDK_VERSION"
    echo "  版本类型: $CN_OR_OS"
    echo "  Scheme: $SCHEME_NAME"
    echo "  构建目录: $BUILD_DIR"
    echo "  SDK目录: $SDK_DIR"
}

# 主函数
main() {
    print_info "开始构建 SDK..."
    
    # 显示构建信息
    show_build_info
    
    # 执行构建步骤
    check_prerequisites
    clean_build
    build_project
    copy_frameworks
    copy_resources
    create_sdk_package
    
    print_info "🎉 构建和打包完成！"
    print_info "最终 SDK 包位置: ../闲闲SDK-iOS-${CN_OR_OS}-${SDK_NAME}-${SDK_VERSION}"
}

# 执行主函数
main "$@"
