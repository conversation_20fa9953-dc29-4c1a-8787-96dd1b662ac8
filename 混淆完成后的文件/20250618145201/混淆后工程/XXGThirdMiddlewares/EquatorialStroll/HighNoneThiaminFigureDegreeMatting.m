





#import <CL_ShanYanSDK/CL_ShanYanSDK.h>

@interface HighNoneThiaminFigureDegreeMatting : NSObject<CLShanYanSDKManagerDelegate>

@property (nonatomic, copy) void(^mostlyDarkRedoAction)(NSInteger);

@end

@implementation HighNoneThiaminFigureDegreeMatting

- (void)dealloc {
    
}

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t rareToken;
    dispatch_once(&rareToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

- (void)basalLemmaRandomDecigramsWideLinearlyDone:(NSString *)appId complete:(void (^_Nullable)(BOOL isCanLogin))complete {

    [CLShanYanSDKManager setCLShanYanSDKManagerDelegate:self];
    
    [CLShanYanSDKManager setPreGetPhonenumberUseCacheIfNoCellularNetwork:NO];
    
    [CLShanYanSDKManager initWithAppId:appId complete:^(CLCompleteResult * _Nonnull completeResult) {
        __block BOOL scrolledDomainLogin = !completeResult.error;
        
        if (scrolledDomainLogin) {
            
            [CLShanYanSDKManager preGetPhonenumber:^(CLCompleteResult * _Nonnull completeResult) {
                dispatch_sync(dispatch_get_main_queue(), ^{
                    complete(completeResult.error == nil);
                });
            }];
            
        } else {
            dispatch_sync(dispatch_get_main_queue(), ^{
                complete(NO);
            });
        }
    }];
}


- (void)notifyNeedFaceBandZoomController:(UIViewController *_Nonnull)controller array:(NSArray *)array success:(void (^_Nullable)(NSDictionary * _Nonnull synthesis))success periodJoin:(void (^_Nullable)(NSString * _Nonnull error))error fontJobAction:(void(^)(NSInteger))action {
    self.mostlyDarkRedoAction = action;
    

    [CLShanYanSDKManager quickAuthLoginWithConfigure:[self sessionsStore:controller tailArray:array] openLoginAuthListener:^(CLCompleteResult * _Nonnull completeResult) {
        if (completeResult.error) {
            error(completeResult.message);
        }
    } oneKeyLoginListener:^(CLCompleteResult * _Nonnull completeResult) {
        
        if (completeResult.error == nil) {
            dispatch_sync(dispatch_get_main_queue(), ^{
                success(completeResult.data);
            });
        }else {
            error(completeResult.message);
        }
    }];
}





- (CLUIConfigure *)sessionsStore:(UIViewController *)viewController tailArray:(NSArray *)array {
    CLUIConfigure *weekSongBad = [[CLUIConfigure alloc] init];
    
    weekSongBad.viewController = viewController;
    
    
    weekSongBad.clNavigationBarHidden = @(YES);
    
    //logo
    weekSongBad.clLogoHiden = @(YES);
    
    
    weekSongBad.clPhoneNumberFont = [UIFont boldSystemFontOfSize:26];
    weekSongBad.clPhoneNumberColor = UIColor.darkGrayColor;
    weekSongBad.clPhoneNumberTextAlignment = @(NSTextAlignmentCenter);
    
    
    weekSongBad.clSloganTextFont = [UIFont systemFontOfSize:14];
    weekSongBad.clSloganTextColor = UIColor.darkGrayColor;
    weekSongBad.clSlogaTextAlignment = @(NSTextAlignmentCenter);
    
    
    weekSongBad.clLoginBtnText = array[4];
    weekSongBad.clLoginBtnTextFont = [UIFont systemFontOfSize:18];
    weekSongBad.clLoginBtnTextColor = UIColor.whiteColor;
    weekSongBad.clLoginBtnBgColor = array[0];
    weekSongBad.clLoginBtnCornerRadius = @(2);
    
    
    weekSongBad.clCheckBoxSize = [NSValue valueWithCGSize:CGSizeMake(0, 0)];
    weekSongBad.clCheckBoxValue = @(YES);
    
    
    weekSongBad.clAppPrivacyNormalDesTextFirst = array[5];
    weekSongBad.clAppPrivacyFirst = @[array[6], array[1]];
    weekSongBad.clAppPrivacyNormalDesTextSecond = array[7];
    weekSongBad.clAppPrivacyNormalDesTextLast = @"";
    weekSongBad.clAppPrivacyTextFont = [UIFont systemFontOfSize:12];
    weekSongBad.clAppPrivacyTextAlignment = @(NSTextAlignmentLeft);
    weekSongBad.clAppPrivacyWebNavigationBarTintColor = UIColor.whiteColor;
    weekSongBad.clAppPrivacyPunctuationMarks = @(YES);
    weekSongBad.clAppPrivacyColor = @[UIColor.darkGrayColor,array[0]];;
    weekSongBad.clAuthTypeUseWindow = @(YES);
    weekSongBad.clPrivacyShowUnderline = @(YES);
    weekSongBad.clAppPrivacyLineSpacing = @(2.5);
    weekSongBad.clAuthWindowModalTransitionStyle = @(UIModalTransitionStyleCrossDissolve);
    
    weekSongBad.clAppPrivacyWebBackBtnImage = array[2];
    
    
    weekSongBad.clLoadingSize = [NSValue valueWithCGSize:CGSizeMake(90, 90)];
    weekSongBad.clLoadingCornerRadius = @(2);
    weekSongBad.clLoadingIndicatorStyle = @(UIActivityIndicatorViewStyleLarge);
    weekSongBad.clLoadingTintColor = UIColor.blackColor;
    weekSongBad.clLoadingBackgroundColor = UIColor.clearColor;
    
    
    CLOrientationLayOut *herFreezingLingerClearedCheck = [[CLOrientationLayOut alloc] init];
    weekSongBad.clOrientationLayOutPortrait = herFreezingLingerClearedCheck;
    
    
    CGFloat y = (([UIScreen mainScreen].bounds.size.height - [array[3] CGSizeValue].height) * 0.5) + 35;
    CGFloat height = 30;
    herFreezingLingerClearedCheck.clLayoutPhoneTop = @(y);
    herFreezingLingerClearedCheck.clLayoutPhoneHeight = @(height);
    herFreezingLingerClearedCheck.clLayoutPhoneCenterX = @(0);
    
    
    y += (height + 20);
    height = 17;
    herFreezingLingerClearedCheck.clLayoutSloganTop = @(y);
    herFreezingLingerClearedCheck.clLayoutSloganCenterX = @(0);
    herFreezingLingerClearedCheck.clLayoutSloganHeight = @(height);

    
    y += (height + 20);
    height = 50;
    herFreezingLingerClearedCheck.clLayoutLoginBtnTop = @(y);
    herFreezingLingerClearedCheck.clLayoutLoginBtnWidth = @([array[3] CGSizeValue].width - 40);
    herFreezingLingerClearedCheck.clLayoutLoginBtnCenterX = @(0);
    herFreezingLingerClearedCheck.clLayoutLoginBtnHeight = @(height);

    
    y += (height + 15);
    herFreezingLingerClearedCheck.clLayoutAppPrivacyTop = @(y);
    herFreezingLingerClearedCheck.clLayoutAppPrivacyCenterX = @(0);
    herFreezingLingerClearedCheck.clLayoutAppPrivacyWidth = @([array[3] CGSizeValue].width - 40);
    
    
    weekSongBad.customAreaView = ^(UIView * _Nonnull customAreaView) {
        
        customAreaView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0];
        
        UIView *brushView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, [array[3] CGSizeValue].width, [array[3] CGSizeValue].height)];
        brushView.backgroundColor = UIColor.whiteColor;
        brushView.layer.cornerRadius = 2.0;
        [customAreaView addSubview:brushView];
        brushView.center = customAreaView.center;
        
        
        UIButton *close = [UIButton buttonWithType:UIButtonTypeCustom];
        [close addTarget:self action:@selector(mindfulTryEyeVirtualContainerBoldHandler:) forControlEvents:(UIControlEventTouchUpInside)];
        [close setBackgroundImage:array[2] forState:UIControlStateNormal];
        [brushView addSubview:close];
        close.frame = CGRectMake(8, 8, 20, 20);
    };
    
    return weekSongBad;
}

- (void)mindfulTryEyeVirtualContainerBoldHandler:(id)sender {
    [CLShanYanSDKManager finishAuthControllerCompletion:nil];
    self.mostlyDarkRedoAction(0);
}

@end
