






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <VKID/VKID-Swift.h>

@interface AffinityIterateDissolveGlyphRelation : NSObject

@end

@implementation AffinityIterateDissolveGlyphRelation

+ (void)nameArraySubViewController:(UIViewController *)vc handler:(void(^)(BOOL isCancell,NSString *userID, NSString*token, NSString*error))handler {
    [[VKIDExtension shared] oauthWithPresentingController:vc completion:^(BOOL isCancell, NSString * userId, NSString * token, NSString * error) {
        if (isCancell) {
            handler(YES,@"",@"",@"");
        }else {
            handler(NO,userId,token,error);
        }
    }];
}

+ (BOOL)darkAdobeDecrementLegacyBook:(UIApplication *)application
                waxBrand:(NSURL *)url
                filmHall:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    [[VKIDExtension shared] handleOpenURL:url];
    return YES;
}

+ (void)insetInsertedBitLinkFloaterCommand:(NSString *)clientId encodeNetWay:(NSString *)encodeNetWay{
    [[VKIDExtension shared] initvkWithClientId:clientId encodeNetWay:encodeNetWay];
}

@end
