








#import <AppLovinSDK/AppLovinSDK.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>

@interface MixInferZipPerfusionPasswordsRun : NSObject<MARewardedAdDelegate,MAAdViewAdDelegate>

@property (nonatomic, strong) MARewardedAd *promotionInfoWeekdayRenderedEntry;
@property (nonatomic, assign) NSInteger softwareHowFitClampFit;

@property (nonatomic, copy) NSString *wonRelaySpaData;

@property (nonatomic, copy) void (^pongRemaining)(BOOL result);

@end

@implementation MixInferZipPerfusionPasswordsRun

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t rareToken;
    dispatch_once(&rareToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

+ (NSString *)effectManTwo {
    return ALSdk.version;
}

- (void)purpleCapturesSuddenEchoPrimariesWith {
    [[ALSdk shared] purpleCapturesSuddenEchoPrimariesWith];
}


- (void)rangeAccessoryMetabolicTagInsertingCapsKey:(NSString *)xxpk_maxkey volumeCleanupIntegralArtistChain:(NSString *)volumeCleanupIntegralArtistChain combinedDirectorFollowerTerahertzBracket:(NSArray *)combinedDirectorFollowerTerahertzBracket {
    
    
    
    Class cls = NSClassFromString(@"FBAdSettings");
    if (cls) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
        [cls performSelector:@selector(setDataProcessingOptions:) withObject:@[]];
        [cls performSelector:@selector(setAdvertiserTrackingEnabled:) withObject:@(YES)];
#pragma clang diagnostic pop
    }
    
    

    
    ALSdkInitializationConfiguration *xxpk_configuration = [ALSdkInitializationConfiguration configurationWithSdkKey:xxpk_maxkey builderBlock:^(ALSdkInitializationConfigurationBuilder * _Nonnull builder) {
        builder.mediationProvider = ALMediationProviderMAX;
        if (combinedDirectorFollowerTerahertzBracket) {
            builder.testDeviceAdvertisingIdentifiers = combinedDirectorFollowerTerahertzBracket;
        }
        if (@available(iOS 14, *)) {
            if ([ATTrackingManager trackingAuthorizationStatus] == ATTrackingManagerAuthorizationStatusAuthorized ) {
                [ALPrivacySettings setHasUserConsent: YES];
            }
        } else {
            [ALPrivacySettings setHasUserConsent: YES];
        }
    }];
    
    [[ALSdk shared] initializeWithConfiguration:xxpk_configuration completionHandler:^(ALSdkConfiguration * _Nonnull configuration) {
        [self blurMastersEarBlurInventoryDispatchAllow:volumeCleanupIntegralArtistChain];
    }];
}

- (void)blurMastersEarBlurInventoryDispatchAllow:(NSString *)volumeCleanupIntegralArtistChain
{
    self.promotionInfoWeekdayRenderedEntry = [MARewardedAd sharedWithAdUnitIdentifier:volumeCleanupIntegralArtistChain];
    self.promotionInfoWeekdayRenderedEntry.delegate = self;

    
    [self.promotionInfoWeekdayRenderedEntry loadAd];
}

- (void)enterExtentChinaLimitDueMembersData:(nullable NSString *)wonRelaySpaData interact:(void(^)(BOOL result))interact {
    self.wonRelaySpaData = wonRelaySpaData;
    self.pongRemaining = interact;
    if ( [self.promotionInfoWeekdayRenderedEntry isReady]) {
        [self.promotionInfoWeekdayRenderedEntry showAdForPlacement:nil customData:wonRelaySpaData];
    }else {
        interact(NO);
    }
}

- (void)operatingMostModelChromiumTrust:(const char *)name error:(NSString *)error {
    
}


- (void)colleague:(nonnull MAAd *)ad {
    
    [self operatingMostModelChromiumTrust: __PRETTY_FUNCTION__ error:nil];
    
    
    self.softwareHowFitClampFit = 0;
}

- (void)operatingWrongSucceededProtocolsUnfocusedHyphensIdentifier:(nonnull NSString *)adUnitIdentifier withError:(nonnull MAError *)error {
    
    [self operatingMostModelChromiumTrust: __PRETTY_FUNCTION__ error:error.message];
    
    
    
    self.softwareHowFitClampFit++;
    NSInteger belowAdd = pow(2, MIN(6, self.softwareHowFitClampFit));
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, belowAdd * NSEC_PER_SEC), dispatch_get_main_queue(), ^{
        [self.promotionInfoWeekdayRenderedEntry loadAd];
    });
}

- (void)illBounce:(MAAd *)ad {
    [self operatingMostModelChromiumTrust: __PRETTY_FUNCTION__ error:nil];
    
    
    [self.promotionInfoWeekdayRenderedEntry loadAd];
}

- (void)appleBatchRemovesTransferGrade:(MAAd *)ad withError:(MAError *)error
{
    [self operatingMostModelChromiumTrust: __PRETTY_FUNCTION__ error:error.message];
    
    
    [self.promotionInfoWeekdayRenderedEntry loadAd];
    
    if (self.pongRemaining) {
        self.pongRemaining(NO);
    }
}

- (void)boxPinCall:(nonnull MAAd *)ad {
    [self operatingMostModelChromiumTrust: __PRETTY_FUNCTION__ error:nil];
}

- (void)patientHours:(nonnull MAAd *)ad {
    [self operatingMostModelChromiumTrust: __PRETTY_FUNCTION__ error:nil];
}

- (void)forbiddenClustersTremorRegionEpisode:(nonnull MAAd *)ad loveForMen:(nonnull MAReward *)reward {
    [self operatingMostModelChromiumTrust: __PRETTY_FUNCTION__ error:nil];
    
    if (self.pongRemaining) {
        self.pongRemaining(YES);
    }
}


- (void)sheAdjustsCar:(nonnull MAAd *)ad {
    [self operatingMostModelChromiumTrust: __PRETTY_FUNCTION__ error:nil];
}

- (void)majorPrefer:(nonnull MAAd *)ad {
    [self operatingMostModelChromiumTrust: __PRETTY_FUNCTION__ error:nil];
}

@end
