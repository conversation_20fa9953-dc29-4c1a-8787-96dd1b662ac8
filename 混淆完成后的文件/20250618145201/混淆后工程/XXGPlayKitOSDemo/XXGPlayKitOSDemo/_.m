/*********************************
 *********************************
 ***当前文件为Demo界面设置，请忽略
 *********************************
 *********************************
 **/





























































































































































































































































































































#import "_.h"
#import <GrossScrap/GrossScrap.h>

@implementation ViewController (Demo)

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return UIInterfaceOrientationMaskPortrait;
}
- (BOOL)prefersHomeIndicatorAutoHidden {
    return NO;
}
- (UIRectEdge)preferredScreenEdgesDeferringSystemGestures {
    return UIRectEdgeAll;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.demo_logs = [NSMutableArray new];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(cubeTenDownloadHourOceanScene) name:SayUseTurn.TreeEstonianDelayEditorSupportedEmptyDeliveryKurdish object:nil];
    self.demo_sdk_version.text = [NSString stringWithFormat:@"SDK VERSION：%@",SayUseTurn.effectManTwo];
    [self pointerDiscardedAgeOddCutterResume];
    [self oneExpectingWasEarOutcomeExecutingLanguage:self.view.subviews];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self touchesConvergedOverwriteNotifiedComposedAnimation];
}

- (void)pointerDiscardedAgeOddCutterResume {
    UIStoryboard *worldSigmoidFilmSubsetGreen = [UIStoryboard storyboardWithName:@"LaunchScreen" bundle:nil];
    UIViewController *noiseCan = [worldSigmoidFilmSubsetGreen instantiateInitialViewController];
    UIView *tooNotView = noiseCan.view;
    tooNotView.frame = self.view.bounds;
    tooNotView.tag = 99;
    [self.view addSubview:tooNotView];
    self.demo_logo.hidden = YES;
}

- (void)touchesConvergedOverwriteNotifiedComposedAnimation {
    UIView *tooNotView = [self.view viewWithTag:99];
    UIView *logoView = [tooNotView viewWithTag:100];
    CGPoint originCenter = [self.view convertPoint:self.demo_logo.center
                                         fromView:self.demo_logo.superview];
    UIView *movingLogo = [logoView snapshotViewAfterScreenUpdates:YES];
    movingLogo.frame = [self.view convertRect:logoView.frame fromView:logoView.superview];
    [self.view addSubview:movingLogo];
    logoView.hidden = YES;
    self.demo_login_status.hidden = YES;
    // 用dispatch_after实现真正的延迟
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:1.1
                              delay:0
                            options:UIViewAnimationOptionCurveEaseInOut
                         animations:^{
            tooNotView.alpha = 0;
            movingLogo.center = originCenter;
            movingLogo.transform = CGAffineTransformMakeScale(self.demo_logo.bounds.size.width/movingLogo.bounds.size.width,
                                                             self.demo_logo.bounds.size.height/movingLogo.bounds.size.height);
        } completion:^(BOOL finished) {
            [UIView animateWithDuration:0.5 animations:^{
                self.demo_logo.hidden = NO;
                self.demo_login_status.hidden = NO;
            }];
            [tooNotView removeFromSuperview];
            [movingLogo removeFromSuperview];
            [self.demo_logo.layer addAnimation:[self optIcyAnimation] forKey:nil];
            // 动画结束后依次显示按钮
            [self malayChlorideResponseBurnJobChromeThumb:self.view.subviews];
        }];
    });
}

- (CABasicAnimation *)optIcyAnimation {
    CABasicAnimation *loud = [CABasicAnimation animationWithKeyPath:@"opacity"];
    loud.fromValue = @0;
    loud.toValue = @1;
    loud.duration = 0.3;
    loud.removedOnCompletion = NO;
    loud.fillMode = kCAFillModeForwards;
    return loud;
}

- (void)cubeTenDownloadHourOceanScene {
    switch (SayUseTurn.willPostKitStatus) {
        case RenameCityLearnedRadiansRectifiedNordic:
            self.demo_login_status.text = @"○ 未登录";
            self.demo_login_status.textColor = UIColor.grayColor;
            break;
        case BirthDiscardEntitiesPinWaxSubscribeTexture:
            self.demo_login_status.text = @"○ 准备登录";
            self.demo_login_status.textColor = UIColor.yellowColor;
            break;
        case HandMusicProtocolsDuctilityNegateExpertExecutor:
            self.demo_login_status.text = @"○ 登录中...";
            self.demo_login_status.textColor = UIColor.blueColor;
            break;
        case PenBitFeetPatternHexVideo:
            self.demo_login_status.text = @"● 已登录";
            self.demo_login_status.textColor = UIColor.greenColor;
            break;
    }
}

- (void)oneExpectingWasEarOutcomeExecutingLanguage:(NSArray *)subviews {
    for (UIView *subview in subviews) {
        [self oneExpectingWasEarOutcomeExecutingLanguage:subview.subviews];
        if ([subview isKindOfClass:[UIButton class]]) {
            subview.layer.cornerRadius = 5.0;
            subview.layer.shadowColor = [UIColor lightGrayColor].CGColor;
            subview.layer.shadowOffset = CGSizeMake(0, 3);
            subview.layer.shadowOpacity = 0.5;
            subview.layer.shadowRadius = 4.0;
            subview.clipsToBounds = NO;
            subview.alpha = 0;
        }
   }
}

- (void)malayChlorideResponseBurnJobChromeThumb:(NSArray *)subviews {
    static NSTimeInterval discovery = 0.08;
    static NSTimeInterval caretFireWay = 0.25;
    __block NSInteger hasIndex = 0;
    for (UIView *subview in subviews) {
        [self malayChlorideResponseBurnJobChromeThumb:subview.subviews];
        if ([subview isKindOfClass:[UIButton class]]) {
            subview.alpha = 0;
            [UIView animateWithDuration:caretFireWay
                                  delay:hasIndex * discovery
                                options:UIViewAnimationOptionCurveEaseInOut
                             animations:^{
                subview.alpha = 1;
            } completion:nil];
            hasIndex++;
        }
    }
}

- (void)demo_log:(NSString *)logMessage {
    NSLog(@"%@",logMessage);
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.demo_logs addObject:logMessage];
        NSIndexPath *newIndexPath = [NSIndexPath indexPathForRow:self.demo_logs.count - 1 inSection:0];
        [self.demo_tableView insertRowsAtIndexPaths:@[newIndexPath] withRowAnimation:UITableViewRowAnimationAutomatic];
        [self.demo_tableView scrollToRowAtIndexPath:newIndexPath
                             atScrollPosition:UITableViewScrollPositionBottom
                                     animated:YES];
    });
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.demo_logs.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"LogCell" forIndexPath:indexPath];
    cell.textLabel.text = self.demo_logs[indexPath.row];
    cell.textLabel.numberOfLines = 0;
    return cell;
}
@end
