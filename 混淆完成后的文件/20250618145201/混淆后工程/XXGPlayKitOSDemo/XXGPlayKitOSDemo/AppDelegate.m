/*********************************
 *********************************
 ***当前SDK接口对接演示，请结合文档对接
 *********************************
 *********************************
 **/
#import "AppDelegate.h"
#import <GrossScrap/GrossScrap.h>

@interface AppDelegate ()

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {

    // MARK: - 启动（必接）
    // 如果您的应用程序使用UIScene ，请从UISceneDelegate实现以下方法，这里无需实现
    [FillerBus subtitlesSayAwakeDiscountIndigoUsesOptions:launchOptions anyBuildOptions:nil];
    
    return YES;
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(nonnull NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    // MARK: - openURL（必接）
    // 如果您的应用程序使用UIScene ，请从UISceneDelegate实现以下方法，这里无需实现
    [FillerBus homeSuitableSidebarCellularCollapseWith:url filmHall:options beginCurlFor:nil];
    return YES;
}

#pragma mark - UISceneSession lifecycle

- (UISceneConfiguration *)application:(UIApplication *)application configurationForConnectingSceneSession:(UISceneSession *)connectingSceneSession options:(UISceneConnectionOptions *)options {
    // Called when a new scene session is being created.
    // Use this method to select a configuration to create the new scene with.
    return [[UISceneConfiguration alloc] initWithName:@"Default Configuration" sessionRole:connectingSceneSession.role];
}

- (void)application:(UIApplication *)application didDiscardSceneSessions:(NSSet<UISceneSession *> *)sceneSessions {
    // Called when the user discards a scene session.
    // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
    // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
}

@end
