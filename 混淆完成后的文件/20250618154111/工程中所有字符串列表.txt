WebView事件-%@
%@-%@
%ld
%.0f
XXGPlayKitOS
[a-zA-z]+://[^\\s]*
SELF MATCHES %@
%@%@
 
%@(%ld)
%@ %@
TW
 %@ 
 %@
[%ld]
%@ (Array)
Delete
HH:mm
dd
%ld%@
MM-dd HH:mm
yyyy-MM-dd HH:mm
CFBundleIcons.CFBundlePrimaryIcon.CFBundleIconFiles
CFBundleIdentifier
CFBundleShortVersionString
CFBundleDisplayName
CFBundleName
[MQTTSSLSecurityPolicyTransport] open
MQTT
Fail to init ssl input stream!
Fail to init ssl output stream!
[MQTTCFSocketEncoder] setState %ld/%ld
[MQTTCFSocketEncoder] NSStreamEventOpenCompleted
[MQTTCFSocketEncoder] NSStreamEventHasBytesAvailable
[MQTTCFSocketEncoder] NSStreamEventHasSpaceAvailable
[MQTTCFSocketEncoder] NSStreamEventEndEncountered
[MQTTCFSocketEncoder] NSStreamEventErrorOccurred
[MQTTCFSocketEncoder] not MQTTCFSocketEncoderStateReady
[MQTTCFSocketEncoder] buffer to write (%lu)=%@...
[MQTTCFSocketEncoder] streamError: %@
[MQTTCFSocketEncoder] buffer partially written: %ld
MQTTSessionManager connectTo:%@
[MQTTSessionManager] reconnecting
[MQTTSessionManager] connecting
connected
connection refused
connection closed
connection error
protocoll error
connection closed by broker
[MQTTSessionManager] eventCode: %@ (%ld) %@
MQTTSessionManager internalSubscriptions: %@
localhost
[MQTTCFSocketTransport] open
[MQTTCFSocketTransport] close
[MQTTCFSocketTransport] no p12 path given
[MQTTCFSocketTransport] reading p12 failed
[MQTTCFSocketTransport] no passphrase given
[MQTTCFSocketTransport] Error while importing pkcs12 [%d]
[MQTTCFSocketTransport] could not CFArrayGetValueAtIndex
[MQTTCFSocketTransport] could not CFDictionaryGetValue
[MQTTCFSocketTransport] SecIdentityCopyCertificate failed [%d]
MQIsdp
[MQTTMessage] wireFormat(%lu)=%@...
[MQTTMessage] message data incomplete remaining length
[MQTTMessage] message data too long remaining length
[MQTTMessage] missing packet identifier
[MQTTMessage] unexpected payload after packet identifier
[MQTTMessage] no returncode
[MQTTMessage] unexpected payload
[MQTTMessage] missing connect variable header
[MQTTMessage] missing connack variable header
[MQTTMessage] missing subscribe variable header
[MQTTMessage] missing suback variable header
[MQTTMessage] missing unsubscribe variable header
[MQTTMessage] illegal header flags
[MQTTMessage] remaining data wrong length
[MQTTMessage] illegal message type
[MQTTMessage] message data length < 2
[MQTTDecoder] #streams=%lu
[MQTTDecoder] NSStreamEventOpenCompleted
[MQTTDecoder] NSStreamEventHasBytesAvailable
[MQTTDecoder] fixedHeader=0x%02x
[MQTTDecoder] digit=0x%02x 0x%02x %d %d
[MQTTDecoder] remainingLength=%d
[MQTTDecoder] read %ld %ld
[MQTTDecoder] received (%lu)=%@...
[MQTTDecoder] oops received (%lu)=%@...
[MQTTDecoder] NSStreamEventHasSpaceAvailable
[MQTTDecoder] NSStreamEventEndEncountered
[MQTTDecoder] NSStreamEventErrorOccurred
MQTTTransport is abstract class
cer
.
pinnedCertificates
[MQTTCFSocketDecoder] NSStreamEventHasBytesAvailable
Unable to apply security policy, the SSL connection is insecure!
[MQTTCoreDataPersistence] deleteAllFlowsForClientId %@
[MQTTPersistence] pre-sync: i%lu u%lu d%lu
[MQTTPersistence] sync error %@
[MQTTPersistence] sync not complete
[MQTTPersistence] postsync: i%lu u%lu d%lu
MQTTFlow
clientId = %@ and incomingFlag = %@
deadline
[MQTTPersistence] allFlowsforClientId %@
flowforClientId requestingPerform
flowforClientId performed
flowforClientId performing
clientId = %@ and incomingFlag = %@ and messageId = %@
[MQTTPersistence] flowForClientId %@
clientId
NSString
incomingFlag
NSNumber
retainedFlag
commandType
qosLevel
messageId
topic
data
NSData
NSDate
MQTTClient
[MQTTPersistence] Persistent store: %@
[MQTTPersistence] managedObjectContext save: %@
[MQTTPersistence] sizes %llu/%llu
[MQTTInMemoryPersistence] deleteAllFlowsForClientId %@
self
[MQTTSession] init
MQTTClient%.0f
topicFilter array in SUBSCRIBE or UNSUBSRIBE must not be empty
%@
topicFilter must be at least 1 characters long
topicFilter may not be longer than 65535 bytes in UTF8 representation
topicFilter length = %lu
topicFilter must not contain non-UTF8 characters
topicFilter = %@
/
+
singlelevel wildcard must be alone on a level of a topic filter
#
multilevel wildcard must be on the last level of a topic filter
multilevel wildcard must be alone on a level of a topic filter
multilevel wildcard must alone on the last level of a topic filter
[MQTTSession] subscribeToTopics:%@]
Illegal QoS level
%d is not 0, 1, or 2
[MQTTSession] unsubscribeTopics:%@
[MQTTSession] publishData:%@... onTopic:%@ retain:%d qos:%ld publishHandler:%p
topic must not be nil
topic must not at least 1 character long
topic may not be longer than 65535 bytes in UTF8 representation
topic length = %lu
topic must not contain non-UTF8 characters
topic = %@
willTopic must not contain wildcards
willTopic = %@
Encoder not ready
[MQTTSession] dropping outgoing message %d
Dropping outgoing Message
[MQTTSession] PUBLISH %d
[MQTTSession] queueing message %d after unsuccessfull attempt
[MQTTSession] queueing message %d
[MQTTSession] closeWithDisconnectHandler:%p 
[MQTTSession] sending DISCONNECT
[MQTTSession] closeInternal
No response
[MQTTSession] keepAlive %@ @%.0f
[MQTTSession] %@ flow %@ %@ %@
[MQTTSession] PUBLISH queued message %@
[MQTTSession] resend PUBLISH %@
[MQTTSession] resend PUBREL %@
MQTTDecoderEventProtocolError
MQTTDecoderEventConnectionClosed
MQTTDecoderEventConnectionError
[MQTTSession] decoder handleEvent: %@ (%d) %@
[MQTTSession] MQTT illegal message received
MQTT illegal message received
MQTT protocol CONNACK expected
unknown
MQTT CONNACK: unacceptable protocol version
MQTT CONNACK: identifier rejected
MQTT CONNACK: server unavailable
MQTT CONNACK: bad user name or password
MQTT CONNACK: not authorized
MQTT CONNACK: reserved for future use
MQTT protocol DISCONNECT instead of CONNACK
MQTT protocol no CONNACK
MQTT protocol DISCONNECT received
[MQTTSession] other state
non UTF8 topic %@
[MQTTSession] dropping incoming messages
Server has closed connection without connack.
nextMsgId synchronizing
nextMsgId synchronized
nextMsgId synchronized done
clientId must be at least 1 character long if cleanSessionFlag is off
clientId length = %lu
clientId must not be nil
clientId may not be longer than 65535 bytes in UTF8 representation
clientId must not contain non-UTF8 characters
clientId = %@
userName may not be longer than 65535 bytes in UTF8 representation
userName length = %lu
userName must not contain non-UTF8 characters
userName = %@
password specified without userName
password = %@
Illegal protocolLevel
%d is not 3, 4, or 5
Will topic must be nil if willFlag is false
Will message must be nil if willFlag is false
Will retain must be false if willFlag is false
%d
Will QoS Level must be 0 if willFlag is false
Illegal will QoS level
Will topic must not be nil if willFlag is true
Will topic must be at least 1 character long
willTopic may not be longer than 65535 bytes in UTF8 representation
willTopic length = %lu
willTopic must not contain non-UTF8 characters
Will message must not be nil if willFlag is true
[MQTTSession] connecting
[MQTTSession] connectWithConnectHandler:%p
[MQTTSession] mqttTransport send
[MQTTSession] trying to send message without wire format
[MQTTSession] trying to send nil message
[MQTTSession] mqttTransport didReceiveMessage
[MQTTSession] mqttTransport mqttTransportDidClose
[MQTTSession] mqttTransportDidOpen
[MQTTSession] sending CONNECT
[MQTTSession] mqttTransport didFailWithError %@
[MQTTCFSocketDecoder] NSStreamEventOpenCompleted
[MQTTCFSocketDecoder] received (%lu)=%@...
[MQTTCFSocketDecoder] NSStreamEventHasSpaceAvailable
[MQTTCFSocketDecoder] NSStreamEventEndEncountered
[MQTTCFSocketDecoder] NSStreamEventErrorOccurred
[MQTTSessionLegacy] initWithClientId:%@ 
[MQTTSessionSynchron] waiting for connect
[MQTTSessionSynchron] end connect
[MQTTSessionSynchron] waiting for suback %d
[MQTTSessionSynchron] end subscribe
[MQTTSessionSynchron] waiting for unsuback %d
[MQTTSessionSynchron] end unsubscribe
[MQTTSessionSynchron] waiting for mid %d
[MQTTSessionSynchron] end publish
[MQTTSessionSynchron] waiting for close
[MQTTSessionSynchron] end close
%02X
%02x
Color value %@ is invalid. It should be a hex value of the form #RBG, #ARGB, #RRGGBB, or #AARRGGBB
BOOL
yes
true
NSInteger
int
double
float
@\"
\"
!*'();:@&=+$,/?%#[]
&
=
NetworkCore
Data processing failed
The data is empty.
HTTPError，code: %ld
none
wifi
cellular
other
==
>=
<=
top
left
bottom
right
leading
trailing
width
height
centerX
centerY
baseline
firstBaseline
lastBaseline
leftMargin
rightMargin
topMargin
bottomMargin
leadingMargin
trailingMargin
centerXWithinMargins
centerYWithinMargins
high
low
medium
required
fitting size
drag can resize window
window size stay put
drag cannot resize window
%@:%@
%@:%p
<
.%@
 * %g
 %g
 %@ %g
-
 ^%@
>
Cannot attach mas_key to %@
Could not find constraint %@
%@[%d]
You didn't pass any attribute to make.attributes(...)
All objects in the array must be views
views to distribute need to bigger than one
Can't constrain views that do not share a common superview. Make sure that all the views in this array have been added into the same view hierarchy.
You must override %@ in a subclass.
MASConstraint is an abstract class, you should not instantiate it directly.
expected an NSValue offset, got: %@
attempting to set layout constant with unsupported value: %@
attempting to add unsupported attribute: %@
Cannot modify constraint multiplier after it has been installed
Cannot modify constraint priority after it has been installed
Redefinition of constraint relation
Attributes should be chained before defining the constraint relation
couldn't find a common superview for %@ and %@
bundle
json
inviteeCovariantEscapingAppleToneKilograms
,
%@\n%@
%lu
{}
{%@}
{\n
%@%@: %@\n
%@}
[]
[%@]
, 
[\n
%@[%ld]: %@\n
%@]
\"%@\"
%@\n%@%@
%@ %ld\n
%@ %@\n
%@\n
%s
(
()
\n
[IAP]:
.account
.service
{HEICS}
LoopCount
DelayTime
UnclampedDelayTime
decodeFirstFrameOnly
decodeScaleFactor
decodePreserveAspectRatio
decodeThumbnailPixelSize
decodeFileExtensionHint
decodeTypeIdentifierHint
decodeUseLazyDecoding
decodeScaleDownLimitBytes
decodeToHDR
encodeToHDR
encodeFirstFrameOnly
encodeCompressionQuality
encodeBackgroundColor
encodeMaxPixelSize
encodeMaxFileSize
encodeEmbedThumbnail
webImageContext
unexpected policy %tu
If you mean to prefetch the image, use -[SDWebImagePrefetcher prefetchURLs] instead
Image url is blacklisted
Image url is nil
Operation cancelled by user during querying the cache
Operation cancelled by user during sending the request
kCGImageDestinationRequestedFileSize
kCGImageDestinationEncodeRequest
kCGImageDestinationEncodeToSDR
kCGImageDestinationEncodeToISOHDR
kCGImageDestinationEncodeToISOGainmap
iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAAA1BMVEUyMjKlMgnVAAAAAXRSTlMyiDGJ5gAAAApJREFUCNdjYAAAAAIAAeIhvDMAAAAASUVORK5CYII=
, @"Detected the current OS's ImageIO PNG Decoder is buggy on indexed color PNG. Perform workaround solution...
For `SDImageIOAnimatedCoder` subclass, you must override %@ method
Animated Coder created CGImageRef should not retain CGImageSourceRef, which may cause thread-safe issue without lock
@[0-9]+\\.?[0-9]*x$
{WebP}
CIStraightenFilter
CIMultiplyBlendMode
CIScreenBlendMode
CIOverlayBlendMode
CIDarkenBlendMode
CILightenBlendMode
CIColorDodgeBlendMode
CIColorBurnBlendMode
CISoftLightBlendMode
CIHardLightBlendMode
CIDifferenceBlendMode
CIExclusionBlendMode
CIHueBlendMode
CISaturationBlendMode
CIColorBlendMode
CILuminosityBlendMode
CISourceAtopCompositing
CISourceInCompositing
CISourceOutCompositing
CISourceOverCompositing
CIAdditionCompositing
CIColorClamp
CIColorControls
, pixelFormat, bitsPerComponent, imageRef);
            CGImageRelease(imageRef);
            return nil;
        }
    }
    
    
    CGDataProviderRef provider = CGImageGetDataProvider(imageRef);
    if (!provider) {
        CGImageRelease(imageRef);
        return nil;
    }
    CFDataRef data = CGDataProviderCopyData(provider);
    if (!data) {
        CGImageRelease(imageRef);
        return nil;
    }
    
    
    size_t bytesPerRow = CGImageGetBytesPerRow(imageRef);
    size_t components = CGImageGetBitsPerPixel(imageRef) / bitsPerComponent;
    
    CFRange range = CFRangeMake(bytesPerRow * y + components * x, components);
    if (CFDataGetLength(data) < range.location + range.length) {
        CFRelease(data);
        CGImageRelease(imageRef);
        return nil;
    }
    
    CGColorSpaceRef colorSpace = CGImageGetColorSpace(imageRef);
    
    
    if (components == 2) {
        Pixel_88 pixel = {0};
        CFDataGetBytes(data, range, pixel);
        CFRelease(data);
        CGImageRelease(imageRef);
        
        return SDGetColorFromGrayscale(pixel, bitmapInfo, colorSpace);
    } else if (components == 3 || components == 4) {
        
        Pixel_8888 pixel = {0};
        CFDataGetBytes(data, range, pixel);
        CFRelease(data);
        CGImageRelease(imageRef);
        
        return SDGetColorFromRGBA8(pixel, bitmapInfo, colorSpace);
    } else {
        SD_LOG("Unsupported components: %zu for CGImage: %@
, pixelFormat, bitsPerComponent, imageRef);
            CGImageRelease(imageRef);
            return nil;
        }
    }
    
    
    CGDataProviderRef provider = CGImageGetDataProvider(imageRef);
    if (!provider) {
        CGImageRelease(imageRef);
        return nil;
    }
    CFDataRef data = CGDataProviderCopyData(provider);
    if (!data) {
        CGImageRelease(imageRef);
        return nil;
    }
    
    
    size_t bytesPerRow = CGImageGetBytesPerRow(imageRef);
    size_t components = CGImageGetBitsPerPixel(imageRef) / bitsPerComponent;
    
    size_t start = bytesPerRow * CGRectGetMinY(rect) + components * CGRectGetMinX(rect);
    size_t end = bytesPerRow * (CGRectGetMaxY(rect) - 1) + components * CGRectGetMaxX(rect);
    if (CFDataGetLength(data) < (CFIndex)end) {
        CFRelease(data);
        CGImageRelease(imageRef);
        return nil;
    }
    
    const UInt8 *pixels = CFDataGetBytePtr(data);
    size_t row = CGRectGetMinY(rect);
    size_t col = CGRectGetMaxX(rect);
    
    
    NSMutableArray<UIColor *> *colors = [NSMutableArray arrayWithCapacity:CGRectGetWidth(rect) * CGRectGetHeight(rect)];
    
    CGColorSpaceRef colorSpace = CGImageGetColorSpace(imageRef);
    for (size_t index = start; index < end; index += components) {
        if (index >= row * bytesPerRow + col * components) {
            
            row++;
            index = row * bytesPerRow + CGRectGetMinX(rect) * components;
            index -= components;
            continue;
        }
        UIColor *color;
        if (components == 2) {
            Pixel_88 pixel = {pixels[index], pixel[index+1]};
            color = SDGetColorFromGrayscale(pixel, bitmapInfo, colorSpace);
        } else {
            if (components == 3) {
                Pixel_8888 pixel = {pixels[index], pixels[index+1], pixels[index+2], 0};
                color = SDGetColorFromRGBA8(pixel, bitmapInfo, colorSpace);
            } else if (components == 4) {
                Pixel_8888 pixel = {pixels[index], pixels[index+1], pixels[index+2], pixels[index+3]};
                color = SDGetColorFromRGBA8(pixel, bitmapInfo, colorSpace);
            } else {
                SD_LOG("Unsupported components: %zu for CGImage: %@
CIGaussianBlur
, err, self);
        return nil;
    }
    err = vImageBuffer_Init(&scratch, effect.height, effect.width, format.bitsPerPixel, kvImageNoFlags);
    if (err != kvImageNoError) {
        SD_LOG("UIImage+Transform error: vImageBuffer_Init returned error code %zi for inputImage: %@
SDWebImageDownloadStartNotification
SDWebImageDownloadReceiveResponseNotification
SDWebImageDownloadStopNotification
SDWebImageDownloadFinishNotification
SDNetworkActivityIndicator
sharedActivityIndicator
startActivity
stopActivity
com.hackemist.SDWebImageDownloader.downloadQueue
%@/%@ (%@; visionOS %@; Scale/%0.2f)
%@/%@ (%@; iOS %@; Scale/%0.2f)
%@/%@ (%@; watchOS %@; Scale/%0.2f)
%@/%@ (Mac OS X %@)
Any-Latin; Latin-ASCII; [:^ASCII:] Remove
User-Agent
Accept
image/*,*/*;q=0.8
Downloader operation is nil
loaderCachedImage
HTTP/1.1
SDWebImageErrorDomain
SDWebImageErrorDownloadResponseKey
SDWebImageErrorDownloadStatusCodeKey
SDWebImageErrorDownloadContentTypeKey
Operation should not be nil, [SDWebImageManager loadImageWithURL:options:context:progress:completed:] break prefetch logic
N8lZxRgC7lfdRS3dRLn+Ag
The sample alpha image (1x1 pixels) returns nil, OS bug ?
The sample non-alpha image (1x1 pixels) returns nil, OS bug ?
)
IP
DP
com.hackemist.SDDiskCache
Use `initWithCachePath:` with the disk cache path
(null)
\0:
%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%@
Custom downloader operation class must subclass NSOperation and conform to `SDWebImageDownloaderOperation` protocol
-Thumbnail(
com.hackemist.SDImageCache
default
Cache namespace should not be nil
The IO queue should not be nil. Your configured `ioQueueAttributes` may be wrong
Custom memory cache class must conform to `SDMemoryCache` protocol
Custom disk cache class must conform to `SDDiskCache` protocol
com.hackemist.SDWebImageCache.default
, error);
        }
    } else {
        @try {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations
, exception);
        }
    }
    if (extendedData) {
        [self.diskCache setExtendedData:extendedData forKey:key];
    }
}

- (void)storeImageToMemory:(UIImage *)image forKey:(NSString *)key {
    if (!image || !key) {
        return;
    }
    NSUInteger cost = image.sd_memoryCost;
    [self.memoryCache setObject:image forKey:key cost:cost];
}

- (void)storeImageDataToDisk:(nullable NSData *)imageData
                      forKey:(nullable NSString *)key {
    if (!imageData || !key) {
        return;
    }
    
    dispatch_sync(self.ioQueue, ^{
        [self _storeImageDataToDisk:imageData forKey:key];
    });
}


- (void)_storeImageDataToDisk:(nullable NSData *)imageData forKey:(nullable NSString *)key {
    if (!imageData || !key) {
        return;
    }
    
    [self.diskCache setData:imageData forKey:key];
}



- (void)diskImageExistsWithKey:(nullable NSString *)key completion:(nullable SDImageCacheCheckCompletionBlock)completionBlock {
    dispatch_async(self.ioQueue, ^{
        BOOL exists = [self _diskImageDataExistsWithKey:key];
        if (completionBlock) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completionBlock(exists);
            });
        }
    });
}

- (BOOL)diskImageDataExistsWithKey:(nullable NSString *)key {
    if (!key) {
        return NO;
    }
    
    __block BOOL exists = NO;
    dispatch_sync(self.ioQueue, ^{
        exists = [self _diskImageDataExistsWithKey:key];
    });
    
    return exists;
}


- (BOOL)_diskImageDataExistsWithKey:(nullable NSString *)key {
    if (!key) {
        return NO;
    }
    
    return [self.diskCache containsDataForKey:key];
}

- (void)diskImageDataQueryForKey:(NSString *)key completion:(SDImageCacheQueryDataCompletionBlock)completionBlock {
    dispatch_async(self.ioQueue, ^{
        NSData *imageData = [self diskImageDataBySearchingAllPathsForKey:key];
        if (completionBlock) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completionBlock(imageData);
            });
        }
    });
}

- (nullable NSData *)diskImageDataForKey:(nullable NSString *)key {
    if (!key) {
        return nil;
    }
    __block NSData *imageData = nil;
    dispatch_sync(self.ioQueue, ^{
        imageData = [self diskImageDataBySearchingAllPathsForKey:key];
    });
    
    return imageData;
}

- (nullable UIImage *)imageFromMemoryCacheForKey:(nullable NSString *)key {
    return [self.memoryCache objectForKey:key];
}

- (nullable UIImage *)imageFromDiskCacheForKey:(nullable NSString *)key {
    return [self imageFromDiskCacheForKey:key options:0 context:nil];
}

- (nullable UIImage *)imageFromDiskCacheForKey:(nullable NSString *)key options:(SDImageCacheOptions)options context:(nullable SDWebImageContext *)context {
    if (!key) {
        return nil;
    }
    NSData *data = [self diskImageDataForKey:key];
    UIImage *diskImage = [self diskImageForKey:key data:data options:options context:context];
    
    BOOL shouldCacheToMemory = YES;
    if (context[SDWebImageContextStoreCacheType]) {
        SDImageCacheType cacheType = [context[SDWebImageContextStoreCacheType] integerValue];
        shouldCacheToMemory = (cacheType == SDImageCacheTypeAll || cacheType == SDImageCacheTypeMemory);
    }
    if (shouldCacheToMemory) {
        
        [self _syncDiskToMemoryWithImage:diskImage forKey:key];
    }

    return diskImage;
}

- (nullable UIImage *)imageFromCacheForKey:(nullable NSString *)key {
    return [self imageFromCacheForKey:key options:0 context:nil];
}

- (nullable UIImage *)imageFromCacheForKey:(nullable NSString *)key options:(SDImageCacheOptions)options context:(nullable SDWebImageContext *)context {
    
    UIImage *image = [self imageFromMemoryCacheForKey:key];
    if (image) {
        if (options & SDImageCacheDecodeFirstFrameOnly) {
            
            if (image.sd_imageFrameCount > 1) {
#if SD_MAC
                image = [[NSImage alloc] initWithCGImage:image.CGImage scale:image.scale orientation:kCGImagePropertyOrientationUp];
#else
                image = [[UIImage alloc] initWithCGImage:image.CGImage scale:image.scale orientation:image.imageOrientation];
#endif
            }
        } else if (options & SDImageCacheMatchAnimatedImageClass) {
            
            Class animatedImageClass = image.class;
            Class desiredImageClass = context[SDWebImageContextAnimatedImageClass];
            if (desiredImageClass && ![animatedImageClass isSubclassOfClass:desiredImageClass]) {
                image = nil;
            }
        }
    }
    
    
    if (image) {
        return image;
    }
    
    
    image = [self imageFromDiskCacheForKey:key options:options context:context];
    return image;
}

- (nullable NSData *)diskImageDataBySearchingAllPathsForKey:(nullable NSString *)key {
    if (!key) {
        return nil;
    }
    
    NSData *data = [self.diskCache dataForKey:key];
    if (data) {
        return data;
    }
    
    
    if (self.additionalCachePathBlock) {
        NSString *filePath = self.additionalCachePathBlock(key);
        if (filePath) {
            data = [NSData dataWithContentsOfFile:filePath options:self.config.diskCacheReadingOptions error:nil];
        }
    }

    return data;
}

- (nullable UIImage *)diskImageForKey:(nullable NSString *)key {
    if (!key) {
        return nil;
    }
    NSData *data = [self diskImageDataForKey:key];
    return [self diskImageForKey:key data:data options:0 context:nil];
}

- (nullable UIImage *)diskImageForKey:(nullable NSString *)key data:(nullable NSData *)data options:(SDImageCacheOptions)options context:(SDWebImageContext *)context {
    if (!data) {
        return nil;
    }
    UIImage *image = SDImageCacheDecodeImageData(data, key, [[self class] imageOptionsFromCacheOptions:options], context);
    [self _unarchiveObjectWithImage:image forKey:key];
    return image;
}

- (void)_syncDiskToMemoryWithImage:(UIImage *)diskImage forKey:(NSString *)key {
    
    if (!self.config.shouldCacheImagesInMemory) {
        return;
    }
    if (!diskImage) {
        return;
    }
    
    
    
    if (diskImage.sd_isThumbnail && !SDIsThumbnailKey(key)) {
        SDImageCoderOptions *options = diskImage.sd_decodeOptions;
        CGSize thumbnailSize = CGSizeZero;
        NSValue *thumbnailSizeValue = options[SDImageCoderDecodeThumbnailPixelSize];
        if (thumbnailSizeValue != nil) {
    #if SD_MAC
            thumbnailSize = thumbnailSizeValue.sizeValue;
    #else
            thumbnailSize = thumbnailSizeValue.CGSizeValue;
    #endif
        }
        BOOL preserveAspectRatio = YES;
        NSNumber *preserveAspectRatioValue = options[SDImageCoderDecodePreserveAspectRatio];
        if (preserveAspectRatioValue != nil) {
            preserveAspectRatio = preserveAspectRatioValue.boolValue;
        }
        
        NSString *thumbnailKey = SDThumbnailedKeyForKey(key, thumbnailSize, preserveAspectRatio);
        
        key = thumbnailKey;
    }
    NSUInteger cost = diskImage.sd_memoryCost;
    [self.memoryCache setObject:diskImage forKey:key cost:cost];
}

- (void)_unarchiveObjectWithImage:(UIImage *)image forKey:(NSString *)key {
    if (!image || !key) {
        return;
    }
    
    NSData *extendedData = [self.diskCache extendedDataForKey:key];
    if (!extendedData) {
        return;
    }
    id extendedObject;
    if (@available(iOS 11, tvOS 11, macOS 10.13, watchOS 4, *)) {
        NSError *error;
        NSKeyedUnarchiver *unarchiver = [[NSKeyedUnarchiver alloc] initForReadingFromData:extendedData error:&error];
        unarchiver.requiresSecureCoding = NO;
        extendedObject = [unarchiver decodeTopLevelObjectForKey:NSKeyedArchiveRootObjectKey error:&error];
        if (error) {
            SD_LOG("NSKeyedUnarchiver unarchive failed with error: %@
, exception);
        }
    }
    image.sd_extendedObject = extendedObject;
}

- (nullable SDImageCacheToken *)queryCacheOperationForKey:(NSString *)key done:(SDImageCacheQueryCompletionBlock)doneBlock {
    return [self queryCacheOperationForKey:key options:0 done:doneBlock];
}

- (nullable SDImageCacheToken *)queryCacheOperationForKey:(NSString *)key options:(SDImageCacheOptions)options done:(SDImageCacheQueryCompletionBlock)doneBlock {
    return [self queryCacheOperationForKey:key options:options context:nil done:doneBlock];
}

- (nullable SDImageCacheToken *)queryCacheOperationForKey:(nullable NSString *)key options:(SDImageCacheOptions)options context:(nullable SDWebImageContext *)context done:(nullable SDImageCacheQueryCompletionBlock)doneBlock {
    return [self queryCacheOperationForKey:key options:options context:context cacheType:SDImageCacheTypeAll done:doneBlock];
}

- (nullable SDImageCacheToken *)queryCacheOperationForKey:(nullable NSString *)key options:(SDImageCacheOptions)options context:(nullable SDWebImageContext *)context cacheType:(SDImageCacheType)queryCacheType done:(nullable SDImageCacheQueryCompletionBlock)doneBlock {
    if (!key) {
        if (doneBlock) {
            doneBlock(nil, nil, SDImageCacheTypeNone);
        }
        return nil;
    }
    
    if (queryCacheType == SDImageCacheTypeNone) {
        if (doneBlock) {
            doneBlock(nil, nil, SDImageCacheTypeNone);
        }
        return nil;
    }
    
    
    UIImage *image;
    BOOL shouldQueryDiskOnly = (queryCacheType == SDImageCacheTypeDisk);
    if (!shouldQueryDiskOnly) {
        image = [self imageFromMemoryCacheForKey:key];
    }
    
    if (image) {
        if (options & SDImageCacheDecodeFirstFrameOnly) {
            
            if (image.sd_imageFrameCount > 1) {
#if SD_MAC
                image = [[NSImage alloc] initWithCGImage:image.CGImage scale:image.scale orientation:kCGImagePropertyOrientationUp];
#else
                image = [[UIImage alloc] initWithCGImage:image.CGImage scale:image.scale orientation:image.imageOrientation];
#endif
            }
        } else if (options & SDImageCacheMatchAnimatedImageClass) {
            
            Class animatedImageClass = image.class;
            Class desiredImageClass = context[SDWebImageContextAnimatedImageClass];
            if (desiredImageClass && ![animatedImageClass isSubclassOfClass:desiredImageClass]) {
                image = nil;
            }
        }
    }

    BOOL shouldQueryMemoryOnly = (queryCacheType == SDImageCacheTypeMemory) || (image && !(options & SDImageCacheQueryMemoryData));
    if (shouldQueryMemoryOnly) {
        if (doneBlock) {
            doneBlock(image, nil, SDImageCacheTypeMemory);
        }
        return nil;
    }
    
    
    SDCallbackQueue *queue = context[SDWebImageContextCallbackQueue];
    SDImageCacheToken *operation = [[SDImageCacheToken alloc] initWithDoneBlock:doneBlock];
    operation.key = key;
    operation.callbackQueue = queue;
    
    
    
    BOOL shouldQueryDiskSync = ((image && options & SDImageCacheQueryMemoryDataSync) ||
                                (!image && options & SDImageCacheQueryDiskDataSync));
    NSData* (^queryDiskDataBlock)(void) = ^NSData* {
        @synchronized (operation) {
            if (operation.isCancelled) {
                return nil;
            }
        }
        
        return [self diskImageDataBySearchingAllPathsForKey:key];
    };
    
    UIImage* (^queryDiskImageBlock)(NSData*) = ^UIImage*(NSData* diskData) {
        @synchronized (operation) {
            if (operation.isCancelled) {
                return nil;
            }
        }
        
        UIImage *diskImage;
        if (image) {
            
            diskImage = image;
        } else if (diskData) {
            
            BOOL shouldCacheToMemory = YES;
            if (context[SDWebImageContextStoreCacheType]) {
                SDImageCacheType cacheType = [context[SDWebImageContextStoreCacheType] integerValue];
                shouldCacheToMemory = (cacheType == SDImageCacheTypeAll || cacheType == SDImageCacheTypeMemory);
            }
            
            
            if (!shouldQueryDiskSync) {
                
                if (!shouldQueryDiskOnly) {
                    diskImage = [self imageFromMemoryCacheForKey:key];
                }
            }
            
            if (!diskImage) {
                diskImage = [self diskImageForKey:key data:diskData options:options context:context];
                
                if (shouldCacheToMemory) {
                    [self _syncDiskToMemoryWithImage:diskImage forKey:key];
                }
            }
        }
        return diskImage;
    };
    
    
    if (shouldQueryDiskSync) {
        __block NSData* diskData;
        __block UIImage* diskImage;
        dispatch_sync(self.ioQueue, ^{
            diskData = queryDiskDataBlock();
            diskImage = queryDiskImageBlock(diskData);
        });
        if (doneBlock) {
            doneBlock(diskImage, diskData, SDImageCacheTypeDisk);
        }
    } else {
        dispatch_async(self.ioQueue, ^{
            NSData* diskData = queryDiskDataBlock();
            UIImage* diskImage = queryDiskImageBlock(diskData);
            @synchronized (operation) {
                if (operation.isCancelled) {
                    return;
                }
            }
            if (doneBlock) {
                [(queue ?: SDCallbackQueue.mainQueue) async:^{
                    
                    
                    @synchronized (operation) {
                        if (operation.isCancelled) {
                            return;
                        }
                    }
                    doneBlock(diskImage, diskData, SDImageCacheTypeDisk);
                }];
            }
        });
    }
    
    return operation;
}



- (void)removeImageForKey:(nullable NSString *)key withCompletion:(nullable SDWebImageNoParamsBlock)completion {
    [self removeImageForKey:key fromDisk:YES withCompletion:completion];
}

- (void)removeImageForKey:(nullable NSString *)key fromDisk:(BOOL)fromDisk withCompletion:(nullable SDWebImageNoParamsBlock)completion {
    [self removeImageForKey:key fromMemory:YES fromDisk:fromDisk withCompletion:completion];
}

- (void)removeImageForKey:(nullable NSString *)key fromMemory:(BOOL)fromMemory fromDisk:(BOOL)fromDisk withCompletion:(nullable SDWebImageNoParamsBlock)completion {
    if (!key) {
        return;
    }

    if (fromMemory && self.config.shouldCacheImagesInMemory) {
        [self.memoryCache removeObjectForKey:key];
    }

    if (fromDisk) {
        dispatch_async(self.ioQueue, ^{
            [self.diskCache removeDataForKey:key];
            
            if (completion) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    completion();
                });
            }
        });
    } else if (completion) {
        completion();
    }
}

- (void)removeImageFromMemoryForKey:(NSString *)key {
    if (!key) {
        return;
    }
    
    [self.memoryCache removeObjectForKey:key];
}

- (void)removeImageFromDiskForKey:(NSString *)key {
    if (!key) {
        return;
    }
    dispatch_sync(self.ioQueue, ^{
        [self _removeImageFromDiskForKey:key];
    });
}


- (void)_removeImageFromDiskForKey:(NSString *)key {
    if (!key) {
        return;
    }
    
    [self.diskCache removeDataForKey:key];
}



- (void)clearMemory {
    [self.memoryCache removeAllObjects];
}

- (void)clearDiskOnCompletion:(nullable SDWebImageNoParamsBlock)completion {
    dispatch_async(self.ioQueue, ^{
        [self.diskCache removeAllData];
        if (completion) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completion();
            });
        }
    });
}

- (void)deleteOldFilesWithCompletionBlock:(nullable SDWebImageNoParamsBlock)completionBlock {
    dispatch_async(self.ioQueue, ^{
        [self.diskCache removeExpiredData];
        if (completionBlock) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completionBlock();
            });
        }
    });
}



#if SD_UIKIT || SD_MAC
- (void)applicationWillTerminate:(NSNotification *)notification {
    
    
    if (!self.config.shouldRemoveExpiredDataWhenTerminate) {
        return;
    }
    dispatch_sync(self.ioQueue, ^{
        [self.diskCache removeExpiredData];
    });
}
#endif



#if SD_UIKIT
- (void)applicationDidEnterBackground:(NSNotification *)notification {
    if (!self.config.shouldRemoveExpiredDataWhenEnterBackground) {
        return;
    }
    Class UIApplicationClass = NSClassFromString(@"UIApplication
com.hackemist.SDWebImageDownloaderOperation.coderQueue
Operation cancelled by user before sending the request
UIApplication
Session delegate is nil and invalid
Task can't be initialized
isFinished
isExecuting
Downloaded image decode failed
Downloaded image has 0 pixels
Download marked as failed because response is nil
Download marked as failed because of invalid response status code %ld
Download marked as failed because of invalid response content type %@
Download response status code is 304 not modified and ignored
Downloaded image is not modified and ignored
Image data is nil
Thumbnail({%f,%f},%d)
For `SDImageBaseTransformer` subclass, you must override %@ method
SDImageRoundCornerTransformer(%f,%lu,%f,%@)
SDImageResizingTransformer({%f,%f},%lu)
SDImageCroppingTransformer({%f,%f,%f,%f})
SDImageFlippingTransformer(%d,%d)
SDImageRotationTransformer(%f,%d)
SDImageTintTransformer(%@,%d)
SDImageBlurTransformer(%f)
SDImageFilterTransformer(%@)
</svg>
RIFF
WEBP
ftypheic
ftypheix
ftyphevc
ftyphevx
ftypmif1
ftypmsf1
PDF
http://
https://
@%@x.
%%40%@x.
setImageOperationKey
customManager
callbackQueue
imageCache
imageLoader
imageCoder
imageTransformer
imageForceDecodePolicy
imageDecodeOptions
imageScaleFactor
imagePreserveAspectRatio
imageThumbnailPixelSize
imageTypeIdentifierHint
imageScaleDownLimitBytes
imageDecodeToHDR
imageEncodeOptions
queryCacheType
storeCacheType
originalQueryCacheType
originalStoreCacheType
originalImageCache
animatedImageClass
downloadRequestModifier
downloadResponseModifier
downloadDecryptor
cacheKeyFilter
cacheSerializer
isSymbolImage
_%@
UIButtonImageOperation%lu
UIButtonBackgroundImageOperation%lu
GET
com.hackemist.SDImageFramePool.fetchQueue
#%08x
isCancelled
Provided key path is invalid.
%s.%s
png
@%@x
public.heic
public.heif
public.heics
org.webmproject.webp
public.image
public.jpeg
public.png
public.tiff
public.svg-image
com.compuserve.gif
com.adobe.pdf
com.microsoft.bmp
public.camera-raw-image
error
function
listxattr
:path
:traverseLink
getxattr
:name
setxattr
:value.length
:overwrite
removexattr
UICKeyChainStoreErrorDomain
the key must not to be nil
failed to convert string to data
failed to convert data to string
Unexpected error has occurred.
key
service
server
class
GenericPassword
accessGroup
InternetPassword
protocol
authenticationType
value
accessibility
synchronizable
account
password
the server property must not to be nil, should use 'keyChainStoreWithServer:protocolType:' initializer to instantiate keychain store
(\n
    %@
Security error has occurred.
orderId
uid
FBAdSettings
public_profile
fb://profile/%@
https://www.facebook.com/%@
ImageDownloadErrorDomain
Invalid URL
HTTP %ld
Failed to decode image data
SDK VERSION：%@
LaunchScreen
opacity
○ 未登录
○ 准备登录
○ 登录中...
● 已登录
LogCell
点击登录
点击退出
点击支付
com.xxgame.sdk.demo.test
6
6元套餐
***********
100001
角色-XXGameSDK
99
2019
点击上报角色
XXGame
1
pet
60
horse
15
power
10000
promote
2
married
0
liveness
2000
hero_level
98
trumps
fabao1
fabao2
wings
wing1
wing2
artifacts
artifact1
artifact2
xxx
点击打开个人中心
My
点击绑定Facebook
绑定 Facebook 成功！userInfo:%@
绑定 Facebook 失败 : %@
点击绑定VK
绑定 VK 成功！userInfo:%@
绑定 VK 失败 : %@
点击事件打点
param_key
param_value
demo_eventName
点击激励广告
customData
完成激励广告任务回调-携带参数-%@
未完成激励广告
login
success
登录回调-登录成功 - %@
退出回调-SDK已登录出
支付回调-支付%@
成功
失败
上报角色回调-上报角色%@
Default Configuration
