






#import "StrongHexViewController.h"
#import "SayToast.h"
#import "OpenPurposeMostlySynthesisFlushed.h"
@import WebKit;

@interface StrongHexViewController ()

@property (nonatomic, strong) UITextField *standZipWorkTextField;
@property (nonatomic, strong) UITextField *brokenExceedsTextField;
@property (nonatomic, strong) UITextField *zipFunSucceededCapsActionTextField;

@end

@implementation StrongHexViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.areClusterButton.hidden = NO;
    
    
    self.standZipWorkTextField = [ArtistBuddy pitchPhotosStorageLeastPriceAccount];
    self.standZipWorkTextField.text = [ArtistBuddy pastEpisodeHexLinearHelloName];
    self.standZipWorkTextField.enabled = NO;
    [self.view addSubview:self.standZipWorkTextField];
    [self.standZipWorkTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(ArtistBuddy.lossFirmware.didRefreshed);
        make.left.mas_equalTo(ArtistBuddy.lossFirmware.notCapStatic);
        make.right.mas_equalTo(-ArtistBuddy.lossFirmware.notCapStatic);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.squaredColor);
    }];
    
    
    self.brokenExceedsTextField = [ArtistBuddy focalLongDaysDeferringArePassword:NO];
    [self.view addSubview:self.brokenExceedsTextField];
    [self.brokenExceedsTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.standZipWorkTextField.mas_bottom).offset(ArtistBuddy.lossFirmware.waySevenOpt);
        make.left.right.equalTo(self.standZipWorkTextField);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.squaredColor);
    }];
    UIButton *chainButton = self.brokenExceedsTextField.rightView.subviews.firstObject;
    [chainButton addTarget:self action:@selector(reductionMagnitudeStonePreferKurdishForHandler:) forControlEvents:(UIControlEventTouchUpInside)];
    
    
    self.zipFunSucceededCapsActionTextField = [ArtistBuddy focalLongDaysDeferringArePassword:YES];
    [self.view addSubview:self.zipFunSucceededCapsActionTextField];
    [self.zipFunSucceededCapsActionTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.brokenExceedsTextField.mas_bottom).offset(ArtistBuddy.lossFirmware.waySevenOpt);
        make.left.right.equalTo(self.standZipWorkTextField);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.squaredColor);
    }];
    UIButton *croatianButton = self.zipFunSucceededCapsActionTextField.rightView.subviews.firstObject;
    [croatianButton addTarget:self action:@selector(utteranceTensionConcertReferenceFrameReceivedWinHandler:) forControlEvents:(UIControlEventTouchUpInside)];
    
    
    UIButton *baseTimeExpiredPanAcceptButton = [ArtistBuddy auditedThumbMinimumUnitBracketedColor:ArtistBuddy.rowKinRedoDisk.golfIdenticalMakeAvailUkrainian];
    [baseTimeExpiredPanAcceptButton addTarget:self action:@selector(productsPivotMonotonicIndexedSpecialDownAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:baseTimeExpiredPanAcceptButton];
    [baseTimeExpiredPanAcceptButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.zipFunSucceededCapsActionTextField.mas_bottom).offset(ArtistBuddy.lossFirmware.waySevenOpt);
        make.left.mas_equalTo(ArtistBuddy.lossFirmware.notCapStatic);
        make.right.mas_equalTo(-ArtistBuddy.lossFirmware.notCapStatic);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.didRefreshed);
    }];
    
    
    UIView *spaFarMostFeedSock = [UIView new];
    spaFarMostFeedSock.userInteractionEnabled = YES;
    spaFarMostFeedSock.backgroundColor = UIColor.clearColor;
    [self.view addSubview:spaFarMostFeedSock];
    [spaFarMostFeedSock mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(ArtistBuddy.lossFirmware.didRefreshed, ArtistBuddy.lossFirmware.didRefreshed));
        make.left.bottom.equalTo(self.view);
    }];
    
    UITapGestureRecognizer *joinAppearGoalScrolledWake = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(walkRetWalkPen)];
    joinAppearGoalScrolledWake.numberOfTapsRequired = ArtistBuddy.lossFirmware.littleSpeak;
    [spaFarMostFeedSock addGestureRecognizer:joinAppearGoalScrolledWake];
}

- (void)walkRetWalkPen {
    [ThickViewController showFromViewController:self];
}

- (void)reductionMagnitudeStonePreferKurdishForHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.brokenExceedsTextField.secureTextEntry = !self.brokenExceedsTextField.isSecureTextEntry;
}

- (void)utteranceTensionConcertReferenceFrameReceivedWinHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.zipFunSucceededCapsActionTextField.secureTextEntry = !self.zipFunSucceededCapsActionTextField.isSecureTextEntry;
}

- (void)productsPivotMonotonicIndexedSpecialDownAction:(UIButton *)sender  {
    if (self.brokenExceedsTextField.text.length < ArtistBuddy.lossFirmware.bankUtility ||
        self.zipFunSucceededCapsActionTextField.text.length < ArtistBuddy.lossFirmware.bankUtility) {
        [ForAlertView tagsTapHyphensThiaminAdvisedPrevents:ArtistBuddy.rowKinRedoDisk.filenames message:ArtistBuddy.rowKinRedoDisk.theFingerEngineerBrushWaxGet completion:nil];
        return;
    }
    if ([self.eulerSlashThe respondsToSelector:@selector(acceptingSimpleSlightOutGreatFilenamesDashCheckoutKey:buttonKey:completion:)]) {
        [BusForDrawView wonExpiresDidWindow];
        [self.eulerSlashThe acceptingSimpleSlightOutGreatFilenamesDashCheckoutKey:self.brokenExceedsTextField.text buttonKey:self.zipFunSucceededCapsActionTextField.text completion:^(id object) {
            [BusForDrawView eighteenAcquireInverseMeanFooterWindow];
            if ([object boolValue]) {
                [[HeartBankManager shared] cadenceTagDeviceVibrancyWorldIterateViewController:self.navigationController];
                [SayToast zipBigClip:ArtistBuddy.rowKinRedoDisk.replacedEyeBeenQuoteNorthReplace];
                
                if (self.ownUnionFit && [self.ownUnionFit isKindOfClass:[WKWebView class]]) {
                    WKWebView *slabBendBox = (WKWebView *)self.ownUnionFit;
                    [slabBendBox reload];
                }
            }
        }];
    }
}

@end
