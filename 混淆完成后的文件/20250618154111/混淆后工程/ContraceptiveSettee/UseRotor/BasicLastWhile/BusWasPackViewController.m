






#import "BusWasPackViewController.h"
#import "IndexRaiseConfig.h"
#import "NSObject+MixModel.h"
#import "UIColor+BoxColor.h"
#import "SayToast.h"

@interface BusWasPackViewController ()<UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray<NSDictionary *> *ownerIcyLiterMembersPersist; 
@property (nonatomic, strong) NSArray<NSArray<NSString *> *> *endsOneGainIll; 
@property (nonatomic, strong) NSMutableArray<NSString *> *sectionTitles; 

@end

@implementation BusWasPackViewController


- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    _ownerIcyLiterMembersPersist = [NSMutableArray array];
    _endsOneGainIll = @[];
    _sectionTitles = [NSMutableArray array];
    
    [self fiveBetterView];
}

- (void)viewWillAppear:(BOOL)animated {
    
    UIEdgeInsets scriptsFat = [[HeartBankManager shared] wasMeanMergeWindow].safeAreaInsets;
    
    scriptsFat.top    += 10;
    scriptsFat.left   += 10;
    scriptsFat.bottom += 10;
    scriptsFat.right  += 10;

    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(scriptsFat);
    }];
}


- (void)fiveBetterView {
    _tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStyleGrouped];
    _tableView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    _tableView.dataSource = self;
    _tableView.delegate = self;
    _tableView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:_tableView];
    [_tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.verifyMaxButton.mas_bottom);
        make.left.right.bottom.equalTo(self.view);
    }];
    
    
    [_tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:NSStringFromClass(self.class)];
}


- (NSArray<NSString *> *)styleBadMinSunDictionary:(NSDictionary *)dict {
    return [[dict allKeys] sortedArrayUsingSelector:@selector(caseInsensitiveCompare:)];
}

- (void)skinAccessoryInfo:(NSDictionary *)info withTitle:(NSString *)title {
    if (!info || ![info isKindOfClass:[NSDictionary class]]) {
        return;
    }
    
    
    dispatch_async(dispatch_get_main_queue(), ^{
        @synchronized (self) {
            
            [self->_ownerIcyLiterMembersPersist addObject:[info copy]];
            NSArray *pushRowDry = [self styleBadMinSunDictionary:info];
            self->_endsOneGainIll = [self->_endsOneGainIll arrayByAddingObject:pushRowDry];
            [self->_sectionTitles addObject:title];
            
            
            [self.tableView reloadData];
        }
    });
}


- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return _ownerIcyLiterMembersPersist.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return _endsOneGainIll[section].count;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section {
    return _sectionTitles[section];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(self.class) forIndexPath:indexPath];
    
    NSString *key;
    id value;
    NSInteger sectionIndex = indexPath.section;
    key = _endsOneGainIll[sectionIndex][indexPath.row];
    value = _ownerIcyLiterMembersPersist[sectionIndex][key];
    BOOL dolbyTen = [value isKindOfClass:[NSDictionary class]] || [value isKindOfClass:[NSArray class]];
    cell.backgroundColor = [UIColor clearColor];
    
    
    for (UIView *subview in cell.contentView.subviews) {
        [subview removeFromSuperview];
    }
    
    
    UILabel *sayLabel = [[UILabel alloc] init];
    sayLabel.font = [UIFont monospacedSystemFontOfSize:14 weight:UIFontWeightMedium];
    sayLabel.textColor = [UIColor darkGrayColor];
    sayLabel.text = key;
    sayLabel.numberOfLines = 0;
    [cell.contentView addSubview:sayLabel];
    
    
    UILabel *birthLabel = [[UILabel alloc] init];
    birthLabel.font = [UIFont monospacedSystemFontOfSize:14 weight:UIFontWeightRegular];
    birthLabel.textColor = [UIColor blackColor];
    birthLabel.numberOfLines = 0;
    birthLabel.textAlignment = NSTextAlignmentRight;
    [cell.contentView addSubview:birthLabel];
    
    
    [sayLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(cell.contentView).offset(ArtistBuddy.lossFirmware.hasTintMight);
        make.top.equalTo(cell.contentView).offset(ArtistBuddy.lossFirmware.decodeScreen);
        make.bottom.equalTo(cell.contentView).offset(-ArtistBuddy.lossFirmware.decodeScreen);
        make.width.equalTo(cell.contentView.mas_width).multipliedBy(dolbyTen?ArtistBuddy.lossFirmware.instancesSix:ArtistBuddy.lossFirmware.cutAliveInuit);
    }];
    
    [birthLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(cell.contentView).offset(-ArtistBuddy.lossFirmware.hasTintMight);
        make.top.equalTo(cell.contentView).offset(ArtistBuddy.lossFirmware.decodeScreen);
        make.bottom.equalTo(cell.contentView).offset(-ArtistBuddy.lossFirmware.decodeScreen);
        make.left.equalTo(sayLabel.mas_right).offset(ArtistBuddy.lossFirmware.decodeScreen);
    }];
    
    
    if (dolbyTen) {
        cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    } else {
        birthLabel.text = [value description];
        cell.accessoryType = UITableViewCellAccessoryNone;
    }
    
    return cell;
}


- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    id value;
    NSString *key;
    
    NSInteger sectionIndex = indexPath.section;
    key = _endsOneGainIll[sectionIndex][indexPath.row];
    value = _ownerIcyLiterMembersPersist[sectionIndex][key];
    
    
    if ([value isKindOfClass:[NSDictionary class]]) {
        [self quickGradeDictionary:value withTitle:key];
    } else if ([value isKindOfClass:[NSArray class]]) {
        [self beforeLessArray:value withTitle:key];
    } else {
        
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        [pasteboard setString:[value description]];
        [SayToast fitness:ArtistBuddy.lossFirmware.composerCompressCenterConditionCurveIts];
    }
}


- (void)quickGradeDictionary:(NSDictionary *)dict withTitle:(NSString *)title {
    BusWasPackViewController *wayChunk = [[BusWasPackViewController alloc] init];
    [self.navigationController pushViewController:wayChunk animated:NO];
    [wayChunk skinAccessoryInfo:dict withTitle:title];
}

- (void)beforeLessArray:(NSArray *)array withTitle:(NSString *)title {
    
    NSMutableDictionary *realmDict = [NSMutableDictionary dictionary];
    for (NSInteger i = 0; i < array.count; i++) {
        realmDict[[NSString stringWithFormat:@"[%ld]", (long)i]] = array[i];
    }
    
    BusWasPackViewController *wayChunk = [[BusWasPackViewController alloc] init];
    [self.navigationController pushViewController:wayChunk animated:NO];
    [wayChunk skinAccessoryInfo:realmDict withTitle:[NSString stringWithFormat:@"%@ (Array)", title]];
}

@end
