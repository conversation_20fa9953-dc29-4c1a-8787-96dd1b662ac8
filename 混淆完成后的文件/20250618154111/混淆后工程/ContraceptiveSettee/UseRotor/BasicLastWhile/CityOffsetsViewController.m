






#import "CityOffsetsViewController.h"
#import "SDWebImageManager.h"
#import "SayToast.h"
#import "NSString+Messaging.h"

@interface CityOffsetsViewController ()

@property (nonatomic, strong) UIImageView *uploadedHexImageView;
@property (nonatomic, strong) UITextField *preservesTextField;
@property (nonatomic, strong) UITextField *toneHasSuchTextField;

@end

@implementation CityOffsetsViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.areClusterButton.hidden = [self.ownUnionFit[0] boolValue];
    
    CGFloat speakingMutableTailDepartureReserved = ArtistBuddy.lossFirmware.panelSummary;
    
    UILabel *tipLabel = [ArtistBuddy chromaModelTurnWetClamped:ArtistBuddy.rowKinRedoDisk.eachContinuedQuietPresentAny];
    tipLabel.numberOfLines = 0;
    [self.view addSubview:tipLabel];
    [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(ArtistBuddy.lossFirmware.lemmaSwahili);
        make.left.mas_equalTo(ArtistBuddy.lossFirmware.initiatedPen);
        make.right.mas_equalTo(-ArtistBuddy.lossFirmware.initiatedPen);
    }];
    
    
    self.preservesTextField = [ArtistBuddy priorOxygenField:ArtistBuddy.rowKinRedoDisk.structureDryImmutableBrotherSemanticsAlignment isSecure:NO];
    [self.view addSubview:self.preservesTextField];
    [self.preservesTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(tipLabel.mas_bottom).offset(speakingMutableTailDepartureReserved);
        make.left.mas_equalTo(ArtistBuddy.lossFirmware.notCapStatic);
        make.right.mas_equalTo(-ArtistBuddy.lossFirmware.notCapStatic);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.squaredColor);
    }];
    
    
    self.toneHasSuchTextField = [ArtistBuddy priorOxygenField:ArtistBuddy.rowKinRedoDisk.blueSafariTabMakerSurrogatePublish isSecure:NO];;
    [self.view addSubview:self.toneHasSuchTextField];
    [self.toneHasSuchTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.preservesTextField.mas_bottom).offset(speakingMutableTailDepartureReserved);
        make.left.right.equalTo(self.preservesTextField);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.squaredColor);
    }];
    
    
    UIImageView *rewardImageView = nil;
    if ([self.ownUnionFit[1] length] > 0) {
        
        rewardImageView = [[UIImageView alloc] init];
        rewardImageView.backgroundColor = UIColor.lightGrayColor;
        rewardImageView.contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:rewardImageView];
        [rewardImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.toneHasSuchTextField.mas_bottom).offset(speakingMutableTailDepartureReserved);
            make.width.mas_equalTo(self.toneHasSuchTextField);
            make.centerX.mas_equalTo(0);
        }];
        self.uploadedHexImageView = rewardImageView;
        
        [[SDWebImageManager sharedManager] loadImageWithURL:[NSURL URLWithString:self.ownUnionFit[1]] options:0 progress:nil completed:^(UIImage * _Nullable image2, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
            dispatch_async(dispatch_get_main_queue(), ^{
                rewardImageView.image = image2;
                CGFloat ratio = image2.size.height / image2.size.width;
                [rewardImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo((ArtistBuddy.lossFirmware.languagesMovementStarYellowHandlesWidth -ArtistBuddy.lossFirmware.notCapStatic*2)*ratio);
                }];
                [self.view layoutIfNeeded];
                
                [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.center.mas_equalTo(0);
                    CGFloat height = self.uploadedHexImageView ? self.uploadedHexImageView.frame.size.height + ArtistBuddy.lossFirmware.panelSummary +ArtistBuddy.lossFirmware.stateSaveLanguageBypassedCaptionGuide : ArtistBuddy.lossFirmware.stateSaveLanguageBypassedCaptionGuide;
                    
                    make.size.mas_equalTo(CGSizeMake(ArtistBuddy.lossFirmware.languagesMovementStarYellowHandlesWidth, MIN(height, UIScreen.mainScreen.bounds.size.height)));
                    if (height > UIScreen.mainScreen.bounds.size.height) {
                        [self.uploadedHexImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                            make.height.mas_equalTo(self.uploadedHexImageView.frame.size.height-(height-UIScreen.mainScreen.bounds.size.height));
                        }];
                    }
                }];
            });
        }];
    }
    
    
    UIButton *xxpk_realButton = [ArtistBuddy auditedThumbMinimumUnitBracketedColor:ArtistBuddy.rowKinRedoDisk.scanDynamicVitaminEpisodeElevation];
    [xxpk_realButton addTarget:self action:@selector(sentencesOnlineShortcutsArtsTriangleReceipt:) forControlEvents:(UIControlEventTouchUpInside)];
    [self.view addSubview:xxpk_realButton];
    [xxpk_realButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(-ArtistBuddy.lossFirmware.conditionTen);
        make.left.right.equalTo(self.preservesTextField);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.didRefreshed);
    }];
    
}

- (void)sentencesOnlineShortcutsArtsTriangleReceipt:(id)sender {

}

@end
