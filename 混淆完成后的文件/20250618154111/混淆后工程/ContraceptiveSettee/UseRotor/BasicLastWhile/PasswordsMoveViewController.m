






#import "PasswordsMoveViewController.h"
#import "ReadTiedKinButton.h"
#import "SayToast.h"
#import "RealEntryTextField.h"
#import "NSString+Messaging.h"
@import WebKit;

@interface PasswordsMoveViewController ()

@property (nonatomic, strong) RealEntryTextField *leapUpscaleTextField;
@property (nonatomic, strong) UITextField *currentlyTextField;
@property (nonatomic, strong) ReadTiedKinButton *quotesAllButton;

@end

@implementation PasswordsMoveViewController


- (ReadTiedKinButton *)quotesAllButton {
    if (!_quotesAllButton) {
        _quotesAllButton = [[ReadTiedKinButton alloc] init];
    }
    return _quotesAllButton;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.areClusterButton.hidden = [self.ownUnionFit[0] boolValue];
    
    UILabel *tipLabel = [ArtistBuddy chromaModelTurnWetClamped:ArtistBuddy.rowKinRedoDisk.turkmenHandlerEditorialHeadphoneArtTry];
    tipLabel.numberOfLines = 0;
    [self.view addSubview:tipLabel];
    [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(ArtistBuddy.lossFirmware.initiatedPen);
        make.left.mas_equalTo(ArtistBuddy.lossFirmware.initiatedPen);
        make.right.mas_equalTo(-ArtistBuddy.lossFirmware.initiatedPen);
    }];
    
    
    self.leapUpscaleTextField = [[RealEntryTextField alloc] initWithController:self];
    [self.view addSubview:self.leapUpscaleTextField];
    [self.leapUpscaleTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(tipLabel.mas_bottom).offset(ArtistBuddy.lossFirmware.panelSummary);
        make.left.mas_equalTo(ArtistBuddy.lossFirmware.notCapStatic);
        make.right.mas_equalTo(-ArtistBuddy.lossFirmware.notCapStatic);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.squaredColor);
    }];
    
    
    self.currentlyTextField = [ArtistBuddy decipherInuitBorderedCoveragePreciseBankCode];
    [self.view addSubview:self.currentlyTextField];
    [self.currentlyTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.leapUpscaleTextField.mas_bottom).offset(ArtistBuddy.lossFirmware.conditionTen);
        make.left.mas_equalTo(ArtistBuddy.lossFirmware.notCapStatic);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.squaredColor);
    }];
    
    sorting(self);
    self.quotesAllButton.cutJoiningMayAction = ^{
        notifyAll(self);
        NSString *netBasqueCode = self.leapUpscaleTextField.setupSpeakGrow;
        NSString *sinChildRun = self.leapUpscaleTextField.teamSymptomHelloExecutorBecome;
        if (self.leapUpscaleTextField.leapUpscaleTextField.text.raceSinBlock) {
            [self.quotesAllButton trackOpaqueAccordingAnyView];
            [ForAlertView tagsTapHyphensThiaminAdvisedPrevents:ArtistBuddy.rowKinRedoDisk.filenames message:ArtistBuddy.rowKinRedoDisk.traverseExcludedArteryLongitudeCoalescePurpose completion:nil];
            return;
        }
        if ([self.eulerSlashThe respondsToSelector:@selector(oldestEmptyResponsesVisionMagneticLambdaSurrogateType:strongBleed:rootCode:completion:)]) {
            [BusForDrawView wonExpiresDidWindow];
            [self.eulerSlashThe oldestEmptyResponsesVisionMagneticLambdaSurrogateType:ArtistBuddy.lossFirmware.atomCapsGerman strongBleed:sinChildRun rootCode:netBasqueCode completion:^(id object) {
                [BusForDrawView eighteenAcquireInverseMeanFooterWindow];
                if ([object boolValue]) {
                    [SayToast zipBigClip:ArtistBuddy.rowKinRedoDisk.responseShotStackedSentPlainDesiredCode];
                }else {
                    [self.quotesAllButton trackOpaqueAccordingAnyView];
                }
            }];
        }
    };
    [self.view addSubview:self.quotesAllButton];
    [self.quotesAllButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.currentlyTextField);
        make.height.equalTo(self.currentlyTextField);
        make.left.equalTo(self.currentlyTextField.mas_right).offset(ArtistBuddy.lossFirmware.decodeScreen);
        make.right.equalTo(self.leapUpscaleTextField);
    }];
    
    
    [self.quotesAllButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    
    UIButton *xxpk_bindButton = [ArtistBuddy auditedThumbMinimumUnitBracketedColor:ArtistBuddy.rowKinRedoDisk.fileIronShowBlobInitial];
    [xxpk_bindButton addTarget:self action:@selector(supplyReactorSignPromptConvergedAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_bindButton];
    [xxpk_bindButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.currentlyTextField.mas_bottom).offset(ArtistBuddy.lossFirmware.conditionTen);
        make.left.mas_equalTo(ArtistBuddy.lossFirmware.notCapStatic);
        make.right.mas_equalTo(-ArtistBuddy.lossFirmware.notCapStatic);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.didRefreshed);
    }];
}

- (void)supplyReactorSignPromptConvergedAction:(id)sender {
    if (self.leapUpscaleTextField.leapUpscaleTextField.text.raceSinBlock) {
        [ForAlertView tagsTapHyphensThiaminAdvisedPrevents:ArtistBuddy.rowKinRedoDisk.filenames message:ArtistBuddy.rowKinRedoDisk.traverseExcludedArteryLongitudeCoalescePurpose completion:nil];
        return;
    }
    if (self.currentlyTextField.text.raceSinBlock) {
        [ForAlertView tagsTapHyphensThiaminAdvisedPrevents:ArtistBuddy.rowKinRedoDisk.filenames message:ArtistBuddy.rowKinRedoDisk.windowCellItsPreserveDublin completion:nil];
        return;
    }
    NSString *netBasqueCode = self.leapUpscaleTextField.setupSpeakGrow;
    NSString *sinChildRun = self.leapUpscaleTextField.teamSymptomHelloExecutorBecome;
    if ([self.eulerSlashThe respondsToSelector:@selector(locallyBandOurOwnFigurePlaceGigahertz:code:rootCode:completion:)]) {
        [BusForDrawView wonExpiresDidWindow];
        [self.eulerSlashThe locallyBandOurOwnFigurePlaceGigahertz:sinChildRun code:self.currentlyTextField.text rootCode:netBasqueCode completion:^(id object) {
            [BusForDrawView eighteenAcquireInverseMeanFooterWindow];
            if ([object boolValue]) {
                [[HeartBankManager shared] cadenceTagDeviceVibrancyWorldIterateViewController:self.navigationController];
                [SayToast zipBigClip:ArtistBuddy.rowKinRedoDisk.canceledCollectScriptsReflectCapturedQuit];
                if ([self.ownUnionFit[1] isKindOfClass:[WKWebView class]]) {
                    WKWebView *slabBendBox = (WKWebView *)self.ownUnionFit[1];
                    [slabBendBox reload];
                }
            }
        }];
    }
}

- (void)dealloc {
    [self.quotesAllButton trackOpaqueAccordingAnyView];
}

@end
