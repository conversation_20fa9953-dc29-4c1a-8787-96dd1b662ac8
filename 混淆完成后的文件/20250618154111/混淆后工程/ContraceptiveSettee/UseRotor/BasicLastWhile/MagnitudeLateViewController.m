






#import "MagnitudeLateViewController.h"
#import "SayToast.h"
#import "PurpleInfo.h"

@interface MagnitudeLateViewController ()

@property (nonatomic, strong) UIImageView *hiddenKinView;
@property (nonatomic, strong) UIButton *tapPrepStayButton;
@property (nonatomic, strong) UIView *yahooLoseView;
@property (nonatomic, strong) UILabel *checkingLabel;
@property (nonatomic, strong) UITextField *standZipWorkTextField;
@property (nonatomic, strong) UITextField *brokenExceedsTextField;
@end

@implementation MagnitudeLateViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.areClusterButton.hidden = YES;
    
    if ([PurpleInfo authorsOptimizedRequestArmpitAwakeImage]) {
        self.hiddenKinView = [[UIImageView alloc] initWithImage:[PurpleInfo authorsOptimizedRequestArmpitAwakeImage]];
        [self.view addSubview:self.hiddenKinView];
        self.hiddenKinView.hidden = YES;
        [self.hiddenKinView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(ArtistBuddy.lossFirmware.didRefreshed);
            make.left.equalTo(self.verifyMaxButton.mas_right);
            make.top.equalTo(self.view).offset(ArtistBuddy.lossFirmware.decodeScreen);
        }];
    }
    
    self.yahooLoseView = [ArtistBuddy yahooLoseView];
    self.yahooLoseView.hidden = YES;
    [self.view addSubview:self.yahooLoseView];
    [self.yahooLoseView mas_makeConstraints:^(MASConstraintMaker *make) {
        if ([PurpleInfo authorsOptimizedRequestArmpitAwakeImage]) {
            make.centerY.equalTo(self.hiddenKinView);
            make.left.equalTo(self.hiddenKinView.mas_right).offset(ArtistBuddy.lossFirmware.waySevenOpt);
        }else {
            make.top.equalTo(self.view).offset(ArtistBuddy.lossFirmware.decodeScreen);
            make.left.equalTo(self.verifyMaxButton.mas_right).offset(ArtistBuddy.lossFirmware.waySevenOpt);
        }
        make.right.equalTo(self.areClusterButton.mas_left);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.didRefreshed);
    }];
    
    self.checkingLabel = [ArtistBuddy chromaModelTurnWetClamped:ArtistBuddy.rowKinRedoDisk.springDeliverCelsiusEnteredAre];
    self.checkingLabel.numberOfLines = 0;
    [self.view addSubview:self.checkingLabel];
    [self.checkingLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(ArtistBuddy.lossFirmware.seeRunAcross);
        make.left.mas_equalTo(ArtistBuddy.lossFirmware.initiatedPen);
        make.right.mas_equalTo(-ArtistBuddy.lossFirmware.initiatedPen);
    }];
    
    
    self.standZipWorkTextField = [ArtistBuddy pitchPhotosStorageLeastPriceAccount];
    self.standZipWorkTextField.enabled = NO;
    self.standZipWorkTextField.text = self.ownUnionFit[ArtistBuddy.lossFirmware.upperArabicName];
    [self denyEndsFullyUighurMiterMarqueeView:self.standZipWorkTextField text:ArtistBuddy.rowKinRedoDisk.zipSummariesShortRegionsDaughter];
    [self.view addSubview:self.standZipWorkTextField];
    [self.standZipWorkTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.checkingLabel.mas_bottom).offset(ArtistBuddy.lossFirmware.panelSummary);
        make.left.mas_equalTo(ArtistBuddy.lossFirmware.notCapStatic);
        make.right.mas_equalTo(-ArtistBuddy.lossFirmware.notCapStatic);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.squaredColor);
    }];
    
    
    self.brokenExceedsTextField = [ArtistBuddy pitchPhotosStorageLeastPriceAccount];
    self.brokenExceedsTextField.enabled = NO;
    self.brokenExceedsTextField.text = self.ownUnionFit[ArtistBuddy.lossFirmware.eraWayBurstKey];
    [self denyEndsFullyUighurMiterMarqueeView:self.brokenExceedsTextField text:ArtistBuddy.rowKinRedoDisk.pressesScalingDownMealOrange];
    [self.view addSubview:self.brokenExceedsTextField];
    [self.brokenExceedsTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.standZipWorkTextField.mas_bottom).offset(ArtistBuddy.lossFirmware.panelSummary);
        make.left.right.equalTo(self.standZipWorkTextField);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.squaredColor);
    }];
    
    
    UIButton *xxpk_saveButton = [ArtistBuddy auditedThumbMinimumUnitBracketedColor:ArtistBuddy.rowKinRedoDisk.waterThatPrintableNumbersFloat];
    [xxpk_saveButton addTarget:self action:@selector(notifyAppearingAskPaddleFlushedAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_saveButton];
    [xxpk_saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.brokenExceedsTextField.mas_bottom).offset(ArtistBuddy.lossFirmware.panelSummary);
        make.left.right.equalTo(self.brokenExceedsTextField);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.didRefreshed);
    }];
    
    
    self.tapPrepStayButton = [ArtistBuddy stillStylePetiteHeadphoneEnhanced:ArtistBuddy.rowKinRedoDisk.truncateGatherReferenceKashmiriGenericsOld];
    [self.tapPrepStayButton setContentEdgeInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
    [self.tapPrepStayButton addTarget:self action:@selector(anySeleniumSpacingSamplesCancelledAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.tapPrepStayButton];
    [self.tapPrepStayButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(xxpk_saveButton.mas_bottom).offset(ArtistBuddy.lossFirmware.republicFun);
        make.centerX.equalTo(self.view);
    }];
}

- (void)denyEndsFullyUighurMiterMarqueeView:(UITextField *)textField text:(NSString *)text
{
    CGRect frame = {{0,0},CGSizeMake(ArtistBuddy.lossFirmware.didRefreshed, ArtistBuddy.lossFirmware.squaredColor)};
    UILabel *leftview = [[UILabel alloc] initWithFrame:frame];
    leftview.text = text;
    leftview.textColor = UIColor.redColor;
    textField.leftViewMode = UITextFieldViewModeAlways;
    textField.leftView = leftview;
}

- (void)anySeleniumSpacingSamplesCancelledAction:(UIButton *)sender {
    [[HeartBankManager shared] cadenceTagDeviceVibrancyWorldIterateViewController:self.navigationController];
}

- (void)notifyAppearingAskPaddleFlushedAction:(UIButton *)sender {
    sender.hidden = YES;
    self.checkingLabel.hidden = YES;
    self.yahooLoseView.hidden = NO;
    self.hiddenKinView.hidden = NO;
    self.tapPrepStayButton.hidden = YES;
    
    [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.view.superview);
        make.size.mas_equalTo(CGSizeMake(ArtistBuddy.lossFirmware.languagesMovementStarYellowHandlesWidth, ArtistBuddy.lossFirmware.stateSaveLanguageBypassedCaptionGuide-ArtistBuddy.lossFirmware.radixGeneric));
    }];
    [self.standZipWorkTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.yahooLoseView.mas_bottom).offset(ArtistBuddy.lossFirmware.panelSummary);
    }];
    [self.view layoutIfNeeded];
    
    BOOL vital = [[[[NSBundle mainBundle] infoDictionary] allKeys] containsObject:ArtistBuddy.lossFirmware.endThinMutableProximityStrengthDeepMartial];
    if (!vital) {
        self.areClusterButton.hidden = NO;
        [ForAlertView tagsTapHyphensThiaminAdvisedPrevents:nil message:ArtistBuddy.rowKinRedoDisk.decomposeTruncatesDiscountPlusThe completion:nil];
        return;
    }
    CGSize size = self.view.frame.size;
    size.height -= ArtistBuddy.lossFirmware.endSpaSpring;
    UIGraphicsBeginImageContextWithOptions(size, NO, 0.0);
    [self.view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    UIImageWriteToSavedPhotosAlbum(image, self, @selector(image:changeClickedCauseIntroInuitAre:contextInfo:), (__bridge void *)self);
}

- (void)image:(UIImage *)image changeClickedCauseIntroInuitAre:(NSError *)error contextInfo:(void *)contextInfo
{
    
    if(!error){
        [[HeartBankManager shared] cadenceTagDeviceVibrancyWorldIterateViewController:self.navigationController];
        [SayToast fitness:ArtistBuddy.rowKinRedoDisk.proposedFeaturesBurnFatOpt];
    }else {
        self.areClusterButton.hidden = NO;
        [ForAlertView tagsTapHyphensThiaminAdvisedPrevents:nil message:ArtistBuddy.rowKinRedoDisk.decomposeTruncatesDiscountPlusThe completion:nil];
    }
}

@end
