






#import "ExtentsPubViewController.h"
#import "BusWasPackViewController.h"
#import "IndexRaiseConfig.h"
#import "NSObject+MixModel.h"
#import "UIColor+BoxColor.h"
#import "SayToast.h"
#import "OpenPurposeMostlySynthesisFlushed.h"

@interface ExtentsPubViewController ()

@property (nonatomic, strong) UIImageView *andIcyImageView;
@property (nonatomic, strong) UIButton *tooButton;
@property (nonatomic, strong) UIButton *sixButton;
@property (nonatomic, strong) UIButton *sobButton;

@end

@implementation ExtentsPubViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.view.backgroundColor = [UIColor tamilDifferentRefinedSobPaperFax:ArtistBuddy.lossFirmware.onlyShePlay];
    
    
    UIView *lingerPushStreetStandSubset = [[UIView alloc] init];
    lingerPushStreetStandSubset.backgroundColor = [UIColor clearColor];
    [self.view addSubview:lingerPushStreetStandSubset];
    
    
    _andIcyImageView = [[UIImageView alloc] init];
    UIImageSymbolConfiguration *config = [UIImageSymbolConfiguration configurationWithPointSize:ArtistBuddy.lossFirmware.initiatedPen weight:UIImageSymbolWeightMedium];
    UIImage *inlandImage = [UIImage systemImageNamed:ArtistBuddy.lossFirmware.activatedStreamsTabHyphenGermanPopNet withConfiguration:config];
    _andIcyImageView.image = inlandImage;
    _andIcyImageView.tintColor = [UIColor tamilDifferentRefinedSobPaperFax:ArtistBuddy.lossFirmware.sunDublinColor];
    _andIcyImageView.contentMode = UIViewContentModeScaleAspectFit;
    [lingerPushStreetStandSubset addSubview:_andIcyImageView];
    
    
    UILabel *usesMethodLabel = [[UILabel alloc] init];
    usesMethodLabel.text = ArtistBuddy.rowKinRedoDisk.summariesCallAirPreserveTagGradient;
    usesMethodLabel.font = [UIFont boldSystemFontOfSize:ArtistBuddy.lossFirmware.estimatedArm];
    usesMethodLabel.textAlignment = NSTextAlignmentLeft;
    usesMethodLabel.textColor = [UIColor tamilDifferentRefinedSobPaperFax:ArtistBuddy.lossFirmware.husbandSock];
    [lingerPushStreetStandSubset addSubview:usesMethodLabel];
    
    
    [lingerPushStreetStandSubset mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(ArtistBuddy.lossFirmware.hasTintMight);
        make.centerX.equalTo(self.view).offset(-ArtistBuddy.lossFirmware.littleSpeak);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.googleDetach);
    }];
    
    
    [_andIcyImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(lingerPushStreetStandSubset);
        make.centerY.equalTo(lingerPushStreetStandSubset);
        make.width.height.mas_equalTo(ArtistBuddy.lossFirmware.googleDetach);
    }];
    
    [usesMethodLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_andIcyImageView.mas_right).offset(ArtistBuddy.lossFirmware.decodeScreen);
        make.centerY.equalTo(lingerPushStreetStandSubset);
        make.right.equalTo(lingerPushStreetStandSubset);
    }];
    
    
    _tooButton = [self yardArrangerPassivelyTatarQualifiedGoogleIcon:ArtistBuddy.lossFirmware.tenEditorialPhotosHisRadioNone
                                                  title:ArtistBuddy.rowKinRedoDisk.femaleWaistDelayEscapingHourly
                                               subtitle:IndexRaiseConfig.shared.bitKazakhSentencesSphereAssistant.makerSummary.catLate];
    [self.view addSubview:_tooButton];
    [_tooButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(lingerPushStreetStandSubset.mas_bottom).offset(ArtistBuddy.lossFirmware.hasTintMight);
        make.left.equalTo(self.view).offset(ArtistBuddy.lossFirmware.notCapStatic);
        make.right.equalTo(self.view).offset(-ArtistBuddy.lossFirmware.notCapStatic);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.googleDetach);
    }];
    [_tooButton addTarget:self action:@selector(unifyVeryNoteAction:) forControlEvents:UIControlEventTouchUpInside];
    
    
    _sixButton = [self yardArrangerPassivelyTatarQualifiedGoogleIcon:ArtistBuddy.lossFirmware.beginAngularCustomDolbyShiftOutputs
                                                   title:ArtistBuddy.rowKinRedoDisk.freeEmptyFourAtomicFriend
                                                subtitle:IndexRaiseConfig.shared.bitKazakhSentencesSphereAssistant.makerSummary.badSurge];
    [self.view addSubview:_sixButton];
    [_sixButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_tooButton.mas_bottom).offset(ArtistBuddy.lossFirmware.conditionTen);
        make.left.equalTo(self.view).offset(ArtistBuddy.lossFirmware.notCapStatic);
        make.right.equalTo(self.view).offset(-ArtistBuddy.lossFirmware.notCapStatic);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.googleDetach);
    }];
    [_sixButton addTarget:self action:@selector(enumerateMediaAction:) forControlEvents:UIControlEventTouchUpInside];
    
    
    _sobButton = [UIButton buttonWithType:UIButtonTypeCustom];
    _sobButton.backgroundColor = [UIColor tamilDifferentRefinedSobPaperFax:ArtistBuddy.lossFirmware.sunDublinColor];
    _sobButton.layer.cornerRadius = ArtistBuddy.lossFirmware.estimatedArm;
    [_sobButton setTitle:ArtistBuddy.rowKinRedoDisk.requestLiftDefaultsFillNetwork forState:UIControlStateNormal];
    [_sobButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    _sobButton.titleLabel.font = [UIFont systemFontOfSize:ArtistBuddy.lossFirmware.hasTintMight weight:UIFontWeightMedium];
    UIImage *busIcon = [UIImage systemImageNamed:ArtistBuddy.lossFirmware.fourthIllVerifyOpaqueStillTertiary];
    [_sobButton setImage:busIcon forState:UIControlStateNormal];
    _sobButton.imageEdgeInsets = UIEdgeInsetsMake(0, -ArtistBuddy.lossFirmware.waySevenOpt, 0, 0);
    _sobButton.titleEdgeInsets = UIEdgeInsetsMake(0, ArtistBuddy.lossFirmware.waySevenOpt, 0, 0);
    _sobButton.tintColor = [UIColor whiteColor];
    [self.view addSubview:_sobButton];
    [_sobButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_sixButton.mas_bottom).offset(ArtistBuddy.lossFirmware.conditionTen);
        make.left.equalTo(self.view).offset(ArtistBuddy.lossFirmware.notCapStatic);
        make.right.equalTo(self.view).offset(-ArtistBuddy.lossFirmware.notCapStatic);
        make.height.mas_equalTo(ArtistBuddy.lossFirmware.googleDetach);
    }];
    [_sobButton addTarget:self action:@selector(twoTransmitManganeseTransformWetAction:) forControlEvents:UIControlEventTouchUpInside];
    
    
    UILabel *zipAmountBin = [[UILabel alloc] init];
    zipAmountBin.text = [NSString stringWithFormat:ArtistBuddy.lossFirmware.maintainHoursGuaraniNegotiateJoinPotential, IndexRaiseConfig.shared.noneAdobe.zipAmountBin];
    zipAmountBin.font = [UIFont systemFontOfSize:ArtistBuddy.lossFirmware.panelSummary weight:UIFontWeightLight];
    zipAmountBin.textAlignment = NSTextAlignmentLeft;
    zipAmountBin.textColor = [UIColor tamilDifferentRefinedSobPaperFax:ArtistBuddy.lossFirmware.appendMixer];
    [self.view addSubview:zipAmountBin];
    [zipAmountBin mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset(-ArtistBuddy.lossFirmware.bankUtility);
        make.centerX.equalTo(self.view);
    }];
    
    
    UIView *capShareView = [UIView new];
    capShareView.userInteractionEnabled = YES;
    capShareView.backgroundColor = UIColor.clearColor;
    [self.view addSubview:capShareView];
    [capShareView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(ArtistBuddy.lossFirmware.didRefreshed, ArtistBuddy.lossFirmware.didRefreshed));
        make.right.bottom.equalTo(self.view);
    }];
    UITapGestureRecognizer *audienceMaterialEstimateRegionNumbers = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(mapOuncesDaughtersLocalizesPenInfo)];
    audienceMaterialEstimateRegionNumbers.numberOfTapsRequired = ArtistBuddy.lossFirmware.littleSpeak;
    [capShareView addGestureRecognizer:audienceMaterialEstimateRegionNumbers];
    
    
    UIView *spaFarMostFeedSock = [UIView new];
    spaFarMostFeedSock.userInteractionEnabled = YES;
    spaFarMostFeedSock.backgroundColor = UIColor.clearColor;
    [self.view addSubview:spaFarMostFeedSock];
    [spaFarMostFeedSock mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(ArtistBuddy.lossFirmware.didRefreshed, ArtistBuddy.lossFirmware.didRefreshed));
        make.left.bottom.equalTo(self.view);
    }];
    
    UITapGestureRecognizer *joinAppearGoalScrolledWake = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(walkRetWalkPen)];
    joinAppearGoalScrolledWake.numberOfTapsRequired = ArtistBuddy.lossFirmware.littleSpeak;
    [spaFarMostFeedSock addGestureRecognizer:joinAppearGoalScrolledWake];
    
    
}


- (UIButton *)yardArrangerPassivelyTatarQualifiedGoogleIcon:(NSString *)iconName title:(NSString *)title subtitle:(NSString *)subtitle {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.backgroundColor = [UIColor whiteColor];
    button.layer.cornerRadius = ArtistBuddy.lossFirmware.estimatedArm;
    
    
    UIImageView *mustView = [[UIImageView alloc] init];
    UIImageSymbolConfiguration *config = [UIImageSymbolConfiguration configurationWithPointSize:ArtistBuddy.lossFirmware.estimatedArm weight:UIImageSymbolWeightMedium];
    UIImage *icon = [UIImage systemImageNamed:iconName withConfiguration:config];
    mustView.image = icon;
    mustView.tintColor = [UIColor tamilDifferentRefinedSobPaperFax:ArtistBuddy.lossFirmware.sunDublinColor];
    mustView.contentMode = UIViewContentModeScaleAspectFit;
    [button addSubview:mustView];
    [mustView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(button).offset(ArtistBuddy.lossFirmware.decodeScreen);
        make.centerY.equalTo(button);
        make.width.height.mas_equalTo(ArtistBuddy.lossFirmware.radixGeneric);
    }];
    
    
    UILabel *usesMethodLabel = [[UILabel alloc] init];
    usesMethodLabel.text = title;
    usesMethodLabel.font = [UIFont systemFontOfSize:ArtistBuddy.lossFirmware.showBigDigit weight:UIFontWeightMedium];
    usesMethodLabel.textColor = [UIColor tamilDifferentRefinedSobPaperFax:ArtistBuddy.lossFirmware.husbandSock];
    [button addSubview:usesMethodLabel];
    [usesMethodLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(mustView.mas_right).offset(ArtistBuddy.lossFirmware.decodeScreen);
        make.centerY.equalTo(button);
    }];
    
    
    UILabel *cacheHexLabel = [[UILabel alloc] init];
    cacheHexLabel.text = subtitle;
    cacheHexLabel.font = [UIFont systemFontOfSize:ArtistBuddy.lossFirmware.showBigDigit];
    cacheHexLabel.textColor = [UIColor tamilDifferentRefinedSobPaperFax:ArtistBuddy.lossFirmware.appendMixer];
    [button addSubview:cacheHexLabel];
    [cacheHexLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(button).offset(-ArtistBuddy.lossFirmware.hasTintMight);
        make.centerY.equalTo(button);
    }];
    
    return button;
}

- (void)unifyVeryNoteAction:(id)sender {
    NSString *indexing = IndexRaiseConfig.shared.bitKazakhSentencesSphereAssistant.makerSummary.catLate;
    if (indexing.length > 0) {
        [self.eulerSlashThe batchDemandCutPurpleIcelandicZipNotify:[NSString stringWithFormat:ArtistBuddy.lossFirmware.gigabytesGreatArrowIndirectTouchCan, indexing]];
    } else {
        [SayToast fitness:ArtistBuddy.rowKinRedoDisk.availableEightHighEditorsCapAmount];
    }
}

- (void)enumerateMediaAction:(id)sender {
    NSString *allocated = IndexRaiseConfig.shared.bitKazakhSentencesSphereAssistant.makerSummary.badSurge;
    if (allocated.length > 0) {
        [self.eulerSlashThe batchDemandCutPurpleIcelandicZipNotify:[NSString stringWithFormat:ArtistBuddy.lossFirmware.russianAreaSelectorsDirtyDesignerKilohertz, allocated]];
    } else {
        [SayToast fitness:ArtistBuddy.rowKinRedoDisk.definesImperialBlockEngineerGlyphHumidity];
    }
}

- (void)twoTransmitManganeseTransformWetAction:(id)sender {
    NSString *lawPersist = IndexRaiseConfig.shared.bitKazakhSentencesSphereAssistant.makerSummary.auditPen;
    if (lawPersist.length > 0) {
        [self.eulerSlashThe batchDemandCutPurpleIcelandicZipNotify:lawPersist];
    } else {
        [SayToast fitness:ArtistBuddy.rowKinRedoDisk.invisibleUserFunLawBevelHover];
    }
}

- (void)mapOuncesDaughtersLocalizesPenInfo {
    BusWasPackViewController *bleedClaim = [BusWasPackViewController new];
    NSDictionary *aboveTarget = @{
        ArtistBuddy.lossFirmware.snapSubstringLockCupDemandUses: [[NSBundle mainBundle] infoDictionary],
        ArtistBuddy.lossFirmware.forkKirghizForceRoundCharSigning: [IndexRaiseConfig.shared.accuracyRunInfo eventMinimalDict],
        ArtistBuddy.lossFirmware.denyPositionSoccerPackageZeroWidth: [IndexRaiseConfig.shared.noneAdobe eventMinimalDict],
        ArtistBuddy.lossFirmware.mergeFeedAreaEventualUnlimitedKeep: [IndexRaiseConfig.shared.givenRowMaxInfo eventMinimalDict],
        ArtistBuddy.lossFirmware.actualMixUnsafeEngravedDeletingPressed: [IndexRaiseConfig.shared.bitKazakhSentencesSphereAssistant eventMinimalDict],
        ArtistBuddy.lossFirmware.milesBirthIncomingMalayalamBagAudio: [IndexRaiseConfig.shared.runAccessedCompositeRebusPen eventMinimalDict]
    };
    [bleedClaim skinAccessoryInfo:aboveTarget withTitle:@""];
    [self.navigationController pushViewController:bleedClaim animated:NO];
}

- (void)walkRetWalkPen {
    [ThickViewController showFromViewController:self];
}
@end
