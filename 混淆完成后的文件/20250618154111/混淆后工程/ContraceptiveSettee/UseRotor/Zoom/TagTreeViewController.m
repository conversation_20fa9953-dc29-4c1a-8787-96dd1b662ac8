






#import "TagTreeViewController.h"

@interface TagTreeViewController ()

@end

@implementation TagTreeViewController

- (UIButton *)verifyMaxButton
{
    if (!_verifyMaxButton) {
        _verifyMaxButton = [[UIButton alloc] init];
        [_verifyMaxButton setTitle:ArtistBuddy.lossFirmware.nordicRevealedLambdaQuotesTenRate forState:UIControlStateNormal];
        [_verifyMaxButton setTitleColor:[ArtistBuddy terahertzColor] forState:UIControlStateNormal];
        [_verifyMaxButton addTarget:self action:@selector(itsDesignerOffsetHerPoloAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _verifyMaxButton;
}

- (UIButton *)areClusterButton
{
    if (!_areClusterButton) {
        _areClusterButton = [[UIButton alloc] init];
        [_areClusterButton setTitle:ArtistBuddy.lossFirmware.centralsRestTipHandleReflectCatalan forState:UIControlStateNormal];
        [_areClusterButton setTitleColor:[ArtistBuddy terahertzColor] forState:UIControlStateNormal];
        [_areClusterButton addTarget:self action:@selector(resizeIllIllRetrieveUnsafeAction:) forControlEvents:UIControlEventTouchUpInside];
        _areClusterButton.hidden = [ArtistBuddy emptyMillionDenseGigahertzBurstSkip];
    }
    return _areClusterButton;
}

- (void)itsDesignerOffsetHerPoloAction:(UIButton *)sender {
    if(self.navigationController.viewControllers.count > 1) {
        [self.view endEditing:YES];
        [self.navigationController popViewControllerAnimated:NO];
    }else {
        [self resizeIllIllRetrieveUnsafeAction:sender];
        [self dismissViewControllerAnimated:NO completion:nil];
    }
}

- (void)resizeIllIllRetrieveUnsafeAction:(UIButton *)sender {
    [[HeartBankManager shared] swapWetPrintWindow];
    [ArtistBuddy resizeIllIllRetrieveUnsafeAction];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.view.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
    self.view.layer.cornerRadius = 2;
    self.view.backgroundColor = [ArtistBuddy menRunSwitchMenuPrefixedColor];
    [self.view addSubview:self.verifyMaxButton];
    [self.view addSubview:self.areClusterButton];
    
    CGFloat fixSize = ArtistBuddy.lossFirmware.initiatedPen;
    [_verifyMaxButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.view).offset(ArtistBuddy.lossFirmware.waySevenOpt);
        make.size.mas_equalTo(CGSizeMake(fixSize, fixSize));
    }];
    [_areClusterButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(ArtistBuddy.lossFirmware.waySevenOpt);
        make.right.equalTo(self.view).offset(-ArtistBuddy.lossFirmware.waySevenOpt);
        make.size.mas_equalTo(CGSizeMake(fixSize, fixSize));
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(tipTrackStrongDecayDetectionDetailFrame:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(mouseHellmanRestoringAdoptRowSecurely:) name:UIKeyboardWillHideNotification object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.view.superview);
        make.size.mas_equalTo([ArtistBuddy potassiumDueContentsBeginEyeMetadataSize]);
    }];
}


- (void)tipTrackStrongDecayDetectionDetailFrame:(NSNotification *)notification {
    
    CGFloat duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] floatValue];
    
    
    CGRect keyboardFrame = [notification.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    
    UIWindow *keyWindow = [HeartBankManager shared].wasMeanMergeWindow;
    if (![keyWindow isMemberOfClass:NSClassFromString(ArtistBuddy.lossFirmware.startingTibetanCarbonSeekIdentifyRoom)]) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
        UIView *firstResponder = [keyWindow performSelector:@selector(firstResponder)];
#pragma clang diagnostic pop
        
        if (firstResponder  && [firstResponder isKindOfClass:UITextField.class]) {

            CGRect capRect = [keyWindow convertRect:firstResponder.frame fromView:firstResponder.superview];
            
            if ((capRect.origin.y + capRect.size.height) > keyboardFrame.origin.y) {
                CGFloat third = ((capRect.origin.y + capRect.size.height) - keyboardFrame.origin.y) + 10;
                
                sorting(self);
                [UIView animateWithDuration:duration animations:^{
                    notifyAll(self);
                    self.navigationController.view.transform = CGAffineTransformMakeTranslation(0, -third);
                }];
            }
        }
    }
}


- (void)mouseHellmanRestoringAdoptRowSecurely:(NSNotification *)notification{
    CGFloat duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] floatValue];
    sorting(self);
    [UIView animateWithDuration:duration animations:^{
        notifyAll(self);
        self.navigationController.view.transform = CGAffineTransformIdentity;
    }];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super touchesEnded:touches withEvent:event];
    [self.view endEditing:YES];
}

- (void)compressBeaconShipmentFathomsBecome:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [self.view endEditing:YES];
}

- (void)dealloc {
    
    [self.view endEditing:YES];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillHideNotification object:nil];
}

@end
