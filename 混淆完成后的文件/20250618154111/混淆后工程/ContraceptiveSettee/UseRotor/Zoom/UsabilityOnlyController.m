






#import "UsabilityOnlyController.h"
#import "TagTreeViewController.h"

@interface UsabilityOnlyController ()

@end

@implementation UsabilityOnlyController


- (BOOL)shouldAutorotate {
    return self.topViewController.shouldAutorotate;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return self.topViewController.supportedInterfaceOrientations;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.interactivePopGestureRecognizer.enabled = NO;
    [self setNavigationBarHidden:YES];
    self.view.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:.3];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    
    CGPoint point = [[touches anyObject] locationInView:self.view];
    
    UIView *funView = self.topViewController.view;
    
    point = [funView.layer convertPoint:point fromLayer:self.view.layer];
    
    TagTreeViewController *vc = (TagTreeViewController *)self.topViewController;
    if (![funView.layer containsPoint:point]) {
        [vc compressBeaconShipmentFathomsBecome:touches withEvent:event];
    }else{  
        [super touchesBegan:touches withEvent:event];
    }
}

- (void)pushViewController:(TagTreeViewController *)viewController animated:(BOOL)animated
{
    if (self.childViewControllers.count > 0) {
        viewController.verifyMaxButton.hidden = NO;
    }else {
        viewController.verifyMaxButton.hidden = YES;
    }
    [super pushViewController:viewController animated:animated];
    
}
- (void)dealloc {
    
}
@end
