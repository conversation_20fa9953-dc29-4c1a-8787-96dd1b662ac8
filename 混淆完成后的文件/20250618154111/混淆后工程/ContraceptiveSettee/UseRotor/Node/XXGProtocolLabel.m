






#import "XXGProtocolLabel.h"
#import "ArtistBuddy.h"
#import "UIImage+CupImage.h"

@implementation XXGProtocolLabel

+ (XXGProtocolLabel *)generatorMileLabel {
    return [self generatorMileLabel:YES];
}

+ (XXGProtocolLabel *)generatorMileLabel:(BOOL)isCheckBox {
    
    XXGProtocolLabel *label = [[XXGProtocolLabel alloc] init];
    label.numberOfLines = 0;
    label.textAlignment = NSTextAlignmentCenter;
    label.textColor = [UIColor lightGrayColor];
    label.font = [UIFont systemFontOfSize:12];
    label.userInteractionEnabled = YES; 

    NSAttributedString *sevenRefreshPurpleUplinkSleep = nil;
    if (isCheckBox) {
        
        NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
        UIImage *varianceImage = [[UIImage injectionCheckingSmoothingWonMobileName:ArtistBuddy.lossFirmware.statementStylisticCenteredIndexingTheOpposite] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        attachment.image = varianceImage; 
        
        attachment.bounds = CGRectMake(0, -5, 20, 20);
        sevenRefreshPurpleUplinkSleep = [NSAttributedString attributedStringWithAttachment:attachment];
    }

    
    NSString *text = ArtistBuddy.rowKinRedoDisk.leaseSunMale;
    NSMutableAttributedString *yearsNominally = [[NSMutableAttributedString alloc] initWithString:text];
    
    
    NSRange goalNativeHand = [text rangeOfString:ArtistBuddy.rowKinRedoDisk.popDustWayRest];
    if (goalNativeHand.location != NSNotFound) {
        [yearsNominally addAttribute:NSForegroundColorAttributeName value:[ArtistBuddy sunDublinColor] range:goalNativeHand];
        [yearsNominally addAttribute:NSUnderlineStyleAttributeName value:@(NSUnderlineStyleSingle) range:goalNativeHand];
    }

    
    NSMutableAttributedString *discoveryPreservedExecuteShortcutSlovak = [[NSMutableAttributedString alloc] init];
    if (sevenRefreshPurpleUplinkSleep) {
        [discoveryPreservedExecuteShortcutSlovak appendAttributedString:sevenRefreshPurpleUplinkSleep];
    }
    [discoveryPreservedExecuteShortcutSlovak appendAttributedString:yearsNominally];
    
    label.attributedText = discoveryPreservedExecuteShortcutSlovak;
    
    
    UITapGestureRecognizer *defineEast = [[UITapGestureRecognizer alloc] initWithTarget:label action:@selector(leaseLowercaseBikePortalMove:)];
    [label addGestureRecognizer:defineEast];
    
    return label;
}

- (void)setHairMapCupForm:(BOOL)hairMapCupForm {
    _hairMapCupForm = !hairMapCupForm;
    [self slantSpanExerciseIrishShutdownMeterLabel:self];
}

- (void)leaseLowercaseBikePortalMove:(UITapGestureRecognizer *)defineEast {
    XXGProtocolLabel *label = (XXGProtocolLabel *)defineEast.view;
    if (!label.attributedText) return;
    
    
    NSTextStorage *textStorage = [[NSTextStorage alloc] initWithAttributedString:label.attributedText];
    NSLayoutManager *layoutManager = [[NSLayoutManager alloc] init];
    NSTextContainer *textContainer = [[NSTextContainer alloc] initWithSize:label.bounds.size];
    
    textContainer.lineFragmentPadding = 0;
    textContainer.maximumNumberOfLines = label.numberOfLines;
    textContainer.lineBreakMode = label.lineBreakMode;
    
    [textStorage addLayoutManager:layoutManager];
    [layoutManager addTextContainer:textContainer];
    
    
    [layoutManager ensureLayoutForTextContainer:textContainer];
    
    
    CGPoint boldThatNow = [defineEast locationInView:label];
    CGRect usedRect = [layoutManager usedRectForTextContainer:textContainer];
    CGPoint textContainerOrigin = CGPointMake(
        (label.bounds.size.width - usedRect.size.width) / 2,   
        (label.bounds.size.height - usedRect.size.height) / 2  
    );
    
    
    CGPoint locationInTextContainer = CGPointMake(
        boldThatNow.x - textContainerOrigin.x,
        boldThatNow.y - textContainerOrigin.y
    );
    
    
    __block BOOL isImageTapped = NO;
    [label.attributedText enumerateAttribute:NSAttachmentAttributeName
                                    inRange:NSMakeRange(0, label.attributedText.length)
                                    options:0
                                 usingBlock:^(id value, NSRange range, BOOL *stop) {
        if ([value isKindOfClass:[NSTextAttachment class]]) {
            
            NSRange glyphRange;
            [layoutManager glyphRangeForCharacterRange:range actualCharacterRange:&glyphRange];
            
            
            CGRect belowRect = [layoutManager boundingRectForGlyphRange:glyphRange
                                                      inTextContainer:textContainer];
            
            
            CGRect millUniformRect = CGRectOffset(belowRect, textContainerOrigin.x, textContainerOrigin.y);
            
            
            if (CGRectContainsPoint(millUniformRect, boldThatNow)) {
                isImageTapped = YES;
                *stop = YES;
            }
        }
    }];
    
    if (isImageTapped) {
        
        
        [self slantSpanExerciseIrishShutdownMeterLabel:label];
        return;
    }
    
    
    NSUInteger characterIndex = [layoutManager characterIndexForPoint:locationInTextContainer
                                                    inTextContainer:textContainer
                           fractionOfDistanceBetweenInsertionPoints:NULL];
    
    NSString *fullText = label.attributedText.string;
    NSRange goalNativeHand = [fullText rangeOfString:ArtistBuddy.rowKinRedoDisk.popDustWayRest];
    
    if (characterIndex != NSNotFound && NSLocationInRange(characterIndex, goalNativeHand)) {
        
        
        if (self.desktopMisplacedUndefinedFunnelHalftoneDither) {
            self.desktopMisplacedUndefinedFunnelHalftoneDither();
        }
    }
}


- (void)slantSpanExerciseIrishShutdownMeterLabel:(XXGProtocolLabel *)label {
    NSMutableAttributedString *attributedText = [label.attributedText mutableCopy];
    __block BOOL licenseYou = NO;
    
    [attributedText enumerateAttribute:NSAttachmentAttributeName
                             inRange:NSMakeRange(0, attributedText.length)
                             options:0
                          usingBlock:^(NSTextAttachment *oldAttachment, NSRange range, BOOL *stop) {
        if (![oldAttachment isKindOfClass:[NSTextAttachment class]]) return;
        
        
        BOOL telephoto = !_hairMapCupForm;
        
        
        NSTextAttachment *workoutsPower = [[NSTextAttachment alloc] init];
        
        
        UIColor *prefixColor = telephoto ? [ArtistBuddy sunDublinColor]: UIColor.lightGrayColor;
        UIImage *sawFiberImage = [UIImage injectionCheckingSmoothingWonMobileName:telephoto ? ArtistBuddy.lossFirmware.penAllPubStriationColleagueBalance :ArtistBuddy.lossFirmware.statementStylisticCenteredIndexingTheOpposite];
        
        
        workoutsPower.image = [[sawFiberImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate]
                                imageWithTintColor:prefixColor];
        workoutsPower.bounds = oldAttachment.bounds;
        
        
        [attributedText removeAttribute:NSAttachmentAttributeName range:range];
        [attributedText addAttribute:NSAttachmentAttributeName value:workoutsPower range:range];
        
        _hairMapCupForm = telephoto;
        licenseYou = YES;
        *stop = YES;
    }];
    
    if (licenseYou) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [UIView transitionWithView:label
                              duration:0.3
                               options:UIViewAnimationOptionTransitionCrossDissolve
                            animations:^{
                                label.attributedText = attributedText;
                            } completion:nil];
            [label setNeedsDisplay];
        });
    }
}

@end
