






#import "BusForDrawView.h"
#import "HeartBankManager.h"
#import "ArtistBuddy.h"
#import "Masonry.h"

@interface BusForDrawView ()

@property (nonatomic, strong) UIView *floatingToolBackgroundView;
@property (nonatomic, strong) UIActivityIndicatorView *dateBedAirlineNibblesSoft;
@property (nonatomic, strong) UILabel *fatLoseLabel;
@end

@implementation BusForDrawView


static BusForDrawView *dividerSignerView = nil;



- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self todayView];
    }
    return self;
}
- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self todayView];
    }
    return self;
}
- (void)todayView {
    
    
    self.floatingToolBackgroundView = [UIView new];
    self.floatingToolBackgroundView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    self.floatingToolBackgroundView.layer.cornerRadius = 2.0;
    self.floatingToolBackgroundView.clipsToBounds = YES;
    [self addSubview:self.floatingToolBackgroundView];
    
    
    self.dateBedAirlineNibblesSoft = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleLarge];
    self.dateBedAirlineNibblesSoft.color = ArtistBuddy.sunDublinColor;
    [self.floatingToolBackgroundView addSubview:self.dateBedAirlineNibblesSoft];
    
    
    self.fatLoseLabel = [[UILabel alloc] init];
    self.fatLoseLabel.text = ArtistBuddy.rowKinRedoDisk.repeatWaySaw;
    self.fatLoseLabel.textColor = [UIColor whiteColor];
    self.fatLoseLabel.font = [UIFont systemFontOfSize:14];
    self.fatLoseLabel.numberOfLines = 0;
    self.fatLoseLabel.textAlignment = NSTextAlignmentCenter;
    [self.floatingToolBackgroundView addSubview:self.fatLoseLabel];
    
    
    [self.floatingToolBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(ArtistBuddy.lossFirmware.rankToolRound, ArtistBuddy.lossFirmware.rankToolRound));
        make.center.equalTo(self);
    }];
    
    [self.dateBedAirlineNibblesSoft mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(ArtistBuddy.lossFirmware.radixGeneric);
        make.centerX.equalTo(self.floatingToolBackgroundView.mas_centerX);
    }];
    
    [self.fatLoseLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.dateBedAirlineNibblesSoft.mas_bottom).offset(ArtistBuddy.lossFirmware.radixGeneric);
        make.centerX.equalTo(self.floatingToolBackgroundView.mas_centerX);
        make.left.equalTo(self.floatingToolBackgroundView.mas_left).offset(ArtistBuddy.lossFirmware.waySevenOpt);
        make.right.equalTo(self.floatingToolBackgroundView.mas_right).offset(-ArtistBuddy.lossFirmware.waySevenOpt);
    }];
    
    
    self.hidden = YES;
}



- (void)startAnimating {
    self.hidden = NO;
    [self.dateBedAirlineNibblesSoft startAnimating];
}

- (void)stopAnimating {
    [self.dateBedAirlineNibblesSoft stopAnimating];
    self.hidden = YES;
}

- (void)duePunjabiText:(NSString *)text {
    self.fatLoseLabel.text = text;
    
    
    CGFloat bookWidth = [text boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX)
                                          options:NSStringDrawingUsesLineFragmentOrigin
                                       attributes:@{NSFontAttributeName: self.fatLoseLabel.font}
                                          context:nil].size.width;
    UIWindow *window = [[HeartBankManager shared] wasMeanMergeWindow];
    CGFloat segmentWidth = MIN(MAX(120, bookWidth + 2 * 8), window.bounds.size.width);
    [self.floatingToolBackgroundView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(segmentWidth);
    }];
    
    [self layoutIfNeeded];
}


+ (void)wonExpiresDidWindow {
    [self scrollsRectumReplyUserPeriodicSetupText:ArtistBuddy.rowKinRedoDisk.repeatWaySaw];
}

+ (void)scrollsRectumReplyUserPeriodicSetupText:(NSString *)text {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIWindow *window = [[HeartBankManager shared] wasMeanMergeWindow];
        
        if (!dividerSignerView) {
            CGSize size = UIScreen.mainScreen.bounds.size;
            dividerSignerView = [[BusForDrawView alloc] initWithFrame:CGRectMake(0, 0, size.width, size.height)];
            dividerSignerView.center = window.center;
        }
        if (!dividerSignerView.superview) {
            [window addSubview:dividerSignerView];
        }
        [dividerSignerView duePunjabiText:text];
        [dividerSignerView startAnimating];
    });
}

+ (void)eighteenAcquireInverseMeanFooterWindow {
    dispatch_async(dispatch_get_main_queue(), ^{
        [dividerSignerView stopAnimating];
        [dividerSignerView removeFromSuperview];
        dividerSignerView = nil;
    });
}


+ (BusForDrawView *)helperThermalView:(UIView *)view {
    return [self helperThermalView:view withText:ArtistBuddy.rowKinRedoDisk.repeatWaySaw];
}

+ (BusForDrawView *)helperThermalView:(UIView *)view withText:(NSString *)text {
    __block BusForDrawView *tooRootView = nil;
    dispatch_async(dispatch_get_main_queue(), ^{
        
        tooRootView = [[BusForDrawView alloc] initWithFrame:CGRectMake(0, 0, view.frame.size.width, view.frame.size.height)];
        tooRootView.center = CGPointMake(CGRectGetMidX(view.bounds), CGRectGetMidY(view.bounds));
        [tooRootView duePunjabiText:text];
        [tooRootView startAnimating];
        [view addSubview:tooRootView];
    });
    return tooRootView;
}

+ (void)leastEnableRegularSubmittedTreeView:(UIView *)view {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        for (UIView *subview in view.subviews) {
            if ([subview isKindOfClass:[BusForDrawView class]]) {
                [(BusForDrawView *)subview stopAnimating];
                [subview removeFromSuperview];
            }
        }
    });
}

@end
