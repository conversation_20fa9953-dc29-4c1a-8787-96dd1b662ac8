






#import "WrongArrayFootButton.h"
#import "ArtistBuddy.h"
#import "UIImage+CupImage.h"
#import "UsabilityOnlyController.h"
#import "UIImage+CupImage.h"
#import "UIColor+BoxColor.h"

@implementation WrongArrayFootButton

- (instancetype)initMixRestoresViewController:(UIViewController *)viewController {
    self = [super init];
    if (self) {
        self.saySupplyFitViewController = viewController;
        [self offAdvisedClearKeysPercent];
    }
    return self;
}


- (void)offAdvisedClearKeysPercent {
    
    NSArray *gregorianAchievedDetectsSupportsOverride = [ElderNotationVolumeResourcesShortcut exactDaySolutionsCompositeFound:[MailNordic class]];
    
    
    NSString *currentCountryCode = [[NSLocale currentLocale] objectForKey:NSLocaleCountryCode];
    
    __block MailNordic *matchedCountry = nil;
    [gregorianAchievedDetectsSupportsOverride enumerateObjectsUsingBlock:^(MailNordic *country, NSUInteger idx, BOOL *stop) {
        if ([country.encryptedPopCode caseInsensitiveCompare:currentCountryCode] == NSOrderedSame) {
            matchedCountry = country;
            *stop = YES; 
        }
    }];
    self.strokedVisitAsleepReferenceOcclusion = matchedCountry;
    
    
    NSString *title = [NSString stringWithFormat:@"%@%@",ArtistBuddy.lossFirmware.nowFaxDashBit, matchedCountry.netBasqueCode];
    [self setTitle:title forState:UIControlStateNormal];
    
    
    UIImage *sawFiberImage = [UIImage injectionCheckingSmoothingWonMobileName:ArtistBuddy.lossFirmware.securityArcheryPersonRealmCupEscaped];
    
    
    CGSize targetImageSize = CGSizeMake(13, 13); 
    
    
    UIImage *scaledImage = [self armPingDragBitImage:sawFiberImage carAllowSize:targetImageSize];
    
    
    [self setImage:scaledImage forState:UIControlStateNormal];
    [self setImage:[scaledImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateHighlighted]; 
    
    
    self.imageView.contentMode = UIViewContentModeScaleAspectFit;
    
    
    self.semanticContentAttribute = UISemanticContentAttributeForceRightToLeft; 
    CGFloat spacing = 3.0; 
    self.imageEdgeInsets = UIEdgeInsetsMake(0, spacing, 0, -spacing);  
    self.titleEdgeInsets = UIEdgeInsetsMake(0, -spacing, 0, spacing);   
    
    
    [self setBackgroundImage:[UIImage rectangleQuietColor:[ArtistBuddy.sunDublinColor gallonManagerBookCallStorylineTotal:8]] forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage rectangleQuietColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]]
                   forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont systemFontOfSize:16];
    self.layer.cornerRadius = 2.f;
    self.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMinXMaxYCorner;
    self.layer.masksToBounds = YES;
    
    
    self.contentEdgeInsets = UIEdgeInsetsMake(8, 12, 8, 12); 
    
    
    [self sizeToFit];
    
    
    [self addTarget:self action:@selector(anyDanceRetClicked) forControlEvents:UIControlEventTouchUpInside];
}


- (UIImage *)armPingDragBitImage:(UIImage *)image carAllowSize:(CGSize)targetSize {
    
    UIGraphicsBeginImageContextWithOptions(targetSize, NO, 0.0);
    
    
    CGFloat uploadFile = targetSize.width / image.size.width;
    CGFloat versionMail = targetSize.height / image.size.height;
    CGFloat scaleFactor = MIN(uploadFile, versionMail);
    
    
    CGRect supplyRect = CGRectMake(0, 0,
                                  image.size.width * scaleFactor,
                                  image.size.height * scaleFactor);
    
    
    CGPoint origin = CGPointMake((targetSize.width - supplyRect.size.width) / 2.0,
                               (targetSize.height - supplyRect.size.height) / 2.0);
    [image drawInRect:CGRectMake(origin.x, origin.y,
                                supplyRect.size.width,
                                supplyRect.size.height)];
    
    UIImage *eraImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return eraImage;
}


- (void)anyDanceRetClicked {
    RingAgeDegradedEyeTextualAppearingViewController *vc = [RingAgeDegradedEyeTextualAppearingViewController new];
    vc.unitVisitDelegate = self;
    [self.saySupplyFitViewController.navigationController pushViewController:vc animated:NO];
}

- (void)sixShowersWaxTopRebusEasyTaggingFork:(MailNordic *)country {
    NSString *title = [NSString stringWithFormat:@"%@%@",ArtistBuddy.lossFirmware.nowFaxDashBit, country.netBasqueCode];
    [self setTitle:title forState:UIControlStateNormal];
    self.strokedVisitAsleepReferenceOcclusion = country;
}

- (void)dealloc {
    
}
@end
