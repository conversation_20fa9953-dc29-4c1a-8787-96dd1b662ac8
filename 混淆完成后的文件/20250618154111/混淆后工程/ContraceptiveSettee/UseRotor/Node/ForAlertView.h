






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN


typedef void(^TheDeclineExplicitMinderClearedVendor)(NSInteger buttonIndex);

@interface ForAlertView : UIView



+ (void)tagsTapHyphensThiaminAdvisedPrevents:(NSString *_Nullable)title
                        message:(NSString *)message
                   disorderNear:(NSArray<NSString *> *)disorderNear
                     completion:(TheDeclineExplicitMinderClearedVendor _Nullable)completion;

+ (void)tagsTapHyphensThiaminAdvisedPrevents:(NSString *_Nullable)title message:(NSString *)message completion:(TheDeclineExplicitMinderClearedVendor _Nullable)completion;


@end

NS_ASSUME_NONNULL_END
