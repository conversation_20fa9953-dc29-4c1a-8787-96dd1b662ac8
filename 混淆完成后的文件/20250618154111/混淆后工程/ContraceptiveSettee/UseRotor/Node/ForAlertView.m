






#import "ForAlertView.h"
#import "ArtistBuddy.h"
#import "HeartBankManager.h"
#import "Masonry.h"

#define sorting(obj) __weak typeof(obj) weak##obj = obj;
#define notifyAll(obj) __strong typeof(obj) obj = weak##obj;

@interface ForAlertView()

@property (nonatomic, strong) UIView *secondsHelpersView;
@property (nonatomic, copy) TheDeclineExplicitMinderClearedVendor completion;
@property (nonatomic, strong) UIStackView *stairSuchFarView;

@end

@implementation ForAlertView

- (void)dealloc {
    
}

- (instancetype)initWithFrame:(CGRect)frame
                          title:(NSString *)title
                        message:(NSString *)message
                   disorderNear:(NSArray<NSString *> *)disorderNear
                     completion:(TheDeclineExplicitMinderClearedVendor)completion {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
        self.completion = completion;
        
        
        self.secondsHelpersView = [[UIView alloc] init];
        self.secondsHelpersView.backgroundColor = [ArtistBuddy sunDublinColor];
        self.secondsHelpersView.layer.cornerRadius = 8.0;
        self.secondsHelpersView.clipsToBounds = YES;
        self.secondsHelpersView.translatesAutoresizingMaskIntoConstraints = NO;
        [self addSubview:self.secondsHelpersView];
        
        
        [NSLayoutConstraint activateConstraints:@[
            [self.secondsHelpersView.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
            [self.secondsHelpersView.centerYAnchor constraintEqualToAnchor:self.centerYAnchor],
            [self.secondsHelpersView.widthAnchor constraintEqualToConstant:270]
        ]];
        
        
        UIView *previousView = nil;
        CGFloat optStormTouchesHourCampaign = 20;
        
        
        if (title.length > 0) {
            UILabel *titleLabel = [[UILabel alloc] init];
            titleLabel.text = title;
            titleLabel.textColor = UIColor.whiteColor;
            titleLabel.font = [UIFont boldSystemFontOfSize:18];
            titleLabel.textAlignment = NSTextAlignmentCenter;
            titleLabel.numberOfLines = 0;
            titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
            [self.secondsHelpersView addSubview:titleLabel];
            
            [NSLayoutConstraint activateConstraints:@[
                [titleLabel.topAnchor constraintEqualToAnchor:self.secondsHelpersView.topAnchor constant:optStormTouchesHourCampaign],
                [titleLabel.leadingAnchor constraintEqualToAnchor:self.secondsHelpersView.leadingAnchor constant:16],
                [titleLabel.trailingAnchor constraintEqualToAnchor:self.secondsHelpersView.trailingAnchor constant:-16]
            ]];
            
            previousView = titleLabel;
        }
        
        
        if (message.length > 0) {
            UILabel *walkingLabel = [[UILabel alloc] init];
            walkingLabel.text = message;
            walkingLabel.textColor = UIColor.whiteColor;
            walkingLabel.font = [UIFont systemFontOfSize:15];
            walkingLabel.textAlignment = NSTextAlignmentCenter;
            walkingLabel.numberOfLines = 0;
            walkingLabel.translatesAutoresizingMaskIntoConstraints = NO;
            [self.secondsHelpersView addSubview:walkingLabel];
            
            NSLayoutYAxisAnchor *topAnchor = previousView ? previousView.bottomAnchor : self.secondsHelpersView.topAnchor;
            CGFloat blackMetal = previousView ? 10 : optStormTouchesHourCampaign;
            [NSLayoutConstraint activateConstraints:@[
                [walkingLabel.topAnchor constraintEqualToAnchor:topAnchor constant:blackMetal],
                [walkingLabel.leadingAnchor constraintEqualToAnchor:self.secondsHelpersView.leadingAnchor constant:16],
                [walkingLabel.trailingAnchor constraintEqualToAnchor:self.secondsHelpersView.trailingAnchor constant:-16]
            ]];
            previousView = walkingLabel;
        }
        
        
        self.stairSuchFarView = [[UIStackView alloc] init];
        self.stairSuchFarView.axis = UILayoutConstraintAxisVertical;
        self.stairSuchFarView.spacing = 1;  
        self.stairSuchFarView.distribution = UIStackViewDistributionFillEqually;
        self.stairSuchFarView.translatesAutoresizingMaskIntoConstraints = NO;
        [self.secondsHelpersView addSubview:self.stairSuchFarView];
        
        
        NSLayoutYAxisAnchor *cycleThroughCalciumAskPerforms = previousView ? previousView.bottomAnchor : self.secondsHelpersView.topAnchor;
        CGFloat buttonsTopPadding = previousView ? optStormTouchesHourCampaign : optStormTouchesHourCampaign;
        
        [NSLayoutConstraint activateConstraints:@[
            [self.stairSuchFarView.topAnchor constraintEqualToAnchor:cycleThroughCalciumAskPerforms constant:buttonsTopPadding],
            [self.stairSuchFarView.leadingAnchor constraintEqualToAnchor:self.secondsHelpersView.leadingAnchor],
            [self.stairSuchFarView.trailingAnchor constraintEqualToAnchor:self.secondsHelpersView.trailingAnchor],
            [self.stairSuchFarView.bottomAnchor constraintEqualToAnchor:self.secondsHelpersView.bottomAnchor]
        ]];
        
        
       
       if (disorderNear.count == 2) {
           
           self.stairSuchFarView = [[UIStackView alloc] init];
           self.stairSuchFarView.axis = UILayoutConstraintAxisHorizontal;
           self.stairSuchFarView.distribution = UIStackViewDistributionFillEqually;
           self.stairSuchFarView.spacing = 1;  
           self.stairSuchFarView.translatesAutoresizingMaskIntoConstraints = NO;
           [self.secondsHelpersView addSubview:self.stairSuchFarView];
           
           NSLayoutYAxisAnchor *cycleThroughCalciumAskPerforms = previousView ? previousView.bottomAnchor : self.secondsHelpersView.topAnchor;
           [NSLayoutConstraint activateConstraints:@[
               [self.stairSuchFarView.topAnchor constraintEqualToAnchor:cycleThroughCalciumAskPerforms constant:optStormTouchesHourCampaign],
               [self.stairSuchFarView.leadingAnchor constraintEqualToAnchor:self.secondsHelpersView.leadingAnchor],
               [self.stairSuchFarView.trailingAnchor constraintEqualToAnchor:self.secondsHelpersView.trailingAnchor],
               [self.stairSuchFarView.bottomAnchor constraintEqualToAnchor:self.secondsHelpersView.bottomAnchor]
           ]];
           
           
           for (NSInteger i = 0; i < disorderNear.count; i++) {
               NSString *canRoman = disorderNear[i];
               UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
               [button setTitle:canRoman forState:UIControlStateNormal];
               button.titleLabel.font = [UIFont systemFontOfSize:17];
               [button setTitleColor:[ArtistBuddy sunDublinColor] forState:UIControlStateNormal];
               [button setTitleColor:UIColor.lightGrayColor forState:UIControlStateHighlighted];
               button.backgroundColor = [UIColor whiteColor];
               button.tag = i;
               [button addTarget:self action:@selector(maskChecking:) forControlEvents:UIControlEventTouchUpInside];
               button.translatesAutoresizingMaskIntoConstraints = NO;
               [button.heightAnchor constraintEqualToConstant:40].active = YES;
               [self.stairSuchFarView addArrangedSubview:button];
           }
       } else {
           
           self.stairSuchFarView = [[UIStackView alloc] init];
           self.stairSuchFarView.axis = UILayoutConstraintAxisVertical;
           self.stairSuchFarView.spacing = 1;
           self.stairSuchFarView.distribution = UIStackViewDistributionFillEqually;
           self.stairSuchFarView.translatesAutoresizingMaskIntoConstraints = NO;
           [self.secondsHelpersView addSubview:self.stairSuchFarView];
           
           NSLayoutYAxisAnchor *cycleThroughCalciumAskPerforms = previousView ? previousView.bottomAnchor : self.secondsHelpersView.topAnchor;
           [NSLayoutConstraint activateConstraints:@[
               [self.stairSuchFarView.topAnchor constraintEqualToAnchor:cycleThroughCalciumAskPerforms constant:optStormTouchesHourCampaign],
               [self.stairSuchFarView.leadingAnchor constraintEqualToAnchor:self.secondsHelpersView.leadingAnchor],
               [self.stairSuchFarView.trailingAnchor constraintEqualToAnchor:self.secondsHelpersView.trailingAnchor],
               [self.stairSuchFarView.bottomAnchor constraintEqualToAnchor:self.secondsHelpersView.bottomAnchor]
           ]];
           
           for (NSInteger i = 0; i < disorderNear.count; i++) {
               NSString *canRoman = disorderNear[i];
               UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
               [button setTitle:canRoman forState:UIControlStateNormal];
               button.titleLabel.font = [UIFont systemFontOfSize:17];
               [button setTitleColor:[ArtistBuddy sunDublinColor] forState:UIControlStateNormal];
               [button setTitleColor:UIColor.lightGrayColor forState:UIControlStateHighlighted];
               button.backgroundColor = [UIColor whiteColor];
               button.tag = i;
               [button addTarget:self action:@selector(maskChecking:) forControlEvents:UIControlEventTouchUpInside];
               button.translatesAutoresizingMaskIntoConstraints = NO;
               [button.heightAnchor constraintEqualToConstant:40].active = YES;
               [self.stairSuchFarView addArrangedSubview:button];
           }
       }
    }
    return self;
}

- (void)maskChecking:(UIButton *)sender {
    if (self.completion) {
        self.completion(sender.tag);
    }
    
    
    [UIView animateWithDuration:0.25 animations:^{
        self.alpha = 0;
    } completion:^(BOOL finished) {
        [HeartBankManager.shared swapWetPrintWindow];
    }];
}

+ (void)tagsTapHyphensThiaminAdvisedPrevents:(NSString *)title
                        message:(NSString *)message
                   disorderNear:(NSArray<NSString *> *)disorderNear
                     completion:(TheDeclineExplicitMinderClearedVendor)completion {
    
    ForAlertView *polar = [[ForAlertView alloc] initWithFrame:[UIScreen mainScreen].bounds
                                                 title:title
                                               message:message
                                          disorderNear:disorderNear
                                            completion:completion];
    
    
    [HeartBankManager.shared titleTryCapableRegionMostlyBlusteryView:polar];
    
    
    polar.alpha = 0.0;
    [UIView animateWithDuration:0.25 animations:^{
        polar.alpha = 1.0;
    }];
}

+ (void)tagsTapHyphensThiaminAdvisedPrevents:(NSString *)title message:(NSString *)message completion:(TheDeclineExplicitMinderClearedVendor)completion {
    [self tagsTapHyphensThiaminAdvisedPrevents:title message:message disorderNear:@[ArtistBuddy.rowKinRedoDisk.enhance] completion:completion];
}

@end
