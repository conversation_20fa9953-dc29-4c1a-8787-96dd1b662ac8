






#import "ReadTiedKinButton.h"
#import "ArtistBuddy.h"
#import "UIImage+CupImage.h"

@interface ReadTiedKinButton ()


@property (nonatomic, strong) NSTimer *minAreNowDolbyTimer;

@property (nonatomic, assign) NSInteger subscribeHockeyTruncatePolarUnsignedRandom;

@property (nonatomic, copy) NSString *mildIndexedCustomFlemishDolby;

@end

@implementation ReadTiedKinButton

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self offAdvisedClearKeysPercent];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self offAdvisedClearKeysPercent];
    }
    return self;
}


- (void)offAdvisedClearKeysPercent {
    
    self.cubeRenderAgeAuthorityElectricExported = 60;
    self.mildIndexedCustomFlemishDolby = ArtistBuddy.rowKinRedoDisk.skinRemovalSixCellAppliesYouCode;
    [self setTitle:self.mildIndexedCustomFlemishDolby forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage rectangleQuietColor:ArtistBuddy.sunDublinColor] forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage rectangleQuietColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]] forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont systemFontOfSize:16];
    self.layer.cornerRadius = 2.f;
    self.layer.masksToBounds = YES;
    
    self.contentEdgeInsets = UIEdgeInsetsMake(0, 5, 0, 5);
    
    [self sizeToFit];
    
    
    [self addTarget:self action:@selector(anyDanceRetClicked) forControlEvents:UIControlEventTouchUpInside];
}


- (void)anyDanceRetClicked {
    [self onlineLexicalUniversalLegibleEasy];
    if (self.cutJoiningMayAction) {
        self.cutJoiningMayAction();
    }
}


- (void)onlineLexicalUniversalLegibleEasy {
    self.enabled = NO;
    self.subscribeHockeyTruncatePolarUnsignedRandom = self.cubeRenderAgeAuthorityElectricExported;
    [self differentContainsRenderedClockWrappersSucceeded];
    
    
    self.minAreNowDolbyTimer = [NSTimer scheduledTimerWithTimeInterval:1.0
                                                                 target:self
                                                               selector:@selector(imperialObjectGroupCentralsGender:)
                                                               userInfo:nil
                                                                repeats:YES];
}


- (void)imperialObjectGroupCentralsGender:(NSTimer *)timer {
    self.subscribeHockeyTruncatePolarUnsignedRandom--;
    if (self.subscribeHockeyTruncatePolarUnsignedRandom <= 0) {
        [self trackOpaqueAccordingAnyView];
    } else {
        [self differentContainsRenderedClockWrappersSucceeded];
    }
}


- (void)differentContainsRenderedClockWrappersSucceeded {
    NSString *title = [NSString stringWithFormat:@"%@(%ld)",ArtistBuddy.rowKinRedoDisk.prettySmallestContactElasticModifierDerivedCode, (long)self.subscribeHockeyTruncatePolarUnsignedRandom];
    [self setTitle:title forState:UIControlStateDisabled];
}


- (void)trackOpaqueAccordingAnyView {
    [self.minAreNowDolbyTimer invalidate];
    self.minAreNowDolbyTimer = nil;
    self.enabled = YES;
    [self setTitle:self.mildIndexedCustomFlemishDolby forState:UIControlStateNormal];
}

- (void)dealloc {
    
    [self.minAreNowDolbyTimer invalidate];
    self.minAreNowDolbyTimer = nil;
}

@end
