






#import "EnergyPotassiumDelaySkinFeatCell.h"
#import "ArtistBuddy.h"
#import "Masonry.h"

@implementation EnergyPotassiumDelaySkinFeatCell

-(id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self){
        
        self.backgroundColor = UIColor.whiteColor;
        self.contentView.backgroundColor = UIColor.whiteColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        
        self.bigPronounView = [UIImageView new];
        self.bigPronounView.tintColor = [ArtistBuddy sunDublinColor];
        self.bigPronounView.layer.cornerRadius = 15;
        [self.contentView addSubview:self.bigPronounView];
        [self.bigPronounView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(ArtistBuddy.lossFirmware.decodeScreen);
            make.centerY.mas_equalTo(self);
            make.width.height.mas_equalTo(ArtistBuddy.lossFirmware.initiatedPen);
        }];
        
        
        self.eastUsesName = [UILabel new];
        self.eastUsesName.font = [UIFont boldSystemFontOfSize:16];
        self.eastUsesName.textColor = UIColor.darkGrayColor;
        [self.contentView addSubview:self.eastUsesName];
        [self.eastUsesName mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.bigPronounView.mas_right).offset(ArtistBuddy.lossFirmware.decodeScreen);
            make.bottom.equalTo(self.contentView.mas_centerY);
        }];
        
        
        self.cervicalRowTime = [UILabel new];
        self.cervicalRowTime.font =  [UIFont systemFontOfSize:11];
        self.cervicalRowTime.textColor = UIColor.grayColor;
        [self.contentView addSubview:self.cervicalRowTime];
        [self.cervicalRowTime mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_centerY).offset(ArtistBuddy.lossFirmware.littleSpeak);
            make.left.equalTo(self.eastUsesName);
        }];
    }
    return self;
}

@end
