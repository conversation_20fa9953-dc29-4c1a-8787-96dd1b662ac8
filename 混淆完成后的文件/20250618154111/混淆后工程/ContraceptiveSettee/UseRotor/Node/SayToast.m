






#import "SayToast.h"
#import "HeartBankManager.h"


static UIColor *proxyMoleSafariSmoothedAzimuthColor = nil;
static UIColor *whoContainedColor = nil;
static UIFont *problemTrash = nil;
static CGFloat notationRedoneRadius = 6.0;
static UIEdgeInsets popMixKernelScaleSigmaPair = {10, 16, 10, 16};

@interface SayToast()
@property (nonatomic, strong) UILabel *walkingLabel;
@property (nonatomic, strong) NSTimer *keyStayTimer;
@property (nonatomic, assign) VendorJoinMutableBringBackup position;
@end

@implementation SayToast


- (instancetype)initMeanMessage:(NSString *)message {
    self = [super initWithFrame:CGRectZero];
    if (self) {
        self.userInteractionEnabled = NO;
        self.backgroundColor = UIColor.clearColor;
        
        
        UIView *container = [UIView new];
        container.backgroundColor = proxyMoleSafariSmoothedAzimuthColor ?:
            [[UIColor blackColor] colorWithAlphaComponent:0.85];
        container.layer.cornerRadius = notationRedoneRadius;
        container.clipsToBounds = YES;
        container.translatesAutoresizingMaskIntoConstraints = NO;
        [self addSubview:container];
        
        
        _walkingLabel = [UILabel new];
        _walkingLabel.text = message;
        _walkingLabel.textColor = whoContainedColor ?: UIColor.whiteColor;
        _walkingLabel.font = problemTrash ?: [UIFont systemFontOfSize:14];
        _walkingLabel.textAlignment = NSTextAlignmentCenter;
        _walkingLabel.numberOfLines = 0;
        _walkingLabel.translatesAutoresizingMaskIntoConstraints = NO;
        [container addSubview:_walkingLabel];
        
        
        [NSLayoutConstraint activateConstraints:@[
            
            [container.leadingAnchor constraintEqualToAnchor:_walkingLabel.leadingAnchor
                                                   constant:-popMixKernelScaleSigmaPair.left],
            [container.trailingAnchor constraintEqualToAnchor:_walkingLabel.trailingAnchor
                                                    constant:popMixKernelScaleSigmaPair.right],
            [container.topAnchor constraintEqualToAnchor:_walkingLabel.topAnchor
                                              constant:-popMixKernelScaleSigmaPair.top],
            [container.bottomAnchor constraintEqualToAnchor:_walkingLabel.bottomAnchor
                                                 constant:popMixKernelScaleSigmaPair.bottom],
            
            
            [container.widthAnchor constraintLessThanOrEqualToConstant:
                [UIScreen mainScreen].bounds.size.width - 40]
        ]];
    }
    return self;
}


+ (void)show:(NSString *)message
    duration:(NSTimeInterval)duration
    position:(VendorJoinMutableBringBackup)position
{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        SayToast *build = [[SayToast alloc] initMeanMessage:message];
        build.position = position;
        [build meterFloorAnimationReceiverThreads];
        [build boostFocalEditReturningDecay:duration];
    });
}

- (void)boostFocalEditReturningDecay:(NSTimeInterval)duration {
    UIWindow *window = [HeartBankManager.shared wasMeanMergeWindow];
    [window addSubview:self];
    
    
    self.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.leadingAnchor constraintEqualToAnchor:window.leadingAnchor],
        [self.trailingAnchor constraintEqualToAnchor:window.trailingAnchor],
        [self.topAnchor constraintEqualToAnchor:window.topAnchor],
        [self.bottomAnchor constraintEqualToAnchor:window.bottomAnchor]
    ]];
    
    
    [self sundaneseCloudyAffineHangAloneAnimation];
    
    
    if (duration > 0) {
        __weak typeof(self) weakSelf = self;
        self.keyStayTimer = [NSTimer scheduledTimerWithTimeInterval:duration repeats:YES block:^(NSTimer * _Nonnull timer) {
            [weakSelf dismiss];
        }];
    }
}

- (void)dismiss {
    [self.keyStayTimer invalidate];
    [self thresholdDashIntrinsicBoxBurnInterlaceWorking:^{
        [self removeFromSuperview];
    }];
}


- (void)sundaneseCloudyAffineHangAloneAnimation {
    CGAffineTransform transform;
    switch (self.position) {
        case StorylineBitHandballForceDay:
            transform = CGAffineTransformMakeTranslation(0, -100);
            break;
        case KelvinSquareAgeWelshSplitUppercase:
            transform = CGAffineTransformMakeTranslation(0, 100);
            break;
        default:
            transform = CGAffineTransformMakeScale(0.8, 0.8);
            break;
    }
    
    self.alpha = 0;
    self.walkingLabel.superview.transform = transform;
    
    [UIView animateWithDuration:0.3
                          delay:0
         usingSpringWithDamping:0.7
          initialSpringVelocity:0.1
                        options:UIViewAnimationOptionCurveEaseOut
                     animations:^{
        self.alpha = 1;
        self.walkingLabel.superview.transform = CGAffineTransformIdentity;
    } completion:nil];
}

- (void)thresholdDashIntrinsicBoxBurnInterlaceWorking:(void(^)(void))completion {
    CGAffineTransform transform;
    switch (self.position) {
        case StorylineBitHandballForceDay:
            transform = CGAffineTransformMakeTranslation(0, -self.walkingLabel.superview.frame.size.height - 50);
            break;
        case KelvinSquareAgeWelshSplitUppercase:
            transform = CGAffineTransformMakeTranslation(0, self.walkingLabel.superview.frame.size.height + 50);
            break;
        default:
            transform = CGAffineTransformMakeScale(0.8, 0.8);
            break;
    }
    
    [UIView animateWithDuration:0.25
                     animations:^{
        self.alpha = 0;
        self.walkingLabel.superview.transform = transform;
    } completion:^(BOOL finished) {
        if (completion) completion();
    }];
}


- (void)meterFloorAnimationReceiverThreads {
    UIView *container = self.walkingLabel.superview;
    
    
    switch (self.position) {
        case StorylineBitHandballForceDay: {
            [container.topAnchor constraintEqualToAnchor:self.safeAreaLayoutGuide.topAnchor
                                               constant:30].active = YES;
            break;
        }
        case MalayalamYellowGreekOddFlagCenter: {
            [container.centerYAnchor constraintEqualToAnchor:self.centerYAnchor].active = YES;
            break;
        }
        case KelvinSquareAgeWelshSplitUppercase: {
            [container.bottomAnchor constraintEqualToAnchor:self.safeAreaLayoutGuide.bottomAnchor
                                                  constant:-30].active = YES;
            break;
        }
    }
    
    
    [container.centerXAnchor constraintEqualToAnchor:self.centerXAnchor].active = YES;
}


+ (void)collectPackFreestyleMountMostlyBelowColor:(UIColor *)color {
    proxyMoleSafariSmoothedAzimuthColor = color;
}

+ (void)sawPanFocalCapColor:(UIColor *)color {
    whoContainedColor = color;
}

+ (void)dayHitForkChat:(UIFont *)font {
    problemTrash = font;
}

+ (void)locationPrintedWorldVeryDenyRadius:(CGFloat)radius {
    notationRedoneRadius = radius;
}


+ (void)fitness:(NSString *)message {
    [self show:message duration:2.0 position:StorylineBitHandballForceDay];
}

+ (void)sizeCenter:(NSString *)message {
    [self show:message duration:2.0 position:MalayalamYellowGreekOddFlagCenter];
}

+ (void)zipBigClip:(NSString *)message {
    [self show:message duration:2.0 position:KelvinSquareAgeWelshSplitUppercase];
}

- (void)dealloc {
    
}

@end
