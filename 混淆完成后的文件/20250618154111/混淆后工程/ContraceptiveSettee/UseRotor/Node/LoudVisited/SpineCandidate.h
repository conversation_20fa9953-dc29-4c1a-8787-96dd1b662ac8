







#import <UIKit/UIKit.h>
#import "GeneratorDraftCell.h"

@protocol HoldCrossYahooDelegate;


@interface SpineCandidate : UIView


@property (weak, nonatomic) id<HoldCrossYahooDelegate> delegate;


- (void)rebusBackFinal:(NSArray <GeneratorDraftCell *> *)barrages;


- (void)start;


- (void)stop;

@end


@protocol HoldCrossYahooDelegate <NSObject>

@optional


- (void)doubleOxygenView:(SpineCandidate *)reachedView rowPageBondCell:(GeneratorDraftCell *)cell;


- (void)martialCatAddDistanceFlowDeleteCocoaSlice:(SpineCandidate *)reachedView;


- (void)doubleOxygenView:(SpineCandidate *)reachedView willDisplayCell:(GeneratorDraftCell *)cell;


- (void)doubleOxygenView:(SpineCandidate *)reachedView didEndDisplayingCell:(GeneratorDraftCell *)cell;

@end
