








#import "SpineCandidate.h"

#define GigahertzNeed self.frame.size.width

@interface SpineCandidate()



@property (nonatomic, strong) NSMutableArray                            *useHomeArray;



@property (nonatomic, strong) NSMutableArray <GeneratorDraftCell *>      *flatArray;



@property (strong, nonatomic) NSMutableArray <GeneratorDraftCell *>      *wetMindSense;



@property (assign, nonatomic) NSInteger                                 count;



@property (nonatomic, assign) EarVortexFullArbitraryHas                          status;



@property (nonatomic, assign) NSInteger                                 channelCount;



@property (nonatomic, assign) CGFloat                                   cupBit;

@end

@implementation SpineCandidate

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.status = BayerShortLoseRuleDownPortrait;
    }
    return self;
}

- (void)tableFactory
{
    
    if (self.flatArray.firstObject) {
        
        
        GeneratorDraftCell *reachedView = self.flatArray.firstObject;
        
        reachedView.frame = CGRectMake(GigahertzNeed, 0, reachedView.waitNapSize.width, reachedView.waitNapSize.height);
        
        self.cupBit = reachedView.cupBit;
        
        self.channelCount = reachedView.channelCount;
        
        
        NSInteger row = [self widthSubscribeLyricistPromptInspiredInsertingAdjusts:reachedView];
        
        
        if (row >= 0) {
            
            
            [self.flatArray removeObjectAtIndex:0];
            
            
            if (![self.subviews containsObject:reachedView]) {
                [self addSubview:reachedView];
            }
            reachedView.frame = CGRectMake(GigahertzNeed,  row * (reachedView.waitNapSize.height + _cupBit), reachedView.waitNapSize.width, reachedView.waitNapSize.height);
            
            
            [_useHomeArray setObject:reachedView atIndexedSubscript:row];
            
            
            if ([self.delegate respondsToSelector:@selector(doubleOxygenView:willDisplayCell:)]) {
                [self.delegate doubleOxygenView:self willDisplayCell:reachedView];
            }
            
            
            [self.wetMindSense addObject:reachedView];
            
            [reachedView pinchHexUnsignedSubfamilyGolden:^{
                
                
                [reachedView setTransform:CGAffineTransformMakeTranslation(- reachedView.frame.size.width-GigahertzNeed, 0)];
                
            } completion:^(BOOL finished) {
                
                [reachedView removeFromSuperview];
                
                
                [self.wetMindSense removeObject:reachedView];
                
                
                if ([self.delegate respondsToSelector:@selector(doubleOxygenView:didEndDisplayingCell:)]) {
                    [self.delegate doubleOxygenView:self didEndDisplayingCell:reachedView];
                }
                
                
                if (--self.count <= 0) {
                    if ([self.delegate respondsToSelector:@selector(martialCatAddDistanceFlowDeleteCocoaSlice:)]) {
                        [self.delegate martialCatAddDistanceFlowDeleteCocoaSlice:self];
                    }
                    self.count = 0;
                }
                
                

            }];
        }
    }
    
    [self performSelector:@selector(tableFactory) withObject:nil afterDelay:0.45f];
}


- (void)rebusBackFinal:(NSArray <GeneratorDraftCell *> *)barrages
{
    self.count += barrages.count;
    [self.flatArray addObjectsFromArray:barrages];
}

- (void)start
{
    if (self.status == LoopInsertionSnowPackPicturesPop) {
        return;
    }
    self.status = LoopInsertionSnowPackPicturesPop;
    
    [self tableFactory];
}

- (void)stop
{
    if (self.status == BayerShortLoseRuleDownPortrait) {
        return;
    }
    self.status = BayerShortLoseRuleDownPortrait;
    
    if (self.wetMindSense.count) {
        [self.wetMindSense makeObjectsPerformSelector:@selector(pause)];
    }
    
    if (self.flatArray.count > 0) {
        [NSObject cancelPreviousPerformRequestsWithTarget:self];
    }
    
    
    [self.wetMindSense  makeObjectsPerformSelector:@selector(removeFromSuperview)];
    self.channelCount       = 0;
    self.count              = 0;
    [self.wetMindSense  removeAllObjects];
    [self.flatArray     removeAllObjects];
    [self.useHomeArray  removeAllObjects];
    
    self.wetMindSense       = nil;
    self.flatArray          = nil;
    self.useHomeArray       = nil;
}


- (NSInteger)widthSubscribeLyricistPromptInspiredInsertingAdjusts:(GeneratorDraftCell *)newBarrage
{
    for (int row = 0; row<_useHomeArray.count; row++) {
        NSObject *object = _useHomeArray[row];
        if ([object isKindOfClass:[NSNumber class]]) { 
            
            return row;
            
        }else if ([object isKindOfClass:[GeneratorDraftCell class]]) { 
            
            GeneratorDraftCell *napKeepSub = (GeneratorDraftCell*)object;
            
            if ([self widthSubscribeLyricistPromptInspiredInsertingAdjusts:napKeepSub askBiotin:newBarrage]) {
                
                return row;
            }
        }
    }
    
    return -1;
}


- (BOOL)widthSubscribeLyricistPromptInspiredInsertingAdjusts:(GeneratorDraftCell *)napKeepSub askBiotin:(GeneratorDraftCell *)newBarrage
{
    
    if (napKeepSub.status == RebusInstallAirConsumedEyeSolo) {
        return NO;
    }
    
    
    CGRect rect = [napKeepSub.layer.presentationLayer frame];
    if (rect.origin.x>GigahertzNeed - napKeepSub.frame.size.width) {
        
        return NO;
    }else if (rect.size.width == 0)
    {
        
        return NO;
    }
    else if (napKeepSub.frame.size.width > newBarrage.frame.size.width) {
        
        return YES;
    }else
    {
        
        CGFloat time = GigahertzNeed/(GigahertzNeed+newBarrage.frame.size.width)*newBarrage.movementFeed;
        
        CGFloat trad = rect.origin.x - time/(napKeepSub.movementFeed)*(GigahertzNeed + napKeepSub.frame.size.width);
        if (trad < -napKeepSub.frame.size.width) {
            
            return YES;
        }
    }
    return NO;
}


- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    UITouch *touch = [touches anyObject];
    CGPoint ownIgnores  = [touch locationInView:self];
    for (GeneratorDraftCell *reachedView in [self subviews])
    {
        if ([reachedView.layer.presentationLayer hitTest:ownIgnores])
        {
            
            if ([self.delegate respondsToSelector:@selector(doubleOxygenView:rowPageBondCell:)]) {
                [self.delegate doubleOxygenView:self rowPageBondCell:reachedView];
            }
            break;
        }
    }
}




- (NSMutableArray<GeneratorDraftCell *> *)flatArray {
    if (!_flatArray) {
        _flatArray = [[NSMutableArray alloc] init];
    }
    return _flatArray;
}


- (NSMutableArray<GeneratorDraftCell *> *)wetMindSense {
    if (!_wetMindSense) {
        _wetMindSense = [[NSMutableArray alloc] init];
    }
    return _wetMindSense;
}


- (void)setChannelCount:(NSInteger)channelCount
{
    
    if (self.useHomeArray.count < channelCount) { 
        
        for (NSInteger row = self.useHomeArray.count; row < channelCount; row++) {
            NSNumber *number = [NSNumber numberWithBool:YES];
            [self.useHomeArray setObject:number atIndexedSubscript:row];
        }
        
    }else {
        
        for (NSInteger row = channelCount; row < self.useHomeArray.count; row++) {
            [self.useHomeArray removeObjectAtIndex:row];
        }
    }
    
    _channelCount = channelCount;
    
}


- (NSMutableArray *)useHomeArray {
    if (!_useHomeArray) {
        _useHomeArray = [[NSMutableArray alloc] init];
    }
    return _useHomeArray;
}

@end
