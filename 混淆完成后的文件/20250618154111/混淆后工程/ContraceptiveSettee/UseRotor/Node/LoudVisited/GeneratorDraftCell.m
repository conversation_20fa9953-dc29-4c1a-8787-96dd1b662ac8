







#import "GeneratorDraftCell.h"
#define sorting(obj) __weak typeof(obj) weak##obj = obj;
#define notifyAll(obj) __strong typeof(obj) obj = weak##obj;

@interface GeneratorDraftCell()



@property (nonatomic, strong) NSTimer *timer;

@property (nonatomic, assign) BOOL dialogDiscover;

@end

@implementation GeneratorDraftCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:CGRectZero];
    if (self) {
        
        _waitNapSize = CGSizeMake(200, 40);
        _movementFeed = 4;
        _model = nil;
        _channelCount = 3;
        _cupBit = 0;
        _enumerateStone = 0;
        _status = BayerShortLoseRuleDownPortrait;
        _dialogDiscover = NO;
        
    }
    return self;
}

- (void)declinedSymmetricGainWristConstantsValue
{
    CGFloat nearDays = [[self.layer presentationLayer] frame].origin.x;
    CGFloat barrageWidth = self.frame.size.width;
    
    
    CGFloat speed = (self.superview.frame.size.width + barrageWidth) / self.movementFeed;
    
    
    CGFloat beginExitTime = barrageWidth / speed;
    
    if (_enumerateStone > 0) {
        self.status = RebusInstallAirConsumedEyeSolo;
        if (-1< nearDays < 1) {
            
            if (_dialogDiscover) { return;}
            _dialogDiscover = YES;
            [self pause];
            [self performSelector:@selector(resume) withObject:nil afterDelay:_enumerateStone];
            [self performSelector:@selector(extrasStatus) withObject:nil afterDelay:_enumerateStone - beginExitTime];
        }
    }
}
- (void)extrasStatus
{
    self.status = LoopInsertionSnowPackPicturesPop;
}

- (void)pinchHexUnsignedSubfamilyGolden:(void(^)(void))animations completion:(void(^)(BOOL))completion
{
    self.status = LoopInsertionSnowPackPicturesPop;
    
    _timer = [NSTimer timerWithTimeInterval:0.01 target:self selector:@selector(declinedSymmetricGainWristConstantsValue) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:_timer forMode:NSRunLoopCommonModes];
    
    
    sorting(self);
    [UIView animateWithDuration:self.movementFeed delay:0 options:(UIViewAnimationOptionCurveLinear | UIViewAnimationOptionAllowUserInteraction) animations:^{
        
        if (animations) {
            animations();
        }
        
    } completion:^(BOOL finished) {
        notifyAll(self);
        self->_status = LoopInsertionSnowPackPicturesPop;
        
        if (completion) {
            completion(finished);
        }
        
        if(self->_timer) {
            [self->_timer invalidate];
            self->_timer = nil;
        }
        
    }];
}

- (void)pause
{
    
    CFTimeInterval aboutTime = [self.layer convertTime:CACurrentMediaTime() fromLayer:nil];
    
    
    self.layer.timeOffset = aboutTime;
    
    
    self.layer.speed = 0;
}

- (void)resume
{
    
    CFTimeInterval aboutTime = self.layer.timeOffset;
    
    CFTimeInterval ownSpaBriefPub = CACurrentMediaTime() - aboutTime;
    
    self.layer.timeOffset = 0;
    
    self.layer.beginTime = ownSpaBriefPub;
    
    self.layer.speed = 1;
}


@end
