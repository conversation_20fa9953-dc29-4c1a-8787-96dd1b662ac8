






#import "GaspBusyStickyCell.h"
#import "CupExtrasMinInfo.h"
#import "Masonry.h"
#import "UIColor+BoxColor.h"

@implementation GaspBusyStickyCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        self.backgroundColor = [UIColor clearColor];
        
        self.clipsToBounds = YES;
        
        [self addSubview:self.walkingLabel];
        
        [self.walkingLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsMake(0, 4, 0, 0));
        }];
        
    }
    return self;
}

- (void)setModel:(CupExtrasMinInfo *)model
{
    _model = model;
    _walkingLabel.text = model.bitTapDevice;
    _walkingLabel.font = [UIFont systemFontOfSize:model.notifyingGainIconLegalLappishActive];
    _walkingLabel.textColor = [UIColor tamilDifferentRefinedSobPaperFax:model.iconSplitProfileFreestyleClientDownhill];
    self.backgroundColor = [[UIColor tamilDifferentRefinedSobPaperFax:model.safariRectumFinalSuspendedTorqueLoss] colorWithAlphaComponent:model.extendsArmKitDigitSelectedAbort];
    self.layer.cornerRadius = 2;
}

- (UILabel *)walkingLabel {
    if (!_walkingLabel) {
        _walkingLabel = [[UILabel alloc] init];
        _walkingLabel.backgroundColor = [UIColor clearColor];
        _walkingLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _walkingLabel;
}

@end
