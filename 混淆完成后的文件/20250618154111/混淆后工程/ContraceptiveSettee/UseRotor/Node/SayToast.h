






#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, VendorJoinMutableBringBackup) {
    StorylineBitHandballForceDay,
    MalayalamYellowGreekOddFlagCenter,
    KelvinSquareAgeWelshSplitUppercase
};

@interface SayToast : UIView


+ (void)show:(NSString *)message
    duration:(NSTimeInterval)duration
    position:(VendorJoinMutableBringBackup)position;


+ (void)fitness:(NSString *)message;
+ (void)sizeCenter:(NSString *)message;
+ (void)zipBigClip:(NSString *)message;


+ (void)collectPackFreestyleMountMostlyBelowColor:(UIColor *)color;
+ (void)sawPanFocalCapColor:(UIColor *)color;
+ (void)dayHitForkChat:(UIFont *)font;
+ (void)locationPrintedWorldVeryDenyRadius:(CGFloat)radius;

@end
NS_ASSUME_NONNULL_END
