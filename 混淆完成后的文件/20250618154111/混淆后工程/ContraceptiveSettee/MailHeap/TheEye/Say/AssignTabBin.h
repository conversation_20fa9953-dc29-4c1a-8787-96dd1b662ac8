






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface AssignTabBin : NSObject



@property (nonatomic, strong) NSString *plateMouth;

@property (nonatomic, strong) NSString *groupingName;


@property (nonatomic, strong) NSString *eastStreetExistAmpereHelpers;

@property (nonatomic, strong) NSString *headlineBuildUniqueRefreshedCalculate;


@property (nonatomic, copy) NSString *ejectReferent;


@property (nonatomic, copy) NSString *areHindiName;
@property (nonatomic, copy) NSString *zipAmountBin;
@property (nonatomic, copy) NSString *itsMixTrigger;
@property (nonatomic, copy) NSString *gaspSimulates;
@property (nonatomic, copy) NSString *sayDomain;

@end

NS_ASSUME_NONNULL_END
