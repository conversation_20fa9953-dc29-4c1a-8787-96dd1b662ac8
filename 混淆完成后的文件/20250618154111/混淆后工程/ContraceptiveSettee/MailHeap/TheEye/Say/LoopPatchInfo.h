






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface LoopPatchInfo : NSObject

@property (nonatomic, copy) NSString *kinLegacy;
@property (nonatomic, copy) NSString *notePlain;
@property (nonatomic, copy) NSString *moireFilm;
@property (nonatomic, copy) NSString *compactOff;
@property (nonatomic, copy) NSString *halfSub;
@property (nonatomic, copy) NSString *boxSeeFaceNeed;
@property (nonatomic, copy) NSString *eraMoreEggZone;
@property (nonatomic, copy) NSString *reuseHasPath;
@property (nonatomic, copy) NSString *artPatchWake;
@property (nonatomic, copy) NSString *zoomSurge;
@property (nonatomic, copy) NSString *handDialog;
@property (nonatomic, copy) NSString *sinWarnHowCase;
@property (nonatomic, copy) NSString *quoteDueCheck;
@property (nonatomic, copy) NSString *opaqueDay;
@property (nonatomic, copy) NSString *factBridgedSenderHandleAngle;

@end

NS_ASSUME_NONNULL_END
