






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SolidManager : NSObject

+ (void)airLaunchingViewController:(UIViewController *)vc handler:(void(^)(<PERSON>OOL isCancell,NSString *userID, NSString*token, NSString*error))handler;

+ (BOOL)hourSolveGlyphCaptionFocused:(UIApplication *)application
                spectral:(NSURL *)url
                maskTied:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options;

+ (void)hexImpactRecycleAttributeSingleSparse:(NSString *)clientId aboutSupport:(NSString *)aboutSupport;

@end

NS_ASSUME_NONNULL_END
