






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface KerningKeyManager : NSObject

+ (void)usesAccordingFailMetricSumOwnerBlocker:(NSString *)appId complete:(void (^_Nullable)(BOOL isCanLogin))complete;

+ (void)applierShipmentJobPreserveEnableController:(UIViewController *_Nonnull)controller array:(NSArray *)array success:(void (^_Nullable)(NSDictionary * _Nonnull planeFlip))success sonRestore:(void (^_Nullable)(NSString * _Nonnull error))error packKeyAction:(void(^)(NSInteger))action;

@end

NS_ASSUME_NONNULL_END
