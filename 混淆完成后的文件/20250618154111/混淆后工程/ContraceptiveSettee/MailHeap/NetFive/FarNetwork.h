






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface FarNetwork : NSObject

@property (nonatomic, copy) NSString *auditPen;

+ (instancetype)heartGenericNetwork;

- (void)midPostalRequest:(NSString *)url
                  params:(NSDictionary * _Nullable)params
                 success:(void(^_Nullable)(NSDictionary *sheEyeInuitZip))success
                 special:(void(^_Nullable)(NSError *error))special;

@end

NS_ASSUME_NONNULL_END
