







#import "MQTTTransportProtocol.h"
#import "MQTTCFSocketDecoder.h"
#import "MQTTCFSocketEncoder.h"



@interface MQTTCFSocketTransport : MQTTTransport <MQTTTransport, MQTTCFSocketDecoderDelegate, MQTTCFSocketEncoderDelegate>



@property (strong, nonatomic) NSString *streamSSLLevel;



@property (strong, nonatomic) NSString *host;



@property (nonatomic) UInt32 port;



@property (nonatomic) BOOL tls;



@property (nonatomic) BOOL voip;



@property (strong, nonatomic) NSArray *certificates;




+ (NSArray *)clientCertsFromP12:(NSString *)path passphrase:(NSString *)passphrase;

@end
