








#import <Foundation/Foundation.h>
#import "MQTTSession.h"

@interface MQTTSession(Synchron)



- (BOOL)connectAndWaitTimeout:(NSTimeInterval)timeout;



- (BOOL)subscribeAndWaitToTopic:(NSString *)topic
                        atLevel:(MQTTQosLevel)qosLevel
                        timeout:(NSTimeInterval)timeout;



- (BOOL)subscribeAndWaitToTopics:(NSDictionary<NSString *, NSNumber *> *)topics
                         timeout:(NSTimeInterval)timeout;



- (BOOL)unsubscribeAndWaitTopic:(NSString *)topic
                        timeout:(NSTimeInterval)timeout;



- (BOOL)unsubscribeAndWaitTopics:(NSArray<NSString *> *)topics
                         timeout:(NSTimeInterval)timeout;



- (BOOL)publishAndWaitData:(NSData *)data
                   onTopic:(NSString *)topic
                    retain:(BOOL)retainFlag
                       qos:(MQTTQosLevel)qos
                   timeout:(NSTimeInterval)timeout;



- (void)closeAndWait:(NSTimeInterval)timeout;

@end
