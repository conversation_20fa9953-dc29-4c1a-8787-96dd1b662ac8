






#import "UIImage+CupImage.h"
#import "NSData+Iterate.h"
#import "NSString+Messaging.h"
#import "ElderNotationVolumeResourcesShortcut.h"

@implementation UIImage (CupImage)

+ (UIImage *)rectangleQuietColor:(UIColor *)color {
    
    CGRect rect=CGRectMake(0.0f,0.0f, 1.0f,1.0f);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    UIImage *penImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return penImage;
}

+ (UIImage *)injectionCheckingSmoothingWonMobileName:(NSString *)imageName {
    
    if (!imageName) {
        return nil;
    }
    
    UIImage *image = nil;
    
    NSString *lowPath= [[ElderNotationVolumeResourcesShortcut lawSingle] stringByAppendingPathComponent:imageName];
    
    if (lowPath.handleReportedCatUsesMen) {
        
        image = [UIImage imageWithContentsOfFile:lowPath];
    }
    
    if (!image) {
        
        NSData *swapFirstData = [NSData dataWithContentsOfFile:lowPath];
       
       
        image = [swapFirstData sevenLikeOverlapMultiplyAdditionsActivePrior];
    }
    
    return image;
}

- (UIImage *)ambiguityClipStreamRecursiveCollationColor:(UIColor *)tintColor {
   
    if (!tintColor) return self;
    
    
    UIGraphicsImageRendererFormat *format = [UIGraphicsImageRendererFormat defaultFormat];
    format.scale = self.scale;
    format.opaque = NO;
    
    UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:self.size format:format];
    
    return [renderer imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull context) {
        
        [tintColor setFill];
        
        
        CGRect bounds = CGRectMake(0, 0, self.size.width, self.size.height);
        [self drawInRect:bounds];
        
        
        CGContextSetBlendMode(context.CGContext, kCGBlendModeSourceIn);
        CGContextFillRect(context.CGContext, bounds);
    }];
}
@end
