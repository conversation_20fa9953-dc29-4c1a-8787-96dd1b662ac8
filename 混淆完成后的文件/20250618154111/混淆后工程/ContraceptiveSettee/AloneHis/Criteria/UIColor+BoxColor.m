






#import "UIColor+BoxColor.h"

@implementation UIColor (BoxColor)

+ (UIColor *)tamilDifferentRefinedSobPaperFax:(NSString *)urgentArt {
    if (urgentArt.length <= 0) return nil;
    
    NSString *colorString = [[urgentArt stringByReplacingOccurrencesOfString: @"#" withString: @""] uppercaseString];
    CGFloat alpha, red, blue, green;
    switch ([colorString length]) {
        case 3: 
            alpha = 1.0f;
            red   = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 0 length: 1];
            green = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 1 length: 1];
            blue  = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 2 length: 1];
            break;
        case 4: 
            alpha = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 0 length: 1];
            red   = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 1 length: 1];
            green = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 2 length: 1];
            blue  = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 3 length: 1];
            break;
        case 6: 
            alpha = 1.0f;
            red   = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 0 length: 2];
            green = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 2 length: 2];
            blue  = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 4 length: 2];
            break;
        case 8: 
            alpha = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 0 length: 2];
            red   = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 2 length: 2];
            green = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 4 length: 2];
            blue  = [self landscapeProxiesEvictionSettingsAfterRope: colorString start: 6 length: 2];
            break;
        default: {
            NSAssert(NO, @"Color value %@ is invalid. It should be a hex value of the form #RBG, #ARGB, #RRGGBB, or #AARRGGBB", urgentArt);
            return nil;
        }
            break;
    }
    return [UIColor colorWithRed: red green: green blue: blue alpha: alpha];
}

+ (CGFloat)landscapeProxiesEvictionSettingsAfterRope:(NSString *)string start:(NSUInteger)start length:(NSUInteger)length {
    NSString *substring = [string substringWithRange: NSMakeRange(start, length)];
    NSString *infoTip = length == 2 ? substring : [NSString stringWithFormat: @"%@%@", substring, substring];
    unsigned hexComponent;
    [[NSScanner scannerWithString: infoTip] scanHexInt: &hexComponent];
    return hexComponent / 255.0;
}

- (UIColor *)gallonManagerBookCallStorylineTotal:(CGFloat)percentage {
    return [self paletteMouseSubsetMartialUplinkAtomic:percentage];
}

- (UIColor *)implicitSecureMirroredGaspNoteMore:(CGFloat)percentage {
    return [self paletteMouseSubsetMartialUplinkAtomic:-1*fabs(percentage)];
}

- (UIColor *)paletteMouseSubsetMartialUplinkAtomic:(CGFloat)percentage {
    CGFloat red,green,blue,alpha;
    if ([self getRed:&red green:&green blue:&blue alpha:&alpha]) {
        return [UIColor colorWithRed:MIN(red+percentage/100, 1.0) green:MIN(green+percentage/100, 1.0) blue:MIN(blue+percentage/100, 1.0) alpha:alpha];
    }else {
        return nil;
    }
}

@end
