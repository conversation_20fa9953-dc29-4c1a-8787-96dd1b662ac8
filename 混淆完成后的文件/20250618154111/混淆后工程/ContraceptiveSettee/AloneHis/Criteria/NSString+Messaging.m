






#import "NSString+Messaging.h"

@implementation NSObject (Messaging)

- (BOOL)raceSinBlock {
    
    
    if (self == nil || (id)self == [NSNull null]) {
        return YES;
    }
    
    
    if ([self isKindOfClass:[NSString class]]) {
        NSString *str = (NSString *)self;
        if (str.length == 0) {
            return YES;
        }
        
        NSString *trySlab = [str stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
        return (trySlab.length == 0);
    }
    
    







    
    
    
    return YES;
}

- (BOOL)handleReportedCatUsesMen {
    return ![self raceSinBlock];
}

//- (BOOL)handleReportedCatUsesMen {

//}

//- (BOOL)raceSinBlock {




//}

@end
