






#import "NSString+UnitZipHair.h"

@implementation NSString (UnitZipHair)

- (NSString *)logExponentsPartiallyHelloIconRendered {
    NSCharacterSet *allowedCharacters = [[NSCharacterSet characterSetWithCharactersInString:@"!*'();:@&=+$,/?%#[]"] invertedSet];
    return [self stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
}

- (NSString *)leftoverTempHighlightPostalCoachedBig {
    NSString *publishCross = [self stringByReplacingOccurrencesOfString:@"+" withString:@" "];
    return [publishCross stringByRemovingPercentEncoding];
}

@end
