






#import "NSURL+SheRopeHow.h"
#import "NSString+UnitZipHair.h"

@implementation NSURL (SheRopeHow)

- (NSDictionary *)mathMovement {
    
    NSArray * array = [[self query] componentsSeparatedByString:@"&"];

    NSMutableDictionary * ParaDict = [NSMutableDictionary new];

    for(int i = 0 ; i < [array count]; i++){

        NSArray * notOldValue = [array[i] componentsSeparatedByString:@"="];

        if([notOldValue count] == 2 && notOldValue[0] && notOldValue[1]){

            [ParaDict setObject:[notOldValue[1] leftoverTempHighlightPostalCoachedBig] forKey:notOldValue[0]];

        }
    }
    return ParaDict;
}

@end
