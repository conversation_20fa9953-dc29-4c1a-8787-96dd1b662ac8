






#import "NSString+Suffix.h"
#import <CommonCrypto/CommonCrypto.h>

@implementation NSString (Suffix)

- (NSString *)car {
    const char* inputStr = [self UTF8String];
    unsigned char result[CC_MD5_DIGEST_LENGTH];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    CC_MD5(inputStr, (CC_LONG)strlen(inputStr), result);
#pragma clang diagnostic pop
    NSMutableString *ret = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH*2];
    for(int i = 0; i<CC_MD5_DIGEST_LENGTH; i++) {
        [ret appendFormat:@"%02x",result[i]];
    }
    return ret;
}

@end
