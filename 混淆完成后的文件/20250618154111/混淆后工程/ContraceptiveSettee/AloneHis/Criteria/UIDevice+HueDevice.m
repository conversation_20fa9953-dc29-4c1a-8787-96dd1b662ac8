






#import "UIDevice+HueDevice.h"
#import "HeartBankManager.h"
@import UIKit;

@implementation UIDevice (HueDevice)

static NSInteger unwrap = -1;
+ (BOOL)unwrap {
    if (unwrap < 0) {
        unwrap = [UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad ? 1 : 0;
    }
    return unwrap > 0;
}

+ (BOOL)capsFont {
    if (@available(iOS 11.0, *)) {
        
        UIWindow *window = HeartBankManager.shared.wasMeanMergeWindow;
        
        UIEdgeInsets safeArea = window.safeAreaInsets;
        
        
        BOOL hashMild = ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPhone);
        
        
        return hashMild && (
            safeArea.top > 20.0 ||          
            safeArea.left > 0 ||            
            safeArea.right > 0              
        );
    }
    return NO; 
}

@end
