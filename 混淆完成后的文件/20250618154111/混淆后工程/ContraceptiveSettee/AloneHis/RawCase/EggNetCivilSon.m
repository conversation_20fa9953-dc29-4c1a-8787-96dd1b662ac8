






#import "EggNetCivilSon.h"

#define sorting(obj) __weak typeof(obj) weak##obj = obj;
#define notifyAll(obj) __strong typeof(obj) obj = weak##obj;

@interface EggNetCivilSon()

@property (nonatomic,strong) NSURLSession *cacheHeapWon;

@end

@implementation EggNetCivilSon


+ (instancetype)shared {
    static EggNetCivilSon *shared = nil;
    static dispatch_once_t ringToken;
    dispatch_once(&ringToken, ^{
        shared = [[super allocWithZone:NULL] init];
        shared.cacheHeapWon = [NSURLSession sessionWithConfiguration:[NSURLSessionConfiguration defaultSessionConfiguration] delegate:shared delegateQueue:[[NSOperationQueue alloc] init]];
        shared.cacheHeapWon.delegateQueue.maxConcurrentOperationCount = 1;
    });
    return shared;
}

- (void)modalBusRightRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock
                     success:(void(^)(NSDictionary * sheEyeInuitZip))success
                     special:(void(^)(NSError *error))special
                  civilCount:(NSInteger)civilCount {

    [self midPostalRequest:request
                   process:processBlock
                   success:success
                   special:special
                civilCount:civilCount
            resignMindFork:0];
}


- (void)midPostalRequest:(NSMutableURLRequest *)request
                 process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock
                 success:(void(^)(NSDictionary * sheEyeInuitZip))success
                 special:(void(^)(NSError *error))special
              civilCount:(NSInteger)civilCount
          resignMindFork:(NSInteger)resignMindFork {

    sorting(self);
    NSURLSessionDataTask *task = [self.cacheHeapWon dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        notifyAll(self);
        
        NSError *flipPashto = [self handleError:error response:response data:data];
        if (flipPashto) {
            

            
            if (resignMindFork < civilCount) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self midPostalRequest:request process:processBlock success:success special:special civilCount:civilCount resignMindFork:resignMindFork + 1];
                });
                return;
            }

            
            if (special) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    special(flipPashto);
                });
            }
            return;
        }

        
        NSData *processedData = processBlock ? processBlock(data) : data;
        if (!processedData) {
            NSError *processingError = [NSError errorWithDomain:@"NetworkCore"
                                                           code:-30002
                                                       userInfo:@{NSLocalizedDescriptionKey : @"Data processing failed"}];
            if (special) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    special(processingError);
                });
            }
            return;
        }

        NSError *jsonError;
        NSDictionary *jsonResponse = [NSJSONSerialization JSONObjectWithData:processedData options:0 error:&jsonError];

        if (!jsonError && [jsonResponse isKindOfClass:[NSDictionary class]]) {
            if (success) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    success(jsonResponse);
                });
            }
        } else {
            
            if (special) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    special(jsonError);
                });
            }
        }
    }];

    [task resume];
}


- (NSError *)handleError:(NSError *)error response:(NSURLResponse *)response data:(NSData *)data {
    if (error) {
        return error;
    }

    if (!data) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:-30001
                               userInfo:@{NSLocalizedDescriptionKey : @"The data is empty."}];
    }

    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
    if (![httpResponse isKindOfClass:[NSHTTPURLResponse class]] || httpResponse.statusCode != 200) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:httpResponse.statusCode
                               userInfo:@{NSLocalizedDescriptionKey : [NSString stringWithFormat:@"HTTPError，code: %ld", (long)httpResponse.statusCode]}];
    }

    return nil;
}

@end
