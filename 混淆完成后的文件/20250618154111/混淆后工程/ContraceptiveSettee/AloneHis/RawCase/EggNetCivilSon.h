






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface EggNetCivilSon : NSObject
<
NSURLSessionDelegate,
NSURLSessionTaskDelegate
>

+ (instancetype)shared;

- (void)modalBusRightRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock 
                     success:(void(^_Nullable)(NSDictionary * sheEyeInuitZip))success
                     special:(void(^_Nullable)(NSError *error))special
                  civilCount:(NSInteger)civilCount;

@end

NS_ASSUME_NONNULL_END
