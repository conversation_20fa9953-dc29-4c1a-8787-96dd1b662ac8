





#import "AlienDelayItemBriefSelector.h"
@import Network;

static NSString *mathWeekTooIts = nil;
static nw_path_monitor_t storeNecessary = NULL;

@implementation AlienDelayItemBriefSelector

+ (BOOL)kerningTruncatesCreateSayAge {
    return mathWeekTooIts != nil;
}

+ (NSString *)growSolveTooType {
    return mathWeekTooIts ?: @"none";
}

+ (void)polarDeferredChatIdenticalCautionExtents:(void (^)(BOOL kerningTruncatesCreateSayAge))completion {
    
    if (storeNecessary != NULL) {
        nw_path_monitor_cancel(storeNecessary);
        storeNecessary = NULL;
    }
    
    
    storeNecessary = nw_path_monitor_create();
    nw_path_monitor_set_queue(storeNecessary, dispatch_get_main_queue());
    
    __block nw_path_monitor_t blockMonitor = storeNecessary;
    nw_path_monitor_set_update_handler(storeNecessary, ^(nw_path_t path) {
        nw_path_status_t status = nw_path_get_status(path);
        if (status == nw_path_status_satisfied) {
            if (nw_path_uses_interface_type(path, nw_interface_type_wifi)) {
                mathWeekTooIts = @"wifi";
            } else if (nw_path_uses_interface_type(path, nw_interface_type_cellular)) {
                mathWeekTooIts = @"cellular";
            } else {
                
                mathWeekTooIts = @"other";
            }
            
            
            if (blockMonitor) {
                nw_path_monitor_cancel(blockMonitor);
                blockMonitor = NULL;
                storeNecessary = NULL;
            }
            
        } else {
            mathWeekTooIts = nil;
        }
        
        
        if (completion) {
            completion([self kerningTruncatesCreateSayAge]);
        }
        
    });
    
    
    nw_path_monitor_start(storeNecessary);
}

@end
