






#import "PurpleInfo.h"
#import "IndexRaiseConfig.h"
#import "OpenPurposeMostlySynthesisFlushed.h"

@import AdSupport;
@import AppTrackingTransparency;
@import UIKit;

#import "sys/utsname.h" //utsname

@implementation PurpleInfo

+ (UIImage *)authorsOptimizedRequestArmpitAwakeImage {
    NSDictionary *capsSleep = [[NSBundle mainBundle] infoDictionary];
    NSString *icon = [[capsSleep valueForKeyPath:@"CFBundleIcons.CFBundlePrimaryIcon.CFBundleIconFiles"] lastObject];
    return [UIImage imageNamed:icon];
}

+ (NSString *)penWayBurnPaceIdentifier {
    return [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"];
}

+ (NSString *)headlineBuildUniqueRefreshedCalculate {
    return [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
}

+ (NSString *)groupingName {
    NSString *displayName = [[NSBundle mainBundle] localizedInfoDictionary][@"CFBundleDisplayName"];

    if (!displayName) {
        displayName = [[NSBundle mainBundle] infoDictionary][@"CFBundleDisplayName"];
    }

    if (!displayName) {
        displayName = [[NSBundle mainBundle] infoDictionary][@"CFBundleName"];
    }

    return displayName;
}

+ (NSString *)greatAirSinName {
    return [UIDevice currentDevice].name;
}

+ (NSString *)recentAreaRejectionWideArabic {
    return [ASIdentifierManager sharedManager].advertisingIdentifier.UUIDString;
}

+ (NSString *)rotationMapDegreesObjectLayout {
    return [UIDevice currentDevice].identifierForVendor.UUIDString;
}

+ (NSString *)armDrainRetModel {
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    return deviceModel;
}

+ (NSString *)resignProcessesDrawingPencilEphemeral {
    return [UIDevice currentDevice].systemVersion;
}

+ (NSString *)decipherWeekendProtocolTradMenstrualPath {
    return NSHomeDirectory().lastPathComponent;
}

+ (BOOL)twelveSleet {
    NSUserDefaults *somaliBatch = [NSUserDefaults standardUserDefaults];
    return [somaliBatch boolForKey:theItsRatio.previewResolvingCoverEnsureBlur];
}
+ (void)setTwelveSleet:(BOOL)twelveSleet {
    NSUserDefaults *somaliBatch = [NSUserDefaults standardUserDefaults];
    [somaliBatch setBool:twelveSleet forKey:theItsRatio.previewResolvingCoverEnsureBlur];
    [somaliBatch synchronize];
}

+ (void)butGenerateBaseLenientOneProblem:(void (^)(void))denyFile {
    static dispatch_once_t ringToken;
    static BOOL mapSuggested = NO;

    
    if (mapSuggested) {
        FinalInfo(theItsRatio.appearRowMountExecuteClickImageLaunched);
        return;
    }

    dispatch_once(&ringToken, ^{
        mapSuggested = YES;
        FinalInfo(theItsRatio.removesMostlyOffVeryHowSiblings);

        if (@available(iOS 14, *)) {
            ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];

            NSString *infoYetTip = [self lawBurstFarTwoStatus:status];

            FinalInfo(theItsRatio.deprecateGramEighteenPinkFinishingDefined, infoYetTip, (long)status);

            switch (status) {
                case ATTrackingManagerAuthorizationStatusAuthorized:
                    FinalInfo(theItsRatio.proposalPrematureDelayBigHandAccessingArm);
                    mapSuggested = NO;
                    if (denyFile) {
                        denyFile();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusDenied:
                    FinalInfo(theItsRatio.dayDisposeAgreementAccessingCleanup);
                    mapSuggested = NO;
                    if (denyFile) {
                        denyFile();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusRestricted:
                    FinalInfo(theItsRatio.eggSubtitleVignettePlusSawNap);
                    mapSuggested = NO;
                    if (denyFile) {
                        denyFile();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusNotDetermined:
                    FinalInfo(theItsRatio.pauseLossChatSubjectFooterRefresh);
                    [self strokedReplaceLettersPurchasedLanguagePreferMetal:^{
                        mapSuggested = NO;
                        if (denyFile) {
                            denyFile();
                        }
                    }];
                    break;
            }
        } else {
            FinalInfo(theItsRatio.panArbitraryRebuildBeenRestartNumeral);
            mapSuggested = NO;
            if (denyFile) {
                denyFile();
            }
        }
    });
}

+ (void)strokedReplaceLettersPurchasedLanguagePreferMetal:(void (^)(void))completion {
    FinalInfo(theItsRatio.removableUpscaleShiftHandoverLettersReporting);

    
    static int noiseSubPeak = 6;

    __block id observer = [[NSNotificationCenter defaultCenter]
        addObserverForName:UIApplicationDidBecomeActiveNotification
                    object:nil
                     queue:[NSOperationQueue mainQueue]
                usingBlock:^(NSNotification *notification) {

        FinalInfo(theItsRatio.resolvingIdentifyChainLiftAdvisedFix, noiseSubPeak);

        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(noiseSubPeak * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{


            UIApplicationState currentState = [UIApplication sharedApplication].applicationState;

            NSString *stairTurn = [self keyRawWaxCanonState:currentState];

            FinalInfo(theItsRatio.hitTrainerOccurredIntervalRetMode, stairTurn);

            if (currentState == UIApplicationStateActive) {
                FinalInfo(theItsRatio.operationCopperDryAppendTripleHybridStalled);
                [self noteAddGreekRequest:completion];
            } else {

                FinalInfo(theItsRatio.siteExporterFlagKitPerformsToo, stairTurn);
                FinalInfo(theItsRatio.seeTruncatesCancelingMonthAddLatency);
                observer = [[NSNotificationCenter defaultCenter]
                    addObserverForName:UIApplicationDidBecomeActiveNotification
                                object:nil
                                 queue:[NSOperationQueue mainQueue]
                            usingBlock:^(NSNotification *notification) {
                    
                    FinalInfo(theItsRatio.styleDeferredBestQualifierAirlineImage);
                    [[NSNotificationCenter defaultCenter] removeObserver:observer];
                    FinalInfo(theItsRatio.speakerRopeFrenchRetainCapBufferNumeral);
                    [self noteAddGreekRequest:completion];
                }];
            }

        });

        FinalInfo(theItsRatio.remainderVitaminBeaconsHoverRaceRole);
        
        [[NSNotificationCenter defaultCenter] removeObserver:observer];
    }];
}

+ (void)noteAddGreekRequest:(void (^)(void))completion {
    if (@available(iOS 14, *)) {
        FinalInfo(theItsRatio.waistOperandMetalOutdoorAirborneEnd);

        [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
            ATTrackingManagerAuthorizationStatus panDustStatus = [ATTrackingManager trackingAuthorizationStatus];

            NSString *gainTightMetricProductsNet = [self lawBurstFarTwoStatus:status];
            NSString *currentStatusDesc = [self lawBurstFarTwoStatus:panDustStatus];

            FinalInfo(theItsRatio.shotUppercasePaceRenderShortResonant);
            FinalInfo(theItsRatio.issuerTaskRopeFinishedPopWorking, gainTightMetricProductsNet, (long)status);
            FinalInfo(theItsRatio.cleanHaveDominantAttitudeSuffixArrivalLoss, currentStatusDesc, (long)panDustStatus);

            
            
            
            
            BOOL outKnowTitle = (panDustStatus == ATTrackingManagerAuthorizationStatusAuthorized) ||
                               (status == ATTrackingManagerAuthorizationStatusAuthorized);

            if (outKnowTitle) {
                FinalInfo(theItsRatio.portBezelDetachingAccountsItalianWorldEgg);
                if (completion) {
                    completion();
                }
            } else if (panDustStatus == ATTrackingManagerAuthorizationStatusNotDetermined) {
                FinalInfo(theItsRatio.revisionsTooStopObserveOtherOtherDiscard);
                [self pascalRateThresholdRealmInvertGrowRecording:completion resignMindFork:0];
            } else {
                FinalInfo(theItsRatio.sixPrimeWelshMoleResponsesProcessorBin);
                if (completion) {
                    completion();
                }
            }
        }];
    }
}


+ (NSString *)lawBurstFarTwoStatus:(ATTrackingManagerAuthorizationStatus)status  API_AVAILABLE(ios(14)){
    if (@available(iOS 14, *)) {
        switch (status) {
            case ATTrackingManagerAuthorizationStatusNotDetermined:
                return theItsRatio.stillModuleIterativeLightIgnoredEffectTorch;
            case ATTrackingManagerAuthorizationStatusRestricted:
                return theItsRatio.blurMouseCriticalCricketPenPositive;
            case ATTrackingManagerAuthorizationStatusDenied:
                return theItsRatio.floorSonStrokingPinEndsExtent;
            case ATTrackingManagerAuthorizationStatusAuthorized:
                return theItsRatio.flatDerivedUnpluggedDirectorRenewDisorder;
            default:
                return [NSString stringWithFormat:theItsRatio.strongGloballyHisGoldenCupLease, (long)status];
        }
    }
    return theItsRatio.hashStarBoldfaceMethodFocusingPatchAuto;
}

+ (NSString *)keyRawWaxCanonState:(UIApplicationState)state {
    switch (state) {
        case UIApplicationStateActive:
            return theItsRatio.digestBankersJabberRoundLongReplaced;
        case UIApplicationStateInactive:
            return theItsRatio.purchasedTraitPotentialCoalescedSpaceQuery;
        case UIApplicationStateBackground:
            return theItsRatio.signalingAirManyGetStrokingPublic;
        default:
            return [NSString stringWithFormat:theItsRatio.webpageLearnConsumesReversingQueuePreview, (long)state];
    }
}


+ (void)pascalRateThresholdRealmInvertGrowRecording:(void (^)(void))denyFile resignMindFork:(NSInteger)resignMindFork {
    NSInteger civilCount = 10;

    if (@available(iOS 14, *)) {
        ATTrackingManagerAuthorizationStatus panDustStatus = [ATTrackingManager trackingAuthorizationStatus];

        NSString *infoYetTip = [self lawBurstFarTwoStatus:panDustStatus];

        FinalInfo(theItsRatio.kitEditSawParserContentsTrash,
              (long)(resignMindFork + 1), (long)civilCount, infoYetTip);

        
        if (panDustStatus == ATTrackingManagerAuthorizationStatusNotDetermined && resignMindFork < civilCount) {
            FinalInfo(theItsRatio.halftoneUploadExpiresFloaterWriteIntegers, (long)(resignMindFork + 2));

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)),
                          dispatch_get_main_queue(), ^{
                [self pascalRateThresholdRealmInvertGrowRecording:denyFile resignMindFork:resignMindFork + 1];
            });
            return;
        } else {
            
            
            if (resignMindFork >= civilCount) {
                FinalInfo(theItsRatio.spineBusyOfferLambdaBetweenSequence, (long)civilCount);
                FinalInfo(theItsRatio.opaqueSkinEmergencySignTapYet, infoYetTip);
            } else {
                FinalInfo(theItsRatio.takeFatalIcelandicCenterDogTrigger, infoYetTip);

                if (panDustStatus == ATTrackingManagerAuthorizationStatusAuthorized) {
                    FinalInfo(theItsRatio.insetWriteSubgroupInitiallyCiphersWake);
                } else if (panDustStatus == ATTrackingManagerAuthorizationStatusDenied) {
                    FinalInfo(theItsRatio.proposalAuditMobileEntitiesTrustedLaotian);
                } else if (panDustStatus == ATTrackingManagerAuthorizationStatusRestricted) {
                    FinalInfo(theItsRatio.altimeterDietarySpaNegotiateBinFailure);
                }
            }

            FinalInfo(theItsRatio.handoverSlovakBlindingResponsePartiallyTemporary);
            if (denyFile) {
                denyFile();
            }
        }
    } else {
        FinalInfo(theItsRatio.regionSmallStretchResetStormBringWas);
        if (denyFile) {
            denyFile();
        }
    }
}
@end
