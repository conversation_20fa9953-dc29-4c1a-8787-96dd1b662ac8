


#import <Foundation/Foundation.h>
#import "SDWebImageCompat.h"

typedef NSURLResponse * _Nullable (^SDWebImageDownloaderResponseModifierBlock)(NSURLResponse * _Nonnull response);



@protocol SDWebImageDownloaderResponseModifier <NSObject>




- (nullable NSURLResponse *)modifiedResponseWithResponse:(nonnull NSURLResponse *)response;

@end



@interface SDWebImageDownloaderResponseModifier : NSObject <SDWebImageDownloaderResponseModifier>



- (nonnull instancetype)initWithBlock:(nonnull SDWebImageDownloaderResponseModifierBlock)block;



+ (nonnull instancetype)responseModifierWithBlock:(nonnull SDWebImageDownloaderResponseModifierBlock)block;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;

@end



@interface SDWebImageDownloaderResponseModifier (Conveniences)




- (nonnull instancetype)initWithStatusCode:(NSInteger)statusCode;




- (nonnull instancetype)initWithVersion:(nullable NSString *)version;




- (nonnull instancetype)initWithHeaders:(nullable NSDictionary<NSString *, NSString *> *)headers;






- (nonnull instancetype)initWithStatusCode:(NSInteger)statusCode version:(nullable NSString *)version headers:(nullable NSDictionary<NSString *, NSString *> *)headers;

@end
