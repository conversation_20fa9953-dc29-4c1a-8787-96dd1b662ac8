


#import <Foundation/Foundation.h>
#import "SDWebImageCompat.h"
#import "SDImageCoder.h"


typedef NS_ENUM(NSUInteger, SDAnimatedImagePlaybackMode) {
    

    SDAnimatedImagePlaybackModeNormal = 0,
    

    SDAnimatedImagePlaybackModeReverse,
    

    SDAnimatedImagePlaybackModeBounce,
    

    SDAnimatedImagePlaybackModeReversedBounce,
};


@interface SDAnimatedImagePlayer : NSObject


@property (nonatomic, readonly, nullable) UIImage *currentFrame;


@property (nonatomic, readonly) NSUInteger currentFrameIndex;


@property (nonatomic, readonly) NSUInteger currentLoopCount;



@property (nonatomic, assign) NSUInteger totalFrameCount;


@property (nonatomic, assign) NSUInteger totalLoopCount;







@property (nonatomic, assign) double playbackRate;


@property (nonatomic, assign) SDAnimatedImagePlaybackMode playbackMode;





@property (nonatomic, assign) NSUInteger maxBufferSize;



@property (nonatomic, copy, nonnull) NSRunLoopMode runLoopMode;





- (nullable instancetype)initWithProvider:(nonnull id<SDAnimatedImageProvider>)provider;





+ (nullable instancetype)playerWithProvider:(nonnull id<SDAnimatedImageProvider>)provider;


@property (nonatomic, copy, nullable) void (^animationFrameHandler)(NSUInteger index, UIImage * _Nonnull frame);


@property (nonatomic, copy, nullable) void (^animationLoopHandler)(NSUInteger loopCount);


@property (nonatomic, readonly) BOOL isPlaying;


- (void)startPlaying;


- (void)pausePlaying;


- (void)stopPlaying;





- (void)seekToFrameAtIndex:(NSUInteger)index loopCount:(NSUInteger)loopCount;



- (void)clearFrameBuffer;

@end
