


#import "SDWebImageCompat.h"

#if SD_MAC



@interface NSImage (Compatibility)



@property (nonatomic, readonly, nullable) CGImageRef CGImage;


@property (nonatomic, readonly, nullable) CIImage *CIImage;


@property (nonatomic, readonly) CGFloat scale;





- (nonnull instancetype)initWithCGImage:(nonnull CGImageRef)cgImage scale:(CGFloat)scale orientation:(CGImagePropertyOrientation)orientation;



- (nonnull instancetype)initWithCIImage:(nonnull CIImage *)ciImage scale:(CGFloat)scale orientation:(CGImagePropertyOrientation)orientation;



- (nullable instancetype)initWithData:(nonnull NSData *)data scale:(CGFloat)scale;

@end

#endif
