


#import <Foundation/Foundation.h>
#import "SDWebImageCompat.h"
#import "SDWebImageDefine.h"
#import "SDWebImageOperation.h"
#import "SDWebImageDownloaderConfig.h"
#import "SDWebImageDownloaderRequestModifier.h"
#import "SDWebImageDownloaderResponseModifier.h"
#import "SDWebImageDownloaderDecryptor.h"
#import "SDImageLoader.h"


typedef NS_OPTIONS(NSUInteger, SDWebImageDownloaderOptions) {
    

    SDWebImageDownloaderLowPriority = 1 << 0,
    
    

    SDWebImageDownloaderProgressiveLoad = 1 << 1,

    

    SDWebImageDownloaderUseNSURLCache = 1 << 2,

    

    SDWebImageDownloaderIgnoreCachedResponse = 1 << 3,
    
    

    SDWebImageDownloaderContinueInBackground = 1 << 4,

    

    SDWebImageDownloaderHandleCookies = 1 << 5,

    

    SDWebImageDownloaderAllowInvalidSSLCertificates = 1 << 6,

    

    SDWebImageDownloaderHighPriority = 1 << 7,
    
    

    SDWebImageDownloaderScaleDownLargeImages = 1 << 8,
    
    

    SDWebImageDownloaderAvoidDecodeImage API_DEPRECATED("Use SDWebImageContextImageForceDecodePolicy instead", macos(10.10, 10.10), ios(8.0, 8.0), tvos(9.0, 9.0), watchos(2.0, 2.0)) = 1 << 9,
    
    

    SDWebImageDownloaderDecodeFirstFrameOnly = 1 << 10,
    
    

    SDWebImageDownloaderPreloadAllFrames = 1 << 11,
    
    

    SDWebImageDownloaderMatchAnimatedImageClass = 1 << 12,
};


FOUNDATION_EXPORT NSNotificationName _Nonnull const SDWebImageDownloadStartNotification;

FOUNDATION_EXPORT NSNotificationName _Nonnull const SDWebImageDownloadReceiveResponseNotification;

FOUNDATION_EXPORT NSNotificationName _Nonnull const SDWebImageDownloadStopNotification;

FOUNDATION_EXPORT NSNotificationName _Nonnull const SDWebImageDownloadFinishNotification;

typedef SDImageLoaderProgressBlock SDWebImageDownloaderProgressBlock;
typedef SDImageLoaderCompletedBlock SDWebImageDownloaderCompletedBlock;



@interface SDWebImageDownloadToken : NSObject <SDWebImageOperation>



- (void)cancel;



@property (nonatomic, strong, nullable, readonly) NSURL *url;



@property (nonatomic, strong, nullable, readonly) NSURLRequest *request;



@property (nonatomic, strong, nullable, readonly) NSURLResponse *response;



@property (nonatomic, strong, nullable, readonly) NSURLSessionTaskMetrics *metrics API_AVAILABLE(macos(10.12), ios(10.0), watchos(3.0), tvos(10.0));

@end




@interface SDWebImageDownloader : NSObject



@property (nonatomic, copy, readonly, nonnull) SDWebImageDownloaderConfig *config;



@property (nonatomic, strong, nullable) id<SDWebImageDownloaderRequestModifier> requestModifier;



@property (nonatomic, strong, nullable) id<SDWebImageDownloaderResponseModifier> responseModifier;



@property (nonatomic, strong, nullable) id<SDWebImageDownloaderDecryptor> decryptor;



@property (nonatomic, readonly, nonnull) NSURLSessionConfiguration *sessionConfiguration;



@property (nonatomic, assign, getter=isSuspended) BOOL suspended;



@property (nonatomic, assign, readonly) NSUInteger currentDownloadCount;



@property (nonatomic, class, readonly, nonnull) SDWebImageDownloader *sharedDownloader;



- (nonnull instancetype)initWithConfig:(nullable SDWebImageDownloaderConfig *)config NS_DESIGNATED_INITIALIZER;



- (void)setValue:(nullable NSString *)value forHTTPHeaderField:(nullable NSString *)field;



- (nullable NSString *)valueForHTTPHeaderField:(nullable NSString *)field;



- (nullable SDWebImageDownloadToken *)downloadImageWithURL:(nullable NSURL *)url
                                                 completed:(nullable SDWebImageDownloaderCompletedBlock)completedBlock;



- (nullable SDWebImageDownloadToken *)downloadImageWithURL:(nullable NSURL *)url
                                                   options:(SDWebImageDownloaderOptions)options
                                                  progress:(nullable SDWebImageDownloaderProgressBlock)progressBlock
                                                 completed:(nullable SDWebImageDownloaderCompletedBlock)completedBlock;



- (nullable SDWebImageDownloadToken *)downloadImageWithURL:(nullable NSURL *)url
                                                   options:(SDWebImageDownloaderOptions)options
                                                   context:(nullable SDWebImageContext *)context
                                                  progress:(nullable SDWebImageDownloaderProgressBlock)progressBlock
                                                 completed:(nullable SDWebImageDownloaderCompletedBlock)completedBlock;



- (void)cancelAllDownloads;



- (void)invalidateSessionAndCancel:(BOOL)cancelPendingOperations;

@end




@interface SDWebImageDownloader (SDImageLoader) <SDImageLoader>

@end
