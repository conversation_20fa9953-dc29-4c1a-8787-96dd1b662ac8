


#import "SDWebImageCompat.h"

#if SD_UIKIT || SD_MAC

#import "SDAnimatedImage.h"
#import "SDAnimatedImagePlayer.h"
#import "SDImageTransformer.h"



NS_SWIFT_UI_ACTOR
@interface SDAnimatedImageView : UIImageView


@property (nonatomic, strong, readonly, nullable) SDAnimatedImagePlayer *player;



@property (nonatomic, strong, nullable) id<SDImageTransformer> animationTransformer;



@property (nonatomic, strong, readonly, nullable) UIImage *currentFrame;


@property (nonatomic, assign, readonly) NSUInteger currentFrameIndex;


@property (nonatomic, assign, readonly) NSUInteger currentLoopCount;


@property (nonatomic, assign) BOOL shouldCustomLoopCount;


@property (nonatomic, assign) NSInteger animationRepeatCount;


@property (nonatomic, assign) double playbackRate;


@property (nonatomic, assign) SDAnimatedImagePlaybackMode playbackMode;



@property (nonatomic, assign) NSUInteger maxBufferSize;


@property (nonatomic, assign) BOOL shouldIncrementalLoad;



@property (nonatomic, assign) BOOL clearBufferWhenStopped;



@property (nonatomic, assign) BOOL resetFrameIndexWhenStopped;



@property (nonatomic, assign) BOOL autoPlayAnimatedImage;



@property (nonatomic, copy, nonnull) NSRunLoopMode runLoopMode;
@end

#endif
