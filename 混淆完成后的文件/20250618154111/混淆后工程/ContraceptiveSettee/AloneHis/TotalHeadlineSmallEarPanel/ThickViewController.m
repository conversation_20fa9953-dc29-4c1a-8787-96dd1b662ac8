

#import "ThickViewController.h"
#import "Blink.h"
#import "MediaDerivedAssignMattingActionExpensive.h"
#import "PopDrainImmediateOnePlug.h"
#import "IndexRaiseConfig.h"

@interface ThickViewController ()
@property (nonatomic, strong) UITextView *textView;
@property (nonatomic, strong) MediaDerivedAssignMattingActionExpensive *storeSongRomanianSpotlightRunning;
@property (nonatomic, strong) PopDrainImmediateOnePlug *revisionChunkyEntropyUrgentIgnore;
@property (nonatomic, strong) NSDate *addBriefDate; 
@end

static PopDrainImmediateOnePlug *provinceTrainerBodyIdentityBinThe = nil;
static MediaDerivedAssignMattingActionExpensive *queueMultipleSheetDeliverBadmintonFour = nil;

@implementation ThickViewController

+ (void)clangCupBar {
    
    [Blink hasBleedRealDismissalSubStrip];

    queueMultipleSheetDeliverBadmintonFour = [[MediaDerivedAssignMattingActionExpensive alloc] init];
    queueMultipleSheetDeliverBadmintonFour.icyBedLevel = MagnesiumCursorEarModifiedPrimaries;

    [Blink patchResignInactiveMillibarsRole:queueMultipleSheetDeliverBadmintonFour];

    provinceTrainerBodyIdentityBinThe = [[PopDrainImmediateOnePlug alloc] init];
    provinceTrainerBodyIdentityBinThe.icyBedLevel = BagEarRetried;
    provinceTrainerBodyIdentityBinThe.locator = 7;
    provinceTrainerBodyIdentityBinThe.leakyLettishOperationKindProfile = YES;
    [Blink patchResignInactiveMillibarsRole:provinceTrainerBodyIdentityBinThe];

    [provinceTrainerBodyIdentityBinThe needRawSubFeed];
}

+ (PopDrainImmediateOnePlug *)contactFreestyleFormatJustRomanSee {
    return provinceTrainerBodyIdentityBinThe;
}
+ (MediaDerivedAssignMattingActionExpensive *)triggerPassAffectingSucceededScannedStartup {
    return queueMultipleSheetDeliverBadmintonFour;
}

- (MediaDerivedAssignMattingActionExpensive *)storeSongRomanianSpotlightRunning {
    return queueMultipleSheetDeliverBadmintonFour;
}

+ (void)showFromViewController:(UIViewController *)parentVC {
    ThickViewController *elder = [[ThickViewController alloc] init];
    UINavigationController *mix = [[UINavigationController alloc] initWithRootViewController:elder];
    mix.modalPresentationStyle = UIModalPresentationFullScreen;
    [parentVC presentViewController:mix animated:YES completion:nil];
}

- (void)viewDidLoad {
    [super viewDidLoad];

    self.title = theItsRatio.alienGreenFamilyPoliciesReportGigahertz;
    self.view.backgroundColor = [UIColor systemBackgroundColor];

    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc]
                                            initWithBarButtonSystemItem:UIBarButtonSystemItemCancel
                                            target:self
                                            action:@selector(shiftAction)];

    self.navigationItem.rightBarButtonItems = @[
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemRefresh
                                                      target:self
                                                      action:@selector(bedFlipAction)],
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemAction
                                                      target:self
                                                      action:@selector(towerAction)],
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemBookmarks
                                                      target:self
                                                      action:@selector(checkerTryAction)]
    ];
    self.navigationController.navigationBar.layoutMargins = UIEdgeInsetsMake(0, 0, 0, -10);

    _textView = [[UITextView alloc] init];
    _textView.font = [UIFont systemFontOfSize:11];
    _textView.editable = NO;
    _textView.backgroundColor = [UIColor systemBackgroundColor];
    _textView.textColor = [UIColor labelColor];
    _textView.translatesAutoresizingMaskIntoConstraints = NO;
    _textView.showsVerticalScrollIndicator = YES;
    _textView.showsHorizontalScrollIndicator = YES;
    _textView.alwaysBounceVertical = YES;
    
    _textView.scrollEnabled = YES;
    [self.view addSubview:_textView];

    [NSLayoutConstraint activateConstraints:@[
        [_textView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [_textView.leadingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.leadingAnchor constant:8],
        [_textView.trailingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.trailingAnchor constant:-8],
        [_textView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];

    self.revisionChunkyEntropyUrgentIgnore = [ThickViewController contactFreestyleFormatJustRomanSee];

    [self senseEnd];
}

- (void)senseEnd {
    if (!self.revisionChunkyEntropyUrgentIgnore) {
        _textView.text = theItsRatio.manOperandAndYellowRearSuch;
        return;
    }

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSString *logs;
        if (self.addBriefDate) {
            logs = [self.revisionChunkyEntropyUrgentIgnore maskMaleHitDate:self.addBriefDate];
        } else {
            logs = [self.revisionChunkyEntropyUrgentIgnore commandRope];
        }

        dispatch_async(dispatch_get_main_queue(), ^{
            if (logs.length > 0) {
                self.textView.text = logs;
                
                [self.textView scrollRangeToVisible:NSMakeRange(logs.length - 1, 1)];
            } else {
                self.textView.text = theItsRatio.enterIllBetweenPongMeteringLexicon;
            }

            [self electricSix];
        });
    });
}

- (void)shiftAction {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)bedFlipAction {
    [self senseEnd];
}

- (void)electricSix {
    if (self.addBriefDate) {
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.dateFormat = theItsRatio.whoRecycleYardTypeWayMidArmpit;
        NSString *softAmount = [formatter stringFromDate:self.addBriefDate];

        NSCalendar *calendar = [NSCalendar currentCalendar];
        if ([calendar isDateInToday:self.addBriefDate]) {
            self.title = theItsRatio.unsavedVisionSemicolonFeaturesHelpersJob;
        } else if ([calendar isDateInYesterday:self.addBriefDate]) {
            self.title = theItsRatio.sumZoneSoundOurParameterLayeringCompress;
        } else {
            self.title = softAmount;
        }
    } else {
        self.title = theItsRatio.sectionDolbyObtainGolfDrawScale;
    }
}

- (void)checkerTryAction {
    if (!self.revisionChunkyEntropyUrgentIgnore) {
        return;
    }

    NSArray<NSDate *> *availableDates = [self.revisionChunkyEntropyUrgentIgnore getBadTatar];
    if (availableDates.count == 0) {
        UIAlertController *polar = [UIAlertController alertControllerWithTitle:theItsRatio.looseRotationYardLooseRawMix
                                                                       message:theItsRatio.enterIllBetweenPongMeteringLexicon
                                                                preferredStyle:UIAlertControllerStyleAlert];
        [polar addAction:[UIAlertAction actionWithTitle:theItsRatio.yiddishTextualDeclineMetalResignError style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:polar animated:YES completion:nil];
        return;
    }

    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:theItsRatio.watchedNowPostcardRoomBufferBedLegal
                                                                         message:nil
                                                                  preferredStyle:UIAlertControllerStyleActionSheet];

    [actionSheet addAction:[UIAlertAction actionWithTitle:theItsRatio.sectionDolbyObtainGolfDrawScale
                                                    style:UIAlertActionStyleDefault
                                                  handler:^(UIAlertAction *action) {
        self.addBriefDate = nil;
        [self senseEnd];
    }]];

    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = theItsRatio.whoRecycleYardTypeWayMidArmpit;

    NSCalendar *calendar = [NSCalendar currentCalendar];

    for (NSDate *date in availableDates) {
        NSString *title;
        if ([calendar isDateInToday:date]) {
            title = theItsRatio.unsavedVisionSemicolonFeaturesHelpersJob;
        } else if ([calendar isDateInYesterday:date]) {
            title = theItsRatio.sumZoneSoundOurParameterLayeringCompress;
        } else {
            title = [formatter stringFromDate:date];
        }

        [actionSheet addAction:[UIAlertAction actionWithTitle:title
                                                        style:UIAlertActionStyleDefault
                                                      handler:^(UIAlertAction *action) {
            self.addBriefDate = date;
            [self senseEnd];
        }]];
    }

    [actionSheet addAction:[UIAlertAction actionWithTitle:theItsRatio.truncateQuickBackupChargingDecryptExceeded style:UIAlertActionStyleCancel handler:nil]];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        actionSheet.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:actionSheet animated:YES completion:nil];
}

- (void)towerAction {
    if (!self.revisionChunkyEntropyUrgentIgnore) {
        return;
    }

    NSArray *ageStale = [self.revisionChunkyEntropyUrgentIgnore lastTopTied];
    if (ageStale.count == 0) {
        UIAlertController *polar = [UIAlertController alertControllerWithTitle:theItsRatio.looseRotationYardLooseRawMix
                                                                       message:theItsRatio.decayUseLowChunkSumRevision
                                                                preferredStyle:UIAlertControllerStyleAlert];
        [polar addAction:[UIAlertAction actionWithTitle:theItsRatio.yiddishTextualDeclineMetalResignError style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:polar animated:YES completion:nil];
        return;
    }

    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:theItsRatio.manganeseBiotinHeightIncorrectKeysBottomSharing
                                                                         message:nil
                                                                  preferredStyle:UIAlertControllerStyleActionSheet];

    [actionSheet addAction:[UIAlertAction actionWithTitle:theItsRatio.instancesColleagueSurrogateCopperPrefixAccountTrash
                                                    style:UIAlertActionStyleDefault
                                                  handler:^(UIAlertAction *action) {
        [self recordedMark];
    }]];

    for (NSURL *fileURL in ageStale) {
        NSString *fileName = fileURL.lastPathComponent;
        [actionSheet addAction:[UIAlertAction actionWithTitle:[NSString stringWithFormat:theItsRatio.randomOutputStylusAdjectiveCadenceIndexToo, fileName]
                                                        style:UIAlertActionStyleDefault
                                                      handler:^(UIAlertAction *action) {
            [self blackPutFile:fileURL];
        }]];
    }

    [actionSheet addAction:[UIAlertAction actionWithTitle:theItsRatio.truncateQuickBackupChargingDecryptExceeded style:UIAlertActionStyleCancel handler:nil]];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        actionSheet.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:actionSheet animated:YES completion:nil];
}

- (void)recordedMark {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        
        NSString *marquee = [self.revisionChunkyEntropyUrgentIgnore theSinEndError];

        dispatch_async(dispatch_get_main_queue(), ^{
            if (marquee.length > 0) {
                UIActivityViewController *auditedIts = [[UIActivityViewController alloc]
                                                       initWithActivityItems:@[marquee]
                                                       applicationActivities:nil];

                if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
                    auditedIts.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
                }

                [self presentViewController:auditedIts animated:YES completion:nil];
            }
        });
    });
}

- (void)blackPutFile:(NSURL *)fileURL {
    UIActivityViewController *auditedIts = [[UIActivityViewController alloc]
                                           initWithActivityItems:@[fileURL]
                                           applicationActivities:nil];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        auditedIts.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:auditedIts animated:YES completion:nil];
}

@end
