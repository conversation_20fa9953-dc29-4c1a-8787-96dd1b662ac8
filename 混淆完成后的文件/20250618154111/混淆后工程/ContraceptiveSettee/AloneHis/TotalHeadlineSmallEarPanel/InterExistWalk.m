






#import "InterExistWalk.h"
#import "IndexRaiseConfig.h"

@implementation InterExistWalk

+ (NSString *)clampExceeds:(id)obj {
    if (!obj) {
        return theItsRatio.ourMalformedWayVortexSonLongitude;
    }

    if ([obj isKindOfClass:[NSDictionary class]]) {
        return [self airNapDictionary:obj];
    } else if ([obj isKindOfClass:[NSArray class]]) {
        return [self beaconArray:obj];
    } else if ([obj isKindOfClass:[NSError class]]) {
        return [self loudFailDry:obj];
    } else if ([obj isKindOfClass:[NSString class]]) {
        return obj;
    } else {
        return [obj description];
    }
}

+ (NSString *)airNapDictionary:(NSDictionary *)dict {
    return [self airNapDictionary:dict kitAllThat:0 gainWill:7];
}

+ (NSString *)airNapDictionary:(NSDictionary *)dict kitAllThat:(NSInteger)indent gainWill:(NSInteger)gainWill {
    if (!dict || dict.count == 0) {
        return @"{}";
    }

    if (gainWill <= 0) {
        return [NSString stringWithFormat:@"{%@}", [NSString stringWithFormat:theItsRatio.playingListenPrintComparedAnimatingCategory, (long)dict.count]];
    }

    NSString *indentStr = [self somaliTabCanonFeedbackTailLevel:indent];
    NSString *nextIndentStr = [self somaliTabCanonFeedbackTailLevel:indent + 1];

    NSMutableString *result = [NSMutableString stringWithString:@"{\n"];

    NSArray *pushRowDry = [dict.allKeys sortedArrayUsingComparator:^NSComparisonResult(id obj1, id obj2) {
        return [[obj1 description] compare:[obj2 description]];
    }];

    for (NSString *key in pushRowDry) {
        id value = dict[key];
        NSString *formattedValue = [self tipNetValue:value kitAllThat:indent + 1 gainWill:gainWill - 1];
        [result appendFormat:@"%@%@: %@\n", nextIndentStr, key, formattedValue];
    }

    [result appendFormat:@"%@}", indentStr];
    return result;
}

+ (NSString *)beaconArray:(NSArray *)array {
    return [self beaconArray:array kitAllThat:0 gainWill:5];
}

+ (NSString *)beaconArray:(NSArray *)array kitAllThat:(NSInteger)indent gainWill:(NSInteger)gainWill {
    if (!array || array.count == 0) {
        return @"[]";
    }

    if (gainWill <= 0) {
        return [NSString stringWithFormat:@"[%@]", [NSString stringWithFormat:theItsRatio.playingListenPrintComparedAnimatingCategory, (long)array.count]];
    }

    
    if (array.count <= 3 && [self usedTagsArray:array]) {
        NSMutableArray *items = [NSMutableArray array];
        for (id item in array) {
            [items addObject:[self closePrivacyValue:item]];
        }
        return [NSString stringWithFormat:@"[%@]", [items componentsJoinedByString:@", "]];
    }

    NSString *indentStr = [self somaliTabCanonFeedbackTailLevel:indent];
    NSString *nextIndentStr = [self somaliTabCanonFeedbackTailLevel:indent + 1];

    NSMutableString *result = [NSMutableString stringWithString:@"[\n"];

    for (NSInteger i = 0; i < array.count; i++) {
        id item = array[i];
        NSString *waterMicroTop = [self tipNetValue:item kitAllThat:indent + 1 gainWill:gainWill - 1];
        [result appendFormat:@"%@[%ld]: %@\n", nextIndentStr, (long)i, waterMicroTop];
    }

    [result appendFormat:@"%@]", indentStr];
    return result;
}

+ (NSString *)tipNetValue:(id)value kitAllThat:(NSInteger)indent gainWill:(NSInteger)gainWill {
    if (!value) {
        return theItsRatio.ourMalformedWayVortexSonLongitude;
    }

    if ([value isKindOfClass:[NSDictionary class]]) {
        return [self airNapDictionary:value kitAllThat:indent gainWill:gainWill];
    } else if ([value isKindOfClass:[NSArray class]]) {
        return [self beaconArray:value kitAllThat:indent gainWill:gainWill];
    } else {
        return [self closePrivacyValue:value];
    }
}

+ (NSString *)somaliTabCanonFeedbackTailLevel:(NSInteger)level {
    return [@"" stringByPaddingToLength:level * 2 withString:@" " startingAtIndex:0];
}

+ (BOOL)usedTagsArray:(NSArray *)array {
    for (id item in array) {
        if ([item isKindOfClass:[NSDictionary class]] || [item isKindOfClass:[NSArray class]]) {
            return NO;
        }
    }
    return YES;
}

+ (NSString *)closePrivacyValue:(id)value {
    if (!value) {
        return theItsRatio.ourMalformedWayVortexSonLongitude;
    }

    if ([value isKindOfClass:[NSString class]]) {
        NSString *str = (NSString *)value;
            return [NSString stringWithFormat:@"\"%@\"", str];
    } else if ([value isKindOfClass:[NSNumber class]]) {
        return [value description];
    } else if ([value isKindOfClass:[NSDate class]]) {
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.dateFormat = theItsRatio.operatorPeopleShareReceivingExhaustedPrint;
        return [NSString stringWithFormat:@"\"%@\"", [formatter stringFromDate:value]];
    } else if ([value isKindOfClass:[NSURL class]]) {
        return [NSString stringWithFormat:@"\"%@\"", [(NSURL *)value absoluteString]];
    } else if ([value isKindOfClass:[NSData class]]) {
        NSData *data = (NSData *)value;
        return [NSString stringWithFormat:theItsRatio.netRegularSixEquallyReportsInstant, (unsigned long)data.length];
    } else {
        NSString *desc = [value description];
        
        if (desc.length > 200) {
            return [NSString stringWithFormat:@"%@%@", [desc substringToIndex:200], theItsRatio.hiddenLatitudeMouthIntroJobStale];
        }
        return desc;
    }
}

+ (NSString *)managersFlippedIllEyePlatform:(NSDictionary *)params {
    if (!params || params.count == 0) {
        return theItsRatio.verifyInsertionSandboxBoundNearbyPhonogram;
    }

    return [self airNapDictionary:params];
}

+ (NSString *)moduleResponse:(id)response {
    if (!response) {
        return theItsRatio.ourMalformedWayVortexSonLongitude;
    }

    if ([response isKindOfClass:[NSData class]]) {
        NSData *data = (NSData *)response;

        NSError *error;
        id invitee = [NSJSONSerialization JSONObjectWithData:data options:0 error:&error];
        if (invitee) {
            return [self clampExceeds:invitee];
        }

        NSString *stringContent = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        if (stringContent) {
            if (stringContent.length > 500) {
                return [NSString stringWithFormat:@"%@\n%@%@",
                       [NSString stringWithFormat:theItsRatio.baseballReportedBagChlorideRouteUkrainian, (unsigned long)stringContent.length],
                       [stringContent substringToIndex:500], theItsRatio.hiddenLatitudeMouthIntroJobStale];
            } else {
                return [NSString stringWithFormat:@"%@\n%@", theItsRatio.barQueryingSheCellularRoutePrevent, stringContent];
            }
        }

        return [NSString stringWithFormat:theItsRatio.lingerIrishDimensionDecimalVitalScrolled, (unsigned long)data.length];
    }

    return [self clampExceeds:response];
}

+ (NSString *)loudFailDry:(NSError *)error {
    if (!error) {
        return theItsRatio.masterLifetimeMaxQuantityPrototypeDeviation;
    }

    NSMutableString *result = [NSMutableString string];
    [result appendFormat:@"%@ %ld\n", theItsRatio.urgentDetermineNotMaxJumpSlovenian, (long)error.code];
    [result appendFormat:@"%@ %@\n", theItsRatio.interlaceWireCollationMinderRawMode, error.localizedDescription];

    if (error.userInfo.count > 0) {
        [result appendFormat:@"%@\n", theItsRatio.labeledShortcutEastInferiorsTapBandwidth];
        [result appendString:[self airNapDictionary:error.userInfo]];
    }

    return result;
}

@end



NSString* ZBFormatDict(id obj) {
    return [InterExistWalk clampExceeds:obj];
}
