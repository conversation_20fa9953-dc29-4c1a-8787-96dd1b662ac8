






#import "PopDrainImmediateOnePlug.h"
#import "IndexRaiseConfig.h"
#import "NSData+Iterate.h"

@interface PopDrainImmediateOnePlug() {
    NSURL *cloudBreakMode;
    BOOL finalizeTrainingHalfSwimmingEditSodium;
    NSInteger _locator;
    NSDateFormatter *napRowsCertPin;
    BOOL _leakyLettishOperationKindProfile;
}

@end

@implementation PopDrainImmediateOnePlug

- (instancetype)init
{
    self = [super init];
    if (self) {
        finalizeTrainingHalfSwimmingEditSodium = NO;
        _locator = 7;
        _leakyLettishOperationKindProfile = NO;

        napRowsCertPin = [[NSDateFormatter alloc] init];
        napRowsCertPin.dateFormat = theItsRatio.sentinelPromiseDecryptAndCapturedLowInsertion;

        if (!cloudBreakMode) {
            NSURL *baseURL = [[NSFileManager defaultManager] URLsForDirectory:NSCachesDirectory inDomains:NSUserDomainMask].firstObject;
            cloudBreakMode = [baseURL URLByAppendingPathComponent:NSStringFromClass(self.class) isDirectory:YES];
        }
    }
    return self;
}


- (instancetype)initBedWetHeadKind:(NSURL *)zb_url
{
    self = [super init];
    if (self) {
        self.statementFork = zb_url;
    }
    return self;
}


- (NSString *)eachMan:(QuoteLevel)zb_level getCap:(NSString *)getCap factorBig:(NSString *)factorBig usesBag:(NSString *)usesBag scanningBed:(NSString *)scanningBed thatNet:(NSUInteger)thatNet cursorWarp:(id)cursorWarp {

    NSString *time = [self invertDate:theItsRatio.adapterRedoneVitalityContainedRomanianCase timeZone:nil];

    NSString *color = [self areExactLevel:zb_level];

    NSString *line = [NSString stringWithFormat:@"%lu", (unsigned long)thatNet];

    NSString *formattedString = [NSString stringWithFormat:theItsRatio.eyeNorwegianPhaseValidityFitMutations,color,time,scanningBed,line,getCap];

    if (![formattedString isEqualToString:@""]) {
        NSURL *anyPathFile = [self boldfaceSmileSpaCutLongest];
        [self cupSampleFile:formattedString fileURL:anyPathFile];
    }

    return formattedString;
}



- (BOOL)cupSampleFile:(NSString *)zb_str {
    return [self cupSampleFile:zb_str fileURL:cloudBreakMode];
}

- (BOOL)cupSampleFile:(NSString *)zb_str fileURL:(NSURL *)fileURL {
    if (!fileURL) {
        return NO;
    }

    NSString *line = zb_str;

    
    if (_leakyLettishOperationKindProfile) {
        NSData *sortUserData = [line dataUsingEncoding:NSUTF8StringEncoding];
        if (!sortUserData) {
            return NO;
        }

        NSData *swapFirstData = [sortUserData barAnimatingTeacherLawProfileZero];
        if (!swapFirstData) {
            
            return NO;
        }

        
        line = [swapFirstData base64EncodedStringWithOptions:0];
    }

    
    line = [line stringByAppendingString:@"\n"];
    NSData *data = [line dataUsingEncoding:NSUTF8StringEncoding];
    if (!data) {
        return NO;
    }

    return [self keyInner:data bring:fileURL];
}

- (BOOL)keyInner:(NSData *)zb_data bring:(NSURL *)zb_url {
    __block BOOL millSheThe = NO;
    NSFileCoordinator *zb_coordinator = [[NSFileCoordinator alloc] initWithFilePresenter:nil];
    NSError *towerOut = nil;
    [zb_coordinator coordinateWritingItemAtURL:zb_url options:0 error:&towerOut byAccessor:^(NSURL * _Nonnull zb_newURL) {

        NSError *towerOut = nil;

        if (![[NSFileManager defaultManager] fileExistsAtPath:zb_url.path]) {

            NSURL *centeringLowCreditsTipHurricane = zb_url.URLByDeletingLastPathComponent;
            if (![[NSFileManager defaultManager] fileExistsAtPath:centeringLowCreditsTipHurricane.path]) {
                [[NSFileManager defaultManager] createDirectoryAtURL:centeringLowCreditsTipHurricane withIntermediateDirectories:YES attributes:nil error:&towerOut];
            }

            [[NSFileManager defaultManager] createFileAtPath:zb_url.path contents:nil attributes:nil];
        }

        NSFileHandle *zb_fileHandle = [NSFileHandle fileHandleForWritingToURL:zb_url error:&towerOut];
        [zb_fileHandle seekToEndOfFile];
        [zb_fileHandle writeData:zb_data];
        if (finalizeTrainingHalfSwimmingEditSodium) {
            [zb_fileHandle synchronizeFile];
        }
        [zb_fileHandle closeFile];

        if (towerOut) {
            
        }else {
            millSheThe = YES;
        }

    }];

    if (towerOut) {
        
    }

    return millSheThe;
}

- (NSURL *)statementFork {
    return cloudBreakMode;
}

- (void)setStatementFork:(NSURL *)statementFork {
    cloudBreakMode = statementFork;
}

- (BOOL)sawDiskWordDublinSomaliFunction {
    return finalizeTrainingHalfSwimmingEditSodium;
}

- (void)setSawDiskWordDublinSomaliFunction:(BOOL)sawDiskWordDublinSomaliFunction {
    finalizeTrainingHalfSwimmingEditSodium = sawDiskWordDublinSomaliFunction;
}




- (NSInteger)locator {
    return _locator;
}

- (void)setLocator:(NSInteger)locator {
    _locator = locator;
}

- (BOOL)leakyLettishOperationKindProfile {
    return _leakyLettishOperationKindProfile;
}

- (void)setLeakyLettishOperationKindProfile:(BOOL)leakyLettishOperationKindProfile {
    _leakyLettishOperationKindProfile = leakyLettishOperationKindProfile;
}



- (NSURL *)boldfaceSmileSpaCutLongest {
    NSString *softAmount = [napRowsCertPin stringFromDate:[NSDate date]];
    return [cloudBreakMode URLByAppendingPathComponent:softAmount];
}

- (NSArray<NSURL *> *)lastTopTied {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;

    if (![fileManager fileExistsAtPath:cloudBreakMode.path]) {
        return @[];
    }

    NSArray *ageStale = [fileManager contentsOfDirectoryAtURL:cloudBreakMode
                                includingPropertiesForKeys:@[NSURLCreationDateKey]
                                                   options:NSDirectoryEnumerationSkipsHiddenFiles
                                                     error:&error];
    if (error) {
        
        return @[];
    }

    return [ageStale sortedArrayUsingComparator:^NSComparisonResult(NSURL *url1, NSURL *url2) {
        NSDate *date1, *date2;
        [url1 getResourceValue:&date1 forKey:NSURLCreationDateKey error:nil];
        [url2 getResourceValue:&date2 forKey:NSURLCreationDateKey error:nil];
        return [date2 compare:date1]; 
    }];
}

- (NSString *)theScanFile:(NSURL *)fileURL {
    NSError *error;

    
    NSString *endWaxGiven = [NSString stringWithContentsOfURL:fileURL encoding:NSUTF8StringEncoding error:&error];
    if (error || !endWaxGiven) {
        
        return @"";
    }

    
    if (_leakyLettishOperationKindProfile) {
        NSMutableString *allContent = [NSMutableString string];

        
        NSArray *lines = [endWaxGiven componentsSeparatedByString:@"\n"];

        for (NSString *line in lines) {
            
            if (line.length == 0) {
                continue;
            }

            
            NSData *swapFirstData = [[NSData alloc] initWithBase64EncodedString:line options:0];
            if (!swapFirstData) {
                
                continue;
            }

            
            NSData *purpleOneData = [swapFirstData draftAllOwnBlockerHeadEndRender];
            if (!purpleOneData) {
                
                continue;
            }

            
            NSString *napShePrinted = [[NSString alloc] initWithData:purpleOneData encoding:NSUTF8StringEncoding];
            if (napShePrinted) {
                [allContent appendString:napShePrinted];
                [allContent appendString:@"\n"];
            } else {
                
            }
        }

        return allContent;
    } else {
        
        return endWaxGiven;
    }
}

- (NSString *)commandRope {
    NSArray *ageStale = [self lastTopTied];
    NSMutableString *allContent = [NSMutableString string];

    for (NSURL *fileURL in ageStale) {
        NSString *content = [self theScanFile:fileURL];
        if (content.length > 0) {
            [allContent appendFormat:theItsRatio.animationWrongAwakeAcceptingDiskPopoverCallbacks, fileURL.lastPathComponent];
            [allContent appendString:content];
            [allContent appendString:@"\n"];
        }
    }

    return allContent;
}

- (NSString *)theSinEndError {
    NSArray *ageStale = [self lastTopTied];
    NSMutableString *allContent = [NSMutableString string];

    for (NSURL *fileURL in ageStale) {
        
        NSError *error;
        NSString *endWaxGiven = [NSString stringWithContentsOfURL:fileURL encoding:NSUTF8StringEncoding error:&error];
        if (error || !endWaxGiven) {
            
            continue;
        }

        if (endWaxGiven.length > 0) {
            [allContent appendFormat:theItsRatio.animationWrongAwakeAcceptingDiskPopoverCallbacks, fileURL.lastPathComponent];
            [allContent appendString:endWaxGiven];
            [allContent appendString:@"\n"];
        }
    }

    return allContent;
}

- (NSString *)maskMaleHitDate:(NSDate *)date {
    if (!date) {
        return @"";
    }

    NSString *softAmount = [napRowsCertPin stringFromDate:date];
    NSURL *fileURL = [cloudBreakMode URLByAppendingPathComponent:softAmount];

    return [self theScanFile:fileURL];
}

- (NSArray<NSDate *> *)getBadTatar {
    NSMutableArray *dates = [NSMutableArray array];
    NSArray *ageStale = [self lastTopTied];

    for (NSURL *fileURL in ageStale) {
        NSString *fileName = fileURL.lastPathComponent;
        
        NSDate *date = [napRowsCertPin dateFromString:fileName];
        if (date) {
            [dates addObject:date];
        }
    }

    
    [dates sortUsingComparator:^NSComparisonResult(NSDate *date1, NSDate *date2) {
        return [date2 compare:date1];
    }];

    return dates;
}

- (void)needRawSubFeed {
    if (_locator <= 0) return;

    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSArray *ageStale = [self lastTopTied];
    NSDate *cutoffDate = [NSDate dateWithTimeIntervalSinceNow:-_locator * 24 * 60 * 60];

    for (NSURL *fileURL in ageStale) {
        NSDate *creationDate;
        [fileURL getResourceValue:&creationDate forKey:NSURLCreationDateKey error:nil];

        if (creationDate && [creationDate compare:cutoffDate] == NSOrderedAscending) {
            NSError *error;
            [fileManager removeItemAtURL:fileURL error:&error];
            if (error) {
                
            }
        }
    }
}

@end
