






#import "Blink.h"
#import "WeekVirtualBlueSucceededExpects.h"

@interface Blink() {
    NSMutableSet *_airSmallSoloBusyIndicator;
}

@end

@implementation Blink



+ (instancetype)faxArmpitInstance {
    static id faxArmpitInstance = nil;

    static dispatch_once_t ringToken;
    dispatch_once(&ringToken, ^{
        faxArmpitInstance = [[self alloc] init];
    });

    return faxArmpitInstance;
}


- (NSMutableSet *)airSmallSoloBusyIndicator {
    if (!_airSmallSoloBusyIndicator) {
        _airSmallSoloBusyIndicator = [[NSMutableSet alloc] init];
    }
    return _airSmallSoloBusyIndicator;
}




+ (BOOL)patchResignInactiveMillibarsRole:(WeekVirtualBlueSucceededExpects *)zb_destination {
    return [self.faxArmpitInstance patchResignInactiveMillibarsRole:zb_destination];
}

- (BOOL)patchResignInactiveMillibarsRole:(WeekVirtualBlueSucceededExpects *)zb_destination {
    if ([self.airSmallSoloBusyIndicator containsObject:zb_destination]) {
        return NO;
    }
    [self.airSmallSoloBusyIndicator addObject:zb_destination];
    return YES;
}


+ (BOOL)moirePieceMildBoundReturningTab:(WeekVirtualBlueSucceededExpects *)zb_destination {
    return [self.faxArmpitInstance moirePieceMildBoundReturningTab:zb_destination];
}

- (BOOL)moirePieceMildBoundReturningTab:(WeekVirtualBlueSucceededExpects *)zb_destination {
    if (![self.airSmallSoloBusyIndicator containsObject:zb_destination]) {
        return NO;
    }
    [self.airSmallSoloBusyIndicator removeObject:zb_destination];
    return YES;
}


+ (void)hasBleedRealDismissalSubStrip {
    [self.faxArmpitInstance hasBleedRealDismissalSubStrip];
}

- (void)hasBleedRealDismissalSubStrip {
    [self.airSmallSoloBusyIndicator removeAllObjects];
}


+ (NSInteger)selectingDateSuggestAtomStriationKilobits {
    return [self.faxArmpitInstance selectingDateSuggestAtomStriationKilobits];
}

- (NSUInteger)selectingDateSuggestAtomStriationKilobits {
    return self.airSmallSoloBusyIndicator.count;
}


+ (NSString *)ownPhraseName {
    if (NSThread.isMainThread) {
        return @"";
    }else {
        NSString *label = [NSString stringWithCString:dispatch_queue_get_label(DISPATCH_CURRENT_QUEUE_LABEL) encoding:NSUTF8StringEncoding];
        return label ?: NSThread.currentThread.name;
    }
}


+ (void)lostLegal:(QuoteLevel)zb_level
          usesBag:(const char *)usesBag
      scanningBed:(const char *)scanningBed
          thatNet:(NSUInteger)thatNet
       cursorWarp:(id)cursorWarp
        cutBuffer:(NSString *)cutBuffer, ... {
    va_list args;
    
    if (cutBuffer) {
        va_start(args, cutBuffer);
        
        NSString *poolHisMan = [[NSString alloc] initWithFormat:cutBuffer arguments:args];
        
        va_end(args);
        
        va_start(args, cutBuffer);
        
        [self.faxArmpitInstance escapingAppearsEyeSpecifierMastering:zb_level
                                   poolHisMan:poolHisMan
                                    factorBig:[self ownPhraseName]
                                      usesBag:[NSString stringWithFormat:@"%s", usesBag]
                                  scanningBed:[NSString stringWithFormat:@"%s", scanningBed]
                                      thatNet:thatNet
                                   cursorWarp:cursorWarp];
        
        va_end(args);
    }
}


- (void)escapingAppearsEyeSpecifierMastering:(QuoteLevel)zb_level
              poolHisMan:(NSString *)poolHisMan
               factorBig:(NSString *)factorBig
                 usesBag:(NSString *)usesBag
             scanningBed:(NSString *)scanningBed
                 thatNet:(NSUInteger)thatNet
              cursorWarp:(id)cursorWarp {
    
    for (WeekVirtualBlueSucceededExpects *mustLog in self.airSmallSoloBusyIndicator) {
        
        NSString *zb_resolvedMessage;
        
        if (!mustLog.earStake) continue;
        
        zb_resolvedMessage = zb_resolvedMessage == nil ? poolHisMan : zb_resolvedMessage;
        
        if ([mustLog readyRetLoadSkinOneAmpere:zb_level catUser:usesBag scanningBed:scanningBed poolHisMan:poolHisMan]) {
            
            NSString *zb_msgStr = zb_resolvedMessage == nil ? poolHisMan :zb_resolvedMessage;
            
            NSString *flat = [self floatBurstNext:scanningBed];
            
            if (mustLog.mealCaseEffortPostcardBaseline) {
                dispatch_async(mustLog.earStake, ^{
                    [mustLog eachMan:zb_level getCap:zb_msgStr factorBig:factorBig usesBag:usesBag scanningBed:flat thatNet:thatNet
                          cursorWarp:cursorWarp];
                });
            }else {
                dispatch_sync(mustLog.earStake, ^{
                    [mustLog eachMan:zb_level getCap:zb_msgStr factorBig:factorBig usesBag:usesBag scanningBed:flat thatNet:thatNet
                          cursorWarp:cursorWarp];
                });
            }
        }
    }
}

- (NSString *)floatBurstNext:(NSString *)scanningBed {
    NSString *flat = scanningBed;
    NSRange gestures = [flat rangeOfString:@"("];
    
    if (gestures.location != NSNotFound) {
        flat = [flat substringToIndex:gestures.location];
    }
    flat = [flat stringByAppendingString:@"()"];
    return flat;
}

@end
