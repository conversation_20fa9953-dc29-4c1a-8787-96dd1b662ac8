// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		00E01C08779E0413B7759AB0 /* SDImageIOAnimatedCoderInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = D8C55DD0688C3A4895612A95 /* SDImageIOAnimatedCoderInternal.h */; };
		01AFB799A897594EBB87DD26 /* MQTTPersistence.h in Headers */ = {isa = PBXBuildFile; fileRef = 817820C617F296D5986FC529 /* MQTTPersistence.h */; };
		01B3096280F87E19B06E63BE /* CupExtrasMinInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = EF985E0EA75CBBA5414DF1EF /* CupExtrasMinInfo.h */; };
		02575943CF294F39DE026999 /* SDImageCachesManagerOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = BF5B543C1566FB4AB017E1B3 /* SDImageCachesManagerOperation.h */; };
		0258481ACA0DBDC2603AC7B7 /* TakeBitPenMask.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E036B0D30FF8BD1E58A89B6 /* TakeBitPenMask.m */; };
		02D425C6D31423E01F9D2F0D /* NSString+UnitZipHair.m in Sources */ = {isa = PBXBuildFile; fileRef = A93BCAD4FA5D18F1202ED71C /* NSString+UnitZipHair.m */; };
		03068D1E1F8552767A55AE5E /* SDAnimatedImage.h in Headers */ = {isa = PBXBuildFile; fileRef = BCB2168BC22F521ABFACED0E /* SDAnimatedImage.h */; };
		033CB17C46293FE82D586DB9 /* TagTreeViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = CC7812D6EC5BD63A9F752841 /* TagTreeViewController.h */; };
		038ECDCDD8117DC0805E8A6F /* MediaDerivedAssignMattingActionExpensive.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A29A1C68E460F5AACDA238B /* MediaDerivedAssignMattingActionExpensive.m */; };
		04967DCDB5FCFA8180DE6A2B /* NSString+Suffix.h in Headers */ = {isa = PBXBuildFile; fileRef = D46BA853EA07A921E84D7DAE /* NSString+Suffix.h */; };
		04CE4CCC81145C5593CFC7FD /* GCDTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = 134B34599D694BEC11B2E0A6 /* GCDTimer.m */; };
		0510F440BA19B4C25B964389 /* SDWebImageDownloaderConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D544ED9992FBF94A6B6BC65 /* SDWebImageDownloaderConfig.h */; };
		05E8429A018E83BC7BED3D9A /* RedoneMode.h in Headers */ = {isa = PBXBuildFile; fileRef = ADACC17BB309AE6E13D21687 /* RedoneMode.h */; };
		065EABB515124043A4F6765B /* NSString+UnitZipHair.h in Headers */ = {isa = PBXBuildFile; fileRef = 70B46B12EF1073DB2C779F1A /* NSString+UnitZipHair.h */; };
		0686AF8760E850D65410450A /* DoneSalt.h in Headers */ = {isa = PBXBuildFile; fileRef = 0A8FF55E211B91FA5A3DC041 /* DoneSalt.h */; };
		073DECFF3E1B5B671548066C /* UIImage+ForceDecode.m in Sources */ = {isa = PBXBuildFile; fileRef = 651C656400B2DF18ACF0C534 /* UIImage+ForceDecode.m */; };
		075198BE6DFC593F53C0D915 /* NextBoxMoreManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0C87A569427F1376BE30EA98 /* NextBoxMoreManager.m */; };
		081E3AA6D5CFF327E3A0B5EF /* MQTTTransportProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 91045984D41569A0A164037D /* MQTTTransportProtocol.h */; };
		0842E10B21ACA89227A7F3CF /* SDImageAPNGCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 4CA4F84FAC72902545F200CB /* SDImageAPNGCoder.h */; };
		08851532EE8483695A6F6CAF /* SDmetamacros.h in Headers */ = {isa = PBXBuildFile; fileRef = C17EF579017D64121CE8A049 /* SDmetamacros.h */; };
		08AC08D8251404BF21F3622C /* JabberStepperContrastHomeReportPrefers.h in Headers */ = {isa = PBXBuildFile; fileRef = 3B7775B5DF1CE8804C86293E /* JabberStepperContrastHomeReportPrefers.h */; };
		08EA6D0C3FBA50C87760699E /* MQTTCFSocketEncoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 2A2353528AFD9E75D98C173C /* MQTTCFSocketEncoder.h */; };
		09B7129457982637D1830195 /* WayRealArmForm.m in Sources */ = {isa = PBXBuildFile; fileRef = D7987C97A11E7C45B87E972A /* WayRealArmForm.m */; };
		0A7CB51AE9EF8B4394B7F63A /* SDImageIOAnimatedCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 1774018FE519AA2033D3E61C /* SDImageIOAnimatedCoder.m */; };
		0A9D14EC3A82E7CD420E913A /* SixTheAppleManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 34754C226F2DE8975C966C73 /* SixTheAppleManager.h */; };
		0A9DB88CAFA3D1381EB5A9E8 /* IndexRaiseConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = C7B999A5375678EF75A016B2 /* IndexRaiseConfig.m */; };
		0D45C0EEECC68DB793CF03F8 /* HelpRaceManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 48A79845B9C18C71111E2327 /* HelpRaceManager.h */; };
		0DCEDA0EF87CE92105181CA2 /* RealEntryTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 3841294CDD50042EE2A283D5 /* RealEntryTextField.m */; };
		0DD308C6DAAEA92914CCE0A7 /* KnowPictureAction.m in Sources */ = {isa = PBXBuildFile; fileRef = A7B0599DAF4434722E858125 /* KnowPictureAction.m */; };
		0E0E288F61B1FAE8031E013C /* RingAgeDegradedEyeTextualAppearingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 809AD5FEDE890BFB7BF2D80A /* RingAgeDegradedEyeTextualAppearingViewController.m */; };
		0EC2074020510BEB2C98320A /* MQTTInMemoryPersistence.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C629B00B2A750E58ED6EADA /* MQTTInMemoryPersistence.m */; };
		0ECB538EAAC84C172D1E105F /* NSData+Iterate.h in Headers */ = {isa = PBXBuildFile; fileRef = 950A6D715494534A53ABBA6C /* NSData+Iterate.h */; };
		0F09B1CBBBF9D6243935AF72 /* MASConstraint+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 9C11DE961FCED40282C8C861 /* MASConstraint+Private.h */; };
		0F395521FC5D91B8FA572C35 /* KeysReverting.m in Sources */ = {isa = PBXBuildFile; fileRef = 82F6DAD0CC7530FB8A5C58E1 /* KeysReverting.m */; };
		0FF0C87B04C846366F6ED90E /* SDAnimatedImageRep.h in Headers */ = {isa = PBXBuildFile; fileRef = AE55D08B4535CEAACC5EB21D /* SDAnimatedImageRep.h */; };
		10B2BC625C14333C0C5A545A /* MQTTSSLSecurityPolicyDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C98EF2A0F469453C3AD21A3 /* MQTTSSLSecurityPolicyDecoder.h */; };
		10BFA715E83355E348D415C7 /* NSImage+Compatibility.m in Sources */ = {isa = PBXBuildFile; fileRef = 412A79A5158A8AD51D63F245 /* NSImage+Compatibility.m */; };
		116EA396F122F3A5CB4E644F /* SequenceStreamKeepReadoutOff.m in Sources */ = {isa = PBXBuildFile; fileRef = 9FED3F0C78DC186D0BE8F38C /* SequenceStreamKeepReadoutOff.m */; };
		11FD0106E03880E90FB53226 /* SDImageCodersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = DDBA3084B225672762B6F661 /* SDImageCodersManager.h */; };
		128A95DA849C31EED64D6C53 /* SobArcheryIll.h in Headers */ = {isa = PBXBuildFile; fileRef = CA575C9C8C1EBA0A0B985C5A /* SobArcheryIll.h */; };
		12FEE33B5610B6C41F788D79 /* PasswordsMoveViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 270818EA3C73F848884F82B4 /* PasswordsMoveViewController.h */; };
		130B5D1A05AD11BE507ECE6F /* SDImageFramePool.h in Headers */ = {isa = PBXBuildFile; fileRef = F21AFDF68F47BF998D9C74F4 /* SDImageFramePool.h */; };
		1387760BA0CDCB6A89965EC1 /* ContraceptiveSettee.h in Headers */ = {isa = PBXBuildFile; fileRef = EF750E87C876699A5EFC0B75 /* ContraceptiveSettee.h */; settings = {ATTRIBUTES = (Public, ); }; };
		140F70491DDDEA60A2CE772F /* ViewController+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 92347F9B30CCAECB4DFA2110 /* ViewController+MASAdditions.m */; };
		14364B2276ED84B4753A6A63 /* SDImageCacheDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = E0E0921C8E9AD957B3FD735A /* SDImageCacheDefine.h */; };
		1498944863162737802E62C5 /* MASViewAttribute.h in Headers */ = {isa = PBXBuildFile; fileRef = B7E050E3519A730828E569F4 /* MASViewAttribute.h */; };
		15C25464E4A5887F1D76FE26 /* MQTTStrict.m in Sources */ = {isa = PBXBuildFile; fileRef = BBD2FC5FAAEA2C0E40D7F6E3 /* MQTTStrict.m */; };
		16281785A53D5B3F4405A589 /* LessCutWinCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 6DB1D47105FD1D92FD7DD337 /* LessCutWinCell.h */; };
		16F2A1AD8F5A4152D63856F5 /* SDDisplayLink.m in Sources */ = {isa = PBXBuildFile; fileRef = 93DF113FD033A4474797568F /* SDDisplayLink.m */; };
		1757427AE3733C89CD6F923C /* CleanWaist.h in Headers */ = {isa = PBXBuildFile; fileRef = A527FE57294D9CEE680833F0 /* CleanWaist.h */; };
		177851C12860ABEED61AC7E3 /* MediaDerivedAssignMattingActionExpensive.h in Headers */ = {isa = PBXBuildFile; fileRef = A3EB7AC0D4EA7571F675EB9C /* MediaDerivedAssignMattingActionExpensive.h */; };
		17E35C590D791D3C668FE74D /* GaspBusyStickyCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 772A931E96479EC5D6C3CA27 /* GaspBusyStickyCell.h */; };
		1921DE98438A7AC7A454B1F2 /* SpineCandidate.h in Headers */ = {isa = PBXBuildFile; fileRef = 7BA2E8AC31108FADD03B930C /* SpineCandidate.h */; };
		19FDDB2BB5E8959263C5D448 /* UsabilityOnlyController.m in Sources */ = {isa = PBXBuildFile; fileRef = 48EF292271472B12C9BCFFF0 /* UsabilityOnlyController.m */; };
		1A53D5E60461B2002393A9FF /* UIView+WebCacheState.h in Headers */ = {isa = PBXBuildFile; fileRef = 8DDCEDC3B1542BA795B09D05 /* UIView+WebCacheState.h */; };
		1A99FB2DB86788C1156C3553 /* EnergyPotassiumDelaySkinFeatCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A471F2BE9F74DBE32B184EBD /* EnergyPotassiumDelaySkinFeatCell.m */; };
		1AF557B1AC8822408F00C774 /* BusForDrawView.m in Sources */ = {isa = PBXBuildFile; fileRef = 83CCB1221EDEE4D4CFCD06B1 /* BusForDrawView.m */; };
		1B61E0CA9F75F917CE37BCF8 /* TripleAir.m in Sources */ = {isa = PBXBuildFile; fileRef = 74F72085F6733326E01D6E95 /* TripleAir.m */; };
		1BFF3EF8900CF595D9A5BAF1 /* EggNetCivilSon.h in Headers */ = {isa = PBXBuildFile; fileRef = 1D0717780CF4D1196DA89833 /* EggNetCivilSon.h */; };
		1C1E0F6A80928479EA4A40D2 /* ReadTiedKinButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 8B97A162ADC3D464593F9D64 /* ReadTiedKinButton.m */; };
		1C6A3F505C39AB57BE6E095D /* SDGraphicsImageRenderer.h in Headers */ = {isa = PBXBuildFile; fileRef = 7B6D4313289F080A1D6BA5CB /* SDGraphicsImageRenderer.h */; };
		1CE9A7F86D04D5043E6E0A7E /* MQTTMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = C8E2CC3CC89E336DEAF2B08B /* MQTTMessage.m */; };
		1F128EA2BE819C0511A5E923 /* SDImageAWebPCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 45B5518DB03DA61750349A9F /* SDImageAWebPCoder.h */; };
		1FD223D19DD1C38DA0DC3315 /* NSString+Suffix.m in Sources */ = {isa = PBXBuildFile; fileRef = 4916228DDE41692E820D6820 /* NSString+Suffix.m */; };
		1FEE23A6165FBE7D991FDC2C /* MagnitudeLateViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 0DF3405F7FB55AF45A00259C /* MagnitudeLateViewController.h */; };
		205E9490ABA95F394EC8ECA7 /* ViewController+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 2D38AE6B58A93A9692673DA6 /* ViewController+MASAdditions.h */; };
		20FC7BD1DB7F715A5A1CE986 /* SDWebImageOptionsProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = 6600EDC07BA6AD1899FB46B1 /* SDWebImageOptionsProcessor.m */; };
		2162265DCD1622792F167242 /* MQTTSessionSynchron.m in Sources */ = {isa = PBXBuildFile; fileRef = 6CD6782D48301BD79051D006 /* MQTTSessionSynchron.m */; };
		225889978192A979787F20B8 /* SDWebImageDownloaderOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = DBA4ED5040F2FFA454B2B4F5 /* SDWebImageDownloaderOperation.h */; };
		22A31172C7D77D42B48F2F74 /* SugarWetLessHostingSphere.m in Sources */ = {isa = PBXBuildFile; fileRef = D30578E73DC0EF53A9E63D1B /* SugarWetLessHostingSphere.m */; };
		2323D8613F7DD2BA99C1FB4B /* UIButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 82642752C6600F595408E5FB /* UIButton+WebCache.h */; };
		23C151348923E9F35195898B /* RestoresViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = C948A885BBEBF31E4CF1D52A /* RestoresViewController.m */; };
		249B036415A1EF4ECEBFE7E9 /* UIImage+CupImage.m in Sources */ = {isa = PBXBuildFile; fileRef = 7976D258750B0DD463607379 /* UIImage+CupImage.m */; };
		24AC03E9CDA09D26DE94EF26 /* SDImageAssetManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 5805A1130F27BB2DF90D36CE /* SDImageAssetManager.m */; };
		2695900DC9A5C17AFD4FCA43 /* ForAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 31B4F71C4BBE5B314BDAEBC5 /* ForAlertView.m */; };
		26AEB7AE88C96992BF120409 /* DogBufferViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 23F0B729AEB3980253E8899A /* DogBufferViewController.m */; };
		26C61486C5BEE00DDC4E493E /* SDMemoryCache.h in Headers */ = {isa = PBXBuildFile; fileRef = AD508EEEBE61CD1777DEC1AB /* SDMemoryCache.h */; };
		2715DC00B6FC22662A218401 /* SolidManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 98D55386CE84EE45F3D7204D /* SolidManager.h */; };
		274C02EBB86557F282B342A0 /* SDImageCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 6F2855EC54E62C5351F356AE /* SDImageCache.h */; };
		28C346CA8B2C8ADFAE3718DE /* RecordOrganizeDropCondensedPortraitsRelative.h in Headers */ = {isa = PBXBuildFile; fileRef = 442FAD31038351AC260C575E /* RecordOrganizeDropCondensedPortraitsRelative.h */; };
		29A6285B3B7AA0F014359491 /* UIImage+MultiFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C173E4F042047E44AFC22F5 /* UIImage+MultiFormat.m */; };
		29C7272A847444D47A838E38 /* SDWebImageTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = 25AFBEC497C3263C5E017B93 /* SDWebImageTransition.m */; };
		2AE322B205826DC44FACAFE3 /* GeneratorDraftCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 84563A3643FB7BD2E42335C3 /* GeneratorDraftCell.h */; };
		2BC7EAE13C819210BCFE5174 /* IndexRaiseConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 400DDDCC30F118D4F8648C00 /* IndexRaiseConfig.h */; };
		2C7EFCBF173CB85C79BDC263 /* SDImageLoadersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 54B75A5CE1F820C306CB4637 /* SDImageLoadersManager.m */; };
		2CE050F7D7412513FF625B0F /* InterExistWalk.m in Sources */ = {isa = PBXBuildFile; fileRef = 160249105CBDAAD53B63E94A /* InterExistWalk.m */; };
		2CEB6E6A791310F160E0E078 /* MASConstraintMaker.m in Sources */ = {isa = PBXBuildFile; fileRef = 78E86B2621B8DD9D603E19A1 /* MASConstraintMaker.m */; };
		2D6F953BF13B5D1FE7A26F8A /* ContraceptiveSettee.m in Sources */ = {isa = PBXBuildFile; fileRef = 3EEA181AF0699ED0B7698F88 /* ContraceptiveSettee.m */; };
		2EAB2A47EF79C5FECF6FB901 /* SDImageCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 00621BBFB161DF7DD4DE4001 /* SDImageCoder.h */; };
		2EB4344DA185E0E79ECC395A /* SDWebImageDownloaderConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 3036842952CA15E1ACC9BE0F /* SDWebImageDownloaderConfig.m */; };
		2F925FDDAA4341276DB08559 /* SDImageCodersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6841C289CEF7571E0BBD7114 /* SDImageCodersManager.m */; };
		303AAB38992E46CBB8CE34F4 /* UIImage+GIF.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D0BDDD16B7F8818E17B3226 /* UIImage+GIF.m */; };
		3072F57C833C6EE043808FCD /* MillHeightWill.h in Headers */ = {isa = PBXBuildFile; fileRef = 7ABFB1C75CB985AA7960C57A /* MillHeightWill.h */; };
		30E64CAFFEC43F6A8A0CC213 /* SayToast.m in Sources */ = {isa = PBXBuildFile; fileRef = 3EEE9B42069AE50949100F46 /* SayToast.m */; };
		31520B50A32E0BCC49AB02FE /* GeneratorDraftCell.m in Sources */ = {isa = PBXBuildFile; fileRef = F028E0878B8D444F489CE4AB /* GeneratorDraftCell.m */; };
		318F9545A081A2DD00BB894E /* DayQueryWho.m in Sources */ = {isa = PBXBuildFile; fileRef = 5DA13F14FB09778E2E4BD65E /* DayQueryWho.m */; };
		32F4198970A0D5905D2717DE /* UIImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 2AE9C608D7CE57A0E4C5C3D0 /* UIImageView+WebCache.m */; };
		35B69FE6B7C559DE26856B89 /* SDImageCoderHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = A8CD3A76A680BAD6E374399D /* SDImageCoderHelper.m */; };
		35EC2FE890111A6ED6F4FE4E /* InferOverViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0534CB0D1428BF495B49B465 /* InferOverViewController.m */; };
		36131AA0170048F57DEF2B9F /* SexualUtilitiesScopeSkipKilobits.h in Headers */ = {isa = PBXBuildFile; fileRef = ECA369802F705D5E9BF8F025 /* SexualUtilitiesScopeSkipKilobits.h */; };
		36C74C904B61AC01A75E46B3 /* UIImage+ExtendedCacheData.m in Sources */ = {isa = PBXBuildFile; fileRef = 75A3193784DF19F01C8B4F69 /* UIImage+ExtendedCacheData.m */; };
		36D1F82789A358EB481C1BE5 /* BetweenMixList.h in Headers */ = {isa = PBXBuildFile; fileRef = AA11661D4C4C9049B6207A7C /* BetweenMixList.h */; };
		36FB96DC48F092EF99614CF9 /* NSObject+CivilAdoptMindfulCoachedCap.m in Sources */ = {isa = PBXBuildFile; fileRef = 672B59D2016BE6EE53A5F55F /* NSObject+CivilAdoptMindfulCoachedCap.m */; };
		38062E36423A45082B439F04 /* TripleAir.h in Headers */ = {isa = PBXBuildFile; fileRef = F6B3990A1685A8F6D4FA7DC8 /* TripleAir.h */; settings = {ATTRIBUTES = (Public, ); }; };
		38B762C6AF1A2698C3DCAE89 /* GatherEuropeanInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = A34165CFA02711A80C940B20 /* GatherEuropeanInfo.m */; };
		38D4F00D6349A3F15CB46397 /* NSData+ImageContentType.h in Headers */ = {isa = PBXBuildFile; fileRef = 86FF3B96AACCAEA27979453C /* NSData+ImageContentType.h */; };
		39823C41CA2E490090DA9E25 /* CubeSindhiViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1415F9C41BB01405DE76388A /* CubeSindhiViewController.m */; };
		3A192FDF7ED0814268A4C38C /* UIViewController+DueViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 97B3A6217F9329522A760FCC /* UIViewController+DueViewController.h */; };
		3A26584699C07D74B6A86F2E /* DogBufferViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = EE974FE4B73A0BEE10E90DB1 /* DogBufferViewController.h */; };
		3A3DF2EF69AE519A24E037F6 /* BusWasPackViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = BA9D155C2ABDB957F303FC25 /* BusWasPackViewController.m */; };
		3A9D7B3E1827944C1A4C8374 /* SDWebImageError.h in Headers */ = {isa = PBXBuildFile; fileRef = 3EE249C712F7333862BB4F69 /* SDWebImageError.h */; };
		3AF4946E10EC7EC738F1EEA2 /* DueTurnWasViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 70EA9F440BEB15CFD18C83CE /* DueTurnWasViewController.h */; };
		3BCF8C5844E9B02385B54954 /* UIView+WebCacheState.m in Sources */ = {isa = PBXBuildFile; fileRef = 930C4F3C61AF519C25B51039 /* UIView+WebCacheState.m */; };
		3CC4B4B810DAC9F77CE17CBE /* ExtentsPubViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 31E069C36D329AC879068356 /* ExtentsPubViewController.m */; };
		3D18AD1CDCDB94B778D888F8 /* UIDevice+HueDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = 71389E587E3462F4F8BE17C6 /* UIDevice+HueDevice.h */; };
		3DEAF9D47CDE444071E18659 /* ReconnectTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = 5D0BAB29618AB638244BE2CC /* ReconnectTimer.m */; };
		3E4FA4164947423A41C1D160 /* NSObject+MixModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 58C4624188D60CCD0049F701 /* NSObject+MixModel.h */; };
		3F031074B244C8103F03E3B7 /* BigArmLocalHow+Birth.m in Sources */ = {isa = PBXBuildFile; fileRef = B5023AE79D98A0B8530EFEB6 /* BigArmLocalHow+Birth.m */; };
		3F841561D73EE5A86675BEE5 /* MQTTSessionLegacy.m in Sources */ = {isa = PBXBuildFile; fileRef = 17DA1009F520757DC05809D8 /* MQTTSessionLegacy.m */; };
		3F9A31B407EDB61408922BC1 /* UIImage+Transform.h in Headers */ = {isa = PBXBuildFile; fileRef = 54908D1100DACD15725F9DA4 /* UIImage+Transform.h */; };
		3FB57875A5888F21927C9286 /* FlowFrontViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = E1F00DDC3F256DA9643776B1 /* FlowFrontViewController.m */; };
		3FF8330F3EAAC7C6508A6E51 /* MASCompositeConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 8A5AFBE49A0CF05E0C1A3CE2 /* MASCompositeConstraint.h */; };
		4043955825ABC0CA6C25DA1C /* AlienDelayItemBriefSelector.m in Sources */ = {isa = PBXBuildFile; fileRef = 43165076F5C9973426EA873A /* AlienDelayItemBriefSelector.m */; };
		405268767932F928F24EA9D1 /* SuddenBufferModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 3558CDC32649CB91C24010E9 /* SuddenBufferModel.h */; };
		41D427B1A4388C969D6475FB /* SDImageCacheDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 6E83A911230B8F32C607210F /* SDImageCacheDefine.m */; };
		42AFAAEDB70511CC36D086A1 /* HowFaxConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 81B6C8C71D4DA38BFD11367A /* HowFaxConfig.m */; };
		42E977FA73147361E86DC940 /* KnowPictureAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 31C4F7EB3105FC7EF034AFEC /* KnowPictureAction.h */; };
		434C62319BF48E58B7BB5F55 /* SDAnimatedImageView.h in Headers */ = {isa = PBXBuildFile; fileRef = 9C720AD34169F1F100807201 /* SDAnimatedImageView.h */; };
		44483CCB45F81C59AED5B861 /* SDImageCachesManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 8C6F0CC5887AA99DC547892A /* SDImageCachesManager.h */; };
		445371C637AE691CB721D04F /* SDMemoryCache.m in Sources */ = {isa = PBXBuildFile; fileRef = B085BAC6A4872AF87EBF86DC /* SDMemoryCache.m */; };
		44BC9CBD2108958B12181084 /* NSLayoutConstraint+MASDebugAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = DD883098B2A682A1DCFF7C2B /* NSLayoutConstraint+MASDebugAdditions.m */; };
		45251EE8729CC097ABD5440E /* PubPrepModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 1A00A355FDD4EE29C47A53BA /* PubPrepModel.h */; };
		455E052F0AA5CD0E5718B2F7 /* SDCallbackQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = 48104E81543B9CF73023C843 /* SDCallbackQueue.m */; };
		458F2581E7E6FD28692B09DD /* SDDiskCache.h in Headers */ = {isa = PBXBuildFile; fileRef = C43A5BC3C8A713DA5F87BA53 /* SDDiskCache.h */; };
		45B8C6FDC550DF906AE905FE /* MQTTSSLSecurityPolicyEncoder.m in Sources */ = {isa = PBXBuildFile; fileRef = E8AF443A69C5256097CED6DE /* MQTTSSLSecurityPolicyEncoder.m */; };
		45DD9E5537E316794D2DB8D2 /* MolarAccept.h in Headers */ = {isa = PBXBuildFile; fileRef = 7847C6F4CEDBA3C97DC8D896 /* MolarAccept.h */; };
		46D991C0615D6D37C64B16DE /* SDWebImageDownloaderDecryptor.h in Headers */ = {isa = PBXBuildFile; fileRef = 367013C26C71CF2AEC6AC647 /* SDWebImageDownloaderDecryptor.h */; };
		47C8955E276A5D83DD5A336C /* MQTTSSLSecurityPolicy.m in Sources */ = {isa = PBXBuildFile; fileRef = 52EC8A9B2078BCF779B41247 /* MQTTSSLSecurityPolicy.m */; };
		4832F0FD08BDA73CA23B0381 /* UIColor+BoxColor.m in Sources */ = {isa = PBXBuildFile; fileRef = CFF6D50BFF1E9103EF72F80A /* UIColor+BoxColor.m */; };
		4B4161B6CD40C49AC85B8C54 /* CityOffsetsViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 5D12ECF04F8BEE1B431684DB /* CityOffsetsViewController.h */; };
		4BBF27A4748A7F0A42DA7E60 /* SuddenBufferModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7E6E26FD0F1E64D9F29F48B7 /* SuddenBufferModel.m */; };
		4CC87FCDCCC915CEEAE5548C /* FarNetwork.h in Headers */ = {isa = PBXBuildFile; fileRef = 7BF9C7162E86BDC941C5241C /* FarNetwork.h */; };
		4DBCDC026CA8A43BB282DB98 /* MASLayoutConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 7E2B1F89FCD9CB153AE11EAA /* MASLayoutConstraint.h */; };
		4DC63E308349B16D6B1A28C3 /* MASLayoutConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 26584EE61B7DEAE22FD6E72D /* MASLayoutConstraint.m */; };
		4E4A8BB8DA31E0ABD98EB3F0 /* RouteJustProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 59F900FE83AA1D74D6D8476F /* RouteJustProtocol.h */; };
		4E7A291CB199D80B24CDF097 /* SDImageAPNGCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DB4EBD56BEE0FFDB2F82287 /* SDImageAPNGCoder.m */; };
		5068807AC627E897C0438985 /* CatAskEmailOldViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 26145E138BB1A5854F92A086 /* CatAskEmailOldViewController.m */; };
		516D4A0E635B00B475CBFFD2 /* BigArmLocalHow+Birth.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D94C65AF548CACE137BBCAA /* BigArmLocalHow+Birth.h */; };
		516F26CA00332DE8DCBB82EB /* CleanWaist.m in Sources */ = {isa = PBXBuildFile; fileRef = 7F5680E8504EC9F757C74653 /* CleanWaist.m */; };
		52DB117E80C2E6F70566121D /* ForegroundReconnection.m in Sources */ = {isa = PBXBuildFile; fileRef = 42DC08131862E3594315B388 /* ForegroundReconnection.m */; };
		52E401225CB80DF3FC1B12EE /* SDFileAttributeHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 82F628295CBE929F2196D18E /* SDFileAttributeHelper.m */; };
		530551280792C2918D0429A6 /* MusicAuditNoteMajorSuddenTool.m in Sources */ = {isa = PBXBuildFile; fileRef = C80DE1DCE41F042000557DB3 /* MusicAuditNoteMajorSuddenTool.m */; };
		53AA7723646A10055B5348DE /* SDAnimatedImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = ACEE51F7F05264D48CB07AA2 /* SDAnimatedImageView+WebCache.m */; };
		5459FFA7F90497D800035E0A /* BigSlabUniversalSpecifiedFoot.h in Headers */ = {isa = PBXBuildFile; fileRef = 2EB6A1C9E8C459DFD7DE23F7 /* BigSlabUniversalSpecifiedFoot.h */; };
		551AAF5A4C3689D66EBD71E4 /* MQTTCFSocketTransport.m in Sources */ = {isa = PBXBuildFile; fileRef = F9E011F379A0F3D3DF9DF46F /* MQTTCFSocketTransport.m */; };
		55BCB0F980C1D9964A4C2489 /* SDWebImageCompat.m in Sources */ = {isa = PBXBuildFile; fileRef = DDD3B922346B7A76717DB77E /* SDWebImageCompat.m */; };
		56064DF79EF938F32E67DE85 /* SDWebImageCacheKeyFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = D9BE869DBD0386C79F8D9EFF /* SDWebImageCacheKeyFilter.m */; };
		56B6CBD54D6D7246281B2783 /* WeekVirtualBlueSucceededExpects.m in Sources */ = {isa = PBXBuildFile; fileRef = FDD2383A639B803FC776CE75 /* WeekVirtualBlueSucceededExpects.m */; };
		56DF5E454720BDA994BC13AF /* FatMailManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6625C376A089820D01770FB5 /* FatMailManager.m */; };
		56F0E9962935722D1B3A3FC3 /* ExtentsPubViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = D898F62FAD2D25E06B03FCA4 /* ExtentsPubViewController.h */; };
		5820F43CAA692EB3309F5D0C /* NSBezierPath+SDRoundedCorners.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DFE249A76BC872DE8F416D6 /* NSBezierPath+SDRoundedCorners.m */; };
		5908D45DC5A199224B178564 /* CatAskEmailOldViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = AB487D4A3E603B7AEAE71B5E /* CatAskEmailOldViewController.h */; };
		59AA2678D5E26399CA26A605 /* AssignTabBin.m in Sources */ = {isa = PBXBuildFile; fileRef = 8EFECEA7EC6E5085D6D2525D /* AssignTabBin.m */; };
		59C14ED0CEF6A041F845D803 /* SDWebImageCacheSerializer.h in Headers */ = {isa = PBXBuildFile; fileRef = F3F1EABDDA2FEB69CF24348E /* SDWebImageCacheSerializer.h */; };
		5A3B098171D865D2D23A0450 /* ChainPauseInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D05125D852BA736DD26DF70 /* ChainPauseInfo.m */; };
		5A485FA86695D5DDF3233E37 /* SDWebImageIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = 6B6D8A1AAA121C3B054E9807 /* SDWebImageIndicator.h */; };
		5A4C51C719234A1F419E663C /* RecordOrganizeDropCondensedPortraitsRelative.m in Sources */ = {isa = PBXBuildFile; fileRef = 7C18D01F1A35A702EE11A789 /* RecordOrganizeDropCondensedPortraitsRelative.m */; };
		5AB98B563545238D11961579 /* SDWebImageManager.h in Headers */ = {isa = PBXBuildFile; fileRef = E209285AF9F522CDC86D38D4 /* SDWebImageManager.h */; };
		5AEB778846846C1A6466981B /* EnergyPotassiumDelaySkinFeatCell.h in Headers */ = {isa = PBXBuildFile; fileRef = BFD30A6410546CCB1881F481 /* EnergyPotassiumDelaySkinFeatCell.h */; };
		5B341A8D3CB64ACBFB9A9692 /* NSError+SawImageBin.m in Sources */ = {isa = PBXBuildFile; fileRef = B2628A9C0E4219B314532E1B /* NSError+SawImageBin.m */; };
		5B6E33E8B691511F924C00FF /* UIButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 75688B93B26FFC322BBDA0E2 /* UIButton+WebCache.m */; };
		5BDD9B41A413ACA61272228B /* RetMidManager.h in Headers */ = {isa = PBXBuildFile; fileRef = F90C91674E76A961388311FB /* RetMidManager.h */; };
		5C18C86FDFB377BB0ED89460 /* SDImageTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = F70C19DFB7CA7EB337436BE3 /* SDImageTransformer.h */; };
		5C6FD5735474C76F40C735E0 /* ChainPauseInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 94D7442691C8A7FE2E469E30 /* ChainPauseInfo.h */; };
		5CCF4A4070685C7B624AC32B /* DownloadsManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 85F47BA07771BAFAD1BBA789 /* DownloadsManager.h */; };
		5D1B2B9D27D4530DBC6C30B2 /* ForegroundReconnection.h in Headers */ = {isa = PBXBuildFile; fileRef = A14ABB1337ABF5E9A1F29FA8 /* ForegroundReconnection.h */; };
		5D240A3E3BD60D1697B4FFD5 /* MailNordic.m in Sources */ = {isa = PBXBuildFile; fileRef = D0E2A356B74FD68E251F96BB /* MailNordic.m */; };
		5D6040EC893F323A59D50A82 /* MASConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 7C8C702DC550916FC1C6ADB5 /* MASConstraint.h */; };
		5D82FEF2D2D9AE1C754CB55F /* UIImage+Transform.m in Sources */ = {isa = PBXBuildFile; fileRef = 54D1CAA78E9ACB8334CDAB3C /* UIImage+Transform.m */; };
		5DD1D4542827699756A74F2B /* SDImageCacheConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 2DB39BD3E3F92AB10A999550 /* SDImageCacheConfig.h */; };
		5F2275759FDBA5E59F1FEEB3 /* TrainingRows.h in Headers */ = {isa = PBXBuildFile; fileRef = 04D063564A0125F3538495D5 /* TrainingRows.h */; };
		5F24F56FBCF6D709B82E1D6B /* SDWebImageDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 98D20514F6509CB79DD55B25 /* SDWebImageDefine.m */; };
		5F3A763F3EE7BA6D2541170B /* MinimizeEstonianIcyFinderRoot.m in Sources */ = {isa = PBXBuildFile; fileRef = B86E7D3EA6F3D228A3123FB8 /* MinimizeEstonianIcyFinderRoot.m */; };
		5F8C04D97FD3C07CD1EF1290 /* SDImageFrame.h in Headers */ = {isa = PBXBuildFile; fileRef = 475F9FB69C8E18DD1F0FD83F /* SDImageFrame.h */; };
		604BCED38041B0FCFC19A194 /* SDWebImageOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 80B44C26D35DB15AD8374F45 /* SDWebImageOperation.m */; };
		60978BBA09704947E442A0DC /* MQTTTransportProtocol.m in Sources */ = {isa = PBXBuildFile; fileRef = 350EA248EF98A46B64E1CF0F /* MQTTTransportProtocol.m */; };
		60E10042C06920DE1ADD06E3 /* SDInternalMacros.m in Sources */ = {isa = PBXBuildFile; fileRef = E2C4908BE0D736F6357C22E5 /* SDInternalMacros.m */; };
		60E3A9E714404F4FE1D080FA /* OpenPurposeMostlySynthesisFlushed.h in Headers */ = {isa = PBXBuildFile; fileRef = 6DF731BC235E210D11537877 /* OpenPurposeMostlySynthesisFlushed.h */; };
		60F93824B292D423ABE8A79A /* SDAnimatedImagePlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = AEB95BA0CF43BEDA1ED41EDD /* SDAnimatedImagePlayer.m */; };
		61C877BEB003E1451C86909A /* SDWebImageDownloaderRequestModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = E17CA76812EA650BAEF247D3 /* SDWebImageDownloaderRequestModifier.h */; };
		624FD5A3006EC22B4E5280E8 /* GaspBusyStickyCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 33F29C413EBE4AB4346B6FDA /* GaspBusyStickyCell.m */; };
		628D129B30FF640C052D63B8 /* UIView+WebCacheOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = F2243889BEBA93835D8A34B8 /* UIView+WebCacheOperation.m */; };
		63183371640D7EAEF61FE73A /* SDAnimatedImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 3EA621424E770F0564BD7354 /* SDAnimatedImageView.m */; };
		6451E2BAEAED90D54E121C80 /* HelpRaceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 5B47589BE26ECFA2B535287B /* HelpRaceManager.m */; };
		647911AEF51C817A2F1B6C85 /* MQTTInMemoryPersistence.h in Headers */ = {isa = PBXBuildFile; fileRef = E3B430FAF31EC7C7CE370632 /* MQTTInMemoryPersistence.h */; };
		6481634661CB9D7F9853C36D /* SDAnimatedImagePlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = B7F48FCCF62F90A18511713D /* SDAnimatedImagePlayer.h */; };
		64AC5CBC195D7D6522848F7C /* EggNetCivilSon.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D5A691CDA0B494FF1FE1E6 /* EggNetCivilSon.m */; };
		64CDDA377B4DF1B5E0BA0222 /* SDDisplayLink.h in Headers */ = {isa = PBXBuildFile; fileRef = A7A63754EC2180AEEE479CC9 /* SDDisplayLink.h */; };
		651342C584ED99F9DADC52E9 /* UIImage+MemoryCacheCost.h in Headers */ = {isa = PBXBuildFile; fileRef = 9F1C43C6D349BB4B729BB317 /* UIImage+MemoryCacheCost.h */; };
		652495BE4CB092D976161633 /* NSBezierPath+SDRoundedCorners.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E49271E786CCE05FDA2F6B3 /* NSBezierPath+SDRoundedCorners.h */; };
		653D158CA657F0B89A71E18D /* LoopPatchInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 89FEBCD3EA3184BE20B05755 /* LoopPatchInfo.h */; };
		65F3E40758F3B35D5BA49A35 /* HowFaxConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 9F4A9EC1243CBA545D24DAA0 /* HowFaxConfig.h */; };
		66D32973E2C8C816193077D7 /* SDImageLoadersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = C264AC54F27CC4A7556F4AAF /* SDImageLoadersManager.h */; };
		675C570581ACB1CCC127EFF7 /* SDAsyncBlockOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 4AB37405C32C3AA579D3031F /* SDAsyncBlockOperation.h */; };
		676AB2D1061BD6AFFFC2E54E /* PaddleDublinManager.h in Headers */ = {isa = PBXBuildFile; fileRef = C9AAFB624C71A12EC4E3D0D5 /* PaddleDublinManager.h */; };
		67E9E36672BE25D570B97A14 /* SDWebImageDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 7E354D346A2D78ECE758777B /* SDWebImageDownloader.m */; };
		6857904C3EABABB7D63C5545 /* UIImage+ForceDecode.h in Headers */ = {isa = PBXBuildFile; fileRef = E58C371AE553B8255C219D3A /* UIImage+ForceDecode.h */; };
		692D5DE71A2A42D1EFAC0F02 /* SDImageAssetManager.h in Headers */ = {isa = PBXBuildFile; fileRef = ACA7EC5A33D84AE089C1DDD4 /* SDImageAssetManager.h */; };
		695AFB5D3DCCDB94ED6E5410 /* BusForDrawView.h in Headers */ = {isa = PBXBuildFile; fileRef = 8AA0D7DF97D1083AF23AED39 /* BusForDrawView.h */; };
		69E97C025940DDDFFEFCEF14 /* PrivacyResultsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = FE06714B9B4A16A712BE7693 /* PrivacyResultsViewController.m */; };
		6A408DC2EF1D97B18520A4AF /* SDImageCoderHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 2D71931F272F906E34A0B0F3 /* SDImageCoderHelper.h */; };
		6B83B7355F4833D685CAB18F /* MQTTSSLSecurityPolicyTransport.h in Headers */ = {isa = PBXBuildFile; fileRef = 0141AC08E7CF5E8EBBCF8ACE /* MQTTSSLSecurityPolicyTransport.h */; };
		6D63A010E05743D43C278427 /* NextBoxMoreManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 4343F7E55E0F560FB3E10FE8 /* NextBoxMoreManager.h */; };
		6DDD65E85EF1CFB3C821D283 /* NSObject+CivilAdoptMindfulCoachedCap.h in Headers */ = {isa = PBXBuildFile; fileRef = DEB72DB9FE86DAA59469C226 /* NSObject+CivilAdoptMindfulCoachedCap.h */; };
		6E7E0C2761236B7A8D6C754C /* InterExistWalk.h in Headers */ = {isa = PBXBuildFile; fileRef = 5D79B94C0892E2346306E11C /* InterExistWalk.h */; };
		6EB4F71C314A28A210BC1F14 /* LacrosseModel.m in Sources */ = {isa = PBXBuildFile; fileRef = C572C94E8C2C931CB979DC7F /* LacrosseModel.m */; };
		6F7A167DA4003C7FAEE4EDAD /* ReadTiedKinButton.h in Headers */ = {isa = PBXBuildFile; fileRef = 6DC90BBD13D0F89BD36C9085 /* ReadTiedKinButton.h */; };
		70DB62520445E5E23C2E488C /* ArtistBuddy.m in Sources */ = {isa = PBXBuildFile; fileRef = B9F6F9508933A30F87B5857A /* ArtistBuddy.m */; };
		7105382E2EB9D60FDA3A5F98 /* AlienDelayItemBriefSelector.h in Headers */ = {isa = PBXBuildFile; fileRef = 3AFC33D952AB5726ADAADBC4 /* AlienDelayItemBriefSelector.h */; };
		714A7D5EB3FBDC78AFA897B4 /* MQTTProperties.m in Sources */ = {isa = PBXBuildFile; fileRef = 45EB850E609F3164CE779B84 /* MQTTProperties.m */; };
		71732846B4291D47F048D292 /* AssignTabBin.h in Headers */ = {isa = PBXBuildFile; fileRef = 94509C1AC47F7CB40CEF3462 /* AssignTabBin.h */; };
		7180AEA8963D0A1B86D98445 /* MQTTSession.h in Headers */ = {isa = PBXBuildFile; fileRef = 6721C73D1A3AA1DD914CFDF4 /* MQTTSession.h */; };
		71D47FC9E48A836F9D7E864B /* SDWebImageCompat.h in Headers */ = {isa = PBXBuildFile; fileRef = 81E4E8F9F85FB6E3C5A5BE6D /* SDWebImageCompat.h */; };
		7246194C86C4A0204CC4791E /* NSError+SawImageBin.h in Headers */ = {isa = PBXBuildFile; fileRef = 7B879A82D250A71C05BAF70D /* NSError+SawImageBin.h */; };
		72678EDA43F75C824F420919 /* LambdaCyrillic.m in Sources */ = {isa = PBXBuildFile; fileRef = 2D08BB8548A2681C22B92395 /* LambdaCyrillic.m */; };
		7314D2FCD7AD577DCECB65EE /* SDImageIOCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 1AE521836E9E6CDFAE1F9721 /* SDImageIOCoder.m */; };
		7344901D81949450A2B7D21B /* IndicatorViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 81ED172EB1822E645853468C /* IndicatorViewController.h */; };
		748DC334F24CB4B990013EDA /* SDWebImageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = E4B45E1E401918C1CF39DBC2 /* SDWebImageManager.m */; };
		748EDB92280AFA4F138A91CB /* ElderNotationVolumeResourcesShortcut.h in Headers */ = {isa = PBXBuildFile; fileRef = DDC7E2DD6050D8F2027648DA /* ElderNotationVolumeResourcesShortcut.h */; };
		7499B68593CE6869489C9C71 /* Masonry.h in Headers */ = {isa = PBXBuildFile; fileRef = 6CD424F67F634ACF88BFDAE1 /* Masonry.h */; };
		74B621767D802F67F0F02786 /* FatMailManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 1461289DC458EE67F3D4AD97 /* FatMailManager.h */; };
		7501D37FF8734B5FEBC73F61 /* MQTTClient.h in Headers */ = {isa = PBXBuildFile; fileRef = 20C4A65703F636E5B80E9CBD /* MQTTClient.h */; };
		7528789E4097E0715288D339 /* UIView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = F38AFFBC39867224349C7AB6 /* UIView+WebCache.h */; };
		7545BA8E655F83039CB45DCA /* WrongArrayFootButton.m in Sources */ = {isa = PBXBuildFile; fileRef = A73F5F354AC31D1858EACEB0 /* WrongArrayFootButton.m */; };
		755EAA381F65EA5A7F30DFD4 /* BigArmLocalHow+SubSub.h in Headers */ = {isa = PBXBuildFile; fileRef = 929E4B1DD0D76C5BC1E80683 /* BigArmLocalHow+SubSub.h */; };
		756711883735FD8B26ECE99E /* SDWebImageDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 383CD14119C8B839C164B4DF /* SDWebImageDownloader.h */; };
		7639E0C08EE9852F224BC2B3 /* DoneBaselineManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 8FF0FB0A5A1BAF2687096649 /* DoneBaselineManager.h */; };
		7651D13C80D04DD603B30A26 /* RedoneMode.m in Sources */ = {isa = PBXBuildFile; fileRef = 08E367B4D4FC8CD8DE30E1FC /* RedoneMode.m */; };
		76AD7A49641CEC6E9ECC50C1 /* TagZipFailManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 4F324AD8E1EF4AF8430F60DC /* TagZipFailManager.h */; };
		76C5F5D7835B78FECBF218AD /* SDAsyncBlockOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = E6EAF0106DFAC065FFD549F0 /* SDAsyncBlockOperation.m */; };
		774582FCBC7FA955B607B6A0 /* SDWebImageDownloaderOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = AADB8F1EEF5A9B4744260358 /* SDWebImageDownloaderOperation.m */; };
		79573C37F64C1532A652CA53 /* SDWebImageDownloaderDecryptor.m in Sources */ = {isa = PBXBuildFile; fileRef = 06B123B981D365F0DEE48507 /* SDWebImageDownloaderDecryptor.m */; };
		796BB1D2E881BE9BC0EF8916 /* NSArray+MASShorthandAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = F16BFA940B1E06BDAE2F3025 /* NSArray+MASShorthandAdditions.h */; };
		79A674E63540E4C975D86809 /* SDImageIOCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = D0326F6C344AE060C6A1DABC /* SDImageIOCoder.h */; };
		79D777721438EDBD0B738CCA /* LacrosseModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 91D4750739BF665376AC50AE /* LacrosseModel.h */; };
		7ACC2B63D893E1A3A03374E4 /* SDWebImageDownloaderResponseModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = E995B4A3B2131A38CAD7BAAE /* SDWebImageDownloaderResponseModifier.h */; };
		7B6E74D26FD7CC1C1F8E25CF /* SDImageCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 2FEAE548863ADF0EAB283BFB /* SDImageCoder.m */; };
		7BA082416675FAD2286D9130 /* TagTreeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A4E1CF25550863E506608349 /* TagTreeViewController.m */; };
		7C021C4830A5CB24A488876D /* RingAgeDegradedEyeTextualAppearingViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 726DC39742533499DDAB07E1 /* RingAgeDegradedEyeTextualAppearingViewController.h */; };
		7C200B1B801605A0F48F9698 /* PopDrainImmediateOnePlug.h in Headers */ = {isa = PBXBuildFile; fileRef = E594298407C00E5D531885D2 /* PopDrainImmediateOnePlug.h */; };
		7E1149D773EAC20BE7051221 /* SDImageGraphics.m in Sources */ = {isa = PBXBuildFile; fileRef = 5CDDABC02C3E45FA28381C92 /* SDImageGraphics.m */; };
		7EF0ACA91242C7B76CA7AE72 /* ElderNotationVolumeResourcesShortcut.m in Sources */ = {isa = PBXBuildFile; fileRef = 11B06AB5B208018FD9464C94 /* ElderNotationVolumeResourcesShortcut.m */; };
		8163E7DFF431A3310BD14C77 /* BusWasPackViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = F4365DA0B4680D632F62BFC8 /* BusWasPackViewController.h */; };
		81DCD72A73130C18A378492A /* MQTTCoreDataPersistence.m in Sources */ = {isa = PBXBuildFile; fileRef = 4F91A5842E4617B666B80A95 /* MQTTCoreDataPersistence.m */; };
		82108CCB0C094011FC36014A /* SDWebImageIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = E4F0B3B53970A67E0AD5336E /* SDWebImageIndicator.m */; };
		8213F0BDF09CEEFA6364ECAD /* SDWebImagePrefetcher.h in Headers */ = {isa = PBXBuildFile; fileRef = 761D610FB87D9B30AE4C024D /* SDWebImagePrefetcher.h */; };
		82BD0B8ADE312351FF226129 /* CopticLearn.m in Sources */ = {isa = PBXBuildFile; fileRef = B32E79FF20FE77AEE00B9D78 /* CopticLearn.m */; };
		840346603F4DF19D8981F2AB /* ThickViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 28ACE33FCDF7951D85998ED0 /* ThickViewController.h */; };
		84072B761D6E3DA51E5B8BCE /* SDDeviceHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = BEF52778E71DCC559B5261CB /* SDDeviceHelper.h */; };
		84211F5353ABE3C3133731C8 /* MQTTDecoder.m in Sources */ = {isa = PBXBuildFile; fileRef = F80CEF7C549E0BA3A4FE14BE /* MQTTDecoder.m */; };
		847A90BA613A9E623B865C99 /* JabberStepperContrastHomeReportPrefers.m in Sources */ = {isa = PBXBuildFile; fileRef = 99B0D2DEC5B9C36133443E50 /* JabberStepperContrastHomeReportPrefers.m */; };
		84968214AB733E27048A1E91 /* LengthAndViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = C73BEC77E6D3C7F0084F7B61 /* LengthAndViewController.m */; };
		84A200564BCE3AC73DDE9C0F /* MASViewConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 8ADFA1E902A670161248CB1B /* MASViewConstraint.h */; };
		84CCB259ADF5E871D9E43155 /* IcyPhase.m in Sources */ = {isa = PBXBuildFile; fileRef = 3254F188E39822392D519389 /* IcyPhase.m */; };
		858AD44AED042FB2E0A90F6E /* UsabilityOnlyController.h in Headers */ = {isa = PBXBuildFile; fileRef = 1A2804FE28CA72D6AF4E51E2 /* UsabilityOnlyController.h */; };
		862E47EA20AC82A08702795D /* SDImageIOAnimatedCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 66E5E37C6C8688615A7BFE8B /* SDImageIOAnimatedCoder.h */; };
		86AC6FD2D7CE363EC8C09B94 /* NSButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 2070FEC4A15C57B418A3DF72 /* NSButton+WebCache.m */; };
		86AE3BE2D5C9420F9530BECC /* WayRealArmForm.h in Headers */ = {isa = PBXBuildFile; fileRef = CB3E3456C2044CAE53CB0610 /* WayRealArmForm.h */; };
		87E20D750DCEFB98F92E2646 /* SDImageFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = F81F63F978D0DBE295462377 /* SDImageFrame.m */; };
		8984EEB336EF47958B92038C /* NSString+Messaging.h in Headers */ = {isa = PBXBuildFile; fileRef = 1E2EF030009ADAABDA3BF993 /* NSString+Messaging.h */; };
		89C421653987F204962405DD /* SDWeakProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 3E6C8E2EBE046BC2CD109E1B /* SDWeakProxy.h */; };
		89F031BC56D068799FB956D9 /* GatherEuropeanInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = E2CAAE9FD85D622EBA57E8DA /* GatherEuropeanInfo.h */; };
		8B2680BE5062A7287314277B /* PasswordsMoveViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 8606D7354FFB895DCBBA6A54 /* PasswordsMoveViewController.m */; };
		8B5114EE94978217FE722C0E /* SDImageLoader.h in Headers */ = {isa = PBXBuildFile; fileRef = C3CFEF907EF6877B478E0180 /* SDImageLoader.h */; };
		8C17055AA067A4457B04AB9E /* SDImageCacheConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = B59CF9C321E7DBD64B043A76 /* SDImageCacheConfig.m */; };
		8C3D3547DD964F1BDE1A8275 /* MagnitudeLateViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 242A9BAA6C3275864A481C46 /* MagnitudeLateViewController.m */; };
		8D1C37B803D0538279E09E34 /* RealEntryTextField.h in Headers */ = {isa = PBXBuildFile; fileRef = 97C74D9C4178AC17341F1F1D /* RealEntryTextField.h */; };
		8DB1534D61C4764B2AD8C3A3 /* UIColor+SDHexString.m in Sources */ = {isa = PBXBuildFile; fileRef = 4FDC1B4C41D78E899AAAFA74 /* UIColor+SDHexString.m */; };
		8E8F1F9942AC5D4A478A321A /* SDImageGIFCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 1B35DC6766CFFD43FA10BD1E /* SDImageGIFCoder.m */; };
		8F3E77DF8E2E6DED8209B6CC /* NSString+Messaging.m in Sources */ = {isa = PBXBuildFile; fileRef = FD71A8C0CAA8D9DB5BBCDE5E /* NSString+Messaging.m */; };
		8FCEF5F0A648F45E06701DC0 /* NSURL+SheRopeHow.h in Headers */ = {isa = PBXBuildFile; fileRef = F589A9DF83215E1E2DAEA8B4 /* NSURL+SheRopeHow.h */; };
		9093AEF624704C46C8EA7A17 /* DueTurnWasViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 258CDBDF9487ACAA5AA5EB68 /* DueTurnWasViewController.m */; };
		915E3C7CD19933F18731CE8B /* MQTTSessionSynchron.h in Headers */ = {isa = PBXBuildFile; fileRef = 46D058B32A4429DEB671474C /* MQTTSessionSynchron.h */; };
		91A981E2AC179DAAD7F2CD85 /* SDAnimatedImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 74CD8856F6935429278DDD61 /* SDAnimatedImageView+WebCache.h */; };
		91B498681CA08838A4CAF69B /* ZipOldRootView.h in Headers */ = {isa = PBXBuildFile; fileRef = BB9E3629691D75FA03BBC894 /* ZipOldRootView.h */; };
		929F14CE015E13F53918302E /* SpineCandidate.m in Sources */ = {isa = PBXBuildFile; fileRef = 0127E9F98C9D56F53BD2E28A /* SpineCandidate.m */; };
		92B1B87CC706E226F7E783DA /* DoneSalt.m in Sources */ = {isa = PBXBuildFile; fileRef = 636D0E279FF1FE49884D9430 /* DoneSalt.m */; };
		935F160EBCFF783ACEB50D5D /* IndicatorViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = C3592ECE9C90F13CE404CBFA /* IndicatorViewController.m */; };
		93A984A2395B1F48EA57D3E6 /* TagZipFailManager.m in Sources */ = {isa = PBXBuildFile; fileRef = DBD014FBA942283808AEA6C6 /* TagZipFailManager.m */; };
		943A3094B65EEC18513B606E /* MQTTSession.m in Sources */ = {isa = PBXBuildFile; fileRef = C58C4C7D266580271E35205E /* MQTTSession.m */; };
		947F15D08510510836EC18A1 /* MQTTLog.h in Headers */ = {isa = PBXBuildFile; fileRef = 5302A6E4AB90AC37AEAD510B /* MQTTLog.h */; };
		94B5B0662A9777EAE37D4087 /* SexualUtilitiesScopeSkipKilobits.m in Sources */ = {isa = PBXBuildFile; fileRef = 806EABE19229433563CDE529 /* SexualUtilitiesScopeSkipKilobits.m */; };
		95C18D53B51B0459DBDEA772 /* SDWebImageOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = B8BD693F29D381073E6F23E2 /* SDWebImageOperation.h */; };
		961D89650CDF3D6AACA57EC4 /* SDWebImageDownloaderRequestModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = 23D2FA67C72CB4150731927D /* SDWebImageDownloaderRequestModifier.m */; };
		961FE13FEF25DEDB6B926EB5 /* RadialSkip.h in Headers */ = {isa = PBXBuildFile; fileRef = 0FB23742819ABAB05DC2C70C /* RadialSkip.h */; };
		988334267E1EAADCFF2316C5 /* MQTTCFSocketDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 4145F5EA5BF53DA10CC05E01 /* MQTTCFSocketDecoder.h */; };
		98F6178CAF420BEB6AE0C3BD /* NSURL+SheRopeHow.m in Sources */ = {isa = PBXBuildFile; fileRef = F4B368F4E80A6E143B98D9CF /* NSURL+SheRopeHow.m */; };
		99CAC814FB71D3EEC28AA09C /* ReconnectTimer.h in Headers */ = {isa = PBXBuildFile; fileRef = 525F793B64D68E3B7AAA91F1 /* ReconnectTimer.h */; };
		9A4FE05D5EA3DF9FA95EFE8E /* BigArmLocalHow+HailPrice.m in Sources */ = {isa = PBXBuildFile; fileRef = DAA6F6D2E41F2BE793E42DBA /* BigArmLocalHow+HailPrice.m */; };
		9AAC46D9BE8C2F2967512168 /* SDWebImageCacheKeyFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = BC9144FD73B7D89416E2222E /* SDWebImageCacheKeyFilter.h */; };
		9B406575CD044D7565FE9E2F /* UIImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 3F6C207190E40118EEAE135E /* UIImageView+WebCache.h */; };
		9C348776BFBCBCB80EAF0F97 /* MQTTCoreDataPersistence.h in Headers */ = {isa = PBXBuildFile; fileRef = 3746C2D6AD84CA345FDFA76D /* MQTTCoreDataPersistence.h */; };
		9CD313F3413C3EC6908BBC62 /* StrongHexViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 09B6D21DC1A98571727692B1 /* StrongHexViewController.m */; };
		9DC2C3063F050CF831395888 /* KeysReverting.h in Headers */ = {isa = PBXBuildFile; fileRef = 2DBF81C919A57FD2987C9C61 /* KeysReverting.h */; };
		9DE9D95635C4215DF88E60EF /* SDImageCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C3315522EEFDF32B7F387D9 /* SDImageCache.m */; };
		9E426547092620E96003B93E /* PopDrainImmediateOnePlug.m in Sources */ = {isa = PBXBuildFile; fileRef = 6F0CDF7392A84879664CE6E1 /* PopDrainImmediateOnePlug.m */; };
		9E76C5A8F3EF43107630938D /* DownloadsManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0C8CD09FB9C084B2EFA4BABB /* DownloadsManager.m */; };
		9EB1976A13694D584C48D4BE /* XXGProtocolLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = A61DB1D2F96835B35656CFA4 /* XXGProtocolLabel.m */; };
		9ED40B4A32483377B5BE9524 /* LessCutWinCell.m in Sources */ = {isa = PBXBuildFile; fileRef = C6937FB1582C6C473B78F7CD /* LessCutWinCell.m */; };
		9FAF7BC7D5C2A24D8F289749 /* UIImage+Metadata.h in Headers */ = {isa = PBXBuildFile; fileRef = 57B3C365FB51A9732AC9F5F4 /* UIImage+Metadata.h */; };
		9FFD6C8BEE328869757581BF /* DoneBaselineManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 70FC991BA8BE1F998CE428B0 /* DoneBaselineManager.m */; };
		A0C2B210F11D1B8E6BCB6C9F /* SDWebImageCacheSerializer.m in Sources */ = {isa = PBXBuildFile; fileRef = E590A68C0B59FFDCE615652F /* SDWebImageCacheSerializer.m */; };
		A0CA2122157EA5D73046064D /* FlowFrontViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = ED68E528EFDF6EFDCB79C404 /* FlowFrontViewController.h */; };
		A0D666543E6BCF2595339DC5 /* NSArray+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 4A98710EE05F98E31F6527E2 /* NSArray+MASAdditions.m */; };
		A196AB9278707C0E88E8CB04 /* NineRowsProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 9E116F9FEBD1565E954FB238 /* NineRowsProtocol.h */; };
		A1EE83134DF53A24C6A77C3E /* MQTTSessionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DA0D38F59E05651F1AA1C9B /* MQTTSessionManager.h */; };
		A2313C616FCCFB4029D74F11 /* BigArmLocalHow.m in Sources */ = {isa = PBXBuildFile; fileRef = 499B4FAC0C1CD927AF7B268C /* BigArmLocalHow.m */; };
		A2426C0997285AB9025845D5 /* RetMidManager.m in Sources */ = {isa = PBXBuildFile; fileRef = E2F0702C35C9594D2D7263D1 /* RetMidManager.m */; };
		A406C9D58F9E655B8AE21EC7 /* MailNordic.h in Headers */ = {isa = PBXBuildFile; fileRef = 39959CD8364853325A4BE187 /* MailNordic.h */; };
		A41EAD1CE7A5A06AF5F37C98 /* SDImageHEICCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = E69C86E560FF68DAC4F1DC0B /* SDImageHEICCoder.m */; };
		A58EBF6D32EBDAB59654E9FF /* StrongHexViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = CD79F1E57B11B55FA5B355CC /* StrongHexViewController.h */; };
		A5B9536F6E8BFB363689792C /* BestArteryModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 5CBB2E2E785F3667894332A6 /* BestArteryModel.h */; };
		A613B9BE8991D66A651EDF03 /* MASCompositeConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = F2CE7EED22BA0E3AC4374053 /* MASCompositeConstraint.m */; };
		A6208B4F9B5B340417AD8962 /* PrivacyResultsViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 247D03906ED1CD893A6B1560 /* PrivacyResultsViewController.h */; };
		A64C2073A52856B6E83F2EB2 /* GCDTimer.h in Headers */ = {isa = PBXBuildFile; fileRef = 81C783BC7BD08D0C6442D1BC /* GCDTimer.h */; };
		A888ED49AEB44E9EF775CE64 /* MQTTSSLSecurityPolicy.h in Headers */ = {isa = PBXBuildFile; fileRef = 94F99FB99E73A333979E048A /* MQTTSSLSecurityPolicy.h */; };
		A8F4DAC4B9E68B65DB56E692 /* NSData+ImageContentType.m in Sources */ = {isa = PBXBuildFile; fileRef = 932BBBC584CE4B597DD49A80 /* NSData+ImageContentType.m */; };
		A94014A859AAC7519FA8B585 /* MQTTSSLSecurityPolicyDecoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 8653F2BF00BC655555B54CFF /* MQTTSSLSecurityPolicyDecoder.m */; };
		A95DA5BF8F421F3ADAC76978 /* LambdaCyrillic.h in Headers */ = {isa = PBXBuildFile; fileRef = 6A41FC3AA48D1FAE3FE73DA8 /* LambdaCyrillic.h */; };
		AA45867C42FB6B7C6D465069 /* EngineManager.h in Headers */ = {isa = PBXBuildFile; fileRef = B741709667CD141D00090C1E /* EngineManager.h */; };
		AADA9ADC7BC181046EE938E8 /* HisClickColor.h in Headers */ = {isa = PBXBuildFile; fileRef = 9E736D9DE53F68F9F55541CA /* HisClickColor.h */; };
		AAFEED424BD99D5BBDBA09E6 /* SDWebImageTransition.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E21AE6E039273D15BABC7BC /* SDWebImageTransition.h */; };
		AB1439D051BC38E74FA11A22 /* PurpleInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 5A5D7A7C31C8CEFE8BE90D05 /* PurpleInfo.h */; };
		AC2712ADC53A81FD9D54884F /* SDWebImageError.m in Sources */ = {isa = PBXBuildFile; fileRef = 9BF2C8428FDA3726F6EBF3B2 /* SDWebImageError.m */; };
		AC718505D224804E063FE5D8 /* UIView+WebCacheOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 21849584A2C871E3EFAC04D1 /* UIView+WebCacheOperation.h */; };
		AD8625C6176B9D82E86CCDB1 /* SDDiskCache.m in Sources */ = {isa = PBXBuildFile; fileRef = EB068262B21F43A08ADE8DF9 /* SDDiskCache.m */; };
		ADC9187BC8D8AF87F2F160D3 /* MQTTLog.m in Sources */ = {isa = PBXBuildFile; fileRef = E8AA3B992B7E7FC3CD94D392 /* MQTTLog.m */; };
		AE22C71869F2564230A6B800 /* MinimizeEstonianIcyFinderRoot.h in Headers */ = {isa = PBXBuildFile; fileRef = D768F642D053C7D24408CA2A /* MinimizeEstonianIcyFinderRoot.h */; };
		AE7341CD8C0C894542091BD4 /* NSData+Iterate.m in Sources */ = {isa = PBXBuildFile; fileRef = C4EDB2F18D849289AD27E54A /* NSData+Iterate.m */; };
		AE805FED96AEBE48968B6509 /* ThreeAskManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 1E5317C31EF52513D816D05C /* ThreeAskManager.h */; };
		AEAF0B9ED680E472BFB081F1 /* WristSkinInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 00FC3F0A69C6E78C03C55590 /* WristSkinInfo.m */; };
		AF0E64CCA09B0CB67DA07FDF /* UIColor+BoxColor.h in Headers */ = {isa = PBXBuildFile; fileRef = 28D768E71E80ADB40AD98EC7 /* UIColor+BoxColor.h */; };
		B010A2BBD6BFFD9AB6EABB36 /* SDImageTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 3A14DDA1304C234D440BDA68 /* SDImageTransformer.m */; };
		B0A3A069D36E0C1FB797CE82 /* BestArteryModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 5096015B768B43630CA3425B /* BestArteryModel.m */; };
		B0AF83099F64E42E3F983C80 /* SDAssociatedObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 2CBFBE12A65F7EE14682F19E /* SDAssociatedObject.m */; };
		B0AFDC08EDD53BC2B756DD53 /* DayQueryWho.h in Headers */ = {isa = PBXBuildFile; fileRef = 1B21B6998F66B398B461453B /* DayQueryWho.h */; };
		B10508C7F712C11D46907549 /* CarriageAssignWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = DE1C0DAE8EBBA3BA314660D6 /* CarriageAssignWindow.m */; };
		B1FE6E92757CA3EF5E0AA82E /* ReceivedHexViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = F056268AD65604A3B20A1EC4 /* ReceivedHexViewController.h */; };
		B316D194E454685C67CA03E2 /* UIImageView+HighlightedWebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = A7C2B8DFDC59D6E1D3FDCA70 /* UIImageView+HighlightedWebCache.m */; };
		B5D481B5C4897CE91D45187A /* View+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D741318BA60B8C03E0A974E /* View+MASAdditions.m */; };
		B5F2DA4BC3513ACDD6A58240 /* SDImageAWebPCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 67C196C41D0EAA674724F4FA /* SDImageAWebPCoder.m */; };
		B6815CD31DC7C298A18E399A /* SDAnimatedImage.m in Sources */ = {isa = PBXBuildFile; fileRef = 196799D237B84F9168559E0B /* SDAnimatedImage.m */; };
		B6AB86E9EC14A663C5CC3685 /* ClampingSawPrepareAirlineParserModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 6895AE78A0240087EA25C44D /* ClampingSawPrepareAirlineParserModel.h */; };
		B7BB90793EBAD58B07B8C1D8 /* RestoresViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = B3B93536481B04D5F87A45BD /* RestoresViewController.h */; };
		B7BCBA87E855880696F3DFFE /* SobArcheryIll.m in Sources */ = {isa = PBXBuildFile; fileRef = 183729AF2C87DFA2274E5B81 /* SobArcheryIll.m */; };
		BA706FBD09750B77D0909DA7 /* MQTTSSLSecurityPolicyTransport.m in Sources */ = {isa = PBXBuildFile; fileRef = 3E504725704CFA2D196BBC53 /* MQTTSSLSecurityPolicyTransport.m */; };
		BB45BB126D796D06D4D53163 /* CopticLearn.h in Headers */ = {isa = PBXBuildFile; fileRef = 6F8860F2A23162C487276C4A /* CopticLearn.h */; };
		BB522607EC4D60B6103B9784 /* WrongArrayFootButton.h in Headers */ = {isa = PBXBuildFile; fileRef = 6568EEB95F605846FEEE647B /* WrongArrayFootButton.h */; };
		BB80994213BF0370DE27CB03 /* UIColor+SDHexString.h in Headers */ = {isa = PBXBuildFile; fileRef = EAED47CEBA7BD2C0C40747E5 /* UIColor+SDHexString.h */; };
		BBDB0889894963AE6E3C1B7F /* NSImage+Compatibility.h in Headers */ = {isa = PBXBuildFile; fileRef = 3EAD82AC01914684615CCF39 /* NSImage+Compatibility.h */; };
		BC77F874624BA2D954865D4A /* ReceivedHexViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D34C97C54083FE9AE25EBB8B /* ReceivedHexViewController.m */; };
		BCE29C1222B4A483783DAB89 /* UIImage+MultiFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = ACC8B386361431C1BA6E3EA0 /* UIImage+MultiFormat.h */; };
		BCF3C564370C335A2F09EE56 /* ForAlertView.h in Headers */ = {isa = PBXBuildFile; fileRef = 72E7F0949A7211CE3A19BBC5 /* ForAlertView.h */; };
		BE506CB215101DC3C7C774D0 /* PubPrepModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 08F6825CD5DB4DEE0C5D2DB0 /* PubPrepModel.m */; };
		BF3DAC20C391364885914336 /* MQTTSSLSecurityPolicyEncoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 110C338D0B1659A998F83FEA /* MQTTSSLSecurityPolicyEncoder.h */; };
		BF6D4F0392B37C5E5BD12B13 /* ThickViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5F8E65D302ECD716B57DF54C /* ThickViewController.m */; };
		BFBB9D929B336FD0DFB7D3C7 /* WeekVirtualBlueSucceededExpects.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E393D337E7B52A6AC054341 /* WeekVirtualBlueSucceededExpects.h */; };
		C155264C18CDD48FD4D468F7 /* NSArray+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = C2768940A7747EC091C1DB83 /* NSArray+MASAdditions.h */; };
		C2EF1098CFE88091B655A36D /* UIImage+Metadata.m in Sources */ = {isa = PBXBuildFile; fileRef = 9DE8EA78A392BE515CF2120F /* UIImage+Metadata.m */; };
		C5531B2E8CCD799EB3672D8E /* SDImageGraphics.h in Headers */ = {isa = PBXBuildFile; fileRef = F3E1CB383EBB7812B5FA0124 /* SDImageGraphics.h */; };
		C696252A9E20AA40FBA8451F /* UIViewController+DueViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 3AC79EA0C13C2D2D29C55D54 /* UIViewController+DueViewController.m */; };
		C6AFE96F423D25DAD97A7568 /* ChestFingerAxesDraftStyleViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5B3F7C11FFE4BD604D48FA52 /* ChestFingerAxesDraftStyleViewController.m */; };
		C79A5D66F539FE33CC7AC0D9 /* MQTTSessionLegacy.h in Headers */ = {isa = PBXBuildFile; fileRef = DEE654D09B9BEF6D7C8E5CBC /* MQTTSessionLegacy.h */; };
		C91C50EBB55AF7F261B85C08 /* NSButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 9B57FD5D321608FDCD7E4349 /* NSButton+WebCache.h */; };
		CA25748D6039972A1D23C0BA /* MQTTProperties.h in Headers */ = {isa = PBXBuildFile; fileRef = 33657B8D557B39D38C9EF172 /* MQTTProperties.h */; };
		CA30EA8990D01D7159871E6A /* LengthAndViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = DDC5E9B28E2FA28D5EC2EDF6 /* LengthAndViewController.h */; };
		CA773CDD8F3F202E37E9354E /* CatalogProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EAAF6A9B31C8DEB2ABF8BDE5 /* CatalogProtocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CB64699B99D43867821FE12F /* EngineManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F209CB11491DD7A1DC338067 /* EngineManager.m */; };
		CCCEF1D4CFC91ECF52F05A15 /* BigSlabUniversalSpecifiedFoot.m in Sources */ = {isa = PBXBuildFile; fileRef = 328B76FD6F585CEFCA8BFE7B /* BigSlabUniversalSpecifiedFoot.m */; };
		CD2087B9E84C442FAC1423B5 /* NSObject+MixModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 38231985783D1E114FFCE1DB /* NSObject+MixModel.m */; };
		CDDF7A3129973893226F1A0C /* TakeBitPenMask.h in Headers */ = {isa = PBXBuildFile; fileRef = A6231BD5B58A5EF2B27701FF /* TakeBitPenMask.h */; };
		CE97DFD32805BA717F5FE0C5 /* SDAnimatedImageRep.m in Sources */ = {isa = PBXBuildFile; fileRef = 59498A3A9E08DCED918C5AFE /* SDAnimatedImageRep.m */; };
		D065FFD9917D33FE147F11F6 /* ScannedAll.h in Headers */ = {isa = PBXBuildFile; fileRef = 6D96A7946FAD9E5CDB13CD70 /* ScannedAll.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D19F8859E4543740881D4229 /* ModalEggView.m in Sources */ = {isa = PBXBuildFile; fileRef = 2B6BE6F4E10A1A41774395D5 /* ModalEggView.m */; };
		D1C5536D386649E45BC25B45 /* SequenceStreamKeepReadoutOff.h in Headers */ = {isa = PBXBuildFile; fileRef = 899BB818A7B640D4D1017322 /* SequenceStreamKeepReadoutOff.h */; };
		D22E56A8492D2F6F7B8C4DE8 /* SDImageFramePool.m in Sources */ = {isa = PBXBuildFile; fileRef = 56D236F06BEC5053A5C59B20 /* SDImageFramePool.m */; };
		D268BD3B8740513E2CC8E27B /* MASUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = 47A38AA95469857CE4958A0D /* MASUtilities.h */; };
		D2AA2DD7A96E52402505B3D1 /* FarNetwork.m in Sources */ = {isa = PBXBuildFile; fileRef = CA7F8742975033AD9DBBA226 /* FarNetwork.m */; };
		D2D4B3BCB04B3CAC2C4DF47D /* SDImageLoader.m in Sources */ = {isa = PBXBuildFile; fileRef = 847552278079473BFC0C1499 /* SDImageLoader.m */; };
		D36267DA9D6591C8EEC2E03F /* Blink.h in Headers */ = {isa = PBXBuildFile; fileRef = 8D25C79DB18432D435F4A657 /* Blink.h */; };
		D3E5F2E1C435CE9F16B08AB3 /* MQTTSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FB4B85879FEF0C4729730C03 /* MQTTSessionManager.m */; };
		D5468FB99734587F7DA52729 /* MASConstraintMaker.h in Headers */ = {isa = PBXBuildFile; fileRef = 61525998F4DB47604918A6BE /* MASConstraintMaker.h */; };
		D6CBA466FDBBC27E95C9E430 /* MillHeightWill.m in Sources */ = {isa = PBXBuildFile; fileRef = 629DF378D662AD1F874CDF80 /* MillHeightWill.m */; };
		D7D60C03D02CAC6CFE190396 /* HeartBankManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 65839294B25D3C0FA7388184 /* HeartBankManager.h */; };
		D86FF8B516FF8E6F7C32AA6E /* ThreeAskManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A9AEE4C0F59554E8D01DFB67 /* ThreeAskManager.m */; };
		DA22AD70D7191475C9C3424A /* ZipOldRootView.m in Sources */ = {isa = PBXBuildFile; fileRef = 3379081ADC588BE7EB2D4297 /* ZipOldRootView.m */; };
		DA6D89FD55E7C8146C85DE54 /* BusCostCanon.h in Headers */ = {isa = PBXBuildFile; fileRef = A9543B3884FDA20F81B89C32 /* BusCostCanon.h */; };
		DAC6B1F96BAA88749F8E8712 /* SayToast.h in Headers */ = {isa = PBXBuildFile; fileRef = 58286D90DEBBFCA2500C6419 /* SayToast.h */; };
		DB188DB96049BB15CFB02A4F /* SugarWetLessHostingSphere.h in Headers */ = {isa = PBXBuildFile; fileRef = 86A581B44D2A981A502DCBE9 /* SugarWetLessHostingSphere.h */; };
		DBA004A6D1CF36F68E4A4A8D /* CarriageAssignWindow.h in Headers */ = {isa = PBXBuildFile; fileRef = 059291F1056B97BC58DDCFF2 /* CarriageAssignWindow.h */; };
		DBAC4DC708D254581D1B2DD2 /* MASConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = CE89AFCBE81511B5D026094D /* MASConstraint.m */; };
		DBDD609E0FD70AA4588BD0F9 /* SDWebImagePrefetcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 33B11F56D26B0B397BEC0F39 /* SDWebImagePrefetcher.m */; };
		DC12E6CCAF401994E94534A0 /* Blink.m in Sources */ = {isa = PBXBuildFile; fileRef = CDDBCB4C872C651BF423CD7F /* Blink.m */; };
		DC776B3857FA24A0B07532ED /* View+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 928005EED133E1D29AF6736E /* View+MASAdditions.h */; };
		DD1F487E287477572A867602 /* ClampingSawPrepareAirlineParserModel.m in Sources */ = {isa = PBXBuildFile; fileRef = C9190E8363BD5CEF8A5F31AD /* ClampingSawPrepareAirlineParserModel.m */; };
		DD956BA5BA084A4CCCE804C2 /* SDImageHEICCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 731151C5F7AA38EEDFFA268F /* SDImageHEICCoder.h */; };
		DE02B46473E1339E791EFCD8 /* WristSkinInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 4B4CC29107DB4499CF5373F9 /* WristSkinInfo.h */; };
		DE45AFE7B9DB54AD7C236705 /* BusCostCanon.m in Sources */ = {isa = PBXBuildFile; fileRef = 23FC97B1FD0AFA0384864974 /* BusCostCanon.m */; };
		DF86C5F97626C619740A0435 /* SDInternalMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 923350D9AAFA2C59C996DCEE /* SDInternalMacros.h */; };
		DFD22F3CE4C955CFCEF27635 /* SDDeviceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = EA9A9B9EF0D31CD7C4D090D6 /* SDDeviceHelper.m */; };
		E016C5C5E7A430DB9E567746 /* NearBadSeekManager.h in Headers */ = {isa = PBXBuildFile; fileRef = DA56EC021DA6AA281348BA36 /* NearBadSeekManager.h */; };
		E02A8BC708EDBD85FD484E06 /* SDAssociatedObject.h in Headers */ = {isa = PBXBuildFile; fileRef = F251D1B2F64E785A3B29CBEC /* SDAssociatedObject.h */; };
		E04B96F26133E035A78BC180 /* MQTTStrict.h in Headers */ = {isa = PBXBuildFile; fileRef = 3CA72CE7AA1B7C950E2FFB2F /* MQTTStrict.h */; };
		E11C6E2C01E057214BCDC4B1 /* BigArmLocalHow.h in Headers */ = {isa = PBXBuildFile; fileRef = 42448B84EC2A11030926B166 /* BigArmLocalHow.h */; };
		E35E9B52FA56412ABB3798BC /* BetweenMixList.m in Sources */ = {isa = PBXBuildFile; fileRef = FDB03C44DE7A2F1D950F74AF /* BetweenMixList.m */; };
		E376E22D5A7AF7BDED090E0C /* CupExtrasMinInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 9E218A77B77ADF55C3D7B84D /* CupExtrasMinInfo.m */; };
		E38FBCBE06C6AE64576D0B5E /* PaddleDublinManager.m in Sources */ = {isa = PBXBuildFile; fileRef = E0D633A295DC37B508774DD5 /* PaddleDublinManager.m */; };
		E3D7CDCD302FDAF9BFF6D516 /* HeartBankManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 15F67B5866BF8956C37BCFDE /* HeartBankManager.m */; };
		E4C4C4B956724114990A31E7 /* BigArmLocalHow+HailPrice.h in Headers */ = {isa = PBXBuildFile; fileRef = E42397540E279108EFA5B23B /* BigArmLocalHow+HailPrice.h */; };
		E51FAE002B017F52B52E4C17 /* UIImage+MemoryCacheCost.m in Sources */ = {isa = PBXBuildFile; fileRef = 66F1385DC89464B792267D92 /* UIImage+MemoryCacheCost.m */; };
		E576DB7B3497E0DC31918626 /* SDGraphicsImageRenderer.m in Sources */ = {isa = PBXBuildFile; fileRef = 71414A676A2CE6907B0E1006 /* SDGraphicsImageRenderer.m */; };
		E62642DA3ADF2DDBB739BA2A /* SDImageCachesManagerOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = BADE2564CA13434B8C79429A /* SDImageCachesManagerOperation.m */; };
		E74A147589E92FDB6B4749AA /* MQTTCFSocketDecoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 169D8E57763450155E3A31EE /* MQTTCFSocketDecoder.m */; };
		E88E2195D5CAF0EE57399D88 /* ModalEggView.h in Headers */ = {isa = PBXBuildFile; fileRef = F83B1BBC07D4549FF3AF2133 /* ModalEggView.h */; };
		E8C3A963D9F38D9C27C02582 /* SDImageCachesManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 81BAC9267938647ACD737A47 /* SDImageCachesManager.m */; };
		E9E204FA7DFFFA0BE34A3FD9 /* XXGProtocolLabel.h in Headers */ = {isa = PBXBuildFile; fileRef = 5DFAB877AB4CBB5E11E2DCEC /* XXGProtocolLabel.h */; };
		EACD06712D7483758383BA87 /* SDWebImageOptionsProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = 159D2ADDA891BA8243F51859 /* SDWebImageOptionsProcessor.h */; };
		EB0BA5E7923FCF790945727E /* MASViewConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 5044BB2CAFCB29CBCE50B19E /* MASViewConstraint.m */; };
		EB2DA6302A158738A89D929C /* SDWeakProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = B8D603C5BB9D672B462F0A7F /* SDWeakProxy.m */; };
		EB3454DC3F6903F183028674 /* View+MASShorthandAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 8261C641EE96E0DFDCADD3E5 /* View+MASShorthandAdditions.h */; };
		EB71308596481376D938CF78 /* MQTTDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 6321C18474D8003BF688551E /* MQTTDecoder.h */; };
		EC474AAA14838502187444FD /* ArtistBuddy.h in Headers */ = {isa = PBXBuildFile; fileRef = 26566515590C64BFD79132F5 /* ArtistBuddy.h */; };
		EC7077A41E73EC64C1F585E8 /* IcyPhase.h in Headers */ = {isa = PBXBuildFile; fileRef = 9758F41AC8E4E1810497B723 /* IcyPhase.h */; };
		EC8797F4C8EF91C08B5918EC /* UIDevice+HueDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 015E712620E0D1C12D6F5A84 /* UIDevice+HueDevice.m */; };
		EF8812888C286542211EBF87 /* MQTTCFSocketEncoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 314EE65B8366914F72EB408D /* MQTTCFSocketEncoder.m */; };
		EFB06E235DC7B2C1DE685770 /* MQTTCFSocketTransport.h in Headers */ = {isa = PBXBuildFile; fileRef = A676CCCC0E6E3F3E11B4FA67 /* MQTTCFSocketTransport.h */; };
		F07D0E87A6E65BC9AE6D1FA1 /* SDWebImageDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 02FC951E7B809861184BE0F7 /* SDWebImageDefine.h */; };
		F0925211D68FFE22363CCBD7 /* SDWebImageTransitionInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = BA6534B211143296191756DA /* SDWebImageTransitionInternal.h */; };
		F09B1C3D770739925CDC83A6 /* SDImageGIFCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = E826D316805B60B38D3DBD8F /* SDImageGIFCoder.h */; };
		F0AA88F835CF465503AD8447 /* ScannedAll.m in Sources */ = {isa = PBXBuildFile; fileRef = F319AF17FB78302704349AA2 /* ScannedAll.m */; };
		F136EA55CB473ACFBC5034D6 /* UIImage+GIF.h in Headers */ = {isa = PBXBuildFile; fileRef = A68A62359F3D4E83A5651627 /* UIImage+GIF.h */; };
		F18DDA9DFD3BB7B447E3116E /* TrainingRows.m in Sources */ = {isa = PBXBuildFile; fileRef = 816DADB75E31F5B12ACE8B74 /* TrainingRows.m */; };
		F1AFAA4BD02C63CDE09B95AF /* InferOverViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = FE70D32999C06FEB2505A815 /* InferOverViewController.h */; };
		F248CC63BC9A5A74860B9B97 /* UIView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = C2ACDF767EBE278A4805416A /* UIView+WebCache.m */; };
		F256BAD0A060ACF48AB6DCAC /* MQTTMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = EFC8CA3C1FF33FD516337092 /* MQTTMessage.h */; };
		F3052F7655FE0C655E203225 /* MusicAuditNoteMajorSuddenTool.h in Headers */ = {isa = PBXBuildFile; fileRef = CB0C9C3E5017D06FF1E052FA /* MusicAuditNoteMajorSuddenTool.h */; };
		F33943BC55150D8FCEC93DD5 /* UIImageView+HighlightedWebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 0554F3F5B975DB78730142EF /* UIImageView+HighlightedWebCache.h */; };
		F3493914CF4FC05EABA255ED /* NSLayoutConstraint+MASDebugAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 341425921191296A1AA0983A /* NSLayoutConstraint+MASDebugAdditions.h */; };
		F4A9B17F91034D74D1981AFF /* ChestFingerAxesDraftStyleViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = EFEF5A51A3D7518A7C505F1C /* ChestFingerAxesDraftStyleViewController.h */; };
		F50355E2BA0CF60B8B659AE5 /* HisClickColor.m in Sources */ = {isa = PBXBuildFile; fileRef = DFE062B8DBC6CA2E73628418 /* HisClickColor.m */; };
		F5F3C2A5463598B5CA8A9E47 /* SixTheAppleManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 70E95F54D54295A5B26CE027 /* SixTheAppleManager.m */; };
		F6BB976D0FAA5D789C979BFA /* NearBadSeekManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 2485E56ADE3A76E5E6C00F0A /* NearBadSeekManager.m */; };
		F6DA710652DD4206FE8231AA /* UIImage+CupImage.h in Headers */ = {isa = PBXBuildFile; fileRef = 08CD130956508CC4DFF94FB8 /* UIImage+CupImage.h */; };
		F7AE4DA6DDA06E71D9015B4A /* SDFileAttributeHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 49FD04FA74FDEC032C6E836F /* SDFileAttributeHelper.h */; };
		F7B29C6A00537C89D8B1C38E /* PurpleInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = A754D7A92640B8BE0605B858 /* PurpleInfo.m */; };
		F7D9BC6221B1AE2CB89747E7 /* BigArmLocalHow+SubSub.m in Sources */ = {isa = PBXBuildFile; fileRef = 7ECB3AADBFEC385AF4C3C700 /* BigArmLocalHow+SubSub.m */; };
		F82406856508EEE716825395 /* SDCallbackQueue.h in Headers */ = {isa = PBXBuildFile; fileRef = DBD4899AAC409DDDADBFA46A /* SDCallbackQueue.h */; };
		F8E8A79D6156FC33227BD12A /* MASViewAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = 68574976DBC509D51E99A69D /* MASViewAttribute.m */; };
		F995F9007D0C488BE6F1359B /* UIImage+ExtendedCacheData.h in Headers */ = {isa = PBXBuildFile; fileRef = E34341FEF98528D92FF37756 /* UIImage+ExtendedCacheData.h */; };
		FC3AAADF1181F59FE8CA44C1 /* LoopPatchInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = E3BBDDA1CD354FAC2AE3F812 /* LoopPatchInfo.m */; };
		FD52A0B98D560F756928819E /* CubeSindhiViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = FCD6C65501221F346354383D /* CubeSindhiViewController.h */; };
		FD9A8804C521C9876CBDB1B6 /* SolidManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 3E99E89F81DFC32B7B8BB690 /* SolidManager.m */; };
		FE7DE03863BDBDC5FFEA877C /* SDWebImageDownloaderResponseModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = 179E89BB941A0662FF517DCF /* SDWebImageDownloaderResponseModifier.m */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		00621BBFB161DF7DD4DE4001 /* SDImageCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCoder.h; sourceTree = "<group>"; };
		00FC3F0A69C6E78C03C55590 /* WristSkinInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WristSkinInfo.m; sourceTree = "<group>"; };
		0127E9F98C9D56F53BD2E28A /* SpineCandidate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SpineCandidate.m; sourceTree = "<group>"; };
		0141AC08E7CF5E8EBBCF8ACE /* MQTTSSLSecurityPolicyTransport.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicyTransport.h; sourceTree = "<group>"; };
		015E712620E0D1C12D6F5A84 /* UIDevice+HueDevice.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIDevice+HueDevice.m"; sourceTree = "<group>"; };
		02FC951E7B809861184BE0F7 /* SDWebImageDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDefine.h; sourceTree = "<group>"; };
		04D063564A0125F3538495D5 /* TrainingRows.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TrainingRows.h; sourceTree = "<group>"; };
		0534CB0D1428BF495B49B465 /* InferOverViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InferOverViewController.m; sourceTree = "<group>"; };
		0554F3F5B975DB78730142EF /* UIImageView+HighlightedWebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImageView+HighlightedWebCache.h"; sourceTree = "<group>"; };
		059291F1056B97BC58DDCFF2 /* CarriageAssignWindow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CarriageAssignWindow.h; sourceTree = "<group>"; };
		06B123B981D365F0DEE48507 /* SDWebImageDownloaderDecryptor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderDecryptor.m; sourceTree = "<group>"; };
		08CD130956508CC4DFF94FB8 /* UIImage+CupImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+CupImage.h"; sourceTree = "<group>"; };
		08E367B4D4FC8CD8DE30E1FC /* RedoneMode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RedoneMode.m; sourceTree = "<group>"; };
		08F6825CD5DB4DEE0C5D2DB0 /* PubPrepModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PubPrepModel.m; sourceTree = "<group>"; };
		09B6D21DC1A98571727692B1 /* StrongHexViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = StrongHexViewController.m; sourceTree = "<group>"; };
		0A8FF55E211B91FA5A3DC041 /* DoneSalt.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DoneSalt.h; sourceTree = "<group>"; };
		0C87A569427F1376BE30EA98 /* NextBoxMoreManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NextBoxMoreManager.m; sourceTree = "<group>"; };
		0C8CD09FB9C084B2EFA4BABB /* DownloadsManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DownloadsManager.m; sourceTree = "<group>"; };
		0DF3405F7FB55AF45A00259C /* MagnitudeLateViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MagnitudeLateViewController.h; sourceTree = "<group>"; };
		0FB23742819ABAB05DC2C70C /* RadialSkip.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RadialSkip.h; sourceTree = "<group>"; };
		110C338D0B1659A998F83FEA /* MQTTSSLSecurityPolicyEncoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicyEncoder.h; sourceTree = "<group>"; };
		11B06AB5B208018FD9464C94 /* ElderNotationVolumeResourcesShortcut.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ElderNotationVolumeResourcesShortcut.m; sourceTree = "<group>"; };
		134B34599D694BEC11B2E0A6 /* GCDTimer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GCDTimer.m; sourceTree = "<group>"; };
		1415F9C41BB01405DE76388A /* CubeSindhiViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CubeSindhiViewController.m; sourceTree = "<group>"; };
		1461289DC458EE67F3D4AD97 /* FatMailManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FatMailManager.h; sourceTree = "<group>"; };
		159D2ADDA891BA8243F51859 /* SDWebImageOptionsProcessor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageOptionsProcessor.h; sourceTree = "<group>"; };
		15F67B5866BF8956C37BCFDE /* HeartBankManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HeartBankManager.m; sourceTree = "<group>"; };
		160249105CBDAAD53B63E94A /* InterExistWalk.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InterExistWalk.m; sourceTree = "<group>"; };
		169D8E57763450155E3A31EE /* MQTTCFSocketDecoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCFSocketDecoder.m; sourceTree = "<group>"; };
		1774018FE519AA2033D3E61C /* SDImageIOAnimatedCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageIOAnimatedCoder.m; sourceTree = "<group>"; };
		179E89BB941A0662FF517DCF /* SDWebImageDownloaderResponseModifier.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderResponseModifier.m; sourceTree = "<group>"; };
		17DA1009F520757DC05809D8 /* MQTTSessionLegacy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSessionLegacy.m; sourceTree = "<group>"; };
		183729AF2C87DFA2274E5B81 /* SobArcheryIll.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SobArcheryIll.m; sourceTree = "<group>"; };
		196799D237B84F9168559E0B /* SDAnimatedImage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImage.m; sourceTree = "<group>"; };
		1A00A355FDD4EE29C47A53BA /* PubPrepModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PubPrepModel.h; sourceTree = "<group>"; };
		1A2804FE28CA72D6AF4E51E2 /* UsabilityOnlyController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UsabilityOnlyController.h; sourceTree = "<group>"; };
		1A29A1C68E460F5AACDA238B /* MediaDerivedAssignMattingActionExpensive.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MediaDerivedAssignMattingActionExpensive.m; sourceTree = "<group>"; };
		1AE521836E9E6CDFAE1F9721 /* SDImageIOCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageIOCoder.m; sourceTree = "<group>"; };
		1B21B6998F66B398B461453B /* DayQueryWho.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DayQueryWho.h; sourceTree = "<group>"; };
		1B35DC6766CFFD43FA10BD1E /* SDImageGIFCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageGIFCoder.m; sourceTree = "<group>"; };
		1D0717780CF4D1196DA89833 /* EggNetCivilSon.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EggNetCivilSon.h; sourceTree = "<group>"; };
		1E2EF030009ADAABDA3BF993 /* NSString+Messaging.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+Messaging.h"; sourceTree = "<group>"; };
		1E5317C31EF52513D816D05C /* ThreeAskManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ThreeAskManager.h; sourceTree = "<group>"; };
		2070FEC4A15C57B418A3DF72 /* NSButton+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSButton+WebCache.m"; sourceTree = "<group>"; };
		20C4A65703F636E5B80E9CBD /* MQTTClient.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTClient.h; sourceTree = "<group>"; };
		21849584A2C871E3EFAC04D1 /* UIView+WebCacheOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCacheOperation.h"; sourceTree = "<group>"; };
		23D2FA67C72CB4150731927D /* SDWebImageDownloaderRequestModifier.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderRequestModifier.m; sourceTree = "<group>"; };
		23F0B729AEB3980253E8899A /* DogBufferViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DogBufferViewController.m; sourceTree = "<group>"; };
		23FC97B1FD0AFA0384864974 /* BusCostCanon.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BusCostCanon.m; sourceTree = "<group>"; };
		242A9BAA6C3275864A481C46 /* MagnitudeLateViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MagnitudeLateViewController.m; sourceTree = "<group>"; };
		247D03906ED1CD893A6B1560 /* PrivacyResultsViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PrivacyResultsViewController.h; sourceTree = "<group>"; };
		2485E56ADE3A76E5E6C00F0A /* NearBadSeekManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NearBadSeekManager.m; sourceTree = "<group>"; };
		258CDBDF9487ACAA5AA5EB68 /* DueTurnWasViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DueTurnWasViewController.m; sourceTree = "<group>"; };
		25AFBEC497C3263C5E017B93 /* SDWebImageTransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageTransition.m; sourceTree = "<group>"; };
		26145E138BB1A5854F92A086 /* CatAskEmailOldViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CatAskEmailOldViewController.m; sourceTree = "<group>"; };
		26566515590C64BFD79132F5 /* ArtistBuddy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ArtistBuddy.h; sourceTree = "<group>"; };
		26584EE61B7DEAE22FD6E72D /* MASLayoutConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASLayoutConstraint.m; sourceTree = "<group>"; };
		270818EA3C73F848884F82B4 /* PasswordsMoveViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PasswordsMoveViewController.h; sourceTree = "<group>"; };
		28ACE33FCDF7951D85998ED0 /* ThickViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ThickViewController.h; sourceTree = "<group>"; };
		28D768E71E80ADB40AD98EC7 /* UIColor+BoxColor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIColor+BoxColor.h"; sourceTree = "<group>"; };
		2A2353528AFD9E75D98C173C /* MQTTCFSocketEncoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCFSocketEncoder.h; sourceTree = "<group>"; };
		2AE9C608D7CE57A0E4C5C3D0 /* UIImageView+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+WebCache.m"; sourceTree = "<group>"; };
		2B6BE6F4E10A1A41774395D5 /* ModalEggView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ModalEggView.m; sourceTree = "<group>"; };
		2C629B00B2A750E58ED6EADA /* MQTTInMemoryPersistence.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTInMemoryPersistence.m; sourceTree = "<group>"; };
		2CBFBE12A65F7EE14682F19E /* SDAssociatedObject.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAssociatedObject.m; sourceTree = "<group>"; };
		2D08BB8548A2681C22B92395 /* LambdaCyrillic.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LambdaCyrillic.m; sourceTree = "<group>"; };
		2D38AE6B58A93A9692673DA6 /* ViewController+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "ViewController+MASAdditions.h"; sourceTree = "<group>"; };
		2D71931F272F906E34A0B0F3 /* SDImageCoderHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCoderHelper.h; sourceTree = "<group>"; };
		2DB39BD3E3F92AB10A999550 /* SDImageCacheConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCacheConfig.h; sourceTree = "<group>"; };
		2DBF81C919A57FD2987C9C61 /* KeysReverting.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = KeysReverting.h; sourceTree = "<group>"; };
		2E036B0D30FF8BD1E58A89B6 /* TakeBitPenMask.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TakeBitPenMask.m; sourceTree = "<group>"; };
		2E21AE6E039273D15BABC7BC /* SDWebImageTransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageTransition.h; sourceTree = "<group>"; };
		2E393D337E7B52A6AC054341 /* WeekVirtualBlueSucceededExpects.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WeekVirtualBlueSucceededExpects.h; sourceTree = "<group>"; };
		2E49271E786CCE05FDA2F6B3 /* NSBezierPath+SDRoundedCorners.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSBezierPath+SDRoundedCorners.h"; sourceTree = "<group>"; };
		2EB6A1C9E8C459DFD7DE23F7 /* BigSlabUniversalSpecifiedFoot.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BigSlabUniversalSpecifiedFoot.h; sourceTree = "<group>"; };
		2FEAE548863ADF0EAB283BFB /* SDImageCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCoder.m; sourceTree = "<group>"; };
		3036842952CA15E1ACC9BE0F /* SDWebImageDownloaderConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderConfig.m; sourceTree = "<group>"; };
		314EE65B8366914F72EB408D /* MQTTCFSocketEncoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCFSocketEncoder.m; sourceTree = "<group>"; };
		31B4F71C4BBE5B314BDAEBC5 /* ForAlertView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ForAlertView.m; sourceTree = "<group>"; };
		31C4F7EB3105FC7EF034AFEC /* KnowPictureAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = KnowPictureAction.h; sourceTree = "<group>"; };
		31E069C36D329AC879068356 /* ExtentsPubViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ExtentsPubViewController.m; sourceTree = "<group>"; };
		3254F188E39822392D519389 /* IcyPhase.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IcyPhase.m; sourceTree = "<group>"; };
		328B76FD6F585CEFCA8BFE7B /* BigSlabUniversalSpecifiedFoot.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BigSlabUniversalSpecifiedFoot.m; sourceTree = "<group>"; };
		33657B8D557B39D38C9EF172 /* MQTTProperties.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTProperties.h; sourceTree = "<group>"; };
		3379081ADC588BE7EB2D4297 /* ZipOldRootView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZipOldRootView.m; sourceTree = "<group>"; };
		33B11F56D26B0B397BEC0F39 /* SDWebImagePrefetcher.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImagePrefetcher.m; sourceTree = "<group>"; };
		33F29C413EBE4AB4346B6FDA /* GaspBusyStickyCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GaspBusyStickyCell.m; sourceTree = "<group>"; };
		341425921191296A1AA0983A /* NSLayoutConstraint+MASDebugAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSLayoutConstraint+MASDebugAdditions.h"; sourceTree = "<group>"; };
		34754C226F2DE8975C966C73 /* SixTheAppleManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SixTheAppleManager.h; sourceTree = "<group>"; };
		350EA248EF98A46B64E1CF0F /* MQTTTransportProtocol.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTTransportProtocol.m; sourceTree = "<group>"; };
		3558CDC32649CB91C24010E9 /* SuddenBufferModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SuddenBufferModel.h; sourceTree = "<group>"; };
		367013C26C71CF2AEC6AC647 /* SDWebImageDownloaderDecryptor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderDecryptor.h; sourceTree = "<group>"; };
		3746C2D6AD84CA345FDFA76D /* MQTTCoreDataPersistence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCoreDataPersistence.h; sourceTree = "<group>"; };
		38231985783D1E114FFCE1DB /* NSObject+MixModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSObject+MixModel.m"; sourceTree = "<group>"; };
		383CD14119C8B839C164B4DF /* SDWebImageDownloader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloader.h; sourceTree = "<group>"; };
		3841294CDD50042EE2A283D5 /* RealEntryTextField.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RealEntryTextField.m; sourceTree = "<group>"; };
		39876BE41E2C937A31CB0F9E /* KerningKeyManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = KerningKeyManager.h; sourceTree = "<group>"; };
		39959CD8364853325A4BE187 /* MailNordic.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MailNordic.h; sourceTree = "<group>"; };
		3A14DDA1304C234D440BDA68 /* SDImageTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageTransformer.m; sourceTree = "<group>"; };
		3AC79EA0C13C2D2D29C55D54 /* UIViewController+DueViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+DueViewController.m"; sourceTree = "<group>"; };
		3AFC33D952AB5726ADAADBC4 /* AlienDelayItemBriefSelector.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AlienDelayItemBriefSelector.h; sourceTree = "<group>"; };
		3B7775B5DF1CE8804C86293E /* JabberStepperContrastHomeReportPrefers.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JabberStepperContrastHomeReportPrefers.h; sourceTree = "<group>"; };
		3C138019B9AA4863FA13D5C5 /* UserAlone.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserAlone.m; sourceTree = "<group>"; };
		3C173E4F042047E44AFC22F5 /* UIImage+MultiFormat.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+MultiFormat.m"; sourceTree = "<group>"; };
		3C3315522EEFDF32B7F387D9 /* SDImageCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCache.m; sourceTree = "<group>"; };
		3CA72CE7AA1B7C950E2FFB2F /* MQTTStrict.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTStrict.h; sourceTree = "<group>"; };
		3E504725704CFA2D196BBC53 /* MQTTSSLSecurityPolicyTransport.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicyTransport.m; sourceTree = "<group>"; };
		3E6C8E2EBE046BC2CD109E1B /* SDWeakProxy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWeakProxy.h; sourceTree = "<group>"; };
		3E99E89F81DFC32B7B8BB690 /* SolidManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SolidManager.m; sourceTree = "<group>"; };
		3EA621424E770F0564BD7354 /* SDAnimatedImageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImageView.m; sourceTree = "<group>"; };
		3EAD82AC01914684615CCF39 /* NSImage+Compatibility.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSImage+Compatibility.h"; sourceTree = "<group>"; };
		3EE249C712F7333862BB4F69 /* SDWebImageError.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageError.h; sourceTree = "<group>"; };
		3EEA181AF0699ED0B7698F88 /* ContraceptiveSettee.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ContraceptiveSettee.m; sourceTree = "<group>"; };
		3EEE9B42069AE50949100F46 /* SayToast.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SayToast.m; sourceTree = "<group>"; };
		3F6C207190E40118EEAE135E /* UIImageView+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImageView+WebCache.h"; sourceTree = "<group>"; };
		400DDDCC30F118D4F8648C00 /* IndexRaiseConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IndexRaiseConfig.h; sourceTree = "<group>"; };
		412A79A5158A8AD51D63F245 /* NSImage+Compatibility.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSImage+Compatibility.m"; sourceTree = "<group>"; };
		4145F5EA5BF53DA10CC05E01 /* MQTTCFSocketDecoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCFSocketDecoder.h; sourceTree = "<group>"; };
		42448B84EC2A11030926B166 /* BigArmLocalHow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BigArmLocalHow.h; sourceTree = "<group>"; };
		42DC08131862E3594315B388 /* ForegroundReconnection.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ForegroundReconnection.m; sourceTree = "<group>"; };
		43165076F5C9973426EA873A /* AlienDelayItemBriefSelector.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AlienDelayItemBriefSelector.m; sourceTree = "<group>"; };
		4343F7E55E0F560FB3E10FE8 /* NextBoxMoreManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NextBoxMoreManager.h; sourceTree = "<group>"; };
		442FAD31038351AC260C575E /* RecordOrganizeDropCondensedPortraitsRelative.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RecordOrganizeDropCondensedPortraitsRelative.h; sourceTree = "<group>"; };
		45B5518DB03DA61750349A9F /* SDImageAWebPCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageAWebPCoder.h; sourceTree = "<group>"; };
		45EB850E609F3164CE779B84 /* MQTTProperties.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTProperties.m; sourceTree = "<group>"; };
		46D058B32A4429DEB671474C /* MQTTSessionSynchron.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSessionSynchron.h; sourceTree = "<group>"; };
		475F9FB69C8E18DD1F0FD83F /* SDImageFrame.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageFrame.h; sourceTree = "<group>"; };
		47A38AA95469857CE4958A0D /* MASUtilities.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASUtilities.h; sourceTree = "<group>"; };
		48104E81543B9CF73023C843 /* SDCallbackQueue.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDCallbackQueue.m; sourceTree = "<group>"; };
		48A79845B9C18C71111E2327 /* HelpRaceManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HelpRaceManager.h; sourceTree = "<group>"; };
		48EF292271472B12C9BCFFF0 /* UsabilityOnlyController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UsabilityOnlyController.m; sourceTree = "<group>"; };
		4916228DDE41692E820D6820 /* NSString+Suffix.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+Suffix.m"; sourceTree = "<group>"; };
		499B4FAC0C1CD927AF7B268C /* BigArmLocalHow.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BigArmLocalHow.m; sourceTree = "<group>"; };
		49FD04FA74FDEC032C6E836F /* SDFileAttributeHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDFileAttributeHelper.h; sourceTree = "<group>"; };
		4A98710EE05F98E31F6527E2 /* NSArray+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSArray+MASAdditions.m"; sourceTree = "<group>"; };
		4AB37405C32C3AA579D3031F /* SDAsyncBlockOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAsyncBlockOperation.h; sourceTree = "<group>"; };
		4B4CC29107DB4499CF5373F9 /* WristSkinInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WristSkinInfo.h; sourceTree = "<group>"; };
		4CA4F84FAC72902545F200CB /* SDImageAPNGCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageAPNGCoder.h; sourceTree = "<group>"; };
		4D544ED9992FBF94A6B6BC65 /* SDWebImageDownloaderConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderConfig.h; sourceTree = "<group>"; };
		4D94C65AF548CACE137BBCAA /* BigArmLocalHow+Birth.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "BigArmLocalHow+Birth.h"; sourceTree = "<group>"; };
		4DB4EBD56BEE0FFDB2F82287 /* SDImageAPNGCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageAPNGCoder.m; sourceTree = "<group>"; };
		4F324AD8E1EF4AF8430F60DC /* TagZipFailManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TagZipFailManager.h; sourceTree = "<group>"; };
		4F91A5842E4617B666B80A95 /* MQTTCoreDataPersistence.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCoreDataPersistence.m; sourceTree = "<group>"; };
		4FDC1B4C41D78E899AAAFA74 /* UIColor+SDHexString.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIColor+SDHexString.m"; sourceTree = "<group>"; };
		5044BB2CAFCB29CBCE50B19E /* MASViewConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASViewConstraint.m; sourceTree = "<group>"; };
		5096015B768B43630CA3425B /* BestArteryModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BestArteryModel.m; sourceTree = "<group>"; };
		525F793B64D68E3B7AAA91F1 /* ReconnectTimer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReconnectTimer.h; sourceTree = "<group>"; };
		52EC8A9B2078BCF779B41247 /* MQTTSSLSecurityPolicy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicy.m; sourceTree = "<group>"; };
		5302A6E4AB90AC37AEAD510B /* MQTTLog.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTLog.h; sourceTree = "<group>"; };
		54908D1100DACD15725F9DA4 /* UIImage+Transform.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+Transform.h"; sourceTree = "<group>"; };
		54B75A5CE1F820C306CB4637 /* SDImageLoadersManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageLoadersManager.m; sourceTree = "<group>"; };
		54D1CAA78E9ACB8334CDAB3C /* UIImage+Transform.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Transform.m"; sourceTree = "<group>"; };
		56D236F06BEC5053A5C59B20 /* SDImageFramePool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageFramePool.m; sourceTree = "<group>"; };
		57B3C365FB51A9732AC9F5F4 /* UIImage+Metadata.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+Metadata.h"; sourceTree = "<group>"; };
		5805A1130F27BB2DF90D36CE /* SDImageAssetManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageAssetManager.m; sourceTree = "<group>"; };
		58286D90DEBBFCA2500C6419 /* SayToast.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SayToast.h; sourceTree = "<group>"; };
		58C4624188D60CCD0049F701 /* NSObject+MixModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSObject+MixModel.h"; sourceTree = "<group>"; };
		59498A3A9E08DCED918C5AFE /* SDAnimatedImageRep.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImageRep.m; sourceTree = "<group>"; };
		59F900FE83AA1D74D6D8476F /* RouteJustProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RouteJustProtocol.h; sourceTree = "<group>"; };
		5A5D7A7C31C8CEFE8BE90D05 /* PurpleInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PurpleInfo.h; sourceTree = "<group>"; };
		5B3F7C11FFE4BD604D48FA52 /* ChestFingerAxesDraftStyleViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChestFingerAxesDraftStyleViewController.m; sourceTree = "<group>"; };
		5B47589BE26ECFA2B535287B /* HelpRaceManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HelpRaceManager.m; sourceTree = "<group>"; };
		5CBB2E2E785F3667894332A6 /* BestArteryModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BestArteryModel.h; sourceTree = "<group>"; };
		5CDDABC02C3E45FA28381C92 /* SDImageGraphics.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageGraphics.m; sourceTree = "<group>"; };
		5D0BAB29618AB638244BE2CC /* ReconnectTimer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReconnectTimer.m; sourceTree = "<group>"; };
		5D12ECF04F8BEE1B431684DB /* CityOffsetsViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CityOffsetsViewController.h; sourceTree = "<group>"; };
		5D79B94C0892E2346306E11C /* InterExistWalk.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InterExistWalk.h; sourceTree = "<group>"; };
		5DA13F14FB09778E2E4BD65E /* DayQueryWho.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DayQueryWho.m; sourceTree = "<group>"; };
		5DFAB877AB4CBB5E11E2DCEC /* XXGProtocolLabel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGProtocolLabel.h; sourceTree = "<group>"; };
		5F8E65D302ECD716B57DF54C /* ThickViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ThickViewController.m; sourceTree = "<group>"; };
		61525998F4DB47604918A6BE /* MASConstraintMaker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASConstraintMaker.h; sourceTree = "<group>"; };
		629DF378D662AD1F874CDF80 /* MillHeightWill.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MillHeightWill.m; sourceTree = "<group>"; };
		6321C18474D8003BF688551E /* MQTTDecoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTDecoder.h; sourceTree = "<group>"; };
		636D0E279FF1FE49884D9430 /* DoneSalt.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DoneSalt.m; sourceTree = "<group>"; };
		651C656400B2DF18ACF0C534 /* UIImage+ForceDecode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+ForceDecode.m"; sourceTree = "<group>"; };
		6568EEB95F605846FEEE647B /* WrongArrayFootButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WrongArrayFootButton.h; sourceTree = "<group>"; };
		65839294B25D3C0FA7388184 /* HeartBankManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HeartBankManager.h; sourceTree = "<group>"; };
		6600EDC07BA6AD1899FB46B1 /* SDWebImageOptionsProcessor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageOptionsProcessor.m; sourceTree = "<group>"; };
		6625C376A089820D01770FB5 /* FatMailManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FatMailManager.m; sourceTree = "<group>"; };
		66E5E37C6C8688615A7BFE8B /* SDImageIOAnimatedCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageIOAnimatedCoder.h; sourceTree = "<group>"; };
		66F1385DC89464B792267D92 /* UIImage+MemoryCacheCost.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+MemoryCacheCost.m"; sourceTree = "<group>"; };
		6721C73D1A3AA1DD914CFDF4 /* MQTTSession.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSession.h; sourceTree = "<group>"; };
		672B59D2016BE6EE53A5F55F /* NSObject+CivilAdoptMindfulCoachedCap.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSObject+CivilAdoptMindfulCoachedCap.m"; sourceTree = "<group>"; };
		67C196C41D0EAA674724F4FA /* SDImageAWebPCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageAWebPCoder.m; sourceTree = "<group>"; };
		6841C289CEF7571E0BBD7114 /* SDImageCodersManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCodersManager.m; sourceTree = "<group>"; };
		68574976DBC509D51E99A69D /* MASViewAttribute.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASViewAttribute.m; sourceTree = "<group>"; };
		6895AE78A0240087EA25C44D /* ClampingSawPrepareAirlineParserModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ClampingSawPrepareAirlineParserModel.h; sourceTree = "<group>"; };
		6A41FC3AA48D1FAE3FE73DA8 /* LambdaCyrillic.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LambdaCyrillic.h; sourceTree = "<group>"; };
		6B6D8A1AAA121C3B054E9807 /* SDWebImageIndicator.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageIndicator.h; sourceTree = "<group>"; };
		6C98EF2A0F469453C3AD21A3 /* MQTTSSLSecurityPolicyDecoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicyDecoder.h; sourceTree = "<group>"; };
		6CD424F67F634ACF88BFDAE1 /* Masonry.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Masonry.h; sourceTree = "<group>"; };
		6CD6782D48301BD79051D006 /* MQTTSessionSynchron.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSessionSynchron.m; sourceTree = "<group>"; };
		6D96A7946FAD9E5CDB13CD70 /* ScannedAll.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ScannedAll.h; sourceTree = "<group>"; };
		6DB1D47105FD1D92FD7DD337 /* LessCutWinCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LessCutWinCell.h; sourceTree = "<group>"; };
		6DC90BBD13D0F89BD36C9085 /* ReadTiedKinButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReadTiedKinButton.h; sourceTree = "<group>"; };
		6DF731BC235E210D11537877 /* OpenPurposeMostlySynthesisFlushed.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OpenPurposeMostlySynthesisFlushed.h; sourceTree = "<group>"; };
		6E83A911230B8F32C607210F /* SDImageCacheDefine.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCacheDefine.m; sourceTree = "<group>"; };
		6F0CDF7392A84879664CE6E1 /* PopDrainImmediateOnePlug.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PopDrainImmediateOnePlug.m; sourceTree = "<group>"; };
		6F2855EC54E62C5351F356AE /* SDImageCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCache.h; sourceTree = "<group>"; };
		6F8860F2A23162C487276C4A /* CopticLearn.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CopticLearn.h; sourceTree = "<group>"; };
		70B46B12EF1073DB2C779F1A /* NSString+UnitZipHair.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+UnitZipHair.h"; sourceTree = "<group>"; };
		70E95F54D54295A5B26CE027 /* SixTheAppleManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SixTheAppleManager.m; sourceTree = "<group>"; };
		70EA9F440BEB15CFD18C83CE /* DueTurnWasViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DueTurnWasViewController.h; sourceTree = "<group>"; };
		70FC991BA8BE1F998CE428B0 /* DoneBaselineManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DoneBaselineManager.m; sourceTree = "<group>"; };
		71389E587E3462F4F8BE17C6 /* UIDevice+HueDevice.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIDevice+HueDevice.h"; sourceTree = "<group>"; };
		71414A676A2CE6907B0E1006 /* SDGraphicsImageRenderer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDGraphicsImageRenderer.m; sourceTree = "<group>"; };
		726DC39742533499DDAB07E1 /* RingAgeDegradedEyeTextualAppearingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RingAgeDegradedEyeTextualAppearingViewController.h; sourceTree = "<group>"; };
		72E7F0949A7211CE3A19BBC5 /* ForAlertView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ForAlertView.h; sourceTree = "<group>"; };
		731151C5F7AA38EEDFFA268F /* SDImageHEICCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageHEICCoder.h; sourceTree = "<group>"; };
		74CD8856F6935429278DDD61 /* SDAnimatedImageView+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SDAnimatedImageView+WebCache.h"; sourceTree = "<group>"; };
		74F72085F6733326E01D6E95 /* TripleAir.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TripleAir.m; sourceTree = "<group>"; };
		755A27FE5FB1EB2DFB266430 /* ContraceptiveSettee.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = ContraceptiveSettee.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		75688B93B26FFC322BBDA0E2 /* UIButton+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIButton+WebCache.m"; sourceTree = "<group>"; };
		75A3193784DF19F01C8B4F69 /* UIImage+ExtendedCacheData.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+ExtendedCacheData.m"; sourceTree = "<group>"; };
		761D610FB87D9B30AE4C024D /* SDWebImagePrefetcher.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImagePrefetcher.h; sourceTree = "<group>"; };
		772A931E96479EC5D6C3CA27 /* GaspBusyStickyCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GaspBusyStickyCell.h; sourceTree = "<group>"; };
		7847C6F4CEDBA3C97DC8D896 /* MolarAccept.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MolarAccept.h; sourceTree = "<group>"; };
		78E86B2621B8DD9D603E19A1 /* MASConstraintMaker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASConstraintMaker.m; sourceTree = "<group>"; };
		7976D258750B0DD463607379 /* UIImage+CupImage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+CupImage.m"; sourceTree = "<group>"; };
		7ABFB1C75CB985AA7960C57A /* MillHeightWill.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MillHeightWill.h; sourceTree = "<group>"; };
		7B6D4313289F080A1D6BA5CB /* SDGraphicsImageRenderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDGraphicsImageRenderer.h; sourceTree = "<group>"; };
		7B879A82D250A71C05BAF70D /* NSError+SawImageBin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSError+SawImageBin.h"; sourceTree = "<group>"; };
		7BA2E8AC31108FADD03B930C /* SpineCandidate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SpineCandidate.h; sourceTree = "<group>"; };
		7BF9C7162E86BDC941C5241C /* FarNetwork.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FarNetwork.h; sourceTree = "<group>"; };
		7C18D01F1A35A702EE11A789 /* RecordOrganizeDropCondensedPortraitsRelative.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RecordOrganizeDropCondensedPortraitsRelative.m; sourceTree = "<group>"; };
		7C8C702DC550916FC1C6ADB5 /* MASConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASConstraint.h; sourceTree = "<group>"; };
		7D0BDDD16B7F8818E17B3226 /* UIImage+GIF.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+GIF.m"; sourceTree = "<group>"; };
		7DA0D38F59E05651F1AA1C9B /* MQTTSessionManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSessionManager.h; sourceTree = "<group>"; };
		7DFE249A76BC872DE8F416D6 /* NSBezierPath+SDRoundedCorners.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSBezierPath+SDRoundedCorners.m"; sourceTree = "<group>"; };
		7E2B1F89FCD9CB153AE11EAA /* MASLayoutConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASLayoutConstraint.h; sourceTree = "<group>"; };
		7E354D346A2D78ECE758777B /* SDWebImageDownloader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloader.m; sourceTree = "<group>"; };
		7E6E26FD0F1E64D9F29F48B7 /* SuddenBufferModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SuddenBufferModel.m; sourceTree = "<group>"; };
		7ECB3AADBFEC385AF4C3C700 /* BigArmLocalHow+SubSub.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "BigArmLocalHow+SubSub.m"; sourceTree = "<group>"; };
		7F5680E8504EC9F757C74653 /* CleanWaist.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CleanWaist.m; sourceTree = "<group>"; };
		806EABE19229433563CDE529 /* SexualUtilitiesScopeSkipKilobits.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SexualUtilitiesScopeSkipKilobits.m; sourceTree = "<group>"; };
		809AD5FEDE890BFB7BF2D80A /* RingAgeDegradedEyeTextualAppearingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RingAgeDegradedEyeTextualAppearingViewController.m; sourceTree = "<group>"; };
		80B44C26D35DB15AD8374F45 /* SDWebImageOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageOperation.m; sourceTree = "<group>"; };
		816DADB75E31F5B12ACE8B74 /* TrainingRows.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TrainingRows.m; sourceTree = "<group>"; };
		817820C617F296D5986FC529 /* MQTTPersistence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTPersistence.h; sourceTree = "<group>"; };
		81B6C8C71D4DA38BFD11367A /* HowFaxConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HowFaxConfig.m; sourceTree = "<group>"; };
		81BAC9267938647ACD737A47 /* SDImageCachesManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCachesManager.m; sourceTree = "<group>"; };
		81C783BC7BD08D0C6442D1BC /* GCDTimer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GCDTimer.h; sourceTree = "<group>"; };
		81E4E8F9F85FB6E3C5A5BE6D /* SDWebImageCompat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageCompat.h; sourceTree = "<group>"; };
		81ED172EB1822E645853468C /* IndicatorViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IndicatorViewController.h; sourceTree = "<group>"; };
		8261C641EE96E0DFDCADD3E5 /* View+MASShorthandAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "View+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		82642752C6600F595408E5FB /* UIButton+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIButton+WebCache.h"; sourceTree = "<group>"; };
		82F628295CBE929F2196D18E /* SDFileAttributeHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDFileAttributeHelper.m; sourceTree = "<group>"; };
		82F6DAD0CC7530FB8A5C58E1 /* KeysReverting.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = KeysReverting.m; sourceTree = "<group>"; };
		83CCB1221EDEE4D4CFCD06B1 /* BusForDrawView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BusForDrawView.m; sourceTree = "<group>"; };
		84563A3643FB7BD2E42335C3 /* GeneratorDraftCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratorDraftCell.h; sourceTree = "<group>"; };
		847552278079473BFC0C1499 /* SDImageLoader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageLoader.m; sourceTree = "<group>"; };
		85F47BA07771BAFAD1BBA789 /* DownloadsManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DownloadsManager.h; sourceTree = "<group>"; };
		8606D7354FFB895DCBBA6A54 /* PasswordsMoveViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PasswordsMoveViewController.m; sourceTree = "<group>"; };
		8653F2BF00BC655555B54CFF /* MQTTSSLSecurityPolicyDecoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicyDecoder.m; sourceTree = "<group>"; };
		86784CCDE4309D6AABBD13E6 /* PenKeepMicroManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PenKeepMicroManager.h; sourceTree = "<group>"; };
		86A581B44D2A981A502DCBE9 /* SugarWetLessHostingSphere.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SugarWetLessHostingSphere.h; sourceTree = "<group>"; };
		86FF3B96AACCAEA27979453C /* NSData+ImageContentType.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSData+ImageContentType.h"; sourceTree = "<group>"; };
		899BB818A7B640D4D1017322 /* SequenceStreamKeepReadoutOff.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SequenceStreamKeepReadoutOff.h; sourceTree = "<group>"; };
		89FEBCD3EA3184BE20B05755 /* LoopPatchInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LoopPatchInfo.h; sourceTree = "<group>"; };
		8A5AFBE49A0CF05E0C1A3CE2 /* MASCompositeConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASCompositeConstraint.h; sourceTree = "<group>"; };
		8AA0D7DF97D1083AF23AED39 /* BusForDrawView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BusForDrawView.h; sourceTree = "<group>"; };
		8ADFA1E902A670161248CB1B /* MASViewConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASViewConstraint.h; sourceTree = "<group>"; };
		8B97A162ADC3D464593F9D64 /* ReadTiedKinButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReadTiedKinButton.m; sourceTree = "<group>"; };
		8C6F0CC5887AA99DC547892A /* SDImageCachesManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCachesManager.h; sourceTree = "<group>"; };
		8CD01B2E50E488A95EEB7219 /* CityOffsetsViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CityOffsetsViewController.m; sourceTree = "<group>"; };
		8D05125D852BA736DD26DF70 /* ChainPauseInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChainPauseInfo.m; sourceTree = "<group>"; };
		8D25C79DB18432D435F4A657 /* Blink.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Blink.h; sourceTree = "<group>"; };
		8D741318BA60B8C03E0A974E /* View+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "View+MASAdditions.m"; sourceTree = "<group>"; };
		8DDCEDC3B1542BA795B09D05 /* UIView+WebCacheState.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCacheState.h"; sourceTree = "<group>"; };
		8EFECEA7EC6E5085D6D2525D /* AssignTabBin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AssignTabBin.m; sourceTree = "<group>"; };
		8FF0FB0A5A1BAF2687096649 /* DoneBaselineManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DoneBaselineManager.h; sourceTree = "<group>"; };
		91045984D41569A0A164037D /* MQTTTransportProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTTransportProtocol.h; sourceTree = "<group>"; };
		91D4750739BF665376AC50AE /* LacrosseModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LacrosseModel.h; sourceTree = "<group>"; };
		923350D9AAFA2C59C996DCEE /* SDInternalMacros.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDInternalMacros.h; sourceTree = "<group>"; };
		92347F9B30CCAECB4DFA2110 /* ViewController+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "ViewController+MASAdditions.m"; sourceTree = "<group>"; };
		928005EED133E1D29AF6736E /* View+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "View+MASAdditions.h"; sourceTree = "<group>"; };
		929E4B1DD0D76C5BC1E80683 /* BigArmLocalHow+SubSub.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "BigArmLocalHow+SubSub.h"; sourceTree = "<group>"; };
		930C4F3C61AF519C25B51039 /* UIView+WebCacheState.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCacheState.m"; sourceTree = "<group>"; };
		932BBBC584CE4B597DD49A80 /* NSData+ImageContentType.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSData+ImageContentType.m"; sourceTree = "<group>"; };
		93DF113FD033A4474797568F /* SDDisplayLink.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDDisplayLink.m; sourceTree = "<group>"; };
		94509C1AC47F7CB40CEF3462 /* AssignTabBin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AssignTabBin.h; sourceTree = "<group>"; };
		94B0CD2FC801155AAF236D82 /* ThemePartial.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ThemePartial.h; sourceTree = "<group>"; };
		94D7442691C8A7FE2E469E30 /* ChainPauseInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChainPauseInfo.h; sourceTree = "<group>"; };
		94F99FB99E73A333979E048A /* MQTTSSLSecurityPolicy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicy.h; sourceTree = "<group>"; };
		950A6D715494534A53ABBA6C /* NSData+Iterate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSData+Iterate.h"; sourceTree = "<group>"; };
		9575A1E616BE5FF904BFF68D /* UserAlone.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserAlone.h; sourceTree = "<group>"; };
		9758F41AC8E4E1810497B723 /* IcyPhase.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IcyPhase.h; sourceTree = "<group>"; };
		97B3A6217F9329522A760FCC /* UIViewController+DueViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIViewController+DueViewController.h"; sourceTree = "<group>"; };
		97C74D9C4178AC17341F1F1D /* RealEntryTextField.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RealEntryTextField.h; sourceTree = "<group>"; };
		98D20514F6509CB79DD55B25 /* SDWebImageDefine.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDefine.m; sourceTree = "<group>"; };
		98D55386CE84EE45F3D7204D /* SolidManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SolidManager.h; sourceTree = "<group>"; };
		99B0D2DEC5B9C36133443E50 /* JabberStepperContrastHomeReportPrefers.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JabberStepperContrastHomeReportPrefers.m; sourceTree = "<group>"; };
		9B57FD5D321608FDCD7E4349 /* NSButton+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSButton+WebCache.h"; sourceTree = "<group>"; };
		9BF2C8428FDA3726F6EBF3B2 /* SDWebImageError.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageError.m; sourceTree = "<group>"; };
		9C11DE961FCED40282C8C861 /* MASConstraint+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "MASConstraint+Private.h"; sourceTree = "<group>"; };
		9C720AD34169F1F100807201 /* SDAnimatedImageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImageView.h; sourceTree = "<group>"; };
		9DE8EA78A392BE515CF2120F /* UIImage+Metadata.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Metadata.m"; sourceTree = "<group>"; };
		9E116F9FEBD1565E954FB238 /* NineRowsProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NineRowsProtocol.h; sourceTree = "<group>"; };
		9E218A77B77ADF55C3D7B84D /* CupExtrasMinInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CupExtrasMinInfo.m; sourceTree = "<group>"; };
		9E736D9DE53F68F9F55541CA /* HisClickColor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HisClickColor.h; sourceTree = "<group>"; };
		9F1C43C6D349BB4B729BB317 /* UIImage+MemoryCacheCost.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+MemoryCacheCost.h"; sourceTree = "<group>"; };
		9F4A9EC1243CBA545D24DAA0 /* HowFaxConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HowFaxConfig.h; sourceTree = "<group>"; };
		9FED3F0C78DC186D0BE8F38C /* SequenceStreamKeepReadoutOff.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SequenceStreamKeepReadoutOff.m; sourceTree = "<group>"; };
		A14ABB1337ABF5E9A1F29FA8 /* ForegroundReconnection.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ForegroundReconnection.h; sourceTree = "<group>"; };
		A34165CFA02711A80C940B20 /* GatherEuropeanInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GatherEuropeanInfo.m; sourceTree = "<group>"; };
		A3EB7AC0D4EA7571F675EB9C /* MediaDerivedAssignMattingActionExpensive.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MediaDerivedAssignMattingActionExpensive.h; sourceTree = "<group>"; };
		A471F2BE9F74DBE32B184EBD /* EnergyPotassiumDelaySkinFeatCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EnergyPotassiumDelaySkinFeatCell.m; sourceTree = "<group>"; };
		A4E1CF25550863E506608349 /* TagTreeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TagTreeViewController.m; sourceTree = "<group>"; };
		A527FE57294D9CEE680833F0 /* CleanWaist.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CleanWaist.h; sourceTree = "<group>"; };
		A61DB1D2F96835B35656CFA4 /* XXGProtocolLabel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGProtocolLabel.m; sourceTree = "<group>"; };
		A6231BD5B58A5EF2B27701FF /* TakeBitPenMask.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TakeBitPenMask.h; sourceTree = "<group>"; };
		A676CCCC0E6E3F3E11B4FA67 /* MQTTCFSocketTransport.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCFSocketTransport.h; sourceTree = "<group>"; };
		A68A62359F3D4E83A5651627 /* UIImage+GIF.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+GIF.h"; sourceTree = "<group>"; };
		A73F5F354AC31D1858EACEB0 /* WrongArrayFootButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WrongArrayFootButton.m; sourceTree = "<group>"; };
		A754D7A92640B8BE0605B858 /* PurpleInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PurpleInfo.m; sourceTree = "<group>"; };
		A7A63754EC2180AEEE479CC9 /* SDDisplayLink.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDDisplayLink.h; sourceTree = "<group>"; };
		A7B0599DAF4434722E858125 /* KnowPictureAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = KnowPictureAction.m; sourceTree = "<group>"; };
		A7C2B8DFDC59D6E1D3FDCA70 /* UIImageView+HighlightedWebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+HighlightedWebCache.m"; sourceTree = "<group>"; };
		A8CD3A76A680BAD6E374399D /* SDImageCoderHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCoderHelper.m; sourceTree = "<group>"; };
		A93BCAD4FA5D18F1202ED71C /* NSString+UnitZipHair.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+UnitZipHair.m"; sourceTree = "<group>"; };
		A9543B3884FDA20F81B89C32 /* BusCostCanon.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BusCostCanon.h; sourceTree = "<group>"; };
		A9AEE4C0F59554E8D01DFB67 /* ThreeAskManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ThreeAskManager.m; sourceTree = "<group>"; };
		AA11661D4C4C9049B6207A7C /* BetweenMixList.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BetweenMixList.h; sourceTree = "<group>"; };
		AADB8F1EEF5A9B4744260358 /* SDWebImageDownloaderOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderOperation.m; sourceTree = "<group>"; };
		AB487D4A3E603B7AEAE71B5E /* CatAskEmailOldViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CatAskEmailOldViewController.h; sourceTree = "<group>"; };
		ACA7EC5A33D84AE089C1DDD4 /* SDImageAssetManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageAssetManager.h; sourceTree = "<group>"; };
		ACC8B386361431C1BA6E3EA0 /* UIImage+MultiFormat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+MultiFormat.h"; sourceTree = "<group>"; };
		ACEE51F7F05264D48CB07AA2 /* SDAnimatedImageView+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "SDAnimatedImageView+WebCache.m"; sourceTree = "<group>"; };
		AD508EEEBE61CD1777DEC1AB /* SDMemoryCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDMemoryCache.h; sourceTree = "<group>"; };
		ADACC17BB309AE6E13D21687 /* RedoneMode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RedoneMode.h; sourceTree = "<group>"; };
		AE55D08B4535CEAACC5EB21D /* SDAnimatedImageRep.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImageRep.h; sourceTree = "<group>"; };
		AEB95BA0CF43BEDA1ED41EDD /* SDAnimatedImagePlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImagePlayer.m; sourceTree = "<group>"; };
		B085BAC6A4872AF87EBF86DC /* SDMemoryCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDMemoryCache.m; sourceTree = "<group>"; };
		B2628A9C0E4219B314532E1B /* NSError+SawImageBin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSError+SawImageBin.m"; sourceTree = "<group>"; };
		B32E79FF20FE77AEE00B9D78 /* CopticLearn.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CopticLearn.m; sourceTree = "<group>"; };
		B3B93536481B04D5F87A45BD /* RestoresViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RestoresViewController.h; sourceTree = "<group>"; };
		B5023AE79D98A0B8530EFEB6 /* BigArmLocalHow+Birth.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "BigArmLocalHow+Birth.m"; sourceTree = "<group>"; };
		B59CF9C321E7DBD64B043A76 /* SDImageCacheConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCacheConfig.m; sourceTree = "<group>"; };
		B741709667CD141D00090C1E /* EngineManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EngineManager.h; sourceTree = "<group>"; };
		B7E050E3519A730828E569F4 /* MASViewAttribute.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASViewAttribute.h; sourceTree = "<group>"; };
		B7F48FCCF62F90A18511713D /* SDAnimatedImagePlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImagePlayer.h; sourceTree = "<group>"; };
		B86E7D3EA6F3D228A3123FB8 /* MinimizeEstonianIcyFinderRoot.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MinimizeEstonianIcyFinderRoot.m; sourceTree = "<group>"; };
		B8BD693F29D381073E6F23E2 /* SDWebImageOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageOperation.h; sourceTree = "<group>"; };
		B8D603C5BB9D672B462F0A7F /* SDWeakProxy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWeakProxy.m; sourceTree = "<group>"; };
		B9F6F9508933A30F87B5857A /* ArtistBuddy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ArtistBuddy.m; sourceTree = "<group>"; };
		BA6534B211143296191756DA /* SDWebImageTransitionInternal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageTransitionInternal.h; sourceTree = "<group>"; };
		BA9D155C2ABDB957F303FC25 /* BusWasPackViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BusWasPackViewController.m; sourceTree = "<group>"; };
		BADE2564CA13434B8C79429A /* SDImageCachesManagerOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCachesManagerOperation.m; sourceTree = "<group>"; };
		BB96E4CABE8D8D850BEF1C35 /* KerningKeyManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = KerningKeyManager.m; sourceTree = "<group>"; };
		BB9E3629691D75FA03BBC894 /* ZipOldRootView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZipOldRootView.h; sourceTree = "<group>"; };
		BBD2FC5FAAEA2C0E40D7F6E3 /* MQTTStrict.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTStrict.m; sourceTree = "<group>"; };
		BC9144FD73B7D89416E2222E /* SDWebImageCacheKeyFilter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageCacheKeyFilter.h; sourceTree = "<group>"; };
		BCB2168BC22F521ABFACED0E /* SDAnimatedImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImage.h; sourceTree = "<group>"; };
		BEF52778E71DCC559B5261CB /* SDDeviceHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDDeviceHelper.h; sourceTree = "<group>"; };
		BF5B543C1566FB4AB017E1B3 /* SDImageCachesManagerOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCachesManagerOperation.h; sourceTree = "<group>"; };
		BFD30A6410546CCB1881F481 /* EnergyPotassiumDelaySkinFeatCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EnergyPotassiumDelaySkinFeatCell.h; sourceTree = "<group>"; };
		C17EF579017D64121CE8A049 /* SDmetamacros.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDmetamacros.h; sourceTree = "<group>"; };
		C264AC54F27CC4A7556F4AAF /* SDImageLoadersManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageLoadersManager.h; sourceTree = "<group>"; };
		C2768940A7747EC091C1DB83 /* NSArray+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASAdditions.h"; sourceTree = "<group>"; };
		C2ACDF767EBE278A4805416A /* UIView+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCache.m"; sourceTree = "<group>"; };
		C3592ECE9C90F13CE404CBFA /* IndicatorViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IndicatorViewController.m; sourceTree = "<group>"; };
		C3CFEF907EF6877B478E0180 /* SDImageLoader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageLoader.h; sourceTree = "<group>"; };
		C43A5BC3C8A713DA5F87BA53 /* SDDiskCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDDiskCache.h; sourceTree = "<group>"; };
		C4EDB2F18D849289AD27E54A /* NSData+Iterate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSData+Iterate.m"; sourceTree = "<group>"; };
		C572C94E8C2C931CB979DC7F /* LacrosseModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LacrosseModel.m; sourceTree = "<group>"; };
		C58C4C7D266580271E35205E /* MQTTSession.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSession.m; sourceTree = "<group>"; };
		C6937FB1582C6C473B78F7CD /* LessCutWinCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LessCutWinCell.m; sourceTree = "<group>"; };
		C73BEC77E6D3C7F0084F7B61 /* LengthAndViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LengthAndViewController.m; sourceTree = "<group>"; };
		C7B999A5375678EF75A016B2 /* IndexRaiseConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IndexRaiseConfig.m; sourceTree = "<group>"; };
		C7F7B70CDBD65EFE1E15BB20 /* ThemePartial.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ThemePartial.m; sourceTree = "<group>"; };
		C80DE1DCE41F042000557DB3 /* MusicAuditNoteMajorSuddenTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MusicAuditNoteMajorSuddenTool.m; sourceTree = "<group>"; };
		C8E2CC3CC89E336DEAF2B08B /* MQTTMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTMessage.m; sourceTree = "<group>"; };
		C9190E8363BD5CEF8A5F31AD /* ClampingSawPrepareAirlineParserModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ClampingSawPrepareAirlineParserModel.m; sourceTree = "<group>"; };
		C948A885BBEBF31E4CF1D52A /* RestoresViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RestoresViewController.m; sourceTree = "<group>"; };
		C9AAFB624C71A12EC4E3D0D5 /* PaddleDublinManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PaddleDublinManager.h; sourceTree = "<group>"; };
		CA575C9C8C1EBA0A0B985C5A /* SobArcheryIll.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SobArcheryIll.h; sourceTree = "<group>"; };
		CA7F8742975033AD9DBBA226 /* FarNetwork.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FarNetwork.m; sourceTree = "<group>"; };
		CB0C9C3E5017D06FF1E052FA /* MusicAuditNoteMajorSuddenTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MusicAuditNoteMajorSuddenTool.h; sourceTree = "<group>"; };
		CB3E3456C2044CAE53CB0610 /* WayRealArmForm.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WayRealArmForm.h; sourceTree = "<group>"; };
		CC7812D6EC5BD63A9F752841 /* TagTreeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TagTreeViewController.h; sourceTree = "<group>"; };
		CD79F1E57B11B55FA5B355CC /* StrongHexViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = StrongHexViewController.h; sourceTree = "<group>"; };
		CDDBCB4C872C651BF423CD7F /* Blink.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Blink.m; sourceTree = "<group>"; };
		CE89AFCBE81511B5D026094D /* MASConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASConstraint.m; sourceTree = "<group>"; };
		CFF6D50BFF1E9103EF72F80A /* UIColor+BoxColor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIColor+BoxColor.m"; sourceTree = "<group>"; };
		D0326F6C344AE060C6A1DABC /* SDImageIOCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageIOCoder.h; sourceTree = "<group>"; };
		D0E2A356B74FD68E251F96BB /* MailNordic.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MailNordic.m; sourceTree = "<group>"; };
		D30578E73DC0EF53A9E63D1B /* SugarWetLessHostingSphere.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SugarWetLessHostingSphere.m; sourceTree = "<group>"; };
		D34C97C54083FE9AE25EBB8B /* ReceivedHexViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReceivedHexViewController.m; sourceTree = "<group>"; };
		D46BA853EA07A921E84D7DAE /* NSString+Suffix.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+Suffix.h"; sourceTree = "<group>"; };
		D74EB8FBED382602A17A3852 /* PenKeepMicroManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PenKeepMicroManager.m; sourceTree = "<group>"; };
		D768F642D053C7D24408CA2A /* MinimizeEstonianIcyFinderRoot.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MinimizeEstonianIcyFinderRoot.h; sourceTree = "<group>"; };
		D7987C97A11E7C45B87E972A /* WayRealArmForm.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WayRealArmForm.m; sourceTree = "<group>"; };
		D898F62FAD2D25E06B03FCA4 /* ExtentsPubViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ExtentsPubViewController.h; sourceTree = "<group>"; };
		D8C55DD0688C3A4895612A95 /* SDImageIOAnimatedCoderInternal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageIOAnimatedCoderInternal.h; sourceTree = "<group>"; };
		D9BE869DBD0386C79F8D9EFF /* SDWebImageCacheKeyFilter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCacheKeyFilter.m; sourceTree = "<group>"; };
		DA56EC021DA6AA281348BA36 /* NearBadSeekManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NearBadSeekManager.h; sourceTree = "<group>"; };
		DAA6F6D2E41F2BE793E42DBA /* BigArmLocalHow+HailPrice.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "BigArmLocalHow+HailPrice.m"; sourceTree = "<group>"; };
		DBA4ED5040F2FFA454B2B4F5 /* SDWebImageDownloaderOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderOperation.h; sourceTree = "<group>"; };
		DBD014FBA942283808AEA6C6 /* TagZipFailManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TagZipFailManager.m; sourceTree = "<group>"; };
		DBD4899AAC409DDDADBFA46A /* SDCallbackQueue.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDCallbackQueue.h; sourceTree = "<group>"; };
		DD883098B2A682A1DCFF7C2B /* NSLayoutConstraint+MASDebugAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSLayoutConstraint+MASDebugAdditions.m"; sourceTree = "<group>"; };
		DDBA3084B225672762B6F661 /* SDImageCodersManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCodersManager.h; sourceTree = "<group>"; };
		DDC5E9B28E2FA28D5EC2EDF6 /* LengthAndViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LengthAndViewController.h; sourceTree = "<group>"; };
		DDC7E2DD6050D8F2027648DA /* ElderNotationVolumeResourcesShortcut.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ElderNotationVolumeResourcesShortcut.h; sourceTree = "<group>"; };
		DDD3B922346B7A76717DB77E /* SDWebImageCompat.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCompat.m; sourceTree = "<group>"; };
		DE1C0DAE8EBBA3BA314660D6 /* CarriageAssignWindow.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CarriageAssignWindow.m; sourceTree = "<group>"; };
		DEB72DB9FE86DAA59469C226 /* NSObject+CivilAdoptMindfulCoachedCap.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSObject+CivilAdoptMindfulCoachedCap.h"; sourceTree = "<group>"; };
		DEE654D09B9BEF6D7C8E5CBC /* MQTTSessionLegacy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSessionLegacy.h; sourceTree = "<group>"; };
		DFE062B8DBC6CA2E73628418 /* HisClickColor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HisClickColor.m; sourceTree = "<group>"; };
		E0D633A295DC37B508774DD5 /* PaddleDublinManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PaddleDublinManager.m; sourceTree = "<group>"; };
		E0E0921C8E9AD957B3FD735A /* SDImageCacheDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCacheDefine.h; sourceTree = "<group>"; };
		E17CA76812EA650BAEF247D3 /* SDWebImageDownloaderRequestModifier.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderRequestModifier.h; sourceTree = "<group>"; };
		E1F00DDC3F256DA9643776B1 /* FlowFrontViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FlowFrontViewController.m; sourceTree = "<group>"; };
		E209285AF9F522CDC86D38D4 /* SDWebImageManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageManager.h; sourceTree = "<group>"; };
		E2C4908BE0D736F6357C22E5 /* SDInternalMacros.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDInternalMacros.m; sourceTree = "<group>"; };
		E2CAAE9FD85D622EBA57E8DA /* GatherEuropeanInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GatherEuropeanInfo.h; sourceTree = "<group>"; };
		E2F0702C35C9594D2D7263D1 /* RetMidManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RetMidManager.m; sourceTree = "<group>"; };
		E34341FEF98528D92FF37756 /* UIImage+ExtendedCacheData.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+ExtendedCacheData.h"; sourceTree = "<group>"; };
		E3B430FAF31EC7C7CE370632 /* MQTTInMemoryPersistence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTInMemoryPersistence.h; sourceTree = "<group>"; };
		E3BBDDA1CD354FAC2AE3F812 /* LoopPatchInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LoopPatchInfo.m; sourceTree = "<group>"; };
		E42397540E279108EFA5B23B /* BigArmLocalHow+HailPrice.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "BigArmLocalHow+HailPrice.h"; sourceTree = "<group>"; };
		E4B45E1E401918C1CF39DBC2 /* SDWebImageManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageManager.m; sourceTree = "<group>"; };
		E4F0B3B53970A67E0AD5336E /* SDWebImageIndicator.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageIndicator.m; sourceTree = "<group>"; };
		E58C371AE553B8255C219D3A /* UIImage+ForceDecode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+ForceDecode.h"; sourceTree = "<group>"; };
		E590A68C0B59FFDCE615652F /* SDWebImageCacheSerializer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCacheSerializer.m; sourceTree = "<group>"; };
		E594298407C00E5D531885D2 /* PopDrainImmediateOnePlug.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PopDrainImmediateOnePlug.h; sourceTree = "<group>"; };
		E69C86E560FF68DAC4F1DC0B /* SDImageHEICCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageHEICCoder.m; sourceTree = "<group>"; };
		E6EAF0106DFAC065FFD549F0 /* SDAsyncBlockOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAsyncBlockOperation.m; sourceTree = "<group>"; };
		E826D316805B60B38D3DBD8F /* SDImageGIFCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageGIFCoder.h; sourceTree = "<group>"; };
		E8AA3B992B7E7FC3CD94D392 /* MQTTLog.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTLog.m; sourceTree = "<group>"; };
		E8AF443A69C5256097CED6DE /* MQTTSSLSecurityPolicyEncoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicyEncoder.m; sourceTree = "<group>"; };
		E995B4A3B2131A38CAD7BAAE /* SDWebImageDownloaderResponseModifier.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderResponseModifier.h; sourceTree = "<group>"; };
		EA9A9B9EF0D31CD7C4D090D6 /* SDDeviceHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDDeviceHelper.m; sourceTree = "<group>"; };
		EAAF6A9B31C8DEB2ABF8BDE5 /* CatalogProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CatalogProtocol.h; sourceTree = "<group>"; };
		EAED47CEBA7BD2C0C40747E5 /* UIColor+SDHexString.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIColor+SDHexString.h"; sourceTree = "<group>"; };
		EB068262B21F43A08ADE8DF9 /* SDDiskCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDDiskCache.m; sourceTree = "<group>"; };
		ECA369802F705D5E9BF8F025 /* SexualUtilitiesScopeSkipKilobits.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SexualUtilitiesScopeSkipKilobits.h; sourceTree = "<group>"; };
		ED68E528EFDF6EFDCB79C404 /* FlowFrontViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FlowFrontViewController.h; sourceTree = "<group>"; };
		EE974FE4B73A0BEE10E90DB1 /* DogBufferViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DogBufferViewController.h; sourceTree = "<group>"; };
		EF750E87C876699A5EFC0B75 /* ContraceptiveSettee.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ContraceptiveSettee.h; sourceTree = "<group>"; };
		EF985E0EA75CBBA5414DF1EF /* CupExtrasMinInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CupExtrasMinInfo.h; sourceTree = "<group>"; };
		EFC8CA3C1FF33FD516337092 /* MQTTMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTMessage.h; sourceTree = "<group>"; };
		EFEF5A51A3D7518A7C505F1C /* ChestFingerAxesDraftStyleViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChestFingerAxesDraftStyleViewController.h; sourceTree = "<group>"; };
		F028E0878B8D444F489CE4AB /* GeneratorDraftCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GeneratorDraftCell.m; sourceTree = "<group>"; };
		F056268AD65604A3B20A1EC4 /* ReceivedHexViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReceivedHexViewController.h; sourceTree = "<group>"; };
		F16BFA940B1E06BDAE2F3025 /* NSArray+MASShorthandAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		F209CB11491DD7A1DC338067 /* EngineManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EngineManager.m; sourceTree = "<group>"; };
		F21AFDF68F47BF998D9C74F4 /* SDImageFramePool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageFramePool.h; sourceTree = "<group>"; };
		F2243889BEBA93835D8A34B8 /* UIView+WebCacheOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCacheOperation.m"; sourceTree = "<group>"; };
		F251D1B2F64E785A3B29CBEC /* SDAssociatedObject.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAssociatedObject.h; sourceTree = "<group>"; };
		F2CE7EED22BA0E3AC4374053 /* MASCompositeConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASCompositeConstraint.m; sourceTree = "<group>"; };
		F319AF17FB78302704349AA2 /* ScannedAll.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ScannedAll.m; sourceTree = "<group>"; };
		F38AFFBC39867224349C7AB6 /* UIView+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCache.h"; sourceTree = "<group>"; };
		F3E1CB383EBB7812B5FA0124 /* SDImageGraphics.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageGraphics.h; sourceTree = "<group>"; };
		F3F1EABDDA2FEB69CF24348E /* SDWebImageCacheSerializer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageCacheSerializer.h; sourceTree = "<group>"; };
		F4365DA0B4680D632F62BFC8 /* BusWasPackViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BusWasPackViewController.h; sourceTree = "<group>"; };
		F4B368F4E80A6E143B98D9CF /* NSURL+SheRopeHow.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSURL+SheRopeHow.m"; sourceTree = "<group>"; };
		F589A9DF83215E1E2DAEA8B4 /* NSURL+SheRopeHow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSURL+SheRopeHow.h"; sourceTree = "<group>"; };
		F6B3990A1685A8F6D4FA7DC8 /* TripleAir.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TripleAir.h; sourceTree = "<group>"; };
		F70C19DFB7CA7EB337436BE3 /* SDImageTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageTransformer.h; sourceTree = "<group>"; };
		F80CEF7C549E0BA3A4FE14BE /* MQTTDecoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTDecoder.m; sourceTree = "<group>"; };
		F81F63F978D0DBE295462377 /* SDImageFrame.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageFrame.m; sourceTree = "<group>"; };
		F83B1BBC07D4549FF3AF2133 /* ModalEggView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ModalEggView.h; sourceTree = "<group>"; };
		F8D5A691CDA0B494FF1FE1E6 /* EggNetCivilSon.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EggNetCivilSon.m; sourceTree = "<group>"; };
		F90C91674E76A961388311FB /* RetMidManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RetMidManager.h; sourceTree = "<group>"; };
		F9E011F379A0F3D3DF9DF46F /* MQTTCFSocketTransport.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCFSocketTransport.m; sourceTree = "<group>"; };
		FB4B85879FEF0C4729730C03 /* MQTTSessionManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSessionManager.m; sourceTree = "<group>"; };
		FCD6C65501221F346354383D /* CubeSindhiViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CubeSindhiViewController.h; sourceTree = "<group>"; };
		FD71A8C0CAA8D9DB5BBCDE5E /* NSString+Messaging.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+Messaging.m"; sourceTree = "<group>"; };
		FDB03C44DE7A2F1D950F74AF /* BetweenMixList.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BetweenMixList.m; sourceTree = "<group>"; };
		FDD2383A639B803FC776CE75 /* WeekVirtualBlueSucceededExpects.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WeekVirtualBlueSucceededExpects.m; sourceTree = "<group>"; };
		FE06714B9B4A16A712BE7693 /* PrivacyResultsViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PrivacyResultsViewController.m; sourceTree = "<group>"; };
		FE70D32999C06FEB2505A815 /* InferOverViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InferOverViewController.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		4B034BEAC2C3DD0661A6F88C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0276CE70DFAF2F1464214C58 /* PhotoTabPeakRenewTrack */ = {
			isa = PBXGroup;
			children = (
				86A581B44D2A981A502DCBE9 /* SugarWetLessHostingSphere.h */,
				D30578E73DC0EF53A9E63D1B /* SugarWetLessHostingSphere.m */,
			);
			path = PhotoTabPeakRenewTrack;
			sourceTree = "<group>";
		};
		0443A1E28DD3DCD455AD21FE /* FatGasp */ = {
			isa = PBXGroup;
			children = (
				94B0CD2FC801155AAF236D82 /* ThemePartial.h */,
				C7F7B70CDBD65EFE1E15BB20 /* ThemePartial.m */,
				9575A1E616BE5FF904BFF68D /* UserAlone.h */,
				3C138019B9AA4863FA13D5C5 /* UserAlone.m */,
			);
			path = FatGasp;
			sourceTree = "<group>";
		};
		08DA3F24C41147B91714AE81 /* SunSob */ = {
			isa = PBXGroup;
			children = (
				0443A1E28DD3DCD455AD21FE /* FatGasp */,
				D8480EB0FD8EDA03DF91D47C /* Unsaved */,
				6D96A7946FAD9E5CDB13CD70 /* ScannedAll.h */,
				F319AF17FB78302704349AA2 /* ScannedAll.m */,
				EAAF6A9B31C8DEB2ABF8BDE5 /* CatalogProtocol.h */,
			);
			path = SunSob;
			sourceTree = "<group>";
		};
		13BB9EFB22E94BDE0D77E379 /* GaspUse */ = {
			isa = PBXGroup;
			children = (
				1EFE49392D326785260B9F8B /* Canon */,
				6397CBAF86B4E0AA6176F5A2 /* ToolOut */,
				7C1AEEF99E1A16CBD794E8DC /* Noise */,
				9F4A9EC1243CBA545D24DAA0 /* HowFaxConfig.h */,
				81B6C8C71D4DA38BFD11367A /* HowFaxConfig.m */,
				0FB23742819ABAB05DC2C70C /* RadialSkip.h */,
				59F900FE83AA1D74D6D8476F /* RouteJustProtocol.h */,
			);
			path = GaspUse;
			sourceTree = "<group>";
		};
		16A484E42CDDE293DB0CAA80 /* LoudVisited */ = {
			isa = PBXGroup;
			children = (
				BB9E3629691D75FA03BBC894 /* ZipOldRootView.h */,
				3379081ADC588BE7EB2D4297 /* ZipOldRootView.m */,
				84563A3643FB7BD2E42335C3 /* GeneratorDraftCell.h */,
				F028E0878B8D444F489CE4AB /* GeneratorDraftCell.m */,
				7BA2E8AC31108FADD03B930C /* SpineCandidate.h */,
				0127E9F98C9D56F53BD2E28A /* SpineCandidate.m */,
				772A931E96479EC5D6C3CA27 /* GaspBusyStickyCell.h */,
				33F29C413EBE4AB4346B6FDA /* GaspBusyStickyCell.m */,
			);
			path = LoudVisited;
			sourceTree = "<group>";
		};
		1EFE49392D326785260B9F8B /* Canon */ = {
			isa = PBXGroup;
			children = (
				7B879A82D250A71C05BAF70D /* NSError+SawImageBin.h */,
				B2628A9C0E4219B314532E1B /* NSError+SawImageBin.m */,
			);
			path = Canon;
			sourceTree = "<group>";
		};
		25133E3F0E4345B27F929805 /* TotalHeadlineSmallEarPanel */ = {
			isa = PBXGroup;
			children = (
				2E393D337E7B52A6AC054341 /* WeekVirtualBlueSucceededExpects.h */,
				FDD2383A639B803FC776CE75 /* WeekVirtualBlueSucceededExpects.m */,
				A3EB7AC0D4EA7571F675EB9C /* MediaDerivedAssignMattingActionExpensive.h */,
				1A29A1C68E460F5AACDA238B /* MediaDerivedAssignMattingActionExpensive.m */,
				E594298407C00E5D531885D2 /* PopDrainImmediateOnePlug.h */,
				6F0CDF7392A84879664CE6E1 /* PopDrainImmediateOnePlug.m */,
				8D25C79DB18432D435F4A657 /* Blink.h */,
				CDDBCB4C872C651BF423CD7F /* Blink.m */,
				5D79B94C0892E2346306E11C /* InterExistWalk.h */,
				160249105CBDAAD53B63E94A /* InterExistWalk.m */,
				7847C6F4CEDBA3C97DC8D896 /* MolarAccept.h */,
				28ACE33FCDF7951D85998ED0 /* ThickViewController.h */,
				5F8E65D302ECD716B57DF54C /* ThickViewController.m */,
				6DF731BC235E210D11537877 /* OpenPurposeMostlySynthesisFlushed.h */,
			);
			path = TotalHeadlineSmallEarPanel;
			sourceTree = "<group>";
		};
		264839B0A2E28801417BE6B9 /* TheDrop */ = {
			isa = PBXGroup;
			children = (
				400DDDCC30F118D4F8648C00 /* IndexRaiseConfig.h */,
				C7B999A5375678EF75A016B2 /* IndexRaiseConfig.m */,
				42448B84EC2A11030926B166 /* BigArmLocalHow.h */,
				499B4FAC0C1CD927AF7B268C /* BigArmLocalHow.m */,
				4D94C65AF548CACE137BBCAA /* BigArmLocalHow+Birth.h */,
				B5023AE79D98A0B8530EFEB6 /* BigArmLocalHow+Birth.m */,
				E42397540E279108EFA5B23B /* BigArmLocalHow+HailPrice.h */,
				DAA6F6D2E41F2BE793E42DBA /* BigArmLocalHow+HailPrice.m */,
				929E4B1DD0D76C5BC1E80683 /* BigArmLocalHow+SubSub.h */,
				7ECB3AADBFEC385AF4C3C700 /* BigArmLocalHow+SubSub.m */,
				D768F642D053C7D24408CA2A /* MinimizeEstonianIcyFinderRoot.h */,
				B86E7D3EA6F3D228A3123FB8 /* MinimizeEstonianIcyFinderRoot.m */,
				31C4F7EB3105FC7EF034AFEC /* KnowPictureAction.h */,
				A7B0599DAF4434722E858125 /* KnowPictureAction.m */,
			);
			path = TheDrop;
			sourceTree = "<group>";
		};
		31E9B538D745CA53DF544EF0 /* StoodTenSum */ = {
			isa = PBXGroup;
			children = (
				1B21B6998F66B398B461453B /* DayQueryWho.h */,
				5DA13F14FB09778E2E4BD65E /* DayQueryWho.m */,
			);
			path = StoodTenSum;
			sourceTree = "<group>";
		};
		34E8D72FA2AD3AE5C5C45C2A /* Core */ = {
			isa = PBXGroup;
			children = (
				9B57FD5D321608FDCD7E4349 /* NSButton+WebCache.h */,
				2070FEC4A15C57B418A3DF72 /* NSButton+WebCache.m */,
				86FF3B96AACCAEA27979453C /* NSData+ImageContentType.h */,
				932BBBC584CE4B597DD49A80 /* NSData+ImageContentType.m */,
				3EAD82AC01914684615CCF39 /* NSImage+Compatibility.h */,
				412A79A5158A8AD51D63F245 /* NSImage+Compatibility.m */,
				BCB2168BC22F521ABFACED0E /* SDAnimatedImage.h */,
				196799D237B84F9168559E0B /* SDAnimatedImage.m */,
				B7F48FCCF62F90A18511713D /* SDAnimatedImagePlayer.h */,
				AEB95BA0CF43BEDA1ED41EDD /* SDAnimatedImagePlayer.m */,
				AE55D08B4535CEAACC5EB21D /* SDAnimatedImageRep.h */,
				59498A3A9E08DCED918C5AFE /* SDAnimatedImageRep.m */,
				9C720AD34169F1F100807201 /* SDAnimatedImageView.h */,
				3EA621424E770F0564BD7354 /* SDAnimatedImageView.m */,
				74CD8856F6935429278DDD61 /* SDAnimatedImageView+WebCache.h */,
				ACEE51F7F05264D48CB07AA2 /* SDAnimatedImageView+WebCache.m */,
				DBD4899AAC409DDDADBFA46A /* SDCallbackQueue.h */,
				48104E81543B9CF73023C843 /* SDCallbackQueue.m */,
				C43A5BC3C8A713DA5F87BA53 /* SDDiskCache.h */,
				EB068262B21F43A08ADE8DF9 /* SDDiskCache.m */,
				7B6D4313289F080A1D6BA5CB /* SDGraphicsImageRenderer.h */,
				71414A676A2CE6907B0E1006 /* SDGraphicsImageRenderer.m */,
				4CA4F84FAC72902545F200CB /* SDImageAPNGCoder.h */,
				4DB4EBD56BEE0FFDB2F82287 /* SDImageAPNGCoder.m */,
				45B5518DB03DA61750349A9F /* SDImageAWebPCoder.h */,
				67C196C41D0EAA674724F4FA /* SDImageAWebPCoder.m */,
				6F2855EC54E62C5351F356AE /* SDImageCache.h */,
				3C3315522EEFDF32B7F387D9 /* SDImageCache.m */,
				2DB39BD3E3F92AB10A999550 /* SDImageCacheConfig.h */,
				B59CF9C321E7DBD64B043A76 /* SDImageCacheConfig.m */,
				E0E0921C8E9AD957B3FD735A /* SDImageCacheDefine.h */,
				6E83A911230B8F32C607210F /* SDImageCacheDefine.m */,
				8C6F0CC5887AA99DC547892A /* SDImageCachesManager.h */,
				81BAC9267938647ACD737A47 /* SDImageCachesManager.m */,
				00621BBFB161DF7DD4DE4001 /* SDImageCoder.h */,
				2FEAE548863ADF0EAB283BFB /* SDImageCoder.m */,
				2D71931F272F906E34A0B0F3 /* SDImageCoderHelper.h */,
				A8CD3A76A680BAD6E374399D /* SDImageCoderHelper.m */,
				DDBA3084B225672762B6F661 /* SDImageCodersManager.h */,
				6841C289CEF7571E0BBD7114 /* SDImageCodersManager.m */,
				475F9FB69C8E18DD1F0FD83F /* SDImageFrame.h */,
				F81F63F978D0DBE295462377 /* SDImageFrame.m */,
				E826D316805B60B38D3DBD8F /* SDImageGIFCoder.h */,
				1B35DC6766CFFD43FA10BD1E /* SDImageGIFCoder.m */,
				F3E1CB383EBB7812B5FA0124 /* SDImageGraphics.h */,
				5CDDABC02C3E45FA28381C92 /* SDImageGraphics.m */,
				731151C5F7AA38EEDFFA268F /* SDImageHEICCoder.h */,
				E69C86E560FF68DAC4F1DC0B /* SDImageHEICCoder.m */,
				66E5E37C6C8688615A7BFE8B /* SDImageIOAnimatedCoder.h */,
				1774018FE519AA2033D3E61C /* SDImageIOAnimatedCoder.m */,
				D0326F6C344AE060C6A1DABC /* SDImageIOCoder.h */,
				1AE521836E9E6CDFAE1F9721 /* SDImageIOCoder.m */,
				C3CFEF907EF6877B478E0180 /* SDImageLoader.h */,
				847552278079473BFC0C1499 /* SDImageLoader.m */,
				C264AC54F27CC4A7556F4AAF /* SDImageLoadersManager.h */,
				54B75A5CE1F820C306CB4637 /* SDImageLoadersManager.m */,
				F70C19DFB7CA7EB337436BE3 /* SDImageTransformer.h */,
				3A14DDA1304C234D440BDA68 /* SDImageTransformer.m */,
				AD508EEEBE61CD1777DEC1AB /* SDMemoryCache.h */,
				B085BAC6A4872AF87EBF86DC /* SDMemoryCache.m */,
				BC9144FD73B7D89416E2222E /* SDWebImageCacheKeyFilter.h */,
				D9BE869DBD0386C79F8D9EFF /* SDWebImageCacheKeyFilter.m */,
				F3F1EABDDA2FEB69CF24348E /* SDWebImageCacheSerializer.h */,
				E590A68C0B59FFDCE615652F /* SDWebImageCacheSerializer.m */,
				81E4E8F9F85FB6E3C5A5BE6D /* SDWebImageCompat.h */,
				DDD3B922346B7A76717DB77E /* SDWebImageCompat.m */,
				02FC951E7B809861184BE0F7 /* SDWebImageDefine.h */,
				98D20514F6509CB79DD55B25 /* SDWebImageDefine.m */,
				383CD14119C8B839C164B4DF /* SDWebImageDownloader.h */,
				7E354D346A2D78ECE758777B /* SDWebImageDownloader.m */,
				4D544ED9992FBF94A6B6BC65 /* SDWebImageDownloaderConfig.h */,
				3036842952CA15E1ACC9BE0F /* SDWebImageDownloaderConfig.m */,
				367013C26C71CF2AEC6AC647 /* SDWebImageDownloaderDecryptor.h */,
				06B123B981D365F0DEE48507 /* SDWebImageDownloaderDecryptor.m */,
				DBA4ED5040F2FFA454B2B4F5 /* SDWebImageDownloaderOperation.h */,
				AADB8F1EEF5A9B4744260358 /* SDWebImageDownloaderOperation.m */,
				E17CA76812EA650BAEF247D3 /* SDWebImageDownloaderRequestModifier.h */,
				23D2FA67C72CB4150731927D /* SDWebImageDownloaderRequestModifier.m */,
				E995B4A3B2131A38CAD7BAAE /* SDWebImageDownloaderResponseModifier.h */,
				179E89BB941A0662FF517DCF /* SDWebImageDownloaderResponseModifier.m */,
				3EE249C712F7333862BB4F69 /* SDWebImageError.h */,
				9BF2C8428FDA3726F6EBF3B2 /* SDWebImageError.m */,
				6B6D8A1AAA121C3B054E9807 /* SDWebImageIndicator.h */,
				E4F0B3B53970A67E0AD5336E /* SDWebImageIndicator.m */,
				E209285AF9F522CDC86D38D4 /* SDWebImageManager.h */,
				E4B45E1E401918C1CF39DBC2 /* SDWebImageManager.m */,
				B8BD693F29D381073E6F23E2 /* SDWebImageOperation.h */,
				80B44C26D35DB15AD8374F45 /* SDWebImageOperation.m */,
				159D2ADDA891BA8243F51859 /* SDWebImageOptionsProcessor.h */,
				6600EDC07BA6AD1899FB46B1 /* SDWebImageOptionsProcessor.m */,
				761D610FB87D9B30AE4C024D /* SDWebImagePrefetcher.h */,
				33B11F56D26B0B397BEC0F39 /* SDWebImagePrefetcher.m */,
				2E21AE6E039273D15BABC7BC /* SDWebImageTransition.h */,
				25AFBEC497C3263C5E017B93 /* SDWebImageTransition.m */,
				82642752C6600F595408E5FB /* UIButton+WebCache.h */,
				75688B93B26FFC322BBDA0E2 /* UIButton+WebCache.m */,
				E34341FEF98528D92FF37756 /* UIImage+ExtendedCacheData.h */,
				75A3193784DF19F01C8B4F69 /* UIImage+ExtendedCacheData.m */,
				E58C371AE553B8255C219D3A /* UIImage+ForceDecode.h */,
				651C656400B2DF18ACF0C534 /* UIImage+ForceDecode.m */,
				A68A62359F3D4E83A5651627 /* UIImage+GIF.h */,
				7D0BDDD16B7F8818E17B3226 /* UIImage+GIF.m */,
				9F1C43C6D349BB4B729BB317 /* UIImage+MemoryCacheCost.h */,
				66F1385DC89464B792267D92 /* UIImage+MemoryCacheCost.m */,
				57B3C365FB51A9732AC9F5F4 /* UIImage+Metadata.h */,
				9DE8EA78A392BE515CF2120F /* UIImage+Metadata.m */,
				ACC8B386361431C1BA6E3EA0 /* UIImage+MultiFormat.h */,
				3C173E4F042047E44AFC22F5 /* UIImage+MultiFormat.m */,
				54908D1100DACD15725F9DA4 /* UIImage+Transform.h */,
				54D1CAA78E9ACB8334CDAB3C /* UIImage+Transform.m */,
				0554F3F5B975DB78730142EF /* UIImageView+HighlightedWebCache.h */,
				A7C2B8DFDC59D6E1D3FDCA70 /* UIImageView+HighlightedWebCache.m */,
				3F6C207190E40118EEAE135E /* UIImageView+WebCache.h */,
				2AE9C608D7CE57A0E4C5C3D0 /* UIImageView+WebCache.m */,
				F38AFFBC39867224349C7AB6 /* UIView+WebCache.h */,
				C2ACDF767EBE278A4805416A /* UIView+WebCache.m */,
				21849584A2C871E3EFAC04D1 /* UIView+WebCacheOperation.h */,
				F2243889BEBA93835D8A34B8 /* UIView+WebCacheOperation.m */,
				8DDCEDC3B1542BA795B09D05 /* UIView+WebCacheState.h */,
				930C4F3C61AF519C25B51039 /* UIView+WebCacheState.m */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		41CB89C4D8C30D899DD3D16F /* Private */ = {
			isa = PBXGroup;
			children = (
				2E49271E786CCE05FDA2F6B3 /* NSBezierPath+SDRoundedCorners.h */,
				7DFE249A76BC872DE8F416D6 /* NSBezierPath+SDRoundedCorners.m */,
				F251D1B2F64E785A3B29CBEC /* SDAssociatedObject.h */,
				2CBFBE12A65F7EE14682F19E /* SDAssociatedObject.m */,
				4AB37405C32C3AA579D3031F /* SDAsyncBlockOperation.h */,
				E6EAF0106DFAC065FFD549F0 /* SDAsyncBlockOperation.m */,
				BEF52778E71DCC559B5261CB /* SDDeviceHelper.h */,
				EA9A9B9EF0D31CD7C4D090D6 /* SDDeviceHelper.m */,
				A7A63754EC2180AEEE479CC9 /* SDDisplayLink.h */,
				93DF113FD033A4474797568F /* SDDisplayLink.m */,
				49FD04FA74FDEC032C6E836F /* SDFileAttributeHelper.h */,
				82F628295CBE929F2196D18E /* SDFileAttributeHelper.m */,
				ACA7EC5A33D84AE089C1DDD4 /* SDImageAssetManager.h */,
				5805A1130F27BB2DF90D36CE /* SDImageAssetManager.m */,
				BF5B543C1566FB4AB017E1B3 /* SDImageCachesManagerOperation.h */,
				BADE2564CA13434B8C79429A /* SDImageCachesManagerOperation.m */,
				F21AFDF68F47BF998D9C74F4 /* SDImageFramePool.h */,
				56D236F06BEC5053A5C59B20 /* SDImageFramePool.m */,
				D8C55DD0688C3A4895612A95 /* SDImageIOAnimatedCoderInternal.h */,
				923350D9AAFA2C59C996DCEE /* SDInternalMacros.h */,
				E2C4908BE0D736F6357C22E5 /* SDInternalMacros.m */,
				C17EF579017D64121CE8A049 /* SDmetamacros.h */,
				3E6C8E2EBE046BC2CD109E1B /* SDWeakProxy.h */,
				B8D603C5BB9D672B462F0A7F /* SDWeakProxy.m */,
				BA6534B211143296191756DA /* SDWebImageTransitionInternal.h */,
				EAED47CEBA7BD2C0C40747E5 /* UIColor+SDHexString.h */,
				4FDC1B4C41D78E899AAAFA74 /* UIColor+SDHexString.m */,
			);
			path = Private;
			sourceTree = "<group>";
		};
		4229FB09BFB08299EAE6EAA4 /* JouleDriveMind */ = {
			isa = PBXGroup;
			children = (
				726DC39742533499DDAB07E1 /* RingAgeDegradedEyeTextualAppearingViewController.h */,
				809AD5FEDE890BFB7BF2D80A /* RingAgeDegradedEyeTextualAppearingViewController.m */,
				39959CD8364853325A4BE187 /* MailNordic.h */,
				D0E2A356B74FD68E251F96BB /* MailNordic.m */,
				6568EEB95F605846FEEE647B /* WrongArrayFootButton.h */,
				A73F5F354AC31D1858EACEB0 /* WrongArrayFootButton.m */,
			);
			path = JouleDriveMind;
			sourceTree = "<group>";
		};
		44463927DC3261D17938D2DC /* MailHeap */ = {
			isa = PBXGroup;
			children = (
				08DA3F24C41147B91714AE81 /* SunSob */,
				264839B0A2E28801417BE6B9 /* TheDrop */,
				C4A22E7AFE0AB5158F9FDC8F /* TheEye */,
				75327E23699672DFF9CC0800 /* NetFive */,
				D5E147A08EF951E8F41F8382 /* Thermal */,
			);
			path = MailHeap;
			sourceTree = "<group>";
		};
		4558166BC2CF20BA89A1EB8B /* MQTTClient */ = {
			isa = PBXGroup;
			children = (
				A14ABB1337ABF5E9A1F29FA8 /* ForegroundReconnection.h */,
				42DC08131862E3594315B388 /* ForegroundReconnection.m */,
				81C783BC7BD08D0C6442D1BC /* GCDTimer.h */,
				134B34599D694BEC11B2E0A6 /* GCDTimer.m */,
				4145F5EA5BF53DA10CC05E01 /* MQTTCFSocketDecoder.h */,
				169D8E57763450155E3A31EE /* MQTTCFSocketDecoder.m */,
				2A2353528AFD9E75D98C173C /* MQTTCFSocketEncoder.h */,
				314EE65B8366914F72EB408D /* MQTTCFSocketEncoder.m */,
				A676CCCC0E6E3F3E11B4FA67 /* MQTTCFSocketTransport.h */,
				F9E011F379A0F3D3DF9DF46F /* MQTTCFSocketTransport.m */,
				20C4A65703F636E5B80E9CBD /* MQTTClient.h */,
				3746C2D6AD84CA345FDFA76D /* MQTTCoreDataPersistence.h */,
				4F91A5842E4617B666B80A95 /* MQTTCoreDataPersistence.m */,
				6321C18474D8003BF688551E /* MQTTDecoder.h */,
				F80CEF7C549E0BA3A4FE14BE /* MQTTDecoder.m */,
				E3B430FAF31EC7C7CE370632 /* MQTTInMemoryPersistence.h */,
				2C629B00B2A750E58ED6EADA /* MQTTInMemoryPersistence.m */,
				5302A6E4AB90AC37AEAD510B /* MQTTLog.h */,
				E8AA3B992B7E7FC3CD94D392 /* MQTTLog.m */,
				EFC8CA3C1FF33FD516337092 /* MQTTMessage.h */,
				C8E2CC3CC89E336DEAF2B08B /* MQTTMessage.m */,
				817820C617F296D5986FC529 /* MQTTPersistence.h */,
				33657B8D557B39D38C9EF172 /* MQTTProperties.h */,
				45EB850E609F3164CE779B84 /* MQTTProperties.m */,
				6721C73D1A3AA1DD914CFDF4 /* MQTTSession.h */,
				C58C4C7D266580271E35205E /* MQTTSession.m */,
				DEE654D09B9BEF6D7C8E5CBC /* MQTTSessionLegacy.h */,
				17DA1009F520757DC05809D8 /* MQTTSessionLegacy.m */,
				7DA0D38F59E05651F1AA1C9B /* MQTTSessionManager.h */,
				FB4B85879FEF0C4729730C03 /* MQTTSessionManager.m */,
				46D058B32A4429DEB671474C /* MQTTSessionSynchron.h */,
				6CD6782D48301BD79051D006 /* MQTTSessionSynchron.m */,
				94F99FB99E73A333979E048A /* MQTTSSLSecurityPolicy.h */,
				52EC8A9B2078BCF779B41247 /* MQTTSSLSecurityPolicy.m */,
				6C98EF2A0F469453C3AD21A3 /* MQTTSSLSecurityPolicyDecoder.h */,
				8653F2BF00BC655555B54CFF /* MQTTSSLSecurityPolicyDecoder.m */,
				110C338D0B1659A998F83FEA /* MQTTSSLSecurityPolicyEncoder.h */,
				E8AF443A69C5256097CED6DE /* MQTTSSLSecurityPolicyEncoder.m */,
				0141AC08E7CF5E8EBBCF8ACE /* MQTTSSLSecurityPolicyTransport.h */,
				3E504725704CFA2D196BBC53 /* MQTTSSLSecurityPolicyTransport.m */,
				3CA72CE7AA1B7C950E2FFB2F /* MQTTStrict.h */,
				BBD2FC5FAAEA2C0E40D7F6E3 /* MQTTStrict.m */,
				91045984D41569A0A164037D /* MQTTTransportProtocol.h */,
				350EA248EF98A46B64E1CF0F /* MQTTTransportProtocol.m */,
				525F793B64D68E3B7AAA91F1 /* ReconnectTimer.h */,
				5D0BAB29618AB638244BE2CC /* ReconnectTimer.m */,
			);
			path = MQTTClient;
			sourceTree = "<group>";
		};
		45EA2EFDF55D78B3DAD50010 /* ContraceptiveSettee */ = {
			isa = PBXGroup;
			children = (
				44463927DC3261D17938D2DC /* MailHeap */,
				902215DE4EEE09C97A445B5C /* AloneHis */,
				A977C43911FC17C2E994F011 /* UseRotor */,
			);
			path = ContraceptiveSettee;
			sourceTree = "<group>";
		};
		6397CBAF86B4E0AA6176F5A2 /* ToolOut */ = {
			isa = PBXGroup;
			children = (
				4F324AD8E1EF4AF8430F60DC /* TagZipFailManager.h */,
				DBD014FBA942283808AEA6C6 /* TagZipFailManager.m */,
				8FF0FB0A5A1BAF2687096649 /* DoneBaselineManager.h */,
				70FC991BA8BE1F998CE428B0 /* DoneBaselineManager.m */,
			);
			path = ToolOut;
			sourceTree = "<group>";
		};
		665033D40C16B589AC8A1886 /* RedAdverbSin */ = {
			isa = PBXGroup;
			children = (
				DDC7E2DD6050D8F2027648DA /* ElderNotationVolumeResourcesShortcut.h */,
				11B06AB5B208018FD9464C94 /* ElderNotationVolumeResourcesShortcut.m */,
				3558CDC32649CB91C24010E9 /* SuddenBufferModel.h */,
				7E6E26FD0F1E64D9F29F48B7 /* SuddenBufferModel.m */,
				91D4750739BF665376AC50AE /* LacrosseModel.h */,
				C572C94E8C2C931CB979DC7F /* LacrosseModel.m */,
			);
			path = RedAdverbSin;
			sourceTree = "<group>";
		};
		717196ECD7C5027895B95945 = {
			isa = PBXGroup;
			children = (
				45EA2EFDF55D78B3DAD50010 /* ContraceptiveSettee */,
				B9C9840FA69369FE19A4CFA8 /* Products */,
			);
			sourceTree = "<group>";
		};
		75327E23699672DFF9CC0800 /* NetFive */ = {
			isa = PBXGroup;
			children = (
				A527FE57294D9CEE680833F0 /* CleanWaist.h */,
				7F5680E8504EC9F757C74653 /* CleanWaist.m */,
				5CBB2E2E785F3667894332A6 /* BestArteryModel.h */,
				5096015B768B43630CA3425B /* BestArteryModel.m */,
				7BF9C7162E86BDC941C5241C /* FarNetwork.h */,
				CA7F8742975033AD9DBBA226 /* FarNetwork.m */,
				AA11661D4C4C9049B6207A7C /* BetweenMixList.h */,
				FDB03C44DE7A2F1D950F74AF /* BetweenMixList.m */,
			);
			path = NetFive;
			sourceTree = "<group>";
		};
		7667C80E83146C8ECD56265D /* Say */ = {
			isa = PBXGroup;
			children = (
				899BB818A7B640D4D1017322 /* SequenceStreamKeepReadoutOff.h */,
				9FED3F0C78DC186D0BE8F38C /* SequenceStreamKeepReadoutOff.m */,
				04D063564A0125F3538495D5 /* TrainingRows.h */,
				816DADB75E31F5B12ACE8B74 /* TrainingRows.m */,
				94509C1AC47F7CB40CEF3462 /* AssignTabBin.h */,
				8EFECEA7EC6E5085D6D2525D /* AssignTabBin.m */,
				89FEBCD3EA3184BE20B05755 /* LoopPatchInfo.h */,
				E3BBDDA1CD354FAC2AE3F812 /* LoopPatchInfo.m */,
				A6231BD5B58A5EF2B27701FF /* TakeBitPenMask.h */,
				2E036B0D30FF8BD1E58A89B6 /* TakeBitPenMask.m */,
				442FAD31038351AC260C575E /* RecordOrganizeDropCondensedPortraitsRelative.h */,
				7C18D01F1A35A702EE11A789 /* RecordOrganizeDropCondensedPortraitsRelative.m */,
				6F8860F2A23162C487276C4A /* CopticLearn.h */,
				B32E79FF20FE77AEE00B9D78 /* CopticLearn.m */,
			);
			path = Say;
			sourceTree = "<group>";
		};
		7C1AEEF99E1A16CBD794E8DC /* Noise */ = {
			isa = PBXGroup;
			children = (
				6895AE78A0240087EA25C44D /* ClampingSawPrepareAirlineParserModel.h */,
				C9190E8363BD5CEF8A5F31AD /* ClampingSawPrepareAirlineParserModel.m */,
			);
			path = Noise;
			sourceTree = "<group>";
		};
		804B885415132EF040010160 /* RawCase */ = {
			isa = PBXGroup;
			children = (
				1D0717780CF4D1196DA89833 /* EggNetCivilSon.h */,
				F8D5A691CDA0B494FF1FE1E6 /* EggNetCivilSon.m */,
				3AFC33D952AB5726ADAADBC4 /* AlienDelayItemBriefSelector.h */,
				43165076F5C9973426EA873A /* AlienDelayItemBriefSelector.m */,
			);
			path = RawCase;
			sourceTree = "<group>";
		};
		8492D07FCE4CF63DC841F577 /* SDWebImage */ = {
			isa = PBXGroup;
			children = (
				34E8D72FA2AD3AE5C5C45C2A /* Core */,
				41CB89C4D8C30D899DD3D16F /* Private */,
			);
			path = SDWebImage;
			sourceTree = "<group>";
		};
		863EF56752D69DC455CE8FBF /* QuotationFunkPintRunningVerify */ = {
			isa = PBXGroup;
			children = (
				EE974FE4B73A0BEE10E90DB1 /* DogBufferViewController.h */,
				23F0B729AEB3980253E8899A /* DogBufferViewController.m */,
				B3B93536481B04D5F87A45BD /* RestoresViewController.h */,
				C948A885BBEBF31E4CF1D52A /* RestoresViewController.m */,
				FCD6C65501221F346354383D /* CubeSindhiViewController.h */,
				1415F9C41BB01405DE76388A /* CubeSindhiViewController.m */,
			);
			path = QuotationFunkPintRunningVerify;
			sourceTree = "<group>";
		};
		8D0F23D5E4074330CD61D64C /* BasicLastWhile */ = {
			isa = PBXGroup;
			children = (
				FE70D32999C06FEB2505A815 /* InferOverViewController.h */,
				0534CB0D1428BF495B49B465 /* InferOverViewController.m */,
				70EA9F440BEB15CFD18C83CE /* DueTurnWasViewController.h */,
				258CDBDF9487ACAA5AA5EB68 /* DueTurnWasViewController.m */,
				81ED172EB1822E645853468C /* IndicatorViewController.h */,
				C3592ECE9C90F13CE404CBFA /* IndicatorViewController.m */,
				DDC5E9B28E2FA28D5EC2EDF6 /* LengthAndViewController.h */,
				C73BEC77E6D3C7F0084F7B61 /* LengthAndViewController.m */,
				EFEF5A51A3D7518A7C505F1C /* ChestFingerAxesDraftStyleViewController.h */,
				5B3F7C11FFE4BD604D48FA52 /* ChestFingerAxesDraftStyleViewController.m */,
				ED68E528EFDF6EFDCB79C404 /* FlowFrontViewController.h */,
				E1F00DDC3F256DA9643776B1 /* FlowFrontViewController.m */,
				CD79F1E57B11B55FA5B355CC /* StrongHexViewController.h */,
				09B6D21DC1A98571727692B1 /* StrongHexViewController.m */,
				270818EA3C73F848884F82B4 /* PasswordsMoveViewController.h */,
				8606D7354FFB895DCBBA6A54 /* PasswordsMoveViewController.m */,
				F056268AD65604A3B20A1EC4 /* ReceivedHexViewController.h */,
				D34C97C54083FE9AE25EBB8B /* ReceivedHexViewController.m */,
				5D12ECF04F8BEE1B431684DB /* CityOffsetsViewController.h */,
				8CD01B2E50E488A95EEB7219 /* CityOffsetsViewController.m */,
				D898F62FAD2D25E06B03FCA4 /* ExtentsPubViewController.h */,
				31E069C36D329AC879068356 /* ExtentsPubViewController.m */,
				247D03906ED1CD893A6B1560 /* PrivacyResultsViewController.h */,
				FE06714B9B4A16A712BE7693 /* PrivacyResultsViewController.m */,
				0DF3405F7FB55AF45A00259C /* MagnitudeLateViewController.h */,
				242A9BAA6C3275864A481C46 /* MagnitudeLateViewController.m */,
				F4365DA0B4680D632F62BFC8 /* BusWasPackViewController.h */,
				BA9D155C2ABDB957F303FC25 /* BusWasPackViewController.m */,
			);
			path = BasicLastWhile;
			sourceTree = "<group>";
		};
		902215DE4EEE09C97A445B5C /* AloneHis */ = {
			isa = PBXGroup;
			children = (
				0A8FF55E211B91FA5A3DC041 /* DoneSalt.h */,
				636D0E279FF1FE49884D9430 /* DoneSalt.m */,
				25133E3F0E4345B27F929805 /* TotalHeadlineSmallEarPanel */,
				31E9B538D745CA53DF544EF0 /* StoodTenSum */,
				4558166BC2CF20BA89A1EB8B /* MQTTClient */,
				13BB9EFB22E94BDE0D77E379 /* GaspUse */,
				665033D40C16B589AC8A1886 /* RedAdverbSin */,
				8492D07FCE4CF63DC841F577 /* SDWebImage */,
				B8B063E99260A29F08ED704D /* Masonry */,
				E56820B12BF79AA99F3D05E0 /* Criteria */,
				804B885415132EF040010160 /* RawCase */,
				0276CE70DFAF2F1464214C58 /* PhotoTabPeakRenewTrack */,
				5A5D7A7C31C8CEFE8BE90D05 /* PurpleInfo.h */,
				A754D7A92640B8BE0605B858 /* PurpleInfo.m */,
				CB0C9C3E5017D06FF1E052FA /* MusicAuditNoteMajorSuddenTool.h */,
				C80DE1DCE41F042000557DB3 /* MusicAuditNoteMajorSuddenTool.m */,
			);
			path = AloneHis;
			sourceTree = "<group>";
		};
		98CFB3420671FF3DFA9D80A1 /* Node */ = {
			isa = PBXGroup;
			children = (
				4229FB09BFB08299EAE6EAA4 /* JouleDriveMind */,
				BB6C65E650E50E460842BC97 /* HallEventual */,
				16A484E42CDDE293DB0CAA80 /* LoudVisited */,
				72E7F0949A7211CE3A19BBC5 /* ForAlertView.h */,
				31B4F71C4BBE5B314BDAEBC5 /* ForAlertView.m */,
				5DFAB877AB4CBB5E11E2DCEC /* XXGProtocolLabel.h */,
				A61DB1D2F96835B35656CFA4 /* XXGProtocolLabel.m */,
				8AA0D7DF97D1083AF23AED39 /* BusForDrawView.h */,
				83CCB1221EDEE4D4CFCD06B1 /* BusForDrawView.m */,
				BFD30A6410546CCB1881F481 /* EnergyPotassiumDelaySkinFeatCell.h */,
				A471F2BE9F74DBE32B184EBD /* EnergyPotassiumDelaySkinFeatCell.m */,
				6DC90BBD13D0F89BD36C9085 /* ReadTiedKinButton.h */,
				8B97A162ADC3D464593F9D64 /* ReadTiedKinButton.m */,
				58286D90DEBBFCA2500C6419 /* SayToast.h */,
				3EEE9B42069AE50949100F46 /* SayToast.m */,
				6DB1D47105FD1D92FD7DD337 /* LessCutWinCell.h */,
				C6937FB1582C6C473B78F7CD /* LessCutWinCell.m */,
				97C74D9C4178AC17341F1F1D /* RealEntryTextField.h */,
				3841294CDD50042EE2A283D5 /* RealEntryTextField.m */,
			);
			path = Node;
			sourceTree = "<group>";
		};
		A977C43911FC17C2E994F011 /* UseRotor */ = {
			isa = PBXGroup;
			children = (
				9758F41AC8E4E1810497B723 /* IcyPhase.h */,
				3254F188E39822392D519389 /* IcyPhase.m */,
				ECFAB5E0D8F350C2EE810368 /* Zoom */,
				98CFB3420671FF3DFA9D80A1 /* Node */,
				8D0F23D5E4074330CD61D64C /* BasicLastWhile */,
				863EF56752D69DC455CE8FBF /* QuotationFunkPintRunningVerify */,
			);
			path = UseRotor;
			sourceTree = "<group>";
		};
		B8B063E99260A29F08ED704D /* Masonry */ = {
			isa = PBXGroup;
			children = (
				8A5AFBE49A0CF05E0C1A3CE2 /* MASCompositeConstraint.h */,
				F2CE7EED22BA0E3AC4374053 /* MASCompositeConstraint.m */,
				7C8C702DC550916FC1C6ADB5 /* MASConstraint.h */,
				CE89AFCBE81511B5D026094D /* MASConstraint.m */,
				9C11DE961FCED40282C8C861 /* MASConstraint+Private.h */,
				61525998F4DB47604918A6BE /* MASConstraintMaker.h */,
				78E86B2621B8DD9D603E19A1 /* MASConstraintMaker.m */,
				7E2B1F89FCD9CB153AE11EAA /* MASLayoutConstraint.h */,
				26584EE61B7DEAE22FD6E72D /* MASLayoutConstraint.m */,
				6CD424F67F634ACF88BFDAE1 /* Masonry.h */,
				47A38AA95469857CE4958A0D /* MASUtilities.h */,
				B7E050E3519A730828E569F4 /* MASViewAttribute.h */,
				68574976DBC509D51E99A69D /* MASViewAttribute.m */,
				8ADFA1E902A670161248CB1B /* MASViewConstraint.h */,
				5044BB2CAFCB29CBCE50B19E /* MASViewConstraint.m */,
				C2768940A7747EC091C1DB83 /* NSArray+MASAdditions.h */,
				4A98710EE05F98E31F6527E2 /* NSArray+MASAdditions.m */,
				F16BFA940B1E06BDAE2F3025 /* NSArray+MASShorthandAdditions.h */,
				341425921191296A1AA0983A /* NSLayoutConstraint+MASDebugAdditions.h */,
				DD883098B2A682A1DCFF7C2B /* NSLayoutConstraint+MASDebugAdditions.m */,
				928005EED133E1D29AF6736E /* View+MASAdditions.h */,
				8D741318BA60B8C03E0A974E /* View+MASAdditions.m */,
				8261C641EE96E0DFDCADD3E5 /* View+MASShorthandAdditions.h */,
				2D38AE6B58A93A9692673DA6 /* ViewController+MASAdditions.h */,
				92347F9B30CCAECB4DFA2110 /* ViewController+MASAdditions.m */,
			);
			path = Masonry;
			sourceTree = "<group>";
		};
		B9C9840FA69369FE19A4CFA8 /* Products */ = {
			isa = PBXGroup;
			children = (
				755A27FE5FB1EB2DFB266430 /* ContraceptiveSettee.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BB6C65E650E50E460842BC97 /* HallEventual */ = {
			isa = PBXGroup;
			children = (
				F83B1BBC07D4549FF3AF2133 /* ModalEggView.h */,
				2B6BE6F4E10A1A41774395D5 /* ModalEggView.m */,
				059291F1056B97BC58DDCFF2 /* CarriageAssignWindow.h */,
				DE1C0DAE8EBBA3BA314660D6 /* CarriageAssignWindow.m */,
			);
			path = HallEventual;
			sourceTree = "<group>";
		};
		BF05A9B463B1BC99E64587EC /* Era */ = {
			isa = PBXGroup;
			children = (
				DA56EC021DA6AA281348BA36 /* NearBadSeekManager.h */,
				2485E56ADE3A76E5E6C00F0A /* NearBadSeekManager.m */,
				C9AAFB624C71A12EC4E3D0D5 /* PaddleDublinManager.h */,
				E0D633A295DC37B508774DD5 /* PaddleDublinManager.m */,
				4343F7E55E0F560FB3E10FE8 /* NextBoxMoreManager.h */,
				0C87A569427F1376BE30EA98 /* NextBoxMoreManager.m */,
				98D55386CE84EE45F3D7204D /* SolidManager.h */,
				3E99E89F81DFC32B7B8BB690 /* SolidManager.m */,
				85F47BA07771BAFAD1BBA789 /* DownloadsManager.h */,
				0C8CD09FB9C084B2EFA4BABB /* DownloadsManager.m */,
				48A79845B9C18C71111E2327 /* HelpRaceManager.h */,
				5B47589BE26ECFA2B535287B /* HelpRaceManager.m */,
				34754C226F2DE8975C966C73 /* SixTheAppleManager.h */,
				70E95F54D54295A5B26CE027 /* SixTheAppleManager.m */,
			);
			path = Era;
			sourceTree = "<group>";
		};
		C4A22E7AFE0AB5158F9FDC8F /* TheEye */ = {
			isa = PBXGroup;
			children = (
				7667C80E83146C8ECD56265D /* Say */,
				DB57A32FDA386096C372F656 /* Saw */,
			);
			path = TheEye;
			sourceTree = "<group>";
		};
		D5E147A08EF951E8F41F8382 /* Thermal */ = {
			isa = PBXGroup;
			children = (
				FE3958E083EE9A2457A66A29 /* Did */,
				BF05A9B463B1BC99E64587EC /* Era */,
				1E5317C31EF52513D816D05C /* ThreeAskManager.h */,
				A9AEE4C0F59554E8D01DFB67 /* ThreeAskManager.m */,
				F90C91674E76A961388311FB /* RetMidManager.h */,
				E2F0702C35C9594D2D7263D1 /* RetMidManager.m */,
				B741709667CD141D00090C1E /* EngineManager.h */,
				F209CB11491DD7A1DC338067 /* EngineManager.m */,
				1461289DC458EE67F3D4AD97 /* FatMailManager.h */,
				6625C376A089820D01770FB5 /* FatMailManager.m */,
			);
			path = Thermal;
			sourceTree = "<group>";
		};
		D8480EB0FD8EDA03DF91D47C /* Unsaved */ = {
			isa = PBXGroup;
			children = (
				EF750E87C876699A5EFC0B75 /* ContraceptiveSettee.h */,
				3EEA181AF0699ED0B7698F88 /* ContraceptiveSettee.m */,
				F6B3990A1685A8F6D4FA7DC8 /* TripleAir.h */,
				74F72085F6733326E01D6E95 /* TripleAir.m */,
			);
			path = Unsaved;
			sourceTree = "<group>";
		};
		DB57A32FDA386096C372F656 /* Saw */ = {
			isa = PBXGroup;
			children = (
				CA575C9C8C1EBA0A0B985C5A /* SobArcheryIll.h */,
				183729AF2C87DFA2274E5B81 /* SobArcheryIll.m */,
				2DBF81C919A57FD2987C9C61 /* KeysReverting.h */,
				82F6DAD0CC7530FB8A5C58E1 /* KeysReverting.m */,
				1A00A355FDD4EE29C47A53BA /* PubPrepModel.h */,
				08F6825CD5DB4DEE0C5D2DB0 /* PubPrepModel.m */,
				9E736D9DE53F68F9F55541CA /* HisClickColor.h */,
				DFE062B8DBC6CA2E73628418 /* HisClickColor.m */,
				A9543B3884FDA20F81B89C32 /* BusCostCanon.h */,
				23FC97B1FD0AFA0384864974 /* BusCostCanon.m */,
				94D7442691C8A7FE2E469E30 /* ChainPauseInfo.h */,
				8D05125D852BA736DD26DF70 /* ChainPauseInfo.m */,
				ECA369802F705D5E9BF8F025 /* SexualUtilitiesScopeSkipKilobits.h */,
				806EABE19229433563CDE529 /* SexualUtilitiesScopeSkipKilobits.m */,
				7ABFB1C75CB985AA7960C57A /* MillHeightWill.h */,
				629DF378D662AD1F874CDF80 /* MillHeightWill.m */,
				3B7775B5DF1CE8804C86293E /* JabberStepperContrastHomeReportPrefers.h */,
				99B0D2DEC5B9C36133443E50 /* JabberStepperContrastHomeReportPrefers.m */,
				2EB6A1C9E8C459DFD7DE23F7 /* BigSlabUniversalSpecifiedFoot.h */,
				328B76FD6F585CEFCA8BFE7B /* BigSlabUniversalSpecifiedFoot.m */,
				E2CAAE9FD85D622EBA57E8DA /* GatherEuropeanInfo.h */,
				A34165CFA02711A80C940B20 /* GatherEuropeanInfo.m */,
				EF985E0EA75CBBA5414DF1EF /* CupExtrasMinInfo.h */,
				9E218A77B77ADF55C3D7B84D /* CupExtrasMinInfo.m */,
				CB3E3456C2044CAE53CB0610 /* WayRealArmForm.h */,
				D7987C97A11E7C45B87E972A /* WayRealArmForm.m */,
				4B4CC29107DB4499CF5373F9 /* WristSkinInfo.h */,
				00FC3F0A69C6E78C03C55590 /* WristSkinInfo.m */,
			);
			path = Saw;
			sourceTree = "<group>";
		};
		E56820B12BF79AA99F3D05E0 /* Criteria */ = {
			isa = PBXGroup;
			children = (
				58C4624188D60CCD0049F701 /* NSObject+MixModel.h */,
				38231985783D1E114FFCE1DB /* NSObject+MixModel.m */,
				1E2EF030009ADAABDA3BF993 /* NSString+Messaging.h */,
				FD71A8C0CAA8D9DB5BBCDE5E /* NSString+Messaging.m */,
				28D768E71E80ADB40AD98EC7 /* UIColor+BoxColor.h */,
				CFF6D50BFF1E9103EF72F80A /* UIColor+BoxColor.m */,
				97B3A6217F9329522A760FCC /* UIViewController+DueViewController.h */,
				3AC79EA0C13C2D2D29C55D54 /* UIViewController+DueViewController.m */,
				08CD130956508CC4DFF94FB8 /* UIImage+CupImage.h */,
				7976D258750B0DD463607379 /* UIImage+CupImage.m */,
				70B46B12EF1073DB2C779F1A /* NSString+UnitZipHair.h */,
				A93BCAD4FA5D18F1202ED71C /* NSString+UnitZipHair.m */,
				DEB72DB9FE86DAA59469C226 /* NSObject+CivilAdoptMindfulCoachedCap.h */,
				672B59D2016BE6EE53A5F55F /* NSObject+CivilAdoptMindfulCoachedCap.m */,
				F589A9DF83215E1E2DAEA8B4 /* NSURL+SheRopeHow.h */,
				F4B368F4E80A6E143B98D9CF /* NSURL+SheRopeHow.m */,
				D46BA853EA07A921E84D7DAE /* NSString+Suffix.h */,
				4916228DDE41692E820D6820 /* NSString+Suffix.m */,
				950A6D715494534A53ABBA6C /* NSData+Iterate.h */,
				C4EDB2F18D849289AD27E54A /* NSData+Iterate.m */,
				71389E587E3462F4F8BE17C6 /* UIDevice+HueDevice.h */,
				015E712620E0D1C12D6F5A84 /* UIDevice+HueDevice.m */,
			);
			path = Criteria;
			sourceTree = "<group>";
		};
		ECFAB5E0D8F350C2EE810368 /* Zoom */ = {
			isa = PBXGroup;
			children = (
				AB487D4A3E603B7AEAE71B5E /* CatAskEmailOldViewController.h */,
				26145E138BB1A5854F92A086 /* CatAskEmailOldViewController.m */,
				CC7812D6EC5BD63A9F752841 /* TagTreeViewController.h */,
				A4E1CF25550863E506608349 /* TagTreeViewController.m */,
				1A2804FE28CA72D6AF4E51E2 /* UsabilityOnlyController.h */,
				48EF292271472B12C9BCFFF0 /* UsabilityOnlyController.m */,
				65839294B25D3C0FA7388184 /* HeartBankManager.h */,
				15F67B5866BF8956C37BCFDE /* HeartBankManager.m */,
				26566515590C64BFD79132F5 /* ArtistBuddy.h */,
				B9F6F9508933A30F87B5857A /* ArtistBuddy.m */,
				9E116F9FEBD1565E954FB238 /* NineRowsProtocol.h */,
				6A41FC3AA48D1FAE3FE73DA8 /* LambdaCyrillic.h */,
				2D08BB8548A2681C22B92395 /* LambdaCyrillic.m */,
				ADACC17BB309AE6E13D21687 /* RedoneMode.h */,
				08E367B4D4FC8CD8DE30E1FC /* RedoneMode.m */,
			);
			path = Zoom;
			sourceTree = "<group>";
		};
		FE3958E083EE9A2457A66A29 /* Did */ = {
			isa = PBXGroup;
			children = (
				39876BE41E2C937A31CB0F9E /* KerningKeyManager.h */,
				BB96E4CABE8D8D850BEF1C35 /* KerningKeyManager.m */,
				86784CCDE4309D6AABBD13E6 /* PenKeepMicroManager.h */,
				D74EB8FBED382602A17A3852 /* PenKeepMicroManager.m */,
			);
			path = Did;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		EDC46A0AE232681AD0E318A9 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				A5B9536F6E8BFB363689792C /* BestArteryModel.h in Headers */,
				3072F57C833C6EE043808FCD /* MillHeightWill.h in Headers */,
				0D45C0EEECC68DB793CF03F8 /* HelpRaceManager.h in Headers */,
				7C021C4830A5CB24A488876D /* RingAgeDegradedEyeTextualAppearingViewController.h in Headers */,
				A95DA5BF8F421F3ADAC76978 /* LambdaCyrillic.h in Headers */,
				F4A9B17F91034D74D1981AFF /* ChestFingerAxesDraftStyleViewController.h in Headers */,
				A6208B4F9B5B340417AD8962 /* PrivacyResultsViewController.h in Headers */,
				04967DCDB5FCFA8180DE6A2B /* NSString+Suffix.h in Headers */,
				5AEB778846846C1A6466981B /* EnergyPotassiumDelaySkinFeatCell.h in Headers */,
				71732846B4291D47F048D292 /* AssignTabBin.h in Headers */,
				DA6D89FD55E7C8146C85DE54 /* BusCostCanon.h in Headers */,
				5C6FD5735474C76F40C735E0 /* ChainPauseInfo.h in Headers */,
				EC7077A41E73EC64C1F585E8 /* IcyPhase.h in Headers */,
				16281785A53D5B3F4405A589 /* LessCutWinCell.h in Headers */,
				BCF3C564370C335A2F09EE56 /* ForAlertView.h in Headers */,
				7105382E2EB9D60FDA3A5F98 /* AlienDelayItemBriefSelector.h in Headers */,
				3A192FDF7ED0814268A4C38C /* UIViewController+DueViewController.h in Headers */,
				5BDD9B41A413ACA61272228B /* RetMidManager.h in Headers */,
				B1FE6E92757CA3EF5E0AA82E /* ReceivedHexViewController.h in Headers */,
				DAC6B1F96BAA88749F8E8712 /* SayToast.h in Headers */,
				8984EEB336EF47958B92038C /* NSString+Messaging.h in Headers */,
				3E4FA4164947423A41C1D160 /* NSObject+MixModel.h in Headers */,
				45251EE8729CC097ABD5440E /* PubPrepModel.h in Headers */,
				653D158CA657F0B89A71E18D /* LoopPatchInfo.h in Headers */,
				36D1F82789A358EB481C1BE5 /* BetweenMixList.h in Headers */,
				DE02B46473E1339E791EFCD8 /* WristSkinInfo.h in Headers */,
				DB188DB96049BB15CFB02A4F /* SugarWetLessHostingSphere.h in Headers */,
				6DDD65E85EF1CFB3C821D283 /* NSObject+CivilAdoptMindfulCoachedCap.h in Headers */,
				1387760BA0CDCB6A89965EC1 /* ContraceptiveSettee.h in Headers */,
				AADA9ADC7BC181046EE938E8 /* HisClickColor.h in Headers */,
				858AD44AED042FB2E0A90F6E /* UsabilityOnlyController.h in Headers */,
				AB1439D051BC38E74FA11A22 /* PurpleInfo.h in Headers */,
				AA45867C42FB6B7C6D465069 /* EngineManager.h in Headers */,
				AE805FED96AEBE48968B6509 /* ThreeAskManager.h in Headers */,
				F3052F7655FE0C655E203225 /* MusicAuditNoteMajorSuddenTool.h in Headers */,
				1BFF3EF8900CF595D9A5BAF1 /* EggNetCivilSon.h in Headers */,
				05E8429A018E83BC7BED3D9A /* RedoneMode.h in Headers */,
				4B4161B6CD40C49AC85B8C54 /* CityOffsetsViewController.h in Headers */,
				36131AA0170048F57DEF2B9F /* SexualUtilitiesScopeSkipKilobits.h in Headers */,
				D065FFD9917D33FE147F11F6 /* ScannedAll.h in Headers */,
				65F3E40758F3B35D5BA49A35 /* HowFaxConfig.h in Headers */,
				2715DC00B6FC22662A218401 /* SolidManager.h in Headers */,
				76AD7A49641CEC6E9ECC50C1 /* TagZipFailManager.h in Headers */,
				961FE13FEF25DEDB6B926EB5 /* RadialSkip.h in Headers */,
				7639E0C08EE9852F224BC2B3 /* DoneBaselineManager.h in Headers */,
				B6AB86E9EC14A663C5CC3685 /* ClampingSawPrepareAirlineParserModel.h in Headers */,
				4E4A8BB8DA31E0ABD98EB3F0 /* RouteJustProtocol.h in Headers */,
				7246194C86C4A0204CC4791E /* NSError+SawImageBin.h in Headers */,
				E11C6E2C01E057214BCDC4B1 /* BigArmLocalHow.h in Headers */,
				F1AFAA4BD02C63CDE09B95AF /* InferOverViewController.h in Headers */,
				D7D60C03D02CAC6CFE190396 /* HeartBankManager.h in Headers */,
				AF0E64CCA09B0CB67DA07FDF /* UIColor+BoxColor.h in Headers */,
				AE22C71869F2564230A6B800 /* MinimizeEstonianIcyFinderRoot.h in Headers */,
				A58EBF6D32EBDAB59654E9FF /* StrongHexViewController.h in Headers */,
				E4C4C4B956724114990A31E7 /* BigArmLocalHow+HailPrice.h in Headers */,
				9DC2C3063F050CF831395888 /* KeysReverting.h in Headers */,
				128A95DA849C31EED64D6C53 /* SobArcheryIll.h in Headers */,
				42E977FA73147361E86DC940 /* KnowPictureAction.h in Headers */,
				2BC7EAE13C819210BCFE5174 /* IndexRaiseConfig.h in Headers */,
				695AFB5D3DCCDB94ED6E5410 /* BusForDrawView.h in Headers */,
				BB80994213BF0370DE27CB03 /* UIColor+SDHexString.h in Headers */,
				0686AF8760E850D65410450A /* DoneSalt.h in Headers */,
				79A674E63540E4C975D86809 /* SDImageIOCoder.h in Headers */,
				D1C5536D386649E45BC25B45 /* SequenceStreamKeepReadoutOff.h in Headers */,
				F7AE4DA6DDA06E71D9015B4A /* SDFileAttributeHelper.h in Headers */,
				8213F0BDF09CEEFA6364ECAD /* SDWebImagePrefetcher.h in Headers */,
				434C62319BF48E58B7BB5F55 /* SDAnimatedImageView.h in Headers */,
				C91C50EBB55AF7F261B85C08 /* NSButton+WebCache.h in Headers */,
				3A26584699C07D74B6A86F2E /* DogBufferViewController.h in Headers */,
				DD956BA5BA084A4CCCE804C2 /* SDImageHEICCoder.h in Headers */,
				6F7A167DA4003C7FAEE4EDAD /* ReadTiedKinButton.h in Headers */,
				B7BB90793EBAD58B07B8C1D8 /* RestoresViewController.h in Headers */,
				862E47EA20AC82A08702795D /* SDImageIOAnimatedCoder.h in Headers */,
				6A408DC2EF1D97B18520A4AF /* SDImageCoderHelper.h in Headers */,
				6481634661CB9D7F9853C36D /* SDAnimatedImagePlayer.h in Headers */,
				01B3096280F87E19B06E63BE /* CupExtrasMinInfo.h in Headers */,
				7528789E4097E0715288D339 /* UIView+WebCache.h in Headers */,
				A196AB9278707C0E88E8CB04 /* NineRowsProtocol.h in Headers */,
				E9E204FA7DFFFA0BE34A3FD9 /* XXGProtocolLabel.h in Headers */,
				B0AFDC08EDD53BC2B756DD53 /* DayQueryWho.h in Headers */,
				EACD06712D7483758383BA87 /* SDWebImageOptionsProcessor.h in Headers */,
				F0925211D68FFE22363CCBD7 /* SDWebImageTransitionInternal.h in Headers */,
				89C421653987F204962405DD /* SDWeakProxy.h in Headers */,
				74B621767D802F67F0F02786 /* FatMailManager.h in Headers */,
				71D47FC9E48A836F9D7E864B /* SDWebImageCompat.h in Headers */,
				F82406856508EEE716825395 /* SDCallbackQueue.h in Headers */,
				08851532EE8483695A6F6CAF /* SDmetamacros.h in Headers */,
				3F9A31B407EDB61408922BC1 /* UIImage+Transform.h in Headers */,
				BCE29C1222B4A483783DAB89 /* UIImage+MultiFormat.h in Headers */,
				692D5DE71A2A42D1EFAC0F02 /* SDImageAssetManager.h in Headers */,
				02575943CF294F39DE026999 /* SDImageCachesManagerOperation.h in Headers */,
				89F031BC56D068799FB956D9 /* GatherEuropeanInfo.h in Headers */,
				F33943BC55150D8FCEC93DD5 /* UIImageView+HighlightedWebCache.h in Headers */,
				225889978192A979787F20B8 /* SDWebImageDownloaderOperation.h in Headers */,
				46D991C0615D6D37C64B16DE /* SDWebImageDownloaderDecryptor.h in Headers */,
				7344901D81949450A2B7D21B /* IndicatorViewController.h in Headers */,
				1F128EA2BE819C0511A5E923 /* SDImageAWebPCoder.h in Headers */,
				DF86C5F97626C619740A0435 /* SDInternalMacros.h in Headers */,
				91A981E2AC179DAAD7F2CD85 /* SDAnimatedImageView+WebCache.h in Headers */,
				2EAB2A47EF79C5FECF6FB901 /* SDImageCoder.h in Headers */,
				59C14ED0CEF6A041F845D803 /* SDWebImageCacheSerializer.h in Headers */,
				66D32973E2C8C816193077D7 /* SDImageLoadersManager.h in Headers */,
				FD52A0B98D560F756928819E /* CubeSindhiViewController.h in Headers */,
				5CCF4A4070685C7B624AC32B /* DownloadsManager.h in Headers */,
				17E35C590D791D3C668FE74D /* GaspBusyStickyCell.h in Headers */,
				64CDDA377B4DF1B5E0BA0222 /* SDDisplayLink.h in Headers */,
				0A9D14EC3A82E7CD420E913A /* SixTheAppleManager.h in Headers */,
				8163E7DFF431A3310BD14C77 /* BusWasPackViewController.h in Headers */,
				5A485FA86695D5DDF3233E37 /* SDWebImageIndicator.h in Headers */,
				756711883735FD8B26ECE99E /* SDWebImageDownloader.h in Headers */,
				56F0E9962935722D1B3A3FC3 /* ExtentsPubViewController.h in Headers */,
				0FF0C87B04C846366F6ED90E /* SDAnimatedImageRep.h in Headers */,
				5DD1D4542827699756A74F2B /* SDImageCacheConfig.h in Headers */,
				130B5D1A05AD11BE507ECE6F /* SDImageFramePool.h in Headers */,
				177851C12860ABEED61AC7E3 /* MediaDerivedAssignMattingActionExpensive.h in Headers */,
				6E7E0C2761236B7A8D6C754C /* InterExistWalk.h in Headers */,
				840346603F4DF19D8981F2AB /* ThickViewController.h in Headers */,
				45DD9E5537E316794D2DB8D2 /* MolarAccept.h in Headers */,
				BFBB9D929B336FD0DFB7D3C7 /* WeekVirtualBlueSucceededExpects.h in Headers */,
				D36267DA9D6591C8EEC2E03F /* Blink.h in Headers */,
				7C200B1B801605A0F48F9698 /* PopDrainImmediateOnePlug.h in Headers */,
				60E3A9E714404F4FE1D080FA /* OpenPurposeMostlySynthesisFlushed.h in Headers */,
				C5531B2E8CCD799EB3672D8E /* SDImageGraphics.h in Headers */,
				0ECB538EAAC84C172D1E105F /* NSData+Iterate.h in Headers */,
				748EDB92280AFA4F138A91CB /* ElderNotationVolumeResourcesShortcut.h in Headers */,
				F136EA55CB473ACFBC5034D6 /* UIImage+GIF.h in Headers */,
				5F2275759FDBA5E59F1FEEB3 /* TrainingRows.h in Headers */,
				675C570581ACB1CCC127EFF7 /* SDAsyncBlockOperation.h in Headers */,
				14364B2276ED84B4753A6A63 /* SDImageCacheDefine.h in Headers */,
				7ACC2B63D893E1A3A03374E4 /* SDWebImageDownloaderResponseModifier.h in Headers */,
				AC718505D224804E063FE5D8 /* UIView+WebCacheOperation.h in Headers */,
				95C18D53B51B0459DBDEA772 /* SDWebImageOperation.h in Headers */,
				79D777721438EDBD0B738CCA /* LacrosseModel.h in Headers */,
				3AF4946E10EC7EC738F1EEA2 /* DueTurnWasViewController.h in Headers */,
				1C6A3F505C39AB57BE6E095D /* SDGraphicsImageRenderer.h in Headers */,
				516D4A0E635B00B475CBFFD2 /* BigArmLocalHow+Birth.h in Headers */,
				5459FFA7F90497D800035E0A /* BigSlabUniversalSpecifiedFoot.h in Headers */,
				6D63A010E05743D43C278427 /* NextBoxMoreManager.h in Headers */,
				BB522607EC4D60B6103B9784 /* WrongArrayFootButton.h in Headers */,
				8B5114EE94978217FE722C0E /* SDImageLoader.h in Headers */,
				9FAF7BC7D5C2A24D8F289749 /* UIImage+Metadata.h in Headers */,
				1FEE23A6165FBE7D991FDC2C /* MagnitudeLateViewController.h in Headers */,
				405268767932F928F24EA9D1 /* SuddenBufferModel.h in Headers */,
				F09B1C3D770739925CDC83A6 /* SDImageGIFCoder.h in Headers */,
				6857904C3EABABB7D63C5545 /* UIImage+ForceDecode.h in Headers */,
				2AE322B205826DC44FACAFE3 /* GeneratorDraftCell.h in Headers */,
				1921DE98438A7AC7A454B1F2 /* SpineCandidate.h in Headers */,
				5F8C04D97FD3C07CD1EF1290 /* SDImageFrame.h in Headers */,
				F6DA710652DD4206FE8231AA /* UIImage+CupImage.h in Headers */,
				9C348776BFBCBCB80EAF0F97 /* MQTTCoreDataPersistence.h in Headers */,
				915E3C7CD19933F18731CE8B /* MQTTSessionSynchron.h in Headers */,
				A888ED49AEB44E9EF775CE64 /* MQTTSSLSecurityPolicy.h in Headers */,
				01AFB799A897594EBB87DD26 /* MQTTPersistence.h in Headers */,
				99CAC814FB71D3EEC28AA09C /* ReconnectTimer.h in Headers */,
				BF3DAC20C391364885914336 /* MQTTSSLSecurityPolicyEncoder.h in Headers */,
				6B83B7355F4833D685CAB18F /* MQTTSSLSecurityPolicyTransport.h in Headers */,
				CA25748D6039972A1D23C0BA /* MQTTProperties.h in Headers */,
				8FCEF5F0A648F45E06701DC0 /* NSURL+SheRopeHow.h in Headers */,
				E04B96F26133E035A78BC180 /* MQTTStrict.h in Headers */,
				947F15D08510510836EC18A1 /* MQTTLog.h in Headers */,
				5D1B2B9D27D4530DBC6C30B2 /* ForegroundReconnection.h in Headers */,
				F256BAD0A060ACF48AB6DCAC /* MQTTMessage.h in Headers */,
				647911AEF51C817A2F1B6C85 /* MQTTInMemoryPersistence.h in Headers */,
				EB71308596481376D938CF78 /* MQTTDecoder.h in Headers */,
				676AB2D1061BD6AFFFC2E54E /* PaddleDublinManager.h in Headers */,
				DBA004A6D1CF36F68E4A4A8D /* CarriageAssignWindow.h in Headers */,
				A64C2073A52856B6E83F2EB2 /* GCDTimer.h in Headers */,
				08EA6D0C3FBA50C87760699E /* MQTTCFSocketEncoder.h in Headers */,
				EFB06E235DC7B2C1DE685770 /* MQTTCFSocketTransport.h in Headers */,
				3D18AD1CDCDB94B778D888F8 /* UIDevice+HueDevice.h in Headers */,
				081E3AA6D5CFF327E3A0B5EF /* MQTTTransportProtocol.h in Headers */,
				7501D37FF8734B5FEBC73F61 /* MQTTClient.h in Headers */,
				C79A5D66F539FE33CC7AC0D9 /* MQTTSessionLegacy.h in Headers */,
				7180AEA8963D0A1B86D98445 /* MQTTSession.h in Headers */,
				10B2BC625C14333C0C5A545A /* MQTTSSLSecurityPolicyDecoder.h in Headers */,
				988334267E1EAADCFF2316C5 /* MQTTCFSocketDecoder.h in Headers */,
				A1EE83134DF53A24C6A77C3E /* MQTTSessionManager.h in Headers */,
				EC474AAA14838502187444FD /* ArtistBuddy.h in Headers */,
				BBDB0889894963AE6E3C1B7F /* NSImage+Compatibility.h in Headers */,
				03068D1E1F8552767A55AE5E /* SDAnimatedImage.h in Headers */,
				5AB98B563545238D11961579 /* SDWebImageManager.h in Headers */,
				8D1C37B803D0538279E09E34 /* RealEntryTextField.h in Headers */,
				755EAA381F65EA5A7F30DFD4 /* BigArmLocalHow+SubSub.h in Headers */,
				38D4F00D6349A3F15CB46397 /* NSData+ImageContentType.h in Headers */,
				F07D0E87A6E65BC9AE6D1FA1 /* SDWebImageDefine.h in Headers */,
				1A53D5E60461B2002393A9FF /* UIView+WebCacheState.h in Headers */,
				5C18C86FDFB377BB0ED89460 /* SDImageTransformer.h in Headers */,
				458F2581E7E6FD28692B09DD /* SDDiskCache.h in Headers */,
				A0CA2122157EA5D73046064D /* FlowFrontViewController.h in Headers */,
				E02A8BC708EDBD85FD484E06 /* SDAssociatedObject.h in Headers */,
				00E01C08779E0413B7759AB0 /* SDImageIOAnimatedCoderInternal.h in Headers */,
				CA773CDD8F3F202E37E9354E /* CatalogProtocol.h in Headers */,
				065EABB515124043A4F6765B /* NSString+UnitZipHair.h in Headers */,
				84072B761D6E3DA51E5B8BCE /* SDDeviceHelper.h in Headers */,
				11FD0106E03880E90FB53226 /* SDImageCodersManager.h in Headers */,
				0510F440BA19B4C25B964389 /* SDWebImageDownloaderConfig.h in Headers */,
				E016C5C5E7A430DB9E567746 /* NearBadSeekManager.h in Headers */,
				BB45BB126D796D06D4D53163 /* CopticLearn.h in Headers */,
				F995F9007D0C488BE6F1359B /* UIImage+ExtendedCacheData.h in Headers */,
				9B406575CD044D7565FE9E2F /* UIImageView+WebCache.h in Headers */,
				3A9D7B3E1827944C1A4C8374 /* SDWebImageError.h in Headers */,
				0842E10B21ACA89227A7F3CF /* SDImageAPNGCoder.h in Headers */,
				9AAC46D9BE8C2F2967512168 /* SDWebImageCacheKeyFilter.h in Headers */,
				CA30EA8990D01D7159871E6A /* LengthAndViewController.h in Headers */,
				91B498681CA08838A4CAF69B /* ZipOldRootView.h in Headers */,
				651342C584ED99F9DADC52E9 /* UIImage+MemoryCacheCost.h in Headers */,
				61C877BEB003E1451C86909A /* SDWebImageDownloaderRequestModifier.h in Headers */,
				274C02EBB86557F282B342A0 /* SDImageCache.h in Headers */,
				26C61486C5BEE00DDC4E493E /* SDMemoryCache.h in Headers */,
				652495BE4CB092D976161633 /* NSBezierPath+SDRoundedCorners.h in Headers */,
				2323D8613F7DD2BA99C1FB4B /* UIButton+WebCache.h in Headers */,
				08AC08D8251404BF21F3622C /* JabberStepperContrastHomeReportPrefers.h in Headers */,
				AAFEED424BD99D5BBDBA09E6 /* SDWebImageTransition.h in Headers */,
				44483CCB45F81C59AED5B861 /* SDImageCachesManager.h in Headers */,
				1757427AE3733C89CD6F923C /* CleanWaist.h in Headers */,
				4CC87FCDCCC915CEEAE5548C /* FarNetwork.h in Headers */,
				5908D45DC5A199224B178564 /* CatAskEmailOldViewController.h in Headers */,
				205E9490ABA95F394EC8ECA7 /* ViewController+MASAdditions.h in Headers */,
				3FF8330F3EAAC7C6508A6E51 /* MASCompositeConstraint.h in Headers */,
				EB3454DC3F6903F183028674 /* View+MASShorthandAdditions.h in Headers */,
				796BB1D2E881BE9BC0EF8916 /* NSArray+MASShorthandAdditions.h in Headers */,
				C155264C18CDD48FD4D468F7 /* NSArray+MASAdditions.h in Headers */,
				DC776B3857FA24A0B07532ED /* View+MASAdditions.h in Headers */,
				5D6040EC893F323A59D50A82 /* MASConstraint.h in Headers */,
				84A200564BCE3AC73DDE9C0F /* MASViewConstraint.h in Headers */,
				D5468FB99734587F7DA52729 /* MASConstraintMaker.h in Headers */,
				A406C9D58F9E655B8AE21EC7 /* MailNordic.h in Headers */,
				D268BD3B8740513E2CC8E27B /* MASUtilities.h in Headers */,
				4DBCDC026CA8A43BB282DB98 /* MASLayoutConstraint.h in Headers */,
				1498944863162737802E62C5 /* MASViewAttribute.h in Headers */,
				F3493914CF4FC05EABA255ED /* NSLayoutConstraint+MASDebugAdditions.h in Headers */,
				0F09B1CBBBF9D6243935AF72 /* MASConstraint+Private.h in Headers */,
				28C346CA8B2C8ADFAE3718DE /* RecordOrganizeDropCondensedPortraitsRelative.h in Headers */,
				E88E2195D5CAF0EE57399D88 /* ModalEggView.h in Headers */,
				7499B68593CE6869489C9C71 /* Masonry.h in Headers */,
				38062E36423A45082B439F04 /* TripleAir.h in Headers */,
				12FEE33B5610B6C41F788D79 /* PasswordsMoveViewController.h in Headers */,
				86AE3BE2D5C9420F9530BECC /* WayRealArmForm.h in Headers */,
				033CB17C46293FE82D586DB9 /* TagTreeViewController.h in Headers */,
				CDDF7A3129973893226F1A0C /* TakeBitPenMask.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		818E6EC504F6BF8014F92476 /* ContraceptiveSettee */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AE2F2C0A73E0B48CF77A2ABD /* Build configuration list for PBXNativeTarget "ContraceptiveSettee" */;
			buildPhases = (
				EDC46A0AE232681AD0E318A9 /* Headers */,
				FEA5F1941EF0CA7880051522 /* Sources */,
				4B034BEAC2C3DD0661A6F88C /* Frameworks */,
				7D8D31D375C9ED38A7BF0740 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ContraceptiveSettee;
			packageProductDependencies = (
			);
			productName = ContraceptiveSettee;
			productReference = 755A27FE5FB1EB2DFB266430 /* ContraceptiveSettee.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		49AA5AF0181205E50724A833 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					C188893AFE4FE9CFA4559635 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 089AA46331F93CC6B0AFE0B2 /* Build configuration list for PBXProject "ContraceptiveSettee" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 717196ECD7C5027895B95945;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = B9C9840FA69369FE19A4CFA8 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				818E6EC504F6BF8014F92476 /* ContraceptiveSettee */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7D8D31D375C9ED38A7BF0740 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		FEA5F1941EF0CA7880051522 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0F395521FC5D91B8FA572C35 /* KeysReverting.m in Sources */,
				F0AA88F835CF465503AD8447 /* ScannedAll.m in Sources */,
				935F160EBCFF783ACEB50D5D /* IndicatorViewController.m in Sources */,
				140F70491DDDEA60A2CE772F /* ViewController+MASAdditions.m in Sources */,
				0258481ACA0DBDC2603AC7B7 /* TakeBitPenMask.m in Sources */,
				EB0BA5E7923FCF790945727E /* MASViewConstraint.m in Sources */,
				3F031074B244C8103F03E3B7 /* BigArmLocalHow+Birth.m in Sources */,
				44BC9CBD2108958B12181084 /* NSLayoutConstraint+MASDebugAdditions.m in Sources */,
				9ED40B4A32483377B5BE9524 /* LessCutWinCell.m in Sources */,
				DBAC4DC708D254581D1B2DD2 /* MASConstraint.m in Sources */,
				D19F8859E4543740881D4229 /* ModalEggView.m in Sources */,
				4DC63E308349B16D6B1A28C3 /* MASLayoutConstraint.m in Sources */,
				445371C637AE691CB721D04F /* SDMemoryCache.m in Sources */,
				63183371640D7EAEF61FE73A /* SDAnimatedImageView.m in Sources */,
				F18DDA9DFD3BB7B447E3116E /* TrainingRows.m in Sources */,
				5A4C51C719234A1F419E663C /* RecordOrganizeDropCondensedPortraitsRelative.m in Sources */,
				60E10042C06920DE1ADD06E3 /* SDInternalMacros.m in Sources */,
				D86FF8B516FF8E6F7C32AA6E /* ThreeAskManager.m in Sources */,
				B6815CD31DC7C298A18E399A /* SDAnimatedImage.m in Sources */,
				A8F4DAC4B9E68B65DB56E692 /* NSData+ImageContentType.m in Sources */,
				86AC6FD2D7CE363EC8C09B94 /* NSButton+WebCache.m in Sources */,
				B010A2BBD6BFFD9AB6EABB36 /* SDImageTransformer.m in Sources */,
				8C3D3547DD964F1BDE1A8275 /* MagnitudeLateViewController.m in Sources */,
				B5F2DA4BC3513ACDD6A58240 /* SDImageAWebPCoder.m in Sources */,
				76C5F5D7835B78FECBF218AD /* SDAsyncBlockOperation.m in Sources */,
				5B6E33E8B691511F924C00FF /* UIButton+WebCache.m in Sources */,
				53AA7723646A10055B5348DE /* SDAnimatedImageView+WebCache.m in Sources */,
				23C151348923E9F35195898B /* RestoresViewController.m in Sources */,
				36C74C904B61AC01A75E46B3 /* UIImage+ExtendedCacheData.m in Sources */,
				D2D4B3BCB04B3CAC2C4DF47D /* SDImageLoader.m in Sources */,
				79573C37F64C1532A652CA53 /* SDWebImageDownloaderDecryptor.m in Sources */,
				249B036415A1EF4ECEBFE7E9 /* UIImage+CupImage.m in Sources */,
				39823C41CA2E490090DA9E25 /* CubeSindhiViewController.m in Sources */,
				82108CCB0C094011FC36014A /* SDWebImageIndicator.m in Sources */,
				87E20D750DCEFB98F92E2646 /* SDImageFrame.m in Sources */,
				52E401225CB80DF3FC1B12EE /* SDFileAttributeHelper.m in Sources */,
				AC2712ADC53A81FD9D54884F /* SDWebImageError.m in Sources */,
				2EB4344DA185E0E79ECC395A /* SDWebImageDownloaderConfig.m in Sources */,
				2C7EFCBF173CB85C79BDC263 /* SDImageLoadersManager.m in Sources */,
				E51FAE002B017F52B52E4C17 /* UIImage+MemoryCacheCost.m in Sources */,
				B316D194E454685C67CA03E2 /* UIImageView+HighlightedWebCache.m in Sources */,
				7E1149D773EAC20BE7051221 /* SDImageGraphics.m in Sources */,
				E62642DA3ADF2DDBB739BA2A /* SDImageCachesManagerOperation.m in Sources */,
				748DC334F24CB4B990013EDA /* SDWebImageManager.m in Sources */,
				7B6E74D26FD7CC1C1F8E25CF /* SDImageCoder.m in Sources */,
				1C1E0F6A80928479EA4A40D2 /* ReadTiedKinButton.m in Sources */,
				303AAB38992E46CBB8CE34F4 /* UIImage+GIF.m in Sources */,
				BC77F874624BA2D954865D4A /* ReceivedHexViewController.m in Sources */,
				35B69FE6B7C559DE26856B89 /* SDImageCoderHelper.m in Sources */,
				EC8797F4C8EF91C08B5918EC /* UIDevice+HueDevice.m in Sources */,
				5068807AC627E897C0438985 /* CatAskEmailOldViewController.m in Sources */,
				72678EDA43F75C824F420919 /* LambdaCyrillic.m in Sources */,
				961D89650CDF3D6AACA57EC4 /* SDWebImageDownloaderRequestModifier.m in Sources */,
				26AEB7AE88C96992BF120409 /* DogBufferViewController.m in Sources */,
				075198BE6DFC593F53C0D915 /* NextBoxMoreManager.m in Sources */,
				56064DF79EF938F32E67DE85 /* SDWebImageCacheKeyFilter.m in Sources */,
				3CC4B4B810DAC9F77CE17CBE /* ExtentsPubViewController.m in Sources */,
				4BBF27A4748A7F0A42DA7E60 /* SuddenBufferModel.m in Sources */,
				3FB57875A5888F21927C9286 /* FlowFrontViewController.m in Sources */,
				84968214AB733E27048A1E91 /* LengthAndViewController.m in Sources */,
				8C17055AA067A4457B04AB9E /* SDImageCacheConfig.m in Sources */,
				2F925FDDAA4341276DB08559 /* SDImageCodersManager.m in Sources */,
				60F93824B292D423ABE8A79A /* SDAnimatedImagePlayer.m in Sources */,
				AE7341CD8C0C894542091BD4 /* NSData+Iterate.m in Sources */,
				CB64699B99D43867821FE12F /* EngineManager.m in Sources */,
				7314D2FCD7AD577DCECB65EE /* SDImageIOCoder.m in Sources */,
				5D240A3E3BD60D1697B4FFD5 /* MailNordic.m in Sources */,
				7545BA8E655F83039CB45DCA /* WrongArrayFootButton.m in Sources */,
				10BFA715E83355E348D415C7 /* NSImage+Compatibility.m in Sources */,
				5F24F56FBCF6D709B82E1D6B /* SDWebImageDefine.m in Sources */,
				32F4198970A0D5905D2717DE /* UIImageView+WebCache.m in Sources */,
				9CD313F3413C3EC6908BBC62 /* StrongHexViewController.m in Sources */,
				CCCEF1D4CFC91ECF52F05A15 /* BigSlabUniversalSpecifiedFoot.m in Sources */,
				7651D13C80D04DD603B30A26 /* RedoneMode.m in Sources */,
				20FC7BD1DB7F715A5A1CE986 /* SDWebImageOptionsProcessor.m in Sources */,
				E38FBCBE06C6AE64576D0B5E /* PaddleDublinManager.m in Sources */,
				455E052F0AA5CD0E5718B2F7 /* SDCallbackQueue.m in Sources */,
				073DECFF3E1B5B671548066C /* UIImage+ForceDecode.m in Sources */,
				98F6178CAF420BEB6AE0C3BD /* NSURL+SheRopeHow.m in Sources */,
				DFD22F3CE4C955CFCEF27635 /* SDDeviceHelper.m in Sources */,
				8E8F1F9942AC5D4A478A321A /* SDImageGIFCoder.m in Sources */,
				3BCF8C5844E9B02385B54954 /* UIView+WebCacheState.m in Sources */,
				C2EF1098CFE88091B655A36D /* UIImage+Metadata.m in Sources */,
				628D129B30FF640C052D63B8 /* UIView+WebCacheOperation.m in Sources */,
				624FD5A3006EC22B4E5280E8 /* GaspBusyStickyCell.m in Sources */,
				AD8625C6176B9D82E86CCDB1 /* SDDiskCache.m in Sources */,
				41D427B1A4388C969D6475FB /* SDImageCacheDefine.m in Sources */,
				69E97C025940DDDFFEFCEF14 /* PrivacyResultsViewController.m in Sources */,
				36FB96DC48F092EF99614CF9 /* NSObject+CivilAdoptMindfulCoachedCap.m in Sources */,
				0DD308C6DAAEA92914CCE0A7 /* KnowPictureAction.m in Sources */,
				EB2DA6302A158738A89D929C /* SDWeakProxy.m in Sources */,
				FD9A8804C521C9876CBDB1B6 /* SolidManager.m in Sources */,
				774582FCBC7FA955B607B6A0 /* SDWebImageDownloaderOperation.m in Sources */,
				8DB1534D61C4764B2AD8C3A3 /* UIColor+SDHexString.m in Sources */,
				0DCEDA0EF87CE92105181CA2 /* RealEntryTextField.m in Sources */,
				FE7DE03863BDBDC5FFEA877C /* SDWebImageDownloaderResponseModifier.m in Sources */,
				604BCED38041B0FCFC19A194 /* SDWebImageOperation.m in Sources */,
				02D425C6D31423E01F9D2F0D /* NSString+UnitZipHair.m in Sources */,
				5820F43CAA692EB3309F5D0C /* NSBezierPath+SDRoundedCorners.m in Sources */,
				0E0E288F61B1FAE8031E013C /* RingAgeDegradedEyeTextualAppearingViewController.m in Sources */,
				4E7A291CB199D80B24CDF097 /* SDImageAPNGCoder.m in Sources */,
				9E76C5A8F3EF43107630938D /* DownloadsManager.m in Sources */,
				5D82FEF2D2D9AE1C754CB55F /* UIImage+Transform.m in Sources */,
				E376E22D5A7AF7BDED090E0C /* CupExtrasMinInfo.m in Sources */,
				24AC03E9CDA09D26DE94EF26 /* SDImageAssetManager.m in Sources */,
				29C7272A847444D47A838E38 /* SDWebImageTransition.m in Sources */,
				318F9545A081A2DD00BB894E /* DayQueryWho.m in Sources */,
				9DE9D95635C4215DF88E60EF /* SDImageCache.m in Sources */,
				A41EAD1CE7A5A06AF5F37C98 /* SDImageHEICCoder.m in Sources */,
				E8C3A963D9F38D9C27C02582 /* SDImageCachesManager.m in Sources */,
				9093AEF624704C46C8EA7A17 /* DueTurnWasViewController.m in Sources */,
				2162265DCD1622792F167242 /* MQTTSessionSynchron.m in Sources */,
				1FD223D19DD1C38DA0DC3315 /* NSString+Suffix.m in Sources */,
				84211F5353ABE3C3133731C8 /* MQTTDecoder.m in Sources */,
				3F841561D73EE5A86675BEE5 /* MQTTSessionLegacy.m in Sources */,
				47C8955E276A5D83DD5A336C /* MQTTSSLSecurityPolicy.m in Sources */,
				1CE9A7F86D04D5043E6E0A7E /* MQTTMessage.m in Sources */,
				D3E5F2E1C435CE9F16B08AB3 /* MQTTSessionManager.m in Sources */,
				3DEAF9D47CDE444071E18659 /* ReconnectTimer.m in Sources */,
				BA706FBD09750B77D0909DA7 /* MQTTSSLSecurityPolicyTransport.m in Sources */,
				943A3094B65EEC18513B606E /* MQTTSession.m in Sources */,
				81DCD72A73130C18A378492A /* MQTTCoreDataPersistence.m in Sources */,
				551AAF5A4C3689D66EBD71E4 /* MQTTCFSocketTransport.m in Sources */,
				ADC9187BC8D8AF87F2F160D3 /* MQTTLog.m in Sources */,
				A94014A859AAC7519FA8B585 /* MQTTSSLSecurityPolicyDecoder.m in Sources */,
				45B8C6FDC550DF906AE905FE /* MQTTSSLSecurityPolicyEncoder.m in Sources */,
				60978BBA09704947E442A0DC /* MQTTTransportProtocol.m in Sources */,
				0EC2074020510BEB2C98320A /* MQTTInMemoryPersistence.m in Sources */,
				15C25464E4A5887F1D76FE26 /* MQTTStrict.m in Sources */,
				E74A147589E92FDB6B4749AA /* MQTTCFSocketDecoder.m in Sources */,
				F7D9BC6221B1AE2CB89747E7 /* BigArmLocalHow+SubSub.m in Sources */,
				04CE4CCC81145C5593CFC7FD /* GCDTimer.m in Sources */,
				EF8812888C286542211EBF87 /* MQTTCFSocketEncoder.m in Sources */,
				714A7D5EB3FBDC78AFA897B4 /* MQTTProperties.m in Sources */,
				52DB117E80C2E6F70566121D /* ForegroundReconnection.m in Sources */,
				E576DB7B3497E0DC31918626 /* SDGraphicsImageRenderer.m in Sources */,
				F248CC63BC9A5A74860B9B97 /* UIView+WebCache.m in Sources */,
				DBDD609E0FD70AA4588BD0F9 /* SDWebImagePrefetcher.m in Sources */,
				70DB62520445E5E23C2E488C /* ArtistBuddy.m in Sources */,
				A0C2B210F11D1B8E6BCB6C9F /* SDWebImageCacheSerializer.m in Sources */,
				16F2A1AD8F5A4152D63856F5 /* SDDisplayLink.m in Sources */,
				67E9E36672BE25D570B97A14 /* SDWebImageDownloader.m in Sources */,
				D22E56A8492D2F6F7B8C4DE8 /* SDImageFramePool.m in Sources */,
				0A7CB51AE9EF8B4394B7F63A /* SDImageIOAnimatedCoder.m in Sources */,
				29A6285B3B7AA0F014359491 /* UIImage+MultiFormat.m in Sources */,
				B0AF83099F64E42E3F983C80 /* SDAssociatedObject.m in Sources */,
				CE97DFD32805BA717F5FE0C5 /* SDAnimatedImageRep.m in Sources */,
				F5F3C2A5463598B5CA8A9E47 /* SixTheAppleManager.m in Sources */,
				55BCB0F980C1D9964A4C2489 /* SDWebImageCompat.m in Sources */,
				038ECDCDD8117DC0805E8A6F /* MediaDerivedAssignMattingActionExpensive.m in Sources */,
				2CE050F7D7412513FF625B0F /* InterExistWalk.m in Sources */,
				9E426547092620E96003B93E /* PopDrainImmediateOnePlug.m in Sources */,
				DC12E6CCAF401994E94534A0 /* Blink.m in Sources */,
				BF6D4F0392B37C5E5BD12B13 /* ThickViewController.m in Sources */,
				56B6CBD54D6D7246281B2783 /* WeekVirtualBlueSucceededExpects.m in Sources */,
				A0D666543E6BCF2595339DC5 /* NSArray+MASAdditions.m in Sources */,
				2CEB6E6A791310F160E0E078 /* MASConstraintMaker.m in Sources */,
				AEAF0B9ED680E472BFB081F1 /* WristSkinInfo.m in Sources */,
				F8E8A79D6156FC33227BD12A /* MASViewAttribute.m in Sources */,
				A613B9BE8991D66A651EDF03 /* MASCompositeConstraint.m in Sources */,
				B5D481B5C4897CE91D45187A /* View+MASAdditions.m in Sources */,
				35EC2FE890111A6ED6F4FE4E /* InferOverViewController.m in Sources */,
				8B2680BE5062A7287314277B /* PasswordsMoveViewController.m in Sources */,
				9EB1976A13694D584C48D4BE /* XXGProtocolLabel.m in Sources */,
				7BA082416675FAD2286D9130 /* TagTreeViewController.m in Sources */,
				DD1F487E287477572A867602 /* ClampingSawPrepareAirlineParserModel.m in Sources */,
				5B341A8D3CB64ACBFB9A9692 /* NSError+SawImageBin.m in Sources */,
				42AFAAEDB70511CC36D086A1 /* HowFaxConfig.m in Sources */,
				DA22AD70D7191475C9C3424A /* ZipOldRootView.m in Sources */,
				56DF5E454720BDA994BC13AF /* FatMailManager.m in Sources */,
				9FFD6C8BEE328869757581BF /* DoneBaselineManager.m in Sources */,
				38B762C6AF1A2698C3DCAE89 /* GatherEuropeanInfo.m in Sources */,
				2D6F953BF13B5D1FE7A26F8A /* ContraceptiveSettee.m in Sources */,
				93A984A2395B1F48EA57D3E6 /* TagZipFailManager.m in Sources */,
				1A99FB2DB86788C1156C3553 /* EnergyPotassiumDelaySkinFeatCell.m in Sources */,
				A2313C616FCCFB4029D74F11 /* BigArmLocalHow.m in Sources */,
				A2426C0997285AB9025845D5 /* RetMidManager.m in Sources */,
				B7BCBA87E855880696F3DFFE /* SobArcheryIll.m in Sources */,
				1AF557B1AC8822408F00C774 /* BusForDrawView.m in Sources */,
				22A31172C7D77D42B48F2F74 /* SugarWetLessHostingSphere.m in Sources */,
				84CCB259ADF5E871D9E43155 /* IcyPhase.m in Sources */,
				92B1B87CC706E226F7E783DA /* DoneSalt.m in Sources */,
				59AA2678D5E26399CA26A605 /* AssignTabBin.m in Sources */,
				F6BB976D0FAA5D789C979BFA /* NearBadSeekManager.m in Sources */,
				0A9DB88CAFA3D1381EB5A9E8 /* IndexRaiseConfig.m in Sources */,
				3A3DF2EF69AE519A24E037F6 /* BusWasPackViewController.m in Sources */,
				D6CBA466FDBBC27E95C9E430 /* MillHeightWill.m in Sources */,
				CD2087B9E84C442FAC1423B5 /* NSObject+MixModel.m in Sources */,
				C6AFE96F423D25DAD97A7568 /* ChestFingerAxesDraftStyleViewController.m in Sources */,
				19FDDB2BB5E8959263C5D448 /* UsabilityOnlyController.m in Sources */,
				C696252A9E20AA40FBA8451F /* UIViewController+DueViewController.m in Sources */,
				31520B50A32E0BCC49AB02FE /* GeneratorDraftCell.m in Sources */,
				09B7129457982637D1830195 /* WayRealArmForm.m in Sources */,
				929F14CE015E13F53918302E /* SpineCandidate.m in Sources */,
				94B5B0662A9777EAE37D4087 /* SexualUtilitiesScopeSkipKilobits.m in Sources */,
				4043955825ABC0CA6C25DA1C /* AlienDelayItemBriefSelector.m in Sources */,
				516F26CA00332DE8DCBB82EB /* CleanWaist.m in Sources */,
				116EA396F122F3A5CB4E644F /* SequenceStreamKeepReadoutOff.m in Sources */,
				4832F0FD08BDA73CA23B0381 /* UIColor+BoxColor.m in Sources */,
				2695900DC9A5C17AFD4FCA43 /* ForAlertView.m in Sources */,
				B0A3A069D36E0C1FB797CE82 /* BestArteryModel.m in Sources */,
				847A90BA613A9E623B865C99 /* JabberStepperContrastHomeReportPrefers.m in Sources */,
				30E64CAFFEC43F6A8A0CC213 /* SayToast.m in Sources */,
				64AC5CBC195D7D6522848F7C /* EggNetCivilSon.m in Sources */,
				F50355E2BA0CF60B8B659AE5 /* HisClickColor.m in Sources */,
				6451E2BAEAED90D54E121C80 /* HelpRaceManager.m in Sources */,
				BE506CB215101DC3C7C774D0 /* PubPrepModel.m in Sources */,
				530551280792C2918D0429A6 /* MusicAuditNoteMajorSuddenTool.m in Sources */,
				E35E9B52FA56412ABB3798BC /* BetweenMixList.m in Sources */,
				9A4FE05D5EA3DF9FA95EFE8E /* BigArmLocalHow+HailPrice.m in Sources */,
				F7B29C6A00537C89D8B1C38E /* PurpleInfo.m in Sources */,
				D2AA2DD7A96E52402505B3D1 /* FarNetwork.m in Sources */,
				8F3E77DF8E2E6DED8209B6CC /* NSString+Messaging.m in Sources */,
				E3D7CDCD302FDAF9BFF6D516 /* HeartBankManager.m in Sources */,
				7EF0ACA91242C7B76CA7AE72 /* ElderNotationVolumeResourcesShortcut.m in Sources */,
				5F3A763F3EE7BA6D2541170B /* MinimizeEstonianIcyFinderRoot.m in Sources */,
				6EB4F71C314A28A210BC1F14 /* LacrosseModel.m in Sources */,
				B10508C7F712C11D46907549 /* CarriageAssignWindow.m in Sources */,
				DE45AFE7B9DB54AD7C236705 /* BusCostCanon.m in Sources */,
				1B61E0CA9F75F917CE37BCF8 /* TripleAir.m in Sources */,
				FC3AAADF1181F59FE8CA44C1 /* LoopPatchInfo.m in Sources */,
				5A3B098171D865D2D23A0450 /* ChainPauseInfo.m in Sources */,
				82BD0B8ADE312351FF226129 /* CopticLearn.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		3608475B7DD512A0394F483F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = NO;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.ContraceptiveSettee;
				PRODUCT_NAME = ContraceptiveSettee;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		551CF7AF3999E57A5050BBAF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		756F9FFF7FEB09B41A221CA6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		911573D113A0045C08B10040 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = NO;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.ContraceptiveSettee;
				PRODUCT_NAME = ContraceptiveSettee;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		089AA46331F93CC6B0AFE0B2 /* Build configuration list for PBXProject "ContraceptiveSettee" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				551CF7AF3999E57A5050BBAF /* Debug */,
				756F9FFF7FEB09B41A221CA6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AE2F2C0A73E0B48CF77A2ABD /* Build configuration list for PBXNativeTarget "ContraceptiveSettee" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				911573D113A0045C08B10040 /* Debug */,
				3608475B7DD512A0394F483F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 49AA5AF0181205E50724A833 /* Project object */;
}
