








#import <AppLovinSDK/AppLovinSDK.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>

@interface AtomSymbolicAngularRevisionsArtTrimming : NSObject<MARewardedAdDelegate,MAAdViewAdDelegate>

@property (nonatomic, strong) MARewardedAd *oneLookPerformsCausePreserves;
@property (nonatomic, assign) NSInteger kindFoundPlateStreamedClinical;

@property (nonatomic, copy) NSString *rateRestoreData;

@property (nonatomic, copy) void (^artTabMeanAny)(BOOL result);

@end

@implementation AtomSymbolicAngularRevisionsArtTrimming

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t ringToken;
    dispatch_once(&ringToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

+ (NSString *)zipAmountBin {
    return ALSdk.version;
}

- (void)outlineArmFourthExtrasPostcardBox {
    [[ALSdk shared] outlineArmFourthExtrasPostcardBox];
}


- (void)describeTotalLigaturesOfficialQueueSelectingKey:(NSString *)xxpk_maxkey unsignedToneEffortFloatingLigature:(NSString *)unsignedToneEffortFloatingLigature wirelessFourthOwnGeneratesSample:(NSArray *)wirelessFourthOwnGeneratesSample {
    
    
    
    Class cls = NSClassFromString(@"FBAdSettings");
    if (cls) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
        [cls performSelector:@selector(setDataProcessingOptions:) withObject:@[]];
        [cls performSelector:@selector(setAdvertiserTrackingEnabled:) withObject:@(YES)];
#pragma clang diagnostic pop
    }
    
    

    
    ALSdkInitializationConfiguration *xxpk_configuration = [ALSdkInitializationConfiguration configurationWithSdkKey:xxpk_maxkey builderBlock:^(ALSdkInitializationConfigurationBuilder * _Nonnull builder) {
        builder.mediationProvider = ALMediationProviderMAX;
        if (wirelessFourthOwnGeneratesSample) {
            builder.testDeviceAdvertisingIdentifiers = wirelessFourthOwnGeneratesSample;
        }
        if (@available(iOS 14, *)) {
            if ([ATTrackingManager trackingAuthorizationStatus] == ATTrackingManagerAuthorizationStatusAuthorized ) {
                [ALPrivacySettings setHasUserConsent: YES];
            }
        } else {
            [ALPrivacySettings setHasUserConsent: YES];
        }
    }];
    
    [[ALSdk shared] initializeWithConfiguration:xxpk_configuration completionHandler:^(ALSdkConfiguration * _Nonnull configuration) {
        [self stayArrayArabicElementCatWarnExpired:unsignedToneEffortFloatingLigature];
    }];
}

- (void)stayArrayArabicElementCatWarnExpired:(NSString *)unsignedToneEffortFloatingLigature
{
    self.oneLookPerformsCausePreserves = [MARewardedAd sharedWithAdUnitIdentifier:unsignedToneEffortFloatingLigature];
    self.oneLookPerformsCausePreserves.delegate = self;

    
    [self.oneLookPerformsCausePreserves loadAd];
}

- (void)cascadeHandshakeKilometerDriveEngineerCourseData:(nullable NSString *)rateRestoreData denyFile:(void(^)(BOOL result))denyFile {
    self.rateRestoreData = rateRestoreData;
    self.artTabMeanAny = denyFile;
    if ( [self.oneLookPerformsCausePreserves isReady]) {
        [self.oneLookPerformsCausePreserves showAdForPlacement:nil customData:rateRestoreData];
    }else {
        denyFile(NO);
    }
}

- (void)hasMolarSpokenAuxiliaryMetrics:(const char *)name error:(NSString *)error {
    
}


- (void)paperPart:(nonnull MAAd *)ad {
    
    [self hasMolarSpokenAuxiliaryMetrics: __PRETTY_FUNCTION__ error:nil];
    
    
    self.kindFoundPlateStreamedClinical = 0;
}

- (void)howInvokeDiscardDeferringSawFeetIdentifier:(nonnull NSString *)adUnitIdentifier withError:(nonnull MAError *)error {
    
    [self hasMolarSpokenAuxiliaryMetrics: __PRETTY_FUNCTION__ error:error.message];
    
    
    
    self.kindFoundPlateStreamedClinical++;
    NSInteger dolbyTwo = pow(2, MIN(6, self.kindFoundPlateStreamedClinical));
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, dolbyTwo * NSEC_PER_SEC), dispatch_get_main_queue(), ^{
        [self.oneLookPerformsCausePreserves loadAd];
    });
}

- (void)cubicSeek:(MAAd *)ad {
    [self hasMolarSpokenAuxiliaryMetrics: __PRETTY_FUNCTION__ error:nil];
    
    
    [self.oneLookPerformsCausePreserves loadAd];
}

- (void)twitterSeasonIndicesMotionYear:(MAAd *)ad withError:(MAError *)error
{
    [self hasMolarSpokenAuxiliaryMetrics: __PRETTY_FUNCTION__ error:error.message];
    
    
    [self.oneLookPerformsCausePreserves loadAd];
    
    if (self.artTabMeanAny) {
        self.artTabMeanAny(NO);
    }
}

- (void)hueLazyPen:(nonnull MAAd *)ad {
    [self hasMolarSpokenAuxiliaryMetrics: __PRETTY_FUNCTION__ error:nil];
}

- (void)playBadShear:(nonnull MAAd *)ad {
    [self hasMolarSpokenAuxiliaryMetrics: __PRETTY_FUNCTION__ error:nil];
}

- (void)addLostProgramAlienTrait:(nonnull MAAd *)ad itsPassCup:(nonnull MAReward *)reward {
    [self hasMolarSpokenAuxiliaryMetrics: __PRETTY_FUNCTION__ error:nil];
    
    if (self.artTabMeanAny) {
        self.artTabMeanAny(YES);
    }
}


- (void)netCatWaitMid:(nonnull MAAd *)ad {
    [self hasMolarSpokenAuxiliaryMetrics: __PRETTY_FUNCTION__ error:nil];
}

- (void)hertzFriend:(nonnull MAAd *)ad {
    [self hasMolarSpokenAuxiliaryMetrics: __PRETTY_FUNCTION__ error:nil];
}

@end
