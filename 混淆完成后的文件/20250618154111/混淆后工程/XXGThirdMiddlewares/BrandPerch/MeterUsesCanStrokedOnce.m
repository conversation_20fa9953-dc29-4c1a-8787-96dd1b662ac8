






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <VKID/VKID-Swift.h>

@interface MeterUsesCanStrokedOnce : NSObject

@end

@implementation MeterUsesCanStrokedOnce

+ (void)airLaunchingViewController:(UIViewController *)vc handler:(void(^)(BOOL isCancell,NSString *userID, NSString*token, NSString*error))handler {
    [[VKIDExtension shared] oauthWithPresentingController:vc completion:^(BOOL isCancell, NSString * userId, NSString * token, NSString * error) {
        if (isCancell) {
            handler(YES,@"",@"",@"");
        }else {
            handler(NO,userId,token,error);
        }
    }];
}

+ (BOOL)hourSolveGlyphCaptionFocused:(UIApplication *)application
                spectral:(NSURL *)url
                maskTied:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    [[VKIDExtension shared] handleOpenURL:url];
    return YES;
}

+ (void)hexImpactRecycleAttributeSingleSparse:(NSString *)clientId aboutSupport:(NSString *)aboutSupport{
    [[VKIDExtension shared] initvkWithClientId:clientId aboutSupport:aboutSupport];
}

@end
