







#import "BDASignalManager.h"

@interface TallCompareHitMaxGradeWax : NSObject

@end

@implementation TallCompareHitMaxGradeWax

+ (NSString *)zipAmountBin {
    return kBDADSignalSDKVersion;
}

+ (void)earSpotlightGigahertzEditStepsonMisplacedOptions:(NSDictionary *)launchOptions ourEventOptions:(UISceneConnectionOptions *)connetOptions {
    if (launchOptions) {
        
        [BDASignalManager didFinishLaunchingWithOptions:launchOptions connectOptions:nil];
        return;
    }
    if (connetOptions) {
        
        [BDASignalManager didFinishLaunchingWithOptions:nil connectOptions:connetOptions];
    }
}

+ (BOOL)hourSolveGlyphCaptionFocused:(UIApplication *)application
                spectral:(NSURL *)url
                maskTied:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    [BDASignalManager anylyseDeeplinkClickidWithOpenUrl:url.absoluteString];
    return YES;
}

@end
