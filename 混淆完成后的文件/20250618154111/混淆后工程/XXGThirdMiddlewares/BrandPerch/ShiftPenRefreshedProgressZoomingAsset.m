





#import <CL_ShanYanSDK/CL_ShanYanSDK.h>

@interface ShiftPenRefreshedProgressZoomingAsset : NSObject<CLShanYanSDKManagerDelegate>

@property (nonatomic, copy) void(^pathMotionLiftAction)(NSInteger);

@end

@implementation ShiftPenRefreshedProgressZoomingAsset

- (void)dealloc {
    
}

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t ringToken;
    dispatch_once(&ringToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

- (void)usesAccordingFailMetricSumOwnerBlocker:(NSString *)appId complete:(void (^_Nullable)(BOOL isCanLogin))complete {

    [CLShanYanSDKManager setCLShanYanSDKManagerDelegate:self];
    
    [CLShanYanSDKManager setPreGetPhonenumberUseCacheIfNoCellularNetwork:NO];
    
    [CLShanYanSDKManager initWithAppId:appId complete:^(CLCompleteResult * _Nonnull completeResult) {
        __block BOOL editOneNonePopLogin = !completeResult.error;
        
        if (editOneNonePopLogin) {
            
            [CLShanYanSDKManager preGetPhonenumber:^(CLCompleteResult * _Nonnull completeResult) {
                dispatch_sync(dispatch_get_main_queue(), ^{
                    complete(completeResult.error == nil);
                });
            }];
            
        } else {
            dispatch_sync(dispatch_get_main_queue(), ^{
                complete(NO);
            });
        }
    }];
}


- (void)applierShipmentJobPreserveEnableController:(UIViewController *_Nonnull)controller array:(NSArray *)array success:(void (^_Nullable)(NSDictionary * _Nonnull planeFlip))success sonRestore:(void (^_Nullable)(NSString * _Nonnull error))error packKeyAction:(void(^)(NSInteger))action {
    self.pathMotionLiftAction = action;
    

    [CLShanYanSDKManager quickAuthLoginWithConfigure:[self plusRestRadio:controller lookArray:array] openLoginAuthListener:^(CLCompleteResult * _Nonnull completeResult) {
        if (completeResult.error) {
            error(completeResult.message);
        }
    } oneKeyLoginListener:^(CLCompleteResult * _Nonnull completeResult) {
        
        if (completeResult.error == nil) {
            dispatch_sync(dispatch_get_main_queue(), ^{
                success(completeResult.data);
            });
        }else {
            error(completeResult.message);
        }
    }];
}





- (CLUIConfigure *)plusRestRadio:(UIViewController *)viewController lookArray:(NSArray *)array {
    CLUIConfigure *encipherSob = [[CLUIConfigure alloc] init];
    
    encipherSob.viewController = viewController;
    
    
    encipherSob.clNavigationBarHidden = @(YES);
    
    //logo
    encipherSob.clLogoHiden = @(YES);
    
    
    encipherSob.clPhoneNumberFont = [UIFont boldSystemFontOfSize:26];
    encipherSob.clPhoneNumberColor = UIColor.darkGrayColor;
    encipherSob.clPhoneNumberTextAlignment = @(NSTextAlignmentCenter);
    
    
    encipherSob.clSloganTextFont = [UIFont systemFontOfSize:14];
    encipherSob.clSloganTextColor = UIColor.darkGrayColor;
    encipherSob.clSlogaTextAlignment = @(NSTextAlignmentCenter);
    
    
    encipherSob.clLoginBtnText = array[4];
    encipherSob.clLoginBtnTextFont = [UIFont systemFontOfSize:18];
    encipherSob.clLoginBtnTextColor = UIColor.whiteColor;
    encipherSob.clLoginBtnBgColor = array[0];
    encipherSob.clLoginBtnCornerRadius = @(2);
    
    
    encipherSob.clCheckBoxSize = [NSValue valueWithCGSize:CGSizeMake(0, 0)];
    encipherSob.clCheckBoxValue = @(YES);
    
    
    encipherSob.clAppPrivacyNormalDesTextFirst = array[5];
    encipherSob.clAppPrivacyFirst = @[array[6], array[1]];
    encipherSob.clAppPrivacyNormalDesTextSecond = array[7];
    encipherSob.clAppPrivacyNormalDesTextLast = @"";
    encipherSob.clAppPrivacyTextFont = [UIFont systemFontOfSize:12];
    encipherSob.clAppPrivacyTextAlignment = @(NSTextAlignmentLeft);
    encipherSob.clAppPrivacyWebNavigationBarTintColor = UIColor.whiteColor;
    encipherSob.clAppPrivacyPunctuationMarks = @(YES);
    encipherSob.clAppPrivacyColor = @[UIColor.darkGrayColor,array[0]];;
    encipherSob.clAuthTypeUseWindow = @(YES);
    encipherSob.clPrivacyShowUnderline = @(YES);
    encipherSob.clAppPrivacyLineSpacing = @(2.5);
    encipherSob.clAuthWindowModalTransitionStyle = @(UIModalTransitionStyleCrossDissolve);
    
    encipherSob.clAppPrivacyWebBackBtnImage = array[2];
    
    
    encipherSob.clLoadingSize = [NSValue valueWithCGSize:CGSizeMake(90, 90)];
    encipherSob.clLoadingCornerRadius = @(2);
    encipherSob.clLoadingIndicatorStyle = @(UIActivityIndicatorViewStyleLarge);
    encipherSob.clLoadingTintColor = UIColor.blackColor;
    encipherSob.clLoadingBackgroundColor = UIColor.clearColor;
    
    
    CLOrientationLayOut *audioMenstrualYearImmutableSubtract = [[CLOrientationLayOut alloc] init];
    encipherSob.clOrientationLayOutPortrait = audioMenstrualYearImmutableSubtract;
    
    
    CGFloat y = (([UIScreen mainScreen].bounds.size.height - [array[3] CGSizeValue].height) * 0.5) + 35;
    CGFloat height = 30;
    audioMenstrualYearImmutableSubtract.clLayoutPhoneTop = @(y);
    audioMenstrualYearImmutableSubtract.clLayoutPhoneHeight = @(height);
    audioMenstrualYearImmutableSubtract.clLayoutPhoneCenterX = @(0);
    
    
    y += (height + 20);
    height = 17;
    audioMenstrualYearImmutableSubtract.clLayoutSloganTop = @(y);
    audioMenstrualYearImmutableSubtract.clLayoutSloganCenterX = @(0);
    audioMenstrualYearImmutableSubtract.clLayoutSloganHeight = @(height);

    
    y += (height + 20);
    height = 50;
    audioMenstrualYearImmutableSubtract.clLayoutLoginBtnTop = @(y);
    audioMenstrualYearImmutableSubtract.clLayoutLoginBtnWidth = @([array[3] CGSizeValue].width - 40);
    audioMenstrualYearImmutableSubtract.clLayoutLoginBtnCenterX = @(0);
    audioMenstrualYearImmutableSubtract.clLayoutLoginBtnHeight = @(height);

    
    y += (height + 15);
    audioMenstrualYearImmutableSubtract.clLayoutAppPrivacyTop = @(y);
    audioMenstrualYearImmutableSubtract.clLayoutAppPrivacyCenterX = @(0);
    audioMenstrualYearImmutableSubtract.clLayoutAppPrivacyWidth = @([array[3] CGSizeValue].width - 40);
    
    
    encipherSob.customAreaView = ^(UIView * _Nonnull customAreaView) {
        
        customAreaView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0];
        
        UIView *largeView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, [array[3] CGSizeValue].width, [array[3] CGSizeValue].height)];
        largeView.backgroundColor = UIColor.whiteColor;
        largeView.layer.cornerRadius = 2.0;
        [customAreaView addSubview:largeView];
        largeView.center = customAreaView.center;
        
        
        UIButton *close = [UIButton buttonWithType:UIButtonTypeCustom];
        [close addTarget:self action:@selector(deliverSegueExpandFalloffExpandingFrontHandler:) forControlEvents:(UIControlEventTouchUpInside)];
        [close setBackgroundImage:array[2] forState:UIControlStateNormal];
        [largeView addSubview:close];
        close.frame = CGRectMake(8, 8, 20, 20);
    };
    
    return encipherSob;
}

- (void)deliverSegueExpandFalloffExpandingFrontHandler:(id)sender {
    [CLShanYanSDKManager finishAuthControllerCompletion:nil];
    self.pathMotionLiftAction(0);
}

@end
