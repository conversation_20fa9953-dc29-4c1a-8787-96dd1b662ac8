<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSPrivacyCollectedDataTypes</key>
	<array>
		<dict>
			<key>NSPrivacyCollectedDataType</key>
			<string>NSPrivacyCollectedDataTypeDeviceID</string>
			<key>NSPrivacyCollectedDataTypeLinked</key>
			<true/>
			<key>NSPrivacyCollectedDataTypeTracking</key>
			<true/>
			<key>NSPrivacyCollectedDataTypePurposes</key>
			<array>
				<string>NSPrivacyCollectedDataTypePurposeThirdPartyAdvertising</string>
			</array>
		</dict>
		<dict>
			<key>NSPrivacyCollectedDataType</key>
			<string>NSPrivacyCollectedDataTypeProductInteraction</string>
			<key>NSPrivacyCollectedDataTypeLinked</key>
			<false/>
			<key>NSPrivacyCollectedDataTypeTracking</key>
			<false/>
			<key>NSPrivacyCollectedDataTypePurposes</key>
			<array>
				<string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
			</array>
		</dict>
	</array>
	<key>NSPrivacyTrackingDomains</key>
	<array>
		<string>att-attr.whappsflyer.com</string>
		<string>att-attr.appsflyer-cn.com</string>
		<string>att-attr.hevents.appsflyer-cn.com</string>
		<string>att-launches.whappsflyer.com</string>
		<string>att-launches.appsflyer-cn.com</string>
		<string>att-launches.hevents.appsflyer-cn.com</string>
		<string>att-conversions.hevents.appsflyer-cn.com</string>
		<string>att-conversions.appsflyer-cn.com</string>
		<string>att-conversions.whappsflyer.com</string>
		<string>att-dlsdk.hevents.appsflyer-cn.com</string>
		<string>att-dlsdk.appsflyer-cn.com</string>
		<string>att-dlsdk.whappsflyer.com</string>
		<string>att-dlsdk.appsflyersdk.com</string>
		<string>att-conversions.appsflyersdk.com</string>
		<string>att-launches.appsflyersdk.com</string>
		<string>att-attr.appsflyersdk.com</string>
	</array>
	<key>NSPrivacyTracking</key>
	<true/>
	<key>NSPrivacyAccessedAPITypes</key>
	<array>
		<dict>
			<key>NSPrivacyAccessedAPITypeReasons</key>
			<array>
				<string>CA92.1</string>
			</array>
			<key>NSPrivacyAccessedAPIType</key>
			<string>NSPrivacyAccessedAPICategoryUserDefaults</string>
		</dict>
		<dict>
			<key>NSPrivacyAccessedAPITypeReasons</key>
			<array>
				<string>C617.1</string>
			</array>
			<key>NSPrivacyAccessedAPIType</key>
			<string>NSPrivacyAccessedAPICategoryFileTimestamp</string>
		</dict>
	</array>
</dict>
</plist>
