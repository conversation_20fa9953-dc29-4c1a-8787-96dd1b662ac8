//
//  ShareInviteHelper.h
//  AppsFlyerLib
//
//  Created by <PERSON> on 27/01/2017.
//
//

#import <Foundation/Foundation.h>
#import <AppsFlyerLib/AppsFlyerLinkGenerator.h>

/**
 AppsFlyerShareInviteHelper
 */
@interface AppsFlyerShareInviteHelper : NSObject

NS_ASSUME_NONNULL_BEGIN

/**
 *  The AppsFlyerShareInviteHelper class builds the invite URL according to various setter methods 
 *  which allow passing on additional information on the click. 
 *  This information is available through `onConversionDataReceived:` when the user accepts the invite and installs the app.
 *  In addition, campaign and channel parameters are visible within the AppsFlyer Dashboard.
 */
+ (void)generateInviteUrlWithLinkGenerator:(AppsFlyerLinkGenerator *(^)(AppsFlyerLinkGenerator *generator))generatorCreator completionHandler:(void (^)(NSURL *_Nullable url))completionHandler;

/**
 *  It is recommended to generate an in-app event after the invite is sent to log the invites from the senders' perspective. 
 *  This enables you to find the users that tend most to invite friends, and the media sources that get you these users.
 */
+ (void)logInvite:(nullable NSString *)channel parameters:(nullable NSDictionary *)parameters;

@end

NS_ASSUME_NONNULL_END
