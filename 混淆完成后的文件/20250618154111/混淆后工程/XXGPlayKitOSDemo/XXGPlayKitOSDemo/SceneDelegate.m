/*********************************
 *********************************
 ***当前SDK接口对接演示，请结合文档对接
 *********************************
 *********************************
 **/
#import "SceneDelegate.h"
#import <ContraceptiveSettee/ContraceptiveSettee.h>

@interface SceneDelegate ()

@end

@implementation SceneDelegate


- (void)scene:(UIScene *)scene willConnectToSession:(UISceneSession *)session options:(UISceneConnectionOptions *)connectionOptions {
    
    // MARK: - 启动（必接）
    // 如果应用程序使用的 UIScene，下面方法无需在AppDelegate里实现，只在这里实现
    [TripleAir earSpotlightGigahertzEditStepsonMisplacedOptions:nil ourEventOptions:connectionOptions];
}

- (void)scene:(UIScene *)scene openURLContexts:(NSSet<UIOpenURLContext *> *)URLContexts {
    
    // MARK: - openURL（必接）
    // 如果应用程序使用的 UIScene，下面方法无需在AppDelegate里实现，只在这里实现
    [TripleAir pagerBitsFileExpandedPrefixesExecuting:URLContexts.allObjects.firstObject.URL maskTied:nil executingOff:URLContexts];
}


- (void)sceneDidDisconnect:(UIScene *)scene {
    // Called as the scene is being released by the system.
    // This occurs shortly after the scene enters the background, or when its session is discarded.
    // Release any resources associated with this scene that can be re-created the next time the scene connects.
    // The scene may re-connect later, as its session was not necessarily discarded (see `application:didDiscardSceneSessions` instead).
}


- (void)sceneDidBecomeActive:(UIScene *)scene {
    // Called when the scene has moved from an inactive state to an active state.
    // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
    NSLog(@"%s",__func__);
}


- (void)sceneWillResignActive:(UIScene *)scene {
    // Called when the scene will move from an active state to an inactive state.
    // This may occur due to temporary interruptions (ex. an incoming phone call).
    NSLog(@"%s",__func__);
}


- (void)sceneWillEnterForeground:(UIScene *)scene {
    // Called as the scene transitions from the background to the foreground.
    // Use this method to undo the changes made on entering the background.
}


- (void)sceneDidEnterBackground:(UIScene *)scene {
    // Called as the scene transitions from the foreground to the background.
    // Use this method to save data, release shared resources, and store enough scene-specific state information
    // to restore the scene back to its current state.
}


@end
