/*********************************
 *********************************
 ***当前文件为Demo界面设置，请忽略
 *********************************
 *********************************
 **/





























































































































































































































































































































#import "_.h"
#import <ContraceptiveSettee/ContraceptiveSettee.h>

@implementation ViewController (Demo)

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return UIInterfaceOrientationMaskPortrait;
}
- (BOOL)prefersHomeIndicatorAutoHidden {
    return NO;
}
- (UIRectEdge)preferredScreenEdgesDeferringSystemGestures {
    return UIRectEdgeAll;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.demo_logs = [NSMutableArray new];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(ageMobileBarsLogoThreadUnwrap) name:ScannedAll.WorkingCathedralYardOnceCompoundMembersVirtualHue object:nil];
    self.demo_sdk_version.text = [NSString stringWithFormat:@"SDK VERSION：%@",ScannedAll.zipAmountBin];
    [self napFunArrowHundredZeroCommitted];
    [self retainPickerRequireLegalServiceExecutingHour:self.view.subviews];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self fullyFlightAwakeKinConstantAnimation];
}

- (void)napFunArrowHundredZeroCommitted {
    UIStoryboard *heartbeatIndirectShelfResolvingCollect = [UIStoryboard storyboardWithName:@"LaunchScreen" bundle:nil];
    UIViewController *tapsPage = [heartbeatIndirectShelfResolvingCollect instantiateInitialViewController];
    UIView *courseView = tapsPage.view;
    courseView.frame = self.view.bounds;
    courseView.tag = 99;
    [self.view addSubview:courseView];
    self.demo_logo.hidden = YES;
}

- (void)fullyFlightAwakeKinConstantAnimation {
    UIView *courseView = [self.view viewWithTag:99];
    UIView *logoView = [courseView viewWithTag:100];
    CGPoint tryNotCenter = [self.view convertPoint:self.demo_logo.center
                                         fromView:self.demo_logo.superview];
    UIView *movingLogo = [logoView snapshotViewAfterScreenUpdates:YES];
    movingLogo.frame = [self.view convertRect:logoView.frame fromView:logoView.superview];
    [self.view addSubview:movingLogo];
    logoView.hidden = YES;
    self.demo_login_status.hidden = YES;
    // 用dispatch_after实现真正的延迟
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:1.1
                              delay:0
                            options:UIViewAnimationOptionCurveEaseInOut
                         animations:^{
            courseView.alpha = 0;
            movingLogo.center = tryNotCenter;
            movingLogo.transform = CGAffineTransformMakeScale(self.demo_logo.bounds.size.width/movingLogo.bounds.size.width,
                                                             self.demo_logo.bounds.size.height/movingLogo.bounds.size.height);
        } completion:^(BOOL finished) {
            [UIView animateWithDuration:0.5 animations:^{
                self.demo_logo.hidden = NO;
                self.demo_login_status.hidden = NO;
            }];
            [courseView removeFromSuperview];
            [movingLogo removeFromSuperview];
            [self.demo_logo.layer addAnimation:[self topButAnimation] forKey:nil];
            // 动画结束后依次显示按钮
            [self prefixesOriginUnpluggedBeenYoungerSettingJump:self.view.subviews];
        }];
    });
}

- (CABasicAnimation *)topButAnimation {
    CABasicAnimation *flip = [CABasicAnimation animationWithKeyPath:@"opacity"];
    flip.fromValue = @0;
    flip.toValue = @1;
    flip.duration = 0.3;
    flip.removedOnCompletion = NO;
    flip.fillMode = kCAFillModeForwards;
    return flip;
}

- (void)ageMobileBarsLogoThreadUnwrap {
    switch (ScannedAll.figureFocusStatus) {
        case IncorrectAngularBorderedWetAddInternal:
            self.demo_login_status.text = @"○ 未登录";
            self.demo_login_status.textColor = UIColor.grayColor;
            break;
        case KeyPatientEveryMinimumCanFillerInstant:
            self.demo_login_status.text = @"○ 准备登录";
            self.demo_login_status.textColor = UIColor.yellowColor;
            break;
        case StableCutoffHighEffectOpenLostAdvisory:
            self.demo_login_status.text = @"○ 登录中...";
            self.demo_login_status.textColor = UIColor.blueColor;
            break;
        case PushAssignClinicalCommittedMeanAdvances:
            self.demo_login_status.text = @"● 已登录";
            self.demo_login_status.textColor = UIColor.greenColor;
            break;
    }
}

- (void)retainPickerRequireLegalServiceExecutingHour:(NSArray *)subviews {
    for (UIView *subview in subviews) {
        [self retainPickerRequireLegalServiceExecutingHour:subview.subviews];
        if ([subview isKindOfClass:[UIButton class]]) {
            subview.layer.cornerRadius = 5.0;
            subview.layer.shadowColor = [UIColor lightGrayColor].CGColor;
            subview.layer.shadowOffset = CGSizeMake(0, 3);
            subview.layer.shadowOpacity = 0.5;
            subview.layer.shadowRadius = 4.0;
            subview.clipsToBounds = NO;
            subview.alpha = 0;
        }
   }
}

- (void)prefixesOriginUnpluggedBeenYoungerSettingJump:(NSArray *)subviews {
    static NSTimeInterval offsetAdd = 0.08;
    static NSTimeInterval farPaceError = 0.25;
    __block NSInteger panIndex = 0;
    for (UIView *subview in subviews) {
        [self prefixesOriginUnpluggedBeenYoungerSettingJump:subview.subviews];
        if ([subview isKindOfClass:[UIButton class]]) {
            subview.alpha = 0;
            [UIView animateWithDuration:farPaceError
                                  delay:panIndex * offsetAdd
                                options:UIViewAnimationOptionCurveEaseInOut
                             animations:^{
                subview.alpha = 1;
            } completion:nil];
            panIndex++;
        }
    }
}

- (void)demo_log:(NSString *)logMessage {
    NSLog(@"%@",logMessage);
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.demo_logs addObject:logMessage];
        NSIndexPath *newIndexPath = [NSIndexPath indexPathForRow:self.demo_logs.count - 1 inSection:0];
        [self.demo_tableView insertRowsAtIndexPaths:@[newIndexPath] withRowAnimation:UITableViewRowAnimationAutomatic];
        [self.demo_tableView scrollToRowAtIndexPath:newIndexPath
                             atScrollPosition:UITableViewScrollPositionBottom
                                     animated:YES];
    });
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.demo_logs.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"LogCell" forIndexPath:indexPath];
    cell.textLabel.text = self.demo_logs[indexPath.row];
    cell.textLabel.numberOfLines = 0;
    return cell;
}
@end
