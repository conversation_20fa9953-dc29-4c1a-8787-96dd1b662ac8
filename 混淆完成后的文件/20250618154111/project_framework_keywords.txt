AFDMAService
AFSDKAppGroupStorage
AFSDKBuffer
AFSDKCancellableTimer
AFSDKChecksum
AFSDKConversionValueURLParams
AFSDKDevice
AFSDKEXMConfiguration
AFSDKError
AFSDKEvent
AFSDKEventCache
AFSDKException
AFSDKExceptionCache
AFSDKHTTPClientFactory
AFSDKKeychainFactory
AFSDKLog
AFSDKLogBaseClient
AFSDKLogConsoleClient
AFSDKLogExceptionClient
AFSDKLogMessage
AFSDKLogProxyClient
AFSDKOperation
AFSDKPerformance
AFSDKPluginInfo
AFSDKRCConfiguration
AFSDKRCFeatureFactory
AFSDKRCFetch
AFSDKRCFetchResult
AFSDKRCMetadata
AFSDKRCSettings
AFSDKRCUserDefaultsStorage
AFSDKRCValidator
AFSDKRemoteControl
AFSDKRemoteControlFeatureBaseClass
AFSDKRemoteDebugFeatureConfig
AFSDKRemoteDebuggerClient
AFSDKRouter
AFSDKSKANErrorReport
AFSDKSKANMonitoringReport
AFSDKSKAdNetwork
AFSDKSKAdNetworkBaseImpl
AFSDKSKAdNetworkFactory
AFSDKSKAdNetworkOldImpl
AFSDKSKAdNetworkS2SMessage
AFSDKSKAdNetworkV3Impl
AFSDKSKAdNetworkV4Impl
AFSDKStorageFactory
AFSDKTimer
AFSDKUserDefaultsStorage
AFSDKiAdClient
AFSDKmacOSKeychain
AFSystemInfo
APMAEU
APMASLLogger
APMAdExposureReporter
APMAlarm
APMAnalytics
APMAppDelegateInterceptor
APMAppMetadata
APMAppStaticConfig
APMAudience
APMAudienceComparisonValues
APMAudienceTimestampsCache
APMBackwardCompatibilityUtil
APMConditionalUserProperty
APMConditionalUserPropertyController
APMConsentSettingsUtil
APMDailyCounts
APMDataTypeValidator
APMDatabase
APMDefaultIdentitySupport
APMEClient
APMEDatabase
APMEExperiment
APMEExperimentRequestBuilder
APMEManager
APMEMonitor
APMESDKProperty
APMESnapshot
APMETaskManager
APMEnvironmentInfo
APMEvent
APMEventAggregates
APMEventFilter
APMFilterResult
APMIdentifiers
APMIdentity
APMInAppPurchaseItem
APMInAppPurchaseProductCache
APMInAppPurchaseTransactionReporter
APMInfoPlistFileUtil
APMKeychainWrapper
APMLifetimeValueRecorder
APMMeasurement
APMMonitor
APMMonitoringSampledData
APMNumericUtil
APMPArrayCommandEvaluator
APMPArrayValue
APMPBAdCampaignInfo
APMPBAppProperty
APMPBAudience
APMPBAudienceLeafFilterResult
APMPBClientProperty
APMPBConsentConfig
APMPBConsentDefaultEntry
APMPBConsentDelegationEntry
APMPBCorePlatformServiceEntry
APMPBCustomProperty
APMPBDataControl
APMPBDynamicFilterResultTimestamp
APMPBEvent
APMPBEventConfig
APMPBEventFilter
APMPBEventParam
APMPBExperiment
APMPBExperimentRequest
APMPBExperimentResponse
APMPBFilter
APMPBFlag
APMPBMeasurementBatch
APMPBMeasurementBundle
APMPBMeasurementConfig
APMPBNumberFilter
APMPBOgtActivities
APMPBOgtActivity
APMPBProgram
APMPBPropertyFilter
APMPBPsmSetting
APMPBRegion
APMPBResultData
APMPBRuntimeEntity
APMPBSdkResponse
APMPBSequenceFilterResultTimestamp
APMPBSetting
APMPBSgtmSetting
APMPBSkAdNetworkConversionValueRequest
APMPBSkAdNetworkConversionValueResponse
APMPBSnapshot
APMPBStringFilter
APMPBUserAttribute
APMPBitwiseCommandEvaluator
APMPBooleanValue
APMPCommandEvaluator
APMPCommandRegistrar
APMPComparisonCommandEvaluator
APMPControlCommandEvaluator
APMPControlValue
APMPDynamicProvider
APMPEditingCallbackHandler
APMPEvaluator
APMPEvent
APMPEventContext
APMPEventEditing
APMPEventLogger
APMPEventNativeFunctions
APMPFunctionEvaluator
APMPFunctionGetVersion
APMPFunctionLog
APMPFunctionLogSilent
APMPFunctionLogUnmonitored
APMPFunctionValue
APMPKeyValueStore
APMPKeyValueStoreGetValue
APMPLogicalCommandEvaluator
APMPLoopCommandEvaluator
APMPMapValue
APMPMathCommandEvaluator
APMPMessageLogger
APMPNullValue
APMPNumberValue
APMPObjectManager
APMPObjectProvider
APMPPropertyMap
APMPRegisterCallbackNativeFunctionValue
APMPRequire
APMPRuntime
APMPRuntimeEntityConverter
APMPRuntimePlatform
APMPRuntimeValue
APMPScope
APMPStatementValue
APMPStringCommandEvaluator
APMPStringValue
APMPUndefinedValue
APMPVariableCommandEvaluator
APMPersistedConfig
APMPersistentDictionary
APMPixieLogger
APMPlatformIdentitySupport
APMProductsRequest
APMPropertyFilter
APMRawEventData
APMRemoteConfig
APMScheduler
APMScreen
APMScreenViewReporter
APMSearchAdReporter
APMSequenceTimestampsCache
APMSessionReporter
APMSqliteStore
APMTCFClient
APMTCFData
APMUserAttribute
APMUserDefaults
APMValue
AppsFlyerAES128Crypto
AppsFlyerConsent
AppsFlyerCrossPromotionHelper
AppsFlyerDeepLink
AppsFlyerDeepLinkResult
AppsFlyerDictionary
AppsFlyerHTTPClient
AppsFlyerKeychainWrapper
AppsFlyerLib
AppsFlyerLinkGenerator
AppsFlyerShareInviteHelper
AppsFlyerUtils
CATransform3DConcat
CATransform3DMakeScale
CATransform3DMakeTranslation
CoreUIApplication
Defines
FBAEMReporter
FBLPromise
FBSDKAEMManager
FBSDKATEPublisherFactory
FBSDKAccessToken
FBSDKAppEvents
FBSDKAppEventsATEPublisher
FBSDKAppEventsCAPIManager
FBSDKAppEventsConfiguration
FBSDKAppEventsConfigurationManager
FBSDKAppEventsDeviceInfo
FBSDKAppEventsNumberParser
FBSDKAppEventsState
FBSDKAppEventsStateManager
FBSDKAppEventsUtility
FBSDKAppInviteContent
FBSDKAppInviteDestination
FBSDKAppLink
FBSDKAppLinkNavigation
FBSDKAppLinkResolver
FBSDKAppLinkTarget
FBSDKAppLinkUtility
FBSDKApplicationDelegate
FBSDKAuthenticationStatusUtility
FBSDKAuthenticationToken
FBSDKAuthenticationTokenClaims
FBSDKBase64
FBSDKBasicUtility
FBSDKBlocklistEventsManager
FBSDKBridgeAPIProtocolNativeV1
FBSDKBridgeAPIProtocolWebV1
FBSDKBridgeAPIProtocolWebV2
FBSDKBridgeAPIRequest
FBSDKBridgeAPIRequestFactory
FBSDKBridgeAPIResponse
FBSDKButton
FBSDKCameraEffectArguments
FBSDKCameraEffectTextures
FBSDKChooseContextContent
FBSDKChooseContextDialog
FBSDKChooseContextFilter
FBSDKCloseIcon
FBSDKCodeVerifier
FBSDKCodelessIndexer
FBSDKCodelessParameterComponent
FBSDKCodelessPathComponent
FBSDKContainerViewController
FBSDKContextDialogPresenter
FBSDKContextWebDialog
FBSDKCrashHandler
FBSDKCrashObserver
FBSDKCrashShield
FBSDKCreateContextContent
FBSDKCreateContextDialog
FBSDKDefaultAudience
FBSDKDeviceLoginCodeInfo
FBSDKDeviceLoginManager
FBSDKDeviceLoginManagerResult
FBSDKDialogConfiguration
FBSDKDialogConfigurationMapBuilder
FBSDKDomainConfiguration
FBSDKDomainConfigurationManager
FBSDKDomainHandler
FBSDKDynamicFrameworkLoader
FBSDKDynamicFrameworkLoaderProxy
FBSDKErrorConfiguration
FBSDKErrorConfigurationProvider
FBSDKErrorFactory
FBSDKErrorRecoveryAttempter
FBSDKErrorRecoveryConfiguration
FBSDKErrorReporter
FBSDKEventBinding
FBSDKEventBindingManager
FBSDKFeatureExtractor
FBSDKFeatureManager
FBSDKFriendFinderDialog
FBSDKGameRequestActionType
FBSDKGameRequestContent
FBSDKGameRequestDialog
FBSDKGameRequestFilter
FBSDKGameRequestURLProvider
FBSDKGamingContext
FBSDKGamingGroupIntegration
FBSDKGamingImageUploader
FBSDKGamingImageUploaderConfiguration
FBSDKGamingPayload
FBSDKGamingPayloadObserver
FBSDKGamingVideoUploader
FBSDKGamingVideoUploaderConfiguration
FBSDKGateKeeperManager
FBSDKGraphErrorRecoveryProcessor
FBSDKGraphRequest
FBSDKGraphRequestBody
FBSDKGraphRequestConnection
FBSDKGraphRequestConnectionFactory
FBSDKGraphRequestDataAttachment
FBSDKGraphRequestFactory
FBSDKGraphRequestMetadata
FBSDKGraphRequestQueue
FBSDKHashtag
FBSDKHumanSilhouetteIcon
FBSDKHybridAppEventsScriptMessageHandler
FBSDKIcon
FBSDKImageDownloader
FBSDKImpressionLoggerFactory
FBSDKImpressionLoggingButton
FBSDKInstrumentManager
FBSDKIntegrityManager
FBSDKInternalUtility
FBSDKKeychainStore
FBSDKLibAnalyzer
FBSDKLocation
FBSDKLogger
FBSDKLoggerFactory
FBSDKLoginButton
FBSDKLoginButtonTooltipBehavior
FBSDKLoginCompletionParameters
FBSDKLoginConfiguration
FBSDKLoginManager
FBSDKLoginManagerLoginResult
FBSDKLoginTooltip
FBSDKLoginTooltipView
FBSDKLoginTracking
FBSDKLogo
FBSDKMACARuleMatchingManager
FBSDKMath
FBSDKMeasurementEvent
FBSDKMeasurementEventListener
FBSDKMessageDialog
FBSDKMetadataIndexer
FBSDKModelManager
FBSDKModelParser
FBSDKModelUtility
FBSDKNetworkErrorChecker
FBSDKObjectDecoder
FBSDKPaymentObserver
FBSDKPaymentProductRequestor
FBSDKPaymentProductRequestorFactory
FBSDKPermission
FBSDKProductRequestFactory
FBSDKProfile
FBSDKProfilePictureMode
FBSDKProfilePictureView
FBSDKProtectedModeManager
FBSDKRedactedEventsManager
FBSDKRestrictiveDataFilterManager
FBSDKRestrictiveEventFilter
FBSDKSKAdNetworkCoarseCVConfig
FBSDKSKAdNetworkCoarseCVRule
FBSDKSKAdNetworkConversionConfiguration
FBSDKSKAdNetworkEvent
FBSDKSKAdNetworkLockWindowRule
FBSDKSKAdNetworkReporter
FBSDKSKAdNetworkReporterV2
FBSDKSKAdNetworkRule
FBSDKSKAdnetworkUtils
FBSDKSendButton
FBSDKServerConfiguration
FBSDKServerConfigurationManager
FBSDKSettings
FBSDKShareButton
FBSDKShareCameraEffectContent
FBSDKShareDialog
FBSDKShareDialogMode
FBSDKShareLinkContent
FBSDKShareMediaContent
FBSDKSharePhoto
FBSDKSharePhotoContent
FBSDKShareVideo
FBSDKShareVideoContent
FBSDKShimGraphRequestInterceptor
FBSDKSuggestedEventsIndexer
FBSDKSwitchContextContent
FBSDKSwitchContextDialog
FBSDKSwizzle
FBSDKSwizzler
FBSDKSwizzlingOnClass
FBSDKTemporaryErrorRecoveryAttempter
FBSDKTimeSpentData
FBSDKTooltipColorStyle
FBSDKTooltipView
FBSDKTooltipViewArrowDirection
FBSDKTransformer
FBSDKTransformerGraphRequestFactory
FBSDKTypeUtility
FBSDKURL
FBSDKURLSession
FBSDKURLSessionProxyFactory
FBSDKURLSessionTask
FBSDKUnarchiverProvider
FBSDKUserAgeRange
FBSDKUserDataStore
FBSDKUtility
FBSDKViewHierarchy
FBSDKViewImpressionLogger
FBSDKWebDialog
FBSDKWebDialogView
FBSDKWebViewAppLinkResolver
FBSDKWebViewAppLinkResolverWebViewDelegate
FBSDKWebViewFactory
FIRAAdExposureReporter
FIRAConditionalUserProperty
FIRAConditionalUserPropertyController
FIRAEvent
FIRAIdentifiers
FIRAMeasurement
FIRAScreenViewReporter
FIRAUserAttribute
FIRAValue
FIRAnalytics
FIRAnalyticsConfiguration
FIRAnalyticsConnector
FIRApp
FIRBundleUtil
FIRCLSAnalyticsManager
FIRCLSApplicationIdentifierModel
FIRCLSAsyncOperation
FIRCLSCallStackTree
FIRCLSCodeMapping
FIRCLSCompoundOperation
FIRCLSContextInitData
FIRCLSContextManager
FIRCLSDataCollectionArbiter
FIRCLSDataCollectionToken
FIRCLSDemangleOperation
FIRCLSDownloadAndSaveSettingsOperation
FIRCLSEncodedRolloutAssignment
FIRCLSEncodedRolloutsState
FIRCLSExecutionIdentifierModel
FIRCLSExistingReportManager
FIRCLSFABAsyncOperation
FIRCLSFABNetworkClient
FIRCLSFileManager
FIRCLSFrame
FIRCLSInstallIdentifierModel
FIRCLSInternalReport
FIRCLSLaunchMarkerModel
FIRCLSMachOBinary
FIRCLSMachOSlice
FIRCLSManagerData
FIRCLSMetricKitManager
FIRCLSNetworkOperation
FIRCLSNetworkResponseHandler
FIRCLSNotificationManager
FIRCLSOnDemandModel
FIRCLSProcessReportOperation
FIRCLSRecordApplication
FIRCLSRecordBase
FIRCLSRecordHost
FIRCLSRecordIdentity
FIRCLSRemoteConfigManager
FIRCLSReportAdapter
FIRCLSReportManager
FIRCLSReportUploader
FIRCLSRolloutsPersistenceManager
FIRCLSSerializeSymbolicatedFramesOperation
FIRCLSSettings
FIRCLSSettingsManager
FIRCLSSymbolResolver
FIRCLSSymbolicationOperation
FIRCLSThread
FIRCLSThreadArrayOperation
FIRCLSURLBuilder
FIRCLSUserDefaults
FIRCLSdSYM
FIRCLSwiftFileUtility
FIRComponent
FIRComponentContainer
FIRComponentType
FIRConfiguration
FIRConnectorUtils
FIRCrashlytics
FIRCrashlyticsReport
FIRExceptionModel
FIRFirebaseUserAgent
FIRHeartbeatController
FIRHeartbeatLogger
FIRHeartbeatsPayload
FIRInstallations
FIRInstallationsAPIService
FIRInstallationsAuthTokenResult
FIRInstallationsBackoffController
FIRInstallationsBackoffEventData
FIRInstallationsErrorUtil
FIRInstallationsHTTPError
FIRInstallationsIDController
FIRInstallationsIIDStore
FIRInstallationsIIDTokenInfo
FIRInstallationsIIDTokenStore
FIRInstallationsItem
FIRInstallationsSingleOperationPromiseCache
FIRInstallationsStore
FIRInstallationsStoredAuthToken
FIRInstallationsStoredItem
FIRInstallationsURLSessionResponse
FIRLoggerWrapper
FIRNamespaceGoogleMobilePlatform
FIROptions
FIRRemoteConfigConstants
FIRRolloutAssignment
FIRRolloutsState
FIRSessionDetails
FIRSessions
FIRSessionsDependencies
FIRSessionsSubscriberName
FIRStackFrame
FIRTimestamp
GDTCCTCompressionHelper
GDTCCTURLSessionDataResponse
GDTCCTUploadOperation
GDTCCTUploader
GDTCORApplication
GDTCORClock
GDTCORDirectorySizeTracker
GDTCOREndpoints
GDTCOREvent
GDTCORFlatFileStorage
GDTCORLifecycle
GDTCORLogSourceMetrics
GDTCORMetrics
GDTCORMetricsController
GDTCORMetricsMetadata
GDTCORProductData
GDTCORReachability
GDTCORRegistrar
GDTCORStorageEventSelector
GDTCORStorageMetadata
GDTCORTransformer
GDTCORTransport
GDTCORUploadBatch
GDTCORUploadCoordinator
GULAppDelegateObserver
GULAppDelegateSwizzler
GULAppEnvironmentUtil
GULKeychainStorage
GULKeychainUtils
GULLoggerWrapper
GULMutableDictionary
GULNetwork
GULNetworkInfo
GULNetworkURLSession
GULNetwork_logWithLevel
GULReachabilityChecker
GULSceneDelegateSwizzler
GULSceneZeroingWeakContainer
GULSwizzler
GULUserDefaults
GULZeroingWeakContainer
HTTPMethod
JPEGCompressionQuality
JSONObjectWithData
JSONStringForEventsAndOperationalParametersIncludingImplicitEvents
JSONStringForObject
NSArray
NSBundle
NSData
NSDictionary
NSFileManager
NSNotificationCenter
NSNumber
NSProcessInfo
NSString
NSURLSession
NSURLSessionTask
NSUserDefaults
NS_REFINED_FOR_SWIFT
NS_SWIFT_NAME
OS_WEAK
PodsDummy_FirebaseAnalytics
PodsDummy_FirebaseCore
PodsDummy_FirebaseCoreExtension
PodsDummy_FirebaseCoreInternal
PodsDummy_FirebaseCrashlytics
PodsDummy_FirebaseInstallations
PodsDummy_FirebaseRemoteConfigInterop
PodsDummy_FirebaseSessions
PodsDummy_GoogleAppMeasurement
PodsDummy_GoogleAppMeasurementIdentitySupport
PodsDummy_GoogleDataTransport
PodsDummy_GoogleUtilities
PodsDummy_PromisesObjC
PodsDummy_PromisesSwift
PodsDummy_nanopb
SHA256Hash
SKAdNetwork
SWIFT_COMPILE_NAME
SWIFT_ENUM_ATTR
ServerConfigurationProvider
ShareTournamentDialog
UIApplication
UIPasteboard
URLDecode
URLEncode
URLWithInboundURL
URLWithScheme
URLWithURL
WKWebView
_BridgeAPI
_FBSDKAccessTokenExpirer
_Nonnull
_Nullable
_TtC12FBSDKCoreKit10_BridgeAPI
_TtC12FBSDKCoreKit12AEMNetworker
_TtC12FBSDKCoreKit16IAPEventResolver
_TtC12FBSDKCoreKit17AppLinkURLFactory
_TtC12FBSDKCoreKit17CoreUIApplication
_TtC12FBSDKCoreKit19BannedParamsManager
_TtC12FBSDKCoreKit19IAPTransactionCache
_TtC12FBSDKCoreKit20IAPSKProductsRequest
_TtC12FBSDKCoreKit20IAPTransactionLogger
_TtC12FBSDKCoreKit22IAPTransactionObserver
_TtC12FBSDKCoreKit22SensitiveParamsManager
_TtC12FBSDKCoreKit25FBSDKAppEventsCAPIManager
_TtC12FBSDKCoreKit25_BridgeAPIResponseFactory
_TtC12FBSDKCoreKit26StdParamEnforcementManager
_TtC12FBSDKCoreKit27ServerConfigurationProvider
_TtC12FBSDKCoreKit29AppLinkResolverRequestBuilder
_TtC12FBSDKCoreKit35FBSDKTransformerGraphRequestFactory
_TtC13FBSDKLoginKit14ProfileFactory
_TtC16FirebaseSessions17SessionStartEvent
_TtC22FBSDKGamingServicesKit17FileHandleFactory
_TtC22FBSDKGamingServicesKit21ShareTournamentDialog
_TtC22FBSDKGamingServicesKit23GamingServiceController
_TtC22FBSDKGamingServicesKit25CustomUpdateLocalizedText
_TtC8FBAEMKit12AEMNetworker
_TtC8FBAEMKit13AEMInvocation
_TtC8FBAEMKit16AEMConfiguration
_TtC8FBAEMKit27AEMAdvertiserMultiEntryRule
_TtC8FBAEMKit28AEMAdvertiserSingleEntryRule
_TtC8FBAEMKit7AEMRule
_TtC8FBAEMKit8AEMEvent
__attribute__
__instanceForProtocol
_firebase_appquality_sessions_DataCollectionState
_firebase_appquality_sessions_EventType
_firebase_appquality_sessions_LogEnvironment
_firebase_appquality_sessions_NetworkConnectionInfo_MobileSubtype
_firebase_appquality_sessions_NetworkConnectionInfo_NetworkType
_firebase_appquality_sessions_OsName
_isGeneralPasteboard
_name
accessTokenWallet
actionTypeNameForActionType
activateApp
activityParametersDictionaryForEvent
addDependencyWithName
addEvent
addEventsFromAppEventState
addObserver
addParameterValue
addParameters
addPendingObject
addPiggybackRequests
addPushNotificationDeepLinkPath
addRefreshPiggyback
addRequest
addTransaction
advertiserIDCollectionEnabled
advertiserTrackingEnabled
advertisingIDEnabled
advertisingTrackingStatus
ageRangeFromDictionary
all
allEventsImplicit
allHTTPHeaderFieldsForResolveDeepLinkURL
allOn
always
alwaysOn
analyze
anonymousID
any
anyOn
appEventsConfigurationProvider
appInstanceID
appInvitePromotionCodeFromURL
appLinkFromURL
appLinkReferer
appLinkTargetWithURL
appLinkWithSourceURL
appLinksFrom
appNamed
appURLWithHost
appWillBeDeleted
appendParametersToDeepLinkingURLWithString
appleDevicePlatform
applePlatform
application
applicationDidBecomeActive
applicationDidEnterBackground
applicationWillResignActive
array
arrayForKey
arrayValue
async
asyncHeaderValueWithCompletionHandler
asyncOn
attemptRecoveryFromError
attempts
augmentHybridWebView
authTokenForcingRefresh
authTokenWithCompletion
authenticationTokenWallet
autoLogAppEventsEnabled
backToReferrer
base64FromBase64Url
boolForKey
boolValue
bounds
bridgeAPIRequestWithProtocolType
bridgeAPIResponseCancelledWithRequest
bridgeAPIResponseWithRequest
buildDialogConfigurationMapWithRawConfigurations
cachedAppEventsConfiguration
cachedDomainConfiguration
cachedServerConfiguration
callCapiGatewayAPIWith
callbackAppLinkDataForAppWithName
canOpenURL
canShow
cancel
cancelled
catch
catchOn
ceilForSize
checkAndRevokeTimer
checkAndUpdateUnsentReportsWithCompletion
checkAuthenticationStatus
checkFeature
checkForUnsentReportsWithCompletion
checkImplicitlyDisabled
checkRegisteredCanOpenURLScheme
claims
clearCache
clearCrashReportFiles
clearPersistedAppEventsStates
clearUserData
clearUserDataForType
clockSnapshotInTheFuture
codelessDebugLogEnabled
codelessEventsEnabled
coercedToStringValue
coercedToURLValue
compare
completionHandler
componentWithProtocol
componentsToRegister
configure
configureButton
configureNonTVComponentsWithOnDeviceMLModelManager
configureWithAppEventsConfigurationProvider
configureWithApplicationActivationNotifier
configureWithDatasetID
configureWithFactory
configureWithFeatureChecker
configureWithGateKeeperManager
configureWithGraphRequestConnectionFactory
configureWithGraphRequestFactory
configureWithIcon
configureWithImpressionLoggerFactory
configureWithInfoDictionaryProvider
configureWithInternalURLOpener
configureWithName
configureWithNetworker
configureWithOptions
configureWithProfileSetter
configureWithRulesFromKeyProvider
configureWithSettings
configureWithStore
configureWithSwizzler
configureWithTokenCache
configureWithURLSessionProxyFactory
configureWithWebViewProvider
const
contains
contentMode
contextDialog
contextDialogDidCancel
contextDialogDidComplete
continueUserActivity
conversionValueUpdater
convertRequestValue
convertToUnixTime
count
crashlytics
createAppLinkTargetWithURL
createAppLinkURLWithURL
createAppLinkWithSourceURL
createDeepLinkURLWithQueryDictionary
createGraphRequestConnection
createGraphRequestWithGraphPath
createIAPFailedTransactionLogging
createInsecureUnarchiverFor
createKeychainStoreWithService
createLoggerWithLoggingBehavior
createPublisherWithAppID
createRequestorWithTransaction
createSecureUnarchiverFor
createSessionProxyWithDelegate
createStateWithToken
createWebDialogFrameWithWidth
createWebViewWithFrame
createWithProductIdentifiers
currentAccessTokenIsActive
currentContext
currentImplementationForClass
currentProfile
dataAccessExpired
dataExtractor
dataForKey
dataForPasteboardType
dataStore
dataWithJSONObject
dateValue
debugLogWithMessage
declarations
decodeAsData
decodeAsString
decodeObjectOfClass
decodeObjectOfClasses
defaultATEStatus
defaultApp
defaultBackgroundColor
defaultConfiguration
defaultConnectionTimeout
defaultOptions
defaultResolver
defaults
delay
delayOn
delegate
deleteApp
deleteUnsentReports
deleteWithCompletion
density
deploymentType
deviceInformationProvider
deviceLoginManager
deviceModel
deviceSimulatorModel
dialogConfigurationForDialogName
dialogConfigurationMapBuilder
dialogWithContent
dialogWithViewController
dictionary
dictionaryForKey
dictionaryValue
dictionaryWithQueryString
didCrashDuringPreviousExecution
didMoveToWindow
didReceiveCrashLogs
didResolveDeepLink
disable
disableFeature
disableLoggingBehavior
dismiss
displayChildController
displayDuration
do
doLogEvent
doOn
doubleForKey
doubleValue
drawRect
enable
enableAutoSetup
enableFacebookDeferredApplinksWithClass
enableLoggingBehavior
enableTCFDataCollection
enableUpdatesOnAccessTokenChange
enabled
encodeString
encodeWithCoder
enqueueRequest
enqueueRequestMetadata
enqueueRequests
ensureOnMainThread
errorCategory
errorConfiguration
errorFactory
errorWithCode
errorWithDomain
eventCollectionEnabled
eventForTransport
eventForTransportWithProductData
eventLogger
exceptionModelWithName
executeURLRequest
executeWithRequest
expired
extendDictionaryWithDataProcessingOptions
extractPermissionsFromResponse
extractReceiptData
facebookURLWithHostPrefix
fb_addObserver
fb_addObserverForName
fb_boolForKey
fb_cancel
fb_contentsOfDirectoryAtPath
fb_createDirectoryAtPath
fb_dataForKey
fb_dataTaskWithRequest
fb_dataWithContentsOfFile
fb_fileExistsAtPath
fb_integerForKey
fb_isMacCatalystApp
fb_isOperatingSystemAtLeastVersion
fb_objectForInfoDictionaryKey
fb_objectForKey
fb_postNotificationName
fb_removeItemAtPath
fb_removeObjectForKey
fb_removeObserver
fb_resume
fb_setBool
fb_setInteger
fb_setObject
fb_state
fb_stringForKey
fetchCachedProfile
fetchDeferredAppLink
fileManager
filtersNameForFilters
findWindow
firebaseUserAgent
firebase_appquality_sessions_AndroidApplicationInfo_fields
firebase_appquality_sessions_AppleApplicationInfo_fields
firebase_appquality_sessions_ApplicationInfo_fields
firebase_appquality_sessions_DataCollectionStatus_fields
firebase_appquality_sessions_NetworkConnectionInfo_fields
firebase_appquality_sessions_SessionEvent_fields
firebase_appquality_sessions_SessionInfo_fields
flags
floatForKey
floorForSize
flush
flushAsyncWithCompletionHandler
flushBehavior
flushForReason
flushHeartbeatFromToday
flushHeartbeatsIntoPayload
flushHeartbeatsIntoPayloadWithCompletionHandler
flushReasonToString
forAppEvents
forceDisplay
formattedDescription
frame
fulfill
gameRequestDialog
gameRequestDialogDidCancel
generateInviteUrlWithLinkGenerator
getATTScopeEndpointForGraphPath
getAppsFlyerUID
getCampaignIDs
getCleanedGraphPathFromRequest
getDenseFeatures
getFBSDKVersion
getGraphDomainFromToken
getIdiomSpecificField
getInternalHashedDataForType
getItemWithQuery
getMethodsTable
getNetworkRadioType
getNetworkType
getObjectForKey
getRolloutAssignmentsEncodedJsonString
getRulesForKey
getSDKVersion
getTextFeature
getThresholdsForKey
getURL
getURLPrefixForBatchRequest
getURLPrefixForSingleRequest
getUserData
getWeightsForKey
graphErrorRecoveryDisabled
graphRequestConnectionFactory
graphRequestFactory
gul_dataByGzippingData
gul_dataByInflatingGzippedData
gzip
handle
handleEventsForBackgroundURLSession
handleEventsForBackgroundURLSessionID
handleOpenURL
handleOpenUrl
handlePushNotification
handleUserActivity
handler
hasAttachments
hasConsentForAdsPersonalization
hasConsentForDataUsage
hasGranted
hasRestoredPurchases
hash
hashWithInteger
hashWithIntegerArray
headerValue
heartbeatCodeForToday
height
hexadecimalStringFromData
iapManualAndAutoLogDedupWindow
iapObservationTime
id
imageForKey
imageRectForContentRect
imageURLForPictureMode
imageWithSize
implicitLoggingEnabled
implicitPurchaseLoggingEnabled
implicitlyDisabled
impressionTrackingEventName
indicating
init
initDialogContentWithContextID
initDialogContentWithPlayerID
initForGDPRUserWithHasConsentForDataUsage
initInstanceWithName
initNonGDPRUser
initWith
initWithAccessTokenString
initWithAppID
initWithAppLink
initWithAppLinkURL
initWithAppScheme
initWithAssignmentList
initWithCoder
initWithContent
initWithContentsOfFile
initWithData
initWithDataStore
initWithDefaultAudience
initWithDelegate
initWithDictionary
initWithEncodedClaims
initWithEventLogger
initWithEventName
initWithFeatureChecker
initWithFrame
initWithGoogleAppID
initWithGraphPath
initWithGraphRequestFactory
initWithId
initWithIdentifier
initWithImage
initWithImageURL
initWithJSON
initWithLoggingBehavior
initWithMappingID
initWithName
initWithNetworkLoggerDelegate
initWithNotificationCenter
initWithPaymentQueue
initWithPermissions
initWithPhotoAsset
initWithProductID
initWithProfile
initWithReachabilityDelegate
initWithReachabilityHost
initWithRecoveryDescription
initWithRemoteConfig
initWithRequest
initWithRolloutId
initWithSeconds
initWithServerConfigurationProvider
initWithService
initWithSourceURL
initWithString
initWithSuiteName
initWithSymbol
initWithTagline
initWithText
initWithTimestamp
initWithToken
initWithTokenString
initWithTracking
initWithTransaction
initWithURL
initWithUserDataStore
initWithUserID
initWithVideoAsset
initWithVideoURL
initWithViewController
initializeSDK
initiateOnDeviceConversionMeasurementWithEmailAddress
initiateOnDeviceConversionMeasurementWithHashedEmailAddress
initiateOnDeviceConversionMeasurementWithHashedPhoneNumber
initiateOnDeviceConversionMeasurementWithPhoneNumber
installationIDWithCompletion
installations
installationsWithApp
instanceForProtocol
integerForKey
integerValue
integrityParametersProcessor
internalUtility
invalidArgumentErrorWithDomain
invalidArgumentErrorWithName
invalidateAndCancel
invokeCompletionHandlerForConnection
is
isATETimeSufficientlyDelayed
isAdvertiserIDCollectionEnabled
isAdvertiserTrackingEnabled
isAfter
isAppDelegateProxyEnabled
isAppExtension
isAppStoreReceiptSandbox
isAuthenticatedForGamingDomain
isAuthenticationURL
isAutoAppLink
isAutoLogAppEventsEnabled
isBrowserURL
isCancelled
isCodelessDebugLogEnabled
isCompatibleWithAppEventsState
isCompatibleWithTokenString
isCrashlyticsCollectionEnabled
isDataCollectionEnabled
isDataProcessingRestricted
isDebugBuild
isDefaultAppConfigured
isDeferred
isDomainErrorEnabled
isDomainHandlingEnabled
isEmpty
isEnabled
isEqual
isEqualToAccessToken
isEqualToGameRequestContent
isEventDataUsageLimited
isFacebookAppInstalled
isFrictionlessRequestsEnabled
isFromAppStore
isGraphErrorRecoveryEnabled
isMatchURLScheme
isMessengerAppInstalled
isNetworkError
isProtectedModeAppliedWithParameters
isRegisteredCanOpenURLScheme
isRegisteredURLScheme
isReportingEvent
isSKAdNetworkReportEnabled
isSceneDelegateProxyEnabled
isSensitiveUserData
isSetATETimeExceedsInstallTime
isSimulator
isStandardEvent
isUnity
isUpdatedWithAccessTokenChange
isUserGenerated
isUserSubjectToGDPR
isValid
ivarObjectsForObject
kGULNetworkHTTPStatusCodeCannotAcceptTraffic
kGULNetworkHTTPStatusCodeFound
kGULNetworkHTTPStatusCodeMovedPermanently
kGULNetworkHTTPStatusCodeMovedTemporarily
kGULNetworkHTTPStatusCodeMultipleChoices
kGULNetworkHTTPStatusCodeNotFound
kGULNetworkHTTPStatusCodeNotModified
kGULNetworkHTTPStatusCodeUnavailable
kGULNetworkHTTPStatusNoContent
kGULNetworkHTTPStatusOK
kGULNetworkTempFolderExpireTime
kGULNetworkTimeOutInterval
launchFriendFinderDialogWithCompletionHandler
layoutSubviews
loadAppEventsConfigurationWithBlock
loadCurrentProfileWithCompletion
loadDomainConfigurationWithCompletionBlock
loadGateKeepers
loadRequest
loadRulesForKey
loadServerConfigurationWithCompletionBlock
loadURL
loadkSecAttrAccessibleAfterFirstUnlockThisDeviceOnly
locationFromDictionary
log
logAndNotify
logAndOpenStore
logAutoSetupStatus
logCrossPromoteImpression
logEntry
logEvent
logEventWithEventName
logEventWithName
logFailedStoreKit2Purchase
logIfSDKSettingsChanged
logImpressionWithIdentifier
logInFromViewController
logInWithPermissions
logInternalEvent
logInvite
logLocation
logOut
logProductItem
logPurchase
logPushNotificationOpen
logTapEventWithEventName
logWarnings
logWithFormat
logWithLevel
loggerLevel
loggerSerialNumber
loggingBehavior
loginButton
loginButtonDidLogOut
loginButtonWillLogin
loginTooltipEnabled
loginTooltipView
loginTooltipViewWillAppear
loginTooltipViewWillNotAppear
long
makeAndShowChooseContextDialogWithContent
makeAndShowCreateContextDialogWithContent
makeAndShowSwitchContextDialogWithContent
makeImpressionLoggerWithEventName
makeOpener
maxParticipants
minParticipants
mutableCopy
navigate
navigateToAppLink
navigateToURL
navigationDelegate
navigationType
navigationTypeForLink
navigationWithAppLink
netService
new
notificationCenter
numSkipped
numberValue
object
objectForJSONString
objectForKey
objectForKeyedSubscript
objectValue
offsetof
onAppOpenAttribution
onAppOpenAttributionFailure
onConversionDataFail
onConversionDataSuccess
onQueue
onSessionChanged
openBridgeAPIRequest
openGroupPageWithCompletionHandler
openURL
openURLWithSafariViewController
overrideGraphAPIVersion
parametersFromFBURL
parsedGameRequestURLContaining
parsedGamingContextURLContaining
parsedTournamentURLContaining
pathWith
paymentQueue
pending
pendingPromise
performOnAppAttributionWithURL
permissionsFromRawPermissions
persistAppEventsData
persistenceFilePath
phoneNumber
pollingInterval
postNotificationForEventName
postURL
presentFromView
presentInView
presentationAnchorForWebAuthenticationSession
printSwizzles
processError
processEvents
processImplicitEvent
processIntegrity
processInvalidDomainsIfNeeded
processLoadRequestResponse
processManualEvent
processParameters
processSavedEvents
processSuggestedEvents
processorDidAttemptRecovery
processorWillProcessError
profileSetter
protocol
protocolType
proxyOriginalDelegate
proxyOriginalDelegateIncludingAPNSMethods
proxyOriginalSceneDelegate
queryForKey
queryStringWithDictionary
race
raceOn
rawPermissionsFromPermissions
reachability
reachabilityDidChange
reauthorizeDataAccess
recordAndUpdateEvent
recordError
recordEvent
recordExceptionModel
recordInstall
recover
recoverOn
recoveryConfigurationForCode
reduce
reduceOn
refreshCurrentAccessTokenWithCompletion
registerAppDelegateInterceptor
registerAutoResetSourceApplication
registerForAppLinkMeasurementEvents
registerInternalLibrary
registerLibrary
registerRolloutsStateSubscriber
registerSceneDelegateInterceptor
registerTransientObject
registerUninstall
registerWithSubscriber
reject
remoteDebuggingCallV2WithData
remoteDebuggingCallWithData
removeAllObjects
removeItemWithQuery
removeObjectForKey
removeObserver
removeTransaction
request
requestConnection
requestConnectionDidFinishLoading
requestConnectionWillBeginLoading
requestForCustomAudienceThirdPartyIDWithAccessToken
requestForURLs
requestStartTime
requestToLoadServerConfiguration
requestURL
requestURLWithActionID
requiredArgumentErrorWithDomain
requiredArgumentErrorWithName
reset
resetAnalyticsData
resetApps
resetClassDependencies
resetCurrentAuthenticationTokenCache
resetDependencies
resolveAppLink
resolveProducts
resolved
resolvedWith
responseParametersForActionID
restore
retrieveLoggerWith
retrievePersistedAppEventsStates
retry
retryOn
rolloutsStateDidChange
rulesFromKeyProvider
safariViewControllerDidFinish
saveCampaignIDs
saveError
saveException
saveNonProcessedEvents
scheme
selector
sendDataEvent
sendEventBindingsToUnity
sendTelemetryEvent
sendUnsentReports
sessionDataTaskProvider
sessionIDFromAsyncGETRequest
sessionIDFromAsyncPOSTRequest
sessionIDWithCompletion
sessionTimeoutInterval
setAdvertiserRuleMatchInServerEnabled
setAnalyticsCollectionEnabled
setAppleAppID
setApplicationState
setArray
setBaseDeeplink
setBool
setCampaign
setCanMakeRequests
setCatalogMatchingEnabled
setChannel
setConsent
setConsentData
setConversionFilteringEnabled
setCrashlyticsCollectionEnabled
setCurrentContext
setCurrentProfile
setCustomKeysAndValues
setCustomValue
setData
setDataProcessingOptions
setDeeplinkPath
setDefaultDomainInfo
setDefaultEventParameters
setDefaultResolver
setDictionary
setDidFetchDomainConfiguration
setDouble
setEnabledRules
setFloat
setGraphErrorRecoveryDisabled
setHost
setImage
setInteger
setInternalHashData
setIsUnityInitialized
setIsUpdatedWithAccessTokenChange
setItem
setLoggerLevel
setNeedsImageUpdate
setObject
setPartnerDataWithPartnerId
setPluginInfoWith
setPushNotificationsDeviceToken
setPushNotificationsDeviceTokenString
setReferrerCustomerId
setReferrerImageURL
setReferrerName
setReferrerUID
setSessionTimeoutInterval
setSharingFilterForAllPartners
setSharingFilterForPartners
setSourceApplication
setString
setUpInteroperabilityObject
setUserData
setUserEmail
setUserEmails
setUserID
setUserPropertyString
settings
shared
sharedApplication
sharedInstance
sharedSettings
sharer
sharerDidCancel
shouldCutoff
shouldDedupeEvent
shouldDeferVisibility
shouldDropAppEvents
shouldFailOnDataError
shouldForceDisplay
shouldInterceptRequest
shouldLaunchMediaDialog
shouldStopPropagationOfURL
shouldUseCachedValuesForExpensiveMetadata
shouldUseTokenOptimizations
show
showFromViewController
showWithContent
singleShotLogEntry
size
sizeThatFits
skAdNetworkReportEnabled
smartLoginOptions
snapshot
stackFrameWithAddress
stackFrameWithSymbol
standardUserDefaults
start
startGCDTimerWithInterval
startGraphRequestWithGraphPath
startObserving
startObservingApplicationLifecycleNotifications
startObservingTransactions
startWithCompletion
startWithCompletionHandler
state
stop
stopGCDTimer
stopLoading
stopObserving
stopObservingTransactions
stringForKey
stringToHexConverterFor
stringValueOrNil
supportsSecureCoding
suspend
swizzleClass
swizzleSelector
symbolicateCallstack
systemVersion
task
templateVersion
textSizeForText
then
thenOn
timeIntervalValue
timeout
timeoutOn
timestamp
timestampWithDate
timestampWithSeconds
titleRectForContentRect
toString
tokenCache
tokenStringToUseFor
topMostViewController
transformGDTEvent
transportBytes
trimIfNeeded
uninstallTrackingEnabled
unixTimeNow
unknownErrorWithMessage
unregisterAppDelegateInterceptorWithID
unregisterSceneDelegateInterceptorWithID
unregisterTransientObject
unsignedIntegerValue
unswizzleSelector
unversionedFacebookURLWithHostPrefix
updateCoarseConversionValue
updateConversionValue
updatePostbackConversionValue
updateRolloutsStateToPersistenceWithRollouts
updateRolloutsStateWithRolloutsState
updateSessionWithBlock
updateWithArray
uploadImageWithConfiguration
uploadURLForTarget
uploadVideoWithConfiguration
uptimeMilliseconds
useAlternativeDefaultDomainPrefix
useNativeDialogForDialogName
useSafariViewControllerForDialogName
valid
validate
validateAndLogInAppPurchase
validateAndReturnError
validateAppID
validateDomainConfiguration
validateFacebookReservedURLSchemes
validateIdentifier
validateOn
validateRequiredClientAccessToken
validateURLSchemes
validateWithError
validateWithOptions
value
values
version
viewControllerDidDisappear
viewControllerForView
waitForATTUserAuthorizationWithTimeoutInterval
webDialog
webDialogDidCancel
webDialogView
webDialogViewDidCancel
webDialogViewDidFinishLoad
width
wrap2ObjectsOrErrorCompletion
wrap2ObjectsOrErrorCompletionOn
wrapBoolCompletion
wrapBoolOrErrorCompletion
wrapCompletion
wrapCompletionOn
wrapDoubleCompletion
wrapDoubleOrErrorCompletion
wrapDoubleOrErrorCompletionOn
wrapErrorCompletion
wrapErrorCompletionOn
wrapErrorOrObjectCompletion
wrapIntegerCompletion
wrapIntegerOrErrorCompletion
wrapIntegerOrErrorCompletionOn
wrapObjectCompletion
wrapObjectCompletionOn
wrapObjectOrErrorCompletion
