
from ObjectiveC.oc_custom import custom_image
from ObjectiveC.oc_custom import custom_pbxproj
from ObjectiveC import oc_util
from ObjectiveC.oc_custom.custom_file import delete_debug_macros
from ObjectiveC.oc_custom.custom_md_to_html import md_to_html
from ObjectiveC.oc_custom import custom_util
from ObjectiveC.oc_custom import custom_replace
import os
from ObjectiveC.oc_custom import custom_project

# 国内还是国外 1国内 2海外
def init(sdk_cn_or_os):
    print('j资源加密处理开始...')
    custom_image.init()
    print('j资源加密处理完成')

    print('j删除所有 XXGPLAYKIT_DEBUG 宏定义块')
    delete_debug_macros()
    print('j删除所有 XXGPLAYKIT_DEBUG 宏定义块完成')

    print('j清空 Target 的预处理器定义 (GCC_PREPROCESSOR_DEFINITIONS)')
    proj_path = f'{oc_util.path_mix_project}/{oc_util.name_current_project}.xcodeproj/project.pbxproj'
    custom_pbxproj.clear_target_preprocessor_definitions(proj_path,oc_util.name_current_project)
    print('j清空 Target 的预处理器定义 (GCC_PREPROCESSOR_DEFINITIONS)完成')

    print('j处理SDK对接文档开始...')
    handle_sdk_doc(sdk_cn_or_os)
    print('j处理SDK对接文档完成')

    print('j修改XXGThirdMiddlewares名字')
    custom_project.custom_modify_project_name(oc_util.path_mix_project,'XXGThirdMiddlewares')
    print('j修改XXGThirdMiddlewares名字完成')


    # 处理脚本
    handle_script()

# 处理SDK对接文档
def handle_sdk_doc(sdk_cn_or_os):
    # 文档路径
    doc_dir = f'{oc_util.path_mix_project}/SDK对接文档/'
    cn_doc = f'{doc_dir}闲闲SDK-国内版-iOS接入文档（3.0）.md'
    os_doc = f'{doc_dir}闲闲SDK-海外版-iOS接入文档（3.0）.md'
    
    # 根据参数保留对应文档并删除另一个
    if sdk_cn_or_os == '1':  # 国内版
        keep_doc = cn_doc
        delete_doc = os_doc
    else:  # 海外版
        keep_doc = os_doc
        delete_doc = cn_doc
    
    # 删除不需要的文档
    if os.path.exists(delete_doc):
        os.remove(delete_doc)
        print(f'已删除文档: {delete_doc}')
    
    # 将保留的文档转换为HTML，然后转换为PDF
    if os.path.exists(keep_doc):
        html_file = md_to_html(keep_doc)
        # if html_file:
        #     pdf_file = html_to_pdf(html_file)
        #     if pdf_file:
        #         print(f'已将文档转换为PDF: {pdf_file}')
        # 删除原始的Markdown文件
        os.remove(keep_doc)
        print(f'已删除原始Markdown文档: {keep_doc}')

# 处理脚本
def handle_script():
    sdk_cn_or_os = custom_util.sdk_cn_or_os
    script_path = f'{oc_util.path_mix_project}/scripts/build.sh'

    # 根据版本确定项目名称和版本信息
    if sdk_cn_or_os == '1':  # 国内版
        project_name = 'XXGPlayKitCNDemo'
        cn_or_os_text = '国内版'
    else:  # 海外版
        project_name = 'XXGPlayKitOSDemo'
        cn_or_os_text = '国际版'

    # SDK信息
    sdk_name = oc_util.name_current_project
    sdk_version = custom_util.sdk_version

    # 读取脚本文件内容
    try:
        with open(script_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # 定义所有需要替换的模式
        pattern_replacements = [
            # 工作区路径
            # (r'WORKSPACE_PATH="[^"]*"', f'WORKSPACE_PATH="../{sdk_name}.xcworkspace"'),
            # SDK名称
            (r'SDK_NAME="[^"]*"', f'SDK_NAME="{sdk_name}"'),
            # SDK版本
            (r'SDK_VERSION="[^"]*"', f'SDK_VERSION="v{sdk_version}"'),
            # 版本类型
            (r'CN_OR_OS="[^"]*"', f'CN_OR_OS="{cn_or_os_text}"'),
            # Scheme名称
            (r'SCHEME_NAME="[^"]*"', f'SCHEME_NAME="{project_name}"')
        ]

        # 批量替换所有模式
        content = custom_replace.replace_multiple_patterns_in_content(content, pattern_replacements)

        # 写回文件
        with open(script_path, 'w', encoding='utf-8') as file:
            file.write(content)

        print(f'已更新脚本文件: {script_path}')
        # print(f'WORKSPACE_PATH="../{sdk_name}.xcworkspace"')
        print(f'SDK_NAME="{sdk_name}"')
        print(f'SDK_VERSION="v{sdk_version}"')
        print(f'CN_OR_OS="{cn_or_os_text}"')
        print(f'SCHEME_NAME="{project_name}"')

    except Exception as e:
        print(f'处理脚本文件时出错: {e}')